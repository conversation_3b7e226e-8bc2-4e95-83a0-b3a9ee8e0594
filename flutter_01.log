Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter zsh-completion

## exception

FileSystemException: FileSystemException: Failed to get type of stdio handle (fd 0), path = '' (OS Error: Bad file descriptor, errno = 9)

```
#0      _StdIOUtils._getStdioInputStream (dart:io-patch/stdio_patch.dart:13:7)
#1      _stdin (dart:io/stdio.dart:480:34)
#2      _stdin (dart:io/stdio.dart)
#3      stdin (dart:io/stdio.dart:499:40)
#4      Stdio.stdin (package:flutter_tools/src/base/io.dart:269:37)
#5      Stdio.stdinHasTerminal (package:flutter_tools/src/base/io.dart:311:9)
#6      AnsiTerminal.singleCharMode= (package:flutter_tools/src/base/terminal.dart:330:17)
#7      new AnsiTerminal.<anonymous closure> (package:flutter_tools/src/base/terminal.dart:174:41)
#8      _DefaultShutdownHooks.runShutdownHooks (package:flutter_tools/src/base/process.dart:74:27)
#9      exitWithHooks (package:flutter_tools/src/base/process.dart:714:23)
<asynchronous suspension>
#10     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:135:18)
<asynchronous suspension>
#11     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#12     main (package:flutter_tools/executable.dart:93:3)
<asynchronous suspension>
```

## flutter doctor

```
[✓] Flutter (Channel stable, 3.24.0, on macOS 14.7.6 23H626 darwin-x64, locale en-VN)
    • Flutter version 3.24.0 on channel stable at /Users/<USER>/Developer/flutter
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision 80c2e84975 (1 year ago), 2024-07-30 23:06:49 +0700
    • Engine revision b8800d88be
    • Dart version 3.5.0
    • DevTools version 2.37.2

[!] Android toolchain - develop for Android devices (Android SDK version 33.0.1)
    • Android SDK at /Users/<USER>/Library/Android/sdk
    • Platform android-34, build-tools 33.0.1
    • Java binary at: /Applications/Android Studio.app/Contents/jbr/Contents/Home/bin/java
    • Java version OpenJDK Runtime Environment (build 21.0.5+-13047016-b750.29)
    ! Some Android licenses not accepted. To resolve this, run: flutter doctor --android-licenses

[!] Xcode - develop for iOS and macOS (Xcode 16.0)
    • Xcode at /Applications/Xcode.app/Contents/Developer
    • Build 16A242d
    ! iOS 18.0 Simulator not installed; this may be necessary for iOS and macOS development.
      To download and install the platform, open Xcode, select Xcode > Settings > Platforms,
      and click the GET button for the required platform.

      For more information, please visit:
        https://developer.apple.com/documentation/xcode/installing-additional-simulator-runtimes
    • CocoaPods version 1.15.2

[✓] Chrome - develop for the web
    • Chrome at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome

[✓] Android Studio (version 2024.3)
    • Android Studio at /Applications/Android Studio.app/Contents
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    • Java version OpenJDK Runtime Environment (build 21.0.5+-13047016-b750.29)

[✓] IntelliJ IDEA Community Edition (version 2024.3.3)
    • IntelliJ at /Applications/IntelliJ IDEA CE.app
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart

[✓] IntelliJ IDEA Ultimate Edition (version 2025.1)
    • IntelliJ at /Users/<USER>/Applications/IntelliJ IDEA.app
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart

[✓] VS Code (version 1.96.3)
    • VS Code at /Applications/Visual Studio Code.app/Contents
    • Flutter extension version 3.102.0

[✓] Connected device (2 available)
    • macOS (desktop) • macos  • darwin-x64     • macOS 14.7.6 23H626 darwin-x64
    • Chrome (web)    • chrome • web-javascript • Google Chrome 138.0.7204.184

[✓] Network resources
    • All expected network resources are available.

! Doctor found issues in 2 categories.
```
