# 💊 Medication App - Quản lý Tủ thuốc Cá nhân & <PERSON>ia đình

## 📱 **Ứng dụng Flutter + Spring Boot Backend**

Ứng dụng quản lý tủ thuốc thông minh với Flutter frontend và Spring Boot backend, gi<PERSON><PERSON> người dùng theo dõi thuốc, lịch uống thuốc, và quản lý sức khỏe gia đình.

---

## 🏗️ **Kiến trúc Project**

```
medication-app/
├── backend/                    # Spring Boot Backend
│   ├── src/main/java/
│   ├── src/main/resources/
│   └── build.gradle
├── frontend/                   # Flutter App
│   ├── lib/
│   ├── android/
│   ├── ios/
│   └── pubspec.yaml
├── docs/                       # Documentation
│   ├── learning/              # Tài liệu học tập
│   ├── reference/             # Tham khảo nhanh
│   ├── planning/              # Kế hoạch & lịch trình
│   └── specifications/        # Đặc tả yêu cầu
└── README.md                  # File này
```

---

## 🚀 **<PERSON><PERSON>h năng chính**

### **👤 Quản lý người dùng**

- <PERSON><PERSON><PERSON> ký/Đăng nhập
- <PERSON><PERSON> sơ cá nhân và gia đình
- Phân quyền người dùng

### **💊 Quản lý thuốc**

- Thêm/Sửa/Xóa thuốc
- Phân loại thuốc
- Hạn sử dụng
- Số lượng tồn kho

### **📅 Lịch uống thuốc**

- Tạo lịch uống thuốc
- Nhắc nhở thông minh
- Theo dõi tuân thủ

### **📋 Đơn thuốc**

- Quản lý đơn thuốc
- Lịch sử khám bệnh
- Ghi chú bác sĩ

### **🔔 Thông báo**

- Nhắc nhở uống thuốc
- Cảnh báo hết hạn
- Thông báo khẩn cấp

### **📊 Dashboard**

- Tổng quan sức khỏe
- Thống kê sử dụng thuốc
- Báo cáo tuân thủ

---

## 🛠️ **Tech Stack**

### **Backend (Spring Boot)**

- **Framework:** Spring Boot 3.x
- **Database:** PostgreSQL
- **ORM:** Spring Data JPA
- **Security:** Spring Security + JWT
- **Build Tool:** Gradle
- **API:** RESTful APIs

### **Frontend (Flutter)**

- **Framework:** Flutter 3.x
- **State Management:** Bloc Pattern
- **HTTP Client:** Dio
- **Local Storage:** SharedPreferences
- **UI Components:** Material Design 3

### **Development Tools**

- **IDE:** IntelliJ IDEA / VS Code
- **Version Control:** Git
- **Database:** PostgreSQL
- **API Testing:** Postman

---

## 📋 **Yêu cầu hệ thống**

### **Backend**

- Java 17+
- PostgreSQL 12+
- Gradle 7.6+

### **Frontend**

- Flutter 3.24+
- Dart 3.0+
- Android Studio / VS Code

---

## 🚀 **Hướng dẫn Setup**

### **1. Clone Repository**

```bash
git clone <repository-url>
cd medication-app
```

### **2. Setup Backend**

```bash
cd backend
./gradlew build
./gradlew bootRun
```

### **3. Setup Frontend**

```bash
cd frontend
flutter pub get
flutter run
```

### **4. Setup Database**

```bash
# Tạo database PostgreSQL
createdb medication_db

# Cấu hình trong application.properties
spring.datasource.url=**********************************************
```

---

## 📚 **Documentation**

### **📖 Learning (Học tập)**

- [Spring Boot Architecture](docs/learning/SPRING_BOOT_ARCHITECTURE.md)
- [Learning Curve](docs/learning/LEARNING_CURVE.md)
- [Learning Objectives](docs/learning/LEARNING_OBJECTIVES.md)

### **🔍 Reference (Tham khảo)**

- [Quick Reference](docs/reference/QUICK_REFERENCE.md)

### **📅 Planning (Kế hoạch)**

- [Roadmap](docs/planning/ROADMAP.md)
- [Part-time Schedule](docs/planning/PART_TIME_SCHEDULE.md)
- [Daily Checklist](docs/planning/DAILY_CHECKLIST.md)

### **📋 Specifications (Đặc tả)**

- [User Profile](docs/specifications/srs_user_profile.md)
- [Medicine Management](docs/specifications/srs_medicine_management.md)
- [Medication Schedule](docs/specifications/srs_medication_schedule.md)
- [Dashboard](docs/specifications/srs_dashboard.md)
- [Notifications](docs/specifications/srs_notification.md)
- [Utilities](docs/specifications/srs_utilities.md)

---

## 🎯 **Lộ trình phát triển**

### **Phase 1: Foundation (Tuần 1-2)**

- [x] Setup project structure
- [x] Spring Boot backend setup
- [x] Flutter frontend setup
- [x] Database configuration
- [ ] Basic entities and repositories
- [ ] User authentication

### **Phase 2: Core Features (Tuần 3-4)**

- [ ] User management
- [ ] Medicine management
- [ ] Basic CRUD operations
- [ ] API integration

### **Phase 3: Advanced Features (Tuần 5-6)**

- [ ] Medication scheduling
- [ ] Notifications
- [ ] Dashboard
- [ ] Prescription management

### **Phase 4: Polish & Deploy (Tuần 7-8)**

- [ ] UI/UX improvements
- [ ] Testing
- [ ] Performance optimization
- [ ] Deployment

---

## 🤝 **Đóng góp**

### **Cách đóng góp:**

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

### **Coding Standards:**

- **Backend:** Java coding conventions
- **Frontend:** Dart/Flutter conventions
- **Documentation:** Markdown format
- **Commits:** Conventional commits

---

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 📞 **Liên hệ**

- **Developer:** [Your Name]
- **Email:** [<EMAIL>]
- **GitHub:** [your-github-profile]

---

## 🙏 **Cảm ơn**

Cảm ơn bạn đã quan tâm đến dự án Medication App!

**🎯 Mục tiêu:** Tạo ứng dụng quản lý tủ thuốc thông minh, dễ sử dụng, và hữu ích cho mọi gia đình.
