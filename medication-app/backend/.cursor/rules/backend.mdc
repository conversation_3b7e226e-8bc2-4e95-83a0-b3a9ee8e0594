---
alwaysApply: true
---
# 🏗️ Backend Development Rules - Medication App

# 🏗️ Backend Development Rules - Medication App

## 📋 Tổng quan

File này định nghĩa tất cả rules, conventions và best practices cho backend development. Tất cả developers ph<PERSON><PERSON> tu<PERSON> thủ các rules này để đảm bảo code quality và consistency.

---

## 🎯 **1. NAMING CONVENTIONS**

### **1.1 Package Naming**

```java
// ✅ Đúng
package com.medication.controller;
package com.medication.service;
package com.medication.repository;
package com.medication.entity;
package com.medication.dto;
package com.medication.exception;
package com.medication.config;
package com.medication.security;
package com.medication.util;
package com.medication.constant;

// ❌ Sai
package com.medication.Controller;
package com.medication.Service;
package com.medication.controllers;
package com.medication.services;
```

### **1.2 Class Naming**

```java
// ✅ Đúng - PascalCase
public class UserController {}
public class UserService {}
public class UserRepository {}
public class User {}
public class CreateUserRequest {}
public class UserNotFoundException {}

// ❌ Sai
public class userController {}
public class User_Service {}
public class userRepository {}
```

### **1.3 Method Naming**

```java
// ✅ Đúng - camelCase
public User findById(Long id) {}
public List<User> findAll() {}
public User createUser(CreateUserRequest request) {}
public void deleteUser(Long id) {}
public boolean isUserExists(String email) {}

// ❌ Sai
public User FindById(Long id) {}
public List<User> find_all() {}
public User create_user(CreateUserRequest request) {}
```

### **1.4 Variable Naming**

```java
// ✅ Đúng - camelCase
private String fullName;
private String email;
private LocalDateTime createdAt;
private List<Medicine> medicines;
private User currentUser;

// ❌ Sai
private String full_name;
private String Email;
private LocalDateTime created_at;
private List<Medicine> Medicines;
```

### **1.5 Constant Naming**

```java
// ✅ Đúng - UPPER_SNAKE_CASE
public static final String DEFAULT_ROLE = "USER";
public static final int MAX_RETRY_ATTEMPTS = 3;
public static final String JWT_SECRET_KEY = "your-secret-key";

// ❌ Sai
public static final String defaultRole = "USER";
public static final int MAXRETRYATTEMPTS = 3;
```

---

## 🏗️ **2. ENTITY RULES**

### **2.1 Entity Structure**

```java
@Entity
@Table(name = "users") // Tên bảng snake_case
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "full_name", nullable = false) // Tên column snake_case
    private String fullName; // Tên field camelCase

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Bắt buộc có @PrePersist và @PreUpdate
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
```

### **2.2 Relationship Rules**

```java
// ✅ Đúng - Luôn dùng LAZY cho @ManyToOne và @OneToMany
@ManyToOne(fetch = FetchType.LAZY)
@JoinColumn(name = "user_id", nullable = false)
private User user;

@OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
private List<Medicine> medicines;

// ❌ Sai - Không dùng EAGER
@ManyToOne(fetch = FetchType.EAGER)
private User user;
```

### **2.3 Validation Rules**

```java
// ✅ Đúng - Sử dụng validation annotations
@NotBlank(message = "Email không được để trống")
@Email(message = "Email không hợp lệ")
@Column(unique = true, nullable = false)
private String email;

@NotNull(message = "User không được để trống")
@ManyToOne(fetch = FetchType.LAZY)
@JoinColumn(name = "user_id", nullable = false)
private User user;

// ❌ Sai - Không có validation
@Column(nullable = false)
private String email;
```

---

## 🔧 **3. REPOSITORY RULES**

### **3.1 Repository Naming**

```java
// ✅ Đúng
public interface UserRepository extends JpaRepository<User, Long> {}
public interface MedicineRepository extends JpaRepository<Medicine, Long> {}
public interface PrescriptionRepository extends JpaRepository<Prescription, Long> {}

// ❌ Sai
public interface UsersRepository extends JpaRepository<User, Long> {}
public interface UserRepo extends JpaRepository<User, Long> {}
```

### **3.2 Custom Query Naming**

```java
// ✅ Đúng - Method name phải rõ ràng
List<Medicine> findByUser(User user);
List<Medicine> findByUserAndMedicineType(User user, MedicineType type);
Optional<User> findByEmail(String email);
boolean existsByEmail(String email);
long countByUser(User user);

// ❌ Sai - Tên method không rõ ràng
List<Medicine> find(User user);
List<Medicine> findByUserType(User user, MedicineType type);
```

### **3.3 Custom Query với @Query**

```java
// ✅ Đúng - Sử dụng JPQL
@Query("SELECT m FROM Medicine m WHERE m.user = :user AND m.quantity > 0")
List<Medicine> findAvailableMedicinesByUser(@Param("user") User user);

@Query("SELECT COUNT(m) FROM Medicine m WHERE m.user = :user")
long countMedicinesByUser(@Param("user") User user);

// ❌ Sai - Không dùng native SQL trừ khi cần thiết
@Query(value = "SELECT * FROM medicines WHERE user_id = ?1", nativeQuery = true)
List<Medicine> findByUserId(Long userId);
```

---

## 🎯 **4. SERVICE RULES**

### **4.1 Service Structure**

```java
@Service
@Transactional // Bắt buộc có @Transactional
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    // Constructor injection
    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    // Business logic methods
    public User createUser(CreateUserRequest request) {
        // Validation
        validateCreateUserRequest(request);

        // Business logic
        User user = new User();
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setFullName(request.getFullName());

        // Save
        return userRepository.save(user);
    }
}
```

### **4.2 Method Naming**

```java
// ✅ Đúng - Tên method phải mô tả rõ chức năng
public User createUser(CreateUserRequest request) {}
public User updateUser(Long id, UpdateUserRequest request) {}
public void deleteUser(Long id) {}
public User findUserById(Long id) {}
public List<User> findAllUsers() {}
public boolean isUserExists(String email) {}
public void validateUserPermissions(User user, Long resourceId) {}

// ❌ Sai - Tên method không rõ ràng
public User create(CreateUserRequest request) {}
public User update(Long id, UpdateUserRequest request) {}
public void delete(Long id) {}
```

### **4.3 Exception Handling**

```java
// ✅ Đúng - Luôn throw custom exceptions
public User findUserById(Long id) {
    return userRepository.findById(id)
        .orElseThrow(() -> new UserNotFoundException("User not found with id: " + id));
}

public void deleteUser(Long id) {
    if (!userRepository.existsById(id)) {
        throw new UserNotFoundException("User not found with id: " + id);
    }
    userRepository.deleteById(id);
}

// ❌ Sai - Không xử lý exception
public User findUserById(Long id) {
    return userRepository.findById(id).orElse(null);
}
```

---

## 🌐 **5. CONTROLLER RULES**

### **5.1 Controller Structure**

```java
@RestController
@RequestMapping("/api/users") // Luôn có prefix /api
@Validated
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseEntity<UserResponse> createUser(@Valid @RequestBody CreateUserRequest request) {
        User user = userService.createUser(request);
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(UserResponse.from(user));
    }
}
```

### **5.2 HTTP Method Mapping**

```java
// ✅ Đúng - Sử dụng đúng HTTP method
@GetMapping("/{id}")           // GET /api/users/{id}
@PostMapping                   // POST /api/users
@PutMapping("/{id}")          // PUT /api/users/{id}
@PatchMapping("/{id}")        // PATCH /api/users/{id}
@DeleteMapping("/{id}")       // DELETE /api/users/{id}

// ❌ Sai - Không dùng @RequestMapping cho tất cả
@RequestMapping(value = "/{id}", method = RequestMethod.GET)
```

### **5.3 Response Structure**

```java
// ✅ Đúng - Luôn wrap response trong ResponseEntity
@GetMapping("/{id}")
public ResponseEntity<UserResponse> getUserById(@PathVariable Long id) {
    User user = userService.findUserById(id);
    return ResponseEntity.ok(UserResponse.from(user));
}

@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public ResponseEntity<UserResponse> createUser(@Valid @RequestBody CreateUserRequest request) {
    User user = userService.createUser(request);
    return ResponseEntity.status(HttpStatus.CREATED)
        .body(UserResponse.from(user));
}

// ❌ Sai - Không wrap response
@GetMapping("/{id}")
public UserResponse getUserById(@PathVariable Long id) {
    User user = userService.findUserById(id);
    return UserResponse.from(user);
}
```

---

## 📝 **6. DTO RULES**

### **6.1 DTO Naming**

```java
// ✅ Đúng - Tên DTO phải rõ ràng
public class CreateUserRequest {}
public class UpdateUserRequest {}
public class UserResponse {}
public class UserListResponse {}
public class LoginRequest {}
public class LoginResponse {}

// ❌ Sai - Tên DTO không rõ ràng
public class UserRequest {}
public class UserDto {}
public class User {}
```

### **6.2 DTO Structure**

```java
// ✅ Đúng - Request DTO
public class CreateUserRequest {
    @NotBlank(message = "Email không được để trống")
    @Email(message = "Email không hợp lệ")
    private String email;

    @NotBlank(message = "Password không được để trống")
    @Size(min = 6, message = "Password phải có ít nhất 6 ký tự")
    private String password;

    @NotBlank(message = "Full name không được để trống")
    private String fullName;

    // Getters and setters
}

// ✅ Đúng - Response DTO
public class UserResponse {
    private Long id;
    private String email;
    private String fullName;
    private LocalDateTime createdAt;

    // Static factory method
    public static UserResponse from(User user) {
        UserResponse response = new UserResponse();
        response.setId(user.getId());
        response.setEmail(user.getEmail());
        response.setFullName(user.getFullName());
        response.setCreatedAt(user.getCreatedAt());
        return response;
    }
}
```

---

## 🔒 **7. SECURITY RULES**

### **7.1 Password Handling**

```java
// ✅ Đúng - Luôn encode password
@Service
public class UserService {
    private final PasswordEncoder passwordEncoder;

    public User createUser(CreateUserRequest request) {
        User user = new User();
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword())); // Encode password
        user.setFullName(request.getFullName());
        return userRepository.save(user);
    }

    public boolean validatePassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}

// ❌ Sai - Không encode password
user.setPassword(request.getPassword());
```

### **7.2 JWT Token Rules**

```java
// ✅ Đúng - Luôn validate token
@Component
public class JwtTokenProvider {

    public String generateToken(UserDetails userDetails) {
        return Jwts.builder()
            .setSubject(userDetails.getUsername())
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + jwtExpiration))
            .signWith(SignatureAlgorithm.HS512, jwtSecret)
            .compact();
    }

    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
```

---

## 🧪 **8. TESTING RULES**

### **8.1 Test Naming**

```java
// ✅ Đúng - Tên test phải mô tả rõ scenario
@Test
void shouldCreateUser_WhenValidRequestProvided() {}

@Test
void shouldThrowException_WhenUserNotFound() {}

@Test
void shouldReturnUser_WhenValidIdProvided() {}

// ❌ Sai - Tên test không rõ ràng
@Test
void testCreateUser() {}

@Test
void testException() {}
```

### **8.2 Test Structure**

```java
// ✅ Đúng - AAA pattern (Arrange, Act, Assert)
@Test
void shouldCreateUser_WhenValidRequestProvided() {
    // Arrange
    CreateUserRequest request = new CreateUserRequest();
    request.setEmail("<EMAIL>");
    request.setPassword("password123");
    request.setFullName("Test User");

    User savedUser = new User();
    savedUser.setId(1L);
    savedUser.setEmail("<EMAIL>");
    savedUser.setFullName("Test User");

    when(userRepository.save(any(User.class))).thenReturn(savedUser);

    // Act
    UserResponse response = userService.createUser(request);

    // Assert
    assertThat(response.getEmail()).isEqualTo("<EMAIL>");
    assertThat(response.getFullName()).isEqualTo("Test User");
    verify(userRepository).save(any(User.class));
}
```

---

## 📊 **9. LOGGING RULES**

### **9.1 Logging Levels**

```java
// ✅ Đúng - Sử dụng đúng log level
private static final Logger logger = LoggerFactory.getLogger(UserService.class);

// DEBUG - Thông tin chi tiết cho debugging
logger.debug("Creating user with email: {}", email);

// INFO - Thông tin quan trọng về business flow
logger.info("User created successfully with id: {}", userId);

// WARN - Cảnh báo nhưng không ảnh hưởng đến flow
logger.warn("User login attempt with invalid email: {}", email);

// ERROR - Lỗi nghiêm trọng
logger.error("Failed to create user", exception);

// ❌ Sai - Không dùng đúng log level
logger.info("Creating user with email: {}", email); // Nên dùng DEBUG
logger.error("User not found"); // Nên dùng WARN
```

### **9.2 Logging Best Practices**

```java
// ✅ Đúng - Sử dụng placeholder
logger.info("User {} created successfully", user.getEmail());

// ✅ Đúng - Log exception
try {
    userService.createUser(request);
} catch (Exception e) {
    logger.error("Failed to create user: {}", request.getEmail(), e);
    throw e;
}

// ❌ Sai - String concatenation
logger.info("User " + user.getEmail() + " created successfully");

// ❌ Sai - Không log exception
} catch (Exception e) {
    logger.error("Failed to create user");
    throw e;
}
```

---

## 🚀 **10. PERFORMANCE RULES**

### **10.1 Database Queries**

```java
// ✅ Đúng - Sử dụng pagination
@GetMapping
public ResponseEntity<Page<UserResponse>> getUsers(
    @RequestParam(defaultValue = "0") int page,
    @RequestParam(defaultValue = "20") int size) {

    Pageable pageable = PageRequest.of(page, size);
    Page<User> users = userRepository.findAll(pageable);
    Page<UserResponse> responses = users.map(UserResponse::from);

    return ResponseEntity.ok(responses);
}

// ✅ Đúng - Sử dụng JOIN FETCH để tránh N+1
@Query("SELECT m FROM Medicine m JOIN FETCH m.user WHERE m.user = :user")
List<Medicine> findMedicinesWithUser(@Param("user") User user);

// ❌ Sai - Không có pagination
@GetMapping
public ResponseEntity<List<UserResponse>> getUsers() {
    List<User> users = userRepository.findAll(); // Có thể load quá nhiều data
    return ResponseEntity.ok(users.stream().map(UserResponse::from).collect(Collectors.toList()));
}
```

### **10.2 Caching**

```java
// ✅ Đúng - Sử dụng caching cho data ít thay đổi
@Cacheable("users")
public User findUserById(Long id) {
    return userRepository.findById(id)
        .orElseThrow(() -> new UserNotFoundException("User not found"));
}

@CacheEvict("users")
public void deleteUser(Long id) {
    userRepository.deleteById(id);
}
```

---

## 📋 **11. CODE ORGANIZATION RULES**

### **11.1 File Structure**

```
src/main/java/com/medication/
├── controller/          # REST Controllers
├── service/            # Business Logic
├── repository/         # Data Access
├── entity/            # JPA Entities
├── dto/               # Data Transfer Objects
├── exception/         # Custom Exceptions
├── config/            # Configuration Classes
├── security/          # Security Configuration
├── util/              # Utility Classes
└── constant/          # Constants
```

### **11.2 Import Organization**

```java
// ✅ Đúng - Import theo thứ tự
// 1. Java imports
import java.time.LocalDateTime;
import java.util.List;

// 2. Third-party imports
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

// 3. Application imports
import com.medication.entity.User;
import com.medication.repository.UserRepository;
import com.medication.dto.CreateUserRequest;
import com.medication.exception.UserNotFoundException;
```

---

## 🔍 **12. CODE REVIEW CHECKLIST**

### **Trước khi commit code, kiểm tra:**

- [ ] Code tuân thủ naming conventions
- [ ] Có đầy đủ validation
- [ ] Có proper exception handling
- [ ] Có unit tests
- [ ] Có proper logging
- [ ] Không có hardcoded values
- [ ] Có proper documentation
- [ ] Code được format đúng
- [ ] Không có unused imports
- [ ] Không có TODO comments
- [ ] Security được implement đúng
- [ ] Performance được tối ưu

---

## 📚 **13. DOCUMENTATION RULES**

### **13.1 JavaDoc**

```java
/**
 * Service class for managing user operations.
 *
 * <AUTHOR> Name
 * @version 1.0
 */
@Service
@Transactional
public class UserService {

    /**
     * Creates a new user with the provided information.
     *
     * @param request the user creation request containing email, password, and full name
     * @return the created user
     * @throws UserAlreadyExistsException if a user with the same email already exists
     * @throws InvalidRequestException if the request data is invalid
     */
    public User createUser(CreateUserRequest request) {
        // Implementation
    }
}
```

### **13.2 API Documentation**

```java
@Operation(summary = "Create a new user", description = "Creates a new user with the provided information")
@ApiResponses(value = {
    @ApiResponse(responseCode = "201", description = "User created successfully"),
    @ApiResponse(responseCode = "400", description = "Invalid request data"),
    @ApiResponse(responseCode = "409", description = "User already exists")
})
@PostMapping
@ResponseStatus(HttpStatus.CREATED)
public ResponseEntity<UserResponse> createUser(@Valid @RequestBody CreateUserRequest request) {
    // Implementation
}
```

---

## 🎯 **14. DEPLOYMENT RULES**

### **14.1 Environment Configuration**

```yaml
# application-dev.properties
spring.profiles.active=dev
spring.jpa.hibernate.ddl-auto=update
logging.level.com.medication=DEBUG

# application-prod.properties
spring.profiles.active=prod
spring.jpa.hibernate.ddl-auto=validate
logging.level.com.medication=WARN
```

### **14.2 Security in Production**

- [ ] JWT secret key được thay đổi
- [ ] Database credentials được bảo mật
- [ ] HTTPS được enable
- [ ] CORS được cấu hình đúng
- [ ] Rate limiting được implement
- [ ] Input validation được enable

---

## 🔄 **15. EXCEPTION HANDLING RULES**

### **15.1 Custom Exception Structure**

```java
// ✅ Đúng - Custom exception phải extend RuntimeException
public class UserNotFoundException extends RuntimeException {

    public UserNotFoundException(String message) {
        super(message);
    }

    public UserNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}

// ✅ Đúng - Business exception
public class BusinessException extends RuntimeException {
    private final String errorCode;

    public BusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }
}
```

### **15.2 Global Exception Handler**

```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(UserNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleUserNotFound(UserNotFoundException ex) {
        ErrorResponse error = new ErrorResponse("USER_NOT_FOUND", ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationErrors(MethodArgumentNotValidException ex) {
        List<String> errors = ex.getBindingResult()
            .getFieldErrors()
            .stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.toList());

        ErrorResponse error = new ErrorResponse("VALIDATION_ERROR", "Validation failed", errors);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception ex) {
        logger.error("Unexpected error occurred", ex);
        ErrorResponse error = new ErrorResponse("INTERNAL_ERROR", "An unexpected error occurred");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}
```

### **15.3 Error Response Structure**

```java
public class ErrorResponse {
    private String errorCode;
    private String message;
    private List<String> details;
    private LocalDateTime timestamp;

    public ErrorResponse(String errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }

    public ErrorResponse(String errorCode, String message, List<String> details) {
        this(errorCode, message);
        this.details = details;
    }

    // Getters and setters
}
```

---

## 🔐 **16. AUTHENTICATION & AUTHORIZATION RULES**

### **16.1 JWT Token Structure**

```java
@Component
public class JwtTokenProvider {

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", getUserIdFromUserDetails(userDetails));
        claims.put("authorities", userDetails.getAuthorities());

        return Jwts.builder()
            .setClaims(claims)
            .setSubject(userDetails.getUsername())
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + jwtExpiration))
            .signWith(SignatureAlgorithm.HS512, jwtSecret)
            .compact();
    }

    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token);
            return true;
        } catch (SignatureException | MalformedJwtException | ExpiredJwtException | UnsupportedJwtException | IllegalArgumentException e) {
            logger.error("JWT token validation failed: {}", e.getMessage());
            return false;
        }
    }
}
```

### **16.2 Security Configuration**

```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable())
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
```

### **16.3 Role-Based Access Control**

```java
// ✅ Đúng - Sử dụng @PreAuthorize
@PreAuthorize("hasRole('ADMIN') or #userId == authentication.principal.id")
public User updateUser(Long userId, UpdateUserRequest request) {
    // Implementation
}

@PreAuthorize("hasRole('ADMIN')")
public void deleteUser(Long userId) {
    // Implementation
}

// ✅ Đúng - Sử dụng @PostAuthorize
@PostAuthorize("returnObject.user.id == authentication.principal.id or hasRole('ADMIN')")
public Medicine getMedicineById(Long id) {
    return medicineRepository.findById(id)
        .orElseThrow(() -> new MedicineNotFoundException("Medicine not found"));
}
```

---

## 📊 **17. MONITORING & METRICS RULES**

### **17.1 Health Checks**

```java
@Component
public class DatabaseHealthIndicator implements HealthIndicator {

    private final DataSource dataSource;

    public DatabaseHealthIndicator(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public Health health() {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(1000)) {
                return Health.up()
                    .withDetail("database", "Available")
                    .withDetail("url", connection.getMetaData().getURL())
                    .build();
            } else {
                return Health.down()
                    .withDetail("database", "Not available")
                    .build();
            }
        } catch (SQLException e) {
            return Health.down()
                .withDetail("database", "Error")
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

### **17.2 Custom Metrics**

```java
@Component
public class UserMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter userCreationCounter;
    private final Timer userLoginTimer;

    public UserMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.userCreationCounter = Counter.builder("user.creation")
            .description("Number of users created")
            .register(meterRegistry);
        this.userLoginTimer = Timer.builder("user.login")
            .description("Time taken for user login")
            .register(meterRegistry);
    }

    public void incrementUserCreation() {
        userCreationCounter.increment();
    }

    public Timer.Sample startLoginTimer() {
        return Timer.start(meterRegistry);
    }

    public void stopLoginTimer(Timer.Sample sample) {
        sample.stop(userLoginTimer);
    }
}
```

---

## 🧹 **18. CODE CLEANLINESS RULES**

### **18.1 Method Length**

```java
// ✅ Đúng - Method ngắn gọn, dễ đọc
public User createUser(CreateUserRequest request) {
    validateCreateUserRequest(request);
    User user = mapToUser(request);
    return userRepository.save(user);
}

private void validateCreateUserRequest(CreateUserRequest request) {
    if (userRepository.existsByEmail(request.getEmail())) {
        throw new UserAlreadyExistsException("User with email already exists");
    }
}

private User mapToUser(CreateUserRequest request) {
    User user = new User();
    user.setEmail(request.getEmail());
    user.setPassword(passwordEncoder.encode(request.getPassword()));
    user.setFullName(request.getFullName());
    return user;
}

// ❌ Sai - Method quá dài, khó đọc
public User createUser(CreateUserRequest request) {
    // 50+ lines of code...
}
```

### **18.2 Magic Numbers**

```java
// ✅ Đúng - Sử dụng constants
public class UserConstants {
    public static final int MIN_PASSWORD_LENGTH = 6;
    public static final int MAX_PASSWORD_LENGTH = 100;
    public static final int MAX_EMAIL_LENGTH = 255;
    public static final int MAX_FULL_NAME_LENGTH = 100;
}

@Size(min = UserConstants.MIN_PASSWORD_LENGTH, max = UserConstants.MAX_PASSWORD_LENGTH)
private String password;

// ❌ Sai - Magic numbers
@Size(min = 6, max = 100)
private String password;
```

### **18.3 Null Checks**

```java
// ✅ Đúng - Sử dụng Optional hoặc null checks
public User findUserById(Long id) {
    return userRepository.findById(id)
        .orElseThrow(() -> new UserNotFoundException("User not found"));
}

public Optional<User> findUserByEmail(String email) {
    return userRepository.findByEmail(email);
}

// ✅ Đúng - Sử dụng Objects.requireNonNull
public User createUser(CreateUserRequest request) {
    Objects.requireNonNull(request, "Request cannot be null");
    Objects.requireNonNull(request.getEmail(), "Email cannot be null");
    // Implementation
}

// ❌ Sai - Không check null
public User createUser(CreateUserRequest request) {
    // Có thể gây NullPointerException
    userRepository.save(request);
}
```

---

## 🔄 **19. TRANSACTION RULES**

### **19.1 Transaction Boundaries**

```java
// ✅ Đúng - Service method có @Transactional
@Service
@Transactional
public class UserService {

    public User createUser(CreateUserRequest request) {
        // Tất cả database operations trong method này sẽ trong cùng 1 transaction
        User user = new User();
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setFullName(request.getFullName());

        User savedUser = userRepository.save(user);

        // Nếu có lỗi ở đây, toàn bộ transaction sẽ rollback
        auditService.logUserCreation(savedUser);

        return savedUser;
    }
}

// ❌ Sai - Repository method có @Transactional
@Repository
@Transactional // Không nên có @Transactional ở repository
public interface UserRepository extends JpaRepository<User, Long> {
}
```

### **19.2 Transaction Propagation**

```java
// ✅ Đúng - Sử dụng propagation phù hợp
@Service
@Transactional
public class UserService {

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void auditUserAction(String action) {
        // Method này sẽ tạo transaction mới, độc lập với transaction hiện tại
        auditRepository.save(new AuditLog(action));
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public User findUserById(Long id) {
        // Method này sẽ sử dụng transaction hiện tại nếu có, không tạo mới
        return userRepository.findById(id)
            .orElseThrow(() -> new UserNotFoundException("User not found"));
    }
}
```

---

## 📝 **20. API VERSIONING RULES**

### **20.1 URL Versioning**

```java
// ✅ Đúng - Sử dụng URL versioning
@RestController
@RequestMapping("/api/v1/users")
public class UserControllerV1 {
    // Version 1 implementation
}

@RestController
@RequestMapping("/api/v2/users")
public class UserControllerV2 {
    // Version 2 implementation
}
```

### **20.2 Header Versioning**

```java
// ✅ Đúng - Sử dụng header versioning
@RestController
@RequestMapping("/api/users")
public class UserController {

    @GetMapping
    public ResponseEntity<List<UserResponse>> getUsers(
        @RequestHeader(value = "API-Version", defaultValue = "1") String version) {

        if ("2".equals(version)) {
            return ResponseEntity.ok(userServiceV2.getAllUsers());
        } else {
            return ResponseEntity.ok(userServiceV1.getAllUsers());
        }
    }
}
```

---

## 🏗️ **21. CLEAN ARCHITECTURE RULES**

### **21.1 Architecture Layers**

```java
// ✅ Đúng - Tuân thủ Clean Architecture layers
┌─────────────────────────────────────┐
│           Controller Layer          │ ← REST API Endpoints (Presentation)
├─────────────────────────────────────┤
│            Service Layer            │ ← Business Logic (Application)
├─────────────────────────────────────┤
│          Repository Layer           │ ← Data Access (Infrastructure)
├─────────────────────────────────────┤
│            Entity Layer             │ ← Database Mapping (Domain)
└─────────────────────────────────────┘
```

### **21.2 Dependency Direction**

```java
// ✅ Đúng - Dependencies chỉ đi từ ngoài vào trong
Controller → Service → Repository → Entity

// ❌ Sai - Không được đi ngược lại
Entity → Repository → Service → Controller
```

### **21.3 Layer Responsibilities**

#### **Controller Layer (Presentation)**

```java
// ✅ Đúng - Controller chỉ handle HTTP requests/responses
@RestController
@RequestMapping("/api/users")
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @PostMapping
    public ResponseEntity<UserResponse> createUser(@Valid @RequestBody CreateUserRequest request) {
        User user = userService.createUser(request);
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(UserResponse.from(user));
    }

    // ❌ Sai - Không có business logic trong controller
    @PostMapping
    public ResponseEntity<UserResponse> createUser(@Valid @RequestBody CreateUserRequest request) {
        // Business logic không được ở đây
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new UserAlreadyExistsException("Email already exists");
        }
        // ...
    }
}
```

#### **Service Layer (Application)**

```java
// ✅ Đúng - Service chứa business logic
@Service
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    public User createUser(CreateUserRequest request) {
        // Business logic validation
        validateCreateUserRequest(request);

        // Business logic processing
        User user = mapToUser(request);
        User savedUser = userRepository.save(user);

        // Business logic post-processing
        auditService.logUserCreation(savedUser);

        return savedUser;
    }

    private void validateCreateUserRequest(CreateUserRequest request) {
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new UserAlreadyExistsException("User with email already exists");
        }
    }

    private User mapToUser(CreateUserRequest request) {
        User user = new User();
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setFullName(request.getFullName());
        return user;
    }
}
```

#### **Repository Layer (Infrastructure)**

```java
// ✅ Đúng - Repository chỉ handle data access
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    Optional<User> findByEmail(String email);
    boolean existsByEmail(String email);

    @Query("SELECT u FROM User u WHERE u.isActive = true")
    List<User> findActiveUsers();

    // ❌ Sai - Không có business logic trong repository
    @Query("SELECT u FROM User u WHERE u.email = :email AND u.isActive = true AND u.role = 'USER'")
    Optional<User> findValidUserByEmail(@Param("email") String email);
}
```

#### **Entity Layer (Domain)**

```java
// ✅ Đúng - Entity chỉ chứa data mapping và simple validation
@Entity
@Table(name = "users")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Email is required")
    @Email(message = "Invalid email format")
    @Column(unique = true, nullable = false)
    private String email;

    @NotBlank(message = "Password is required")
    @Column(nullable = false)
    private String password;

    // Simple business methods (domain logic)
    public boolean isActive() {
        return isActive != null && isActive;
    }

    public boolean hasRole(UserRole role) {
        return this.role == role;
    }

    // ❌ Sai - Không có complex business logic trong entity
    public void calculateComplianceRate() {
        // Complex business logic không được ở đây
        if (totalDoses == null || totalDoses == 0) {
            complianceRate = BigDecimal.ZERO;
            return;
        }
        // ...
    }
}
```

### **21.4 Business Logic Separation**

#### **✅ Đúng - Business Logic trong Service**

```java
@Service
@Transactional
public class PrescriptionService {

    private final PrescriptionRepository prescriptionRepository;

    public void calculateComplianceRate(Prescription prescription) {
        if (prescription.getTotalDoses() == null || prescription.getTotalDoses() == 0) {
            prescription.setComplianceRate(BigDecimal.ZERO);
            return;
        }

        if (prescription.getTakenDoses() == null) {
            prescription.setTakenDoses(0);
        }

        double rate = (double) prescription.getTakenDoses() / prescription.getTotalDoses() * 100;
        prescription.setComplianceRate(BigDecimal.valueOf(rate).setScale(2, BigDecimal.ROUND_HALF_UP));
    }

    public void updatePrescriptionStatus(Prescription prescription) {
        if (prescription.getStatus() == PrescriptionStatus.CANCELLED) {
            return;
        }

        LocalDate today = LocalDate.now();

        if (prescription.getEndDate() != null && today.isAfter(prescription.getEndDate())) {
            prescription.setStatus(PrescriptionStatus.EXPIRED);
        } else if (prescription.getComplianceRate() != null &&
                   prescription.getComplianceRate().compareTo(BigDecimal.valueOf(100)) >= 0) {
            prescription.setStatus(PrescriptionStatus.COMPLETED);
        } else {
            prescription.setStatus(PrescriptionStatus.ACTIVE);
        }
    }
}
```

#### **❌ Sai - Business Logic trong Entity**

```java
@Entity
public class Prescription {

    @PrePersist
    protected void onCreate() {
        // ❌ Sai - Complex business logic trong entity
        calculateComplianceRate();
        updateStatus();
    }

    @PreUpdate
    protected void onUpdate() {
        // ❌ Sai - Complex business logic trong entity
        calculateComplianceRate();
        updateStatus();
    }

    // ❌ Sai - Complex business logic trong entity
    public void calculateComplianceRate() {
        if (totalDoses == null || totalDoses == 0) {
            complianceRate = BigDecimal.ZERO;
            return;
        }
        // Complex calculation logic...
    }

    public void updateStatus() {
        // Complex status update logic...
    }
}
```

### **21.5 Utility Classes**

#### **✅ Đúng - Tạo Utility classes cho reusable logic**

```java
@Component
public class DateUtils {

    public static Integer getRemainingDays(LocalDate endDate) {
        if (endDate == null) return null;
        long days = Duration.between(LocalDate.now().atStartOfDay(),
                                   endDate.atStartOfDay()).toDays();
        return Math.max(0, (int) days);
    }

    public static BigDecimal getProgressPercentage(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return BigDecimal.ZERO;
        }

        long totalDays = Duration.between(startDate.atStartOfDay(), endDate.atStartOfDay()).toDays();
        if (totalDays <= 0) return BigDecimal.valueOf(100);

        long elapsedDays = Duration.between(startDate.atStartOfDay(),
                                          LocalDate.now().atStartOfDay()).toDays();
        double percentage = Math.min(100.0, Math.max(0.0, (double) elapsedDays / totalDays * 100));
        return BigDecimal.valueOf(percentage).setScale(2, BigDecimal.ROUND_HALF_UP);
    }
}

@Component
public class ScheduleUtils {

    public static String getDayOfWeekName(Integer dayOfWeek) {
        switch (dayOfWeek) {
            case 1: return "Thứ 2";
            case 2: return "Thứ 3";
            case 3: return "Thứ 4";
            case 4: return "Thứ 5";
            case 5: return "Thứ 6";
            case 6: return "Thứ 7";
            case 7: return "Chủ nhật";
            default: return "Không xác định";
        }
    }

    public static boolean isScheduledForToday(Integer dayOfWeek) {
        if (dayOfWeek == null) return false;
        int currentDayOfWeek = LocalDateTime.now().getDayOfWeek().getValue();
        return dayOfWeek.equals(currentDayOfWeek);
    }
}
```

### **21.6 Validation Service**

#### **✅ Đúng - Tạo Validation Service cho complex validation**

```java
@Service
public class PrescriptionValidationService {

    public boolean isDateRangeValid(LocalDate startDate, LocalDate endDate) {
        return startDate == null || endDate == null || !startDate.isAfter(endDate);
    }

    public boolean isDosesValid(Integer takenDoses, Integer totalDoses) {
        return takenDoses == null || totalDoses == null || takenDoses <= totalDoses;
    }

    public boolean isPrescriptionDateValid(LocalDate prescriptionDate, LocalDate startDate) {
        return prescriptionDate == null || startDate == null || !prescriptionDate.isAfter(startDate);
    }

    public void validatePrescription(Prescription prescription) {
        if (!isDateRangeValid(prescription.getStartDate(), prescription.getEndDate())) {
            throw new InvalidPrescriptionException("Start date must be before or equal to end date");
        }

        if (!isDosesValid(prescription.getTakenDoses(), prescription.getTotalDoses())) {
            throw new InvalidPrescriptionException("Taken doses cannot exceed total doses");
        }

        if (!isPrescriptionDateValid(prescription.getPrescriptionDate(), prescription.getStartDate())) {
            throw new InvalidPrescriptionException("Prescription date must be before or equal to start date");
        }
    }
}
```

### **21.7 Package Structure**

```java
// ✅ Đúng - Package structure theo Clean Architecture
com.medication/
├── controller/          # Presentation Layer
│   ├── UserController.java
│   ├── PrescriptionController.java
│   └── MedicineController.java
├── service/            # Application Layer (Business Logic)
│   ├── UserService.java
│   ├── PrescriptionService.java
│   ├── MedicineService.java
│   ├── PrescriptionValidationService.java
│   └── impl/           # Service implementations
│       ├── UserServiceImpl.java
│       └── PrescriptionServiceImpl.java
├── repository/         # Infrastructure Layer
│   ├── UserRepository.java
│   ├── PrescriptionRepository.java
│   └── MedicineRepository.java
├── entity/            # Domain Layer
│   ├── User.java
│   ├── Prescription.java
│   └── Medicine.java
├── dto/               # Data Transfer Objects
│   ├── request/
│   │   ├── CreateUserRequest.java
│   │   └── UpdateUserRequest.java
│   └── response/
│       ├── UserResponse.java
│       └── PrescriptionResponse.java
├── util/              # Utility Classes
│   ├── DateUtils.java
│   ├── ScheduleUtils.java
│   └── ValidationUtils.java
├── exception/         # Custom Exceptions
│   ├── UserNotFoundException.java
│   └── InvalidPrescriptionException.java
└── config/           # Configuration
    ├── SecurityConfig.java
    └── DatabaseConfig.java
```

### **21.8 Clean Architecture Checklist**

#### **Trước khi commit code, kiểm tra:**

- [ ] **Dependency Direction**: Dependencies chỉ đi từ ngoài vào trong
- [ ] **Layer Separation**: Mỗi layer có trách nhiệm rõ ràng
- [ ] **Business Logic**: Business logic chỉ ở Service layer
- [ ] **Entity Purity**: Entities chỉ chứa data mapping và simple validation
- [ ] **Controller Simplicity**: Controllers chỉ handle HTTP requests/responses
- [ ] **Repository Focus**: Repositories chỉ handle data access
- [ ] **Utility Classes**: Reusable logic được tách ra utility classes
- [ ] **Validation Service**: Complex validation được tách ra service riêng
- [ ] **No Cross-Layer Dependencies**: Không có dependencies ngược
- [ ] **Single Responsibility**: Mỗi class có một trách nhiệm duy nhất

---

## 📞 **22. SUPPORT & CONTACT**

### **Khi có thắc mắc về rules:**

1. Đọc lại file này
2. Tham khảo Spring Boot documentation
3. Hỏi team lead hoặc senior developer
4. Tạo issue để thảo luận

### **Cập nhật rules:**

- Chỉ team lead mới được cập nhật rules
- Phải có approval từ team
- Phải update version và changelog

---

**Version**: 1.2  
**Last Updated**: December 2024  
**Status**: Active - Must Follow

### **Changelog:**

**Version 1.2 (December 2024)**

- ✅ Added Clean Architecture Rules (Section 21)
- ✅ Added Business Logic Separation guidelines
- ✅ Added Utility Classes best practices
- ✅ Added Validation Service patterns
- ✅ Added Package Structure recommendations
- ✅ Added Clean Architecture Checklist

**Version 1.1 (December 2024)**

- ✅ Initial version with basic Spring Boot rules
- ✅ Added naming conventions
- ✅ Added entity, repository, service, controller rules
- ✅ Added security and testing guidelines
