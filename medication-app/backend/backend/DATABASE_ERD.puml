@startuml Medication Database ERD

!define table(x) class x << (T,#FFAAAA) >>
!define primary_key(x) <b>x</b>
!define foreign_key(x) <i>x</i>

title Medication Database - Entity Relationship Diagram

' Users table
table(users) {
  primary_key(id) : bigint
  email : varchar(255)
  password : varchar(255)
  full_name : varchar(255)
  created_at : timestamp
  updated_at : timestamp
  phone_number : varchar(20)
  role : user_role_enum
  is_active : boolean
  email_verified : boolean
  last_login_at : timestamp
  user_type : user_type_enum
  anonymous_user_id : uuid
}

' Anonymous Sessions table
table(anonymous_sessions) {
  primary_key(id) : bigint
  foreign_key(anonymous_user_id) : uuid
  device_id : varchar(100)
  app_version : varchar(20)
  created_at : timestamp
  last_activity_at : timestamp
  data_synced : boolean
  foreign_key(migrated_to_user_id) : bigint
  migrated_at : timestamp
  status : anonymous_session_status_enum
}

' Families table
table(families) {
  primary_key(id) : bigint
  name : varchar(255)
  description : varchar(500)
  is_active : boolean
  created_at : timestamp
  updated_at : timestamp
}

' Family Members table
table(family_members) {
  primary_key(id) : bigint
  foreign_key(user_id) : bigint
  foreign_key(family_id) : bigint
  role : family_role_enum
  is_active : boolean
  created_at : timestamp
  updated_at : timestamp
}

' Medicines table
table(medicines) {
  primary_key(id) : bigint
  foreign_key(user_id) : bigint
  name : varchar(255)
  description : text
  manufacturer : varchar(255)
  active_ingredient : varchar(255)
  dosage_form : varchar(100)
  strength : varchar(100)
  quantity : integer
  unit : varchar(50)
  expiry_date : date
  batch_number : varchar(100)
  is_active : boolean
  created_at : timestamp
  updated_at : timestamp
  user_type : user_type_enum
  foreign_key(anonymous_user_id) : uuid
}

' Prescriptions table
table(prescriptions) {
  primary_key(id) : bigint
  foreign_key(patient_id) : bigint
  foreign_key(prescribed_by) : bigint
  prescription_date : date
  start_date : date
  end_date : date
  diagnosis : text
  instructions : text
  status : prescription_status_enum
  is_active : boolean
  created_at : timestamp
  updated_at : timestamp
  user_type : user_type_enum
  foreign_key(anonymous_user_id) : uuid
}

' Medication Doses table
table(medication_doses) {
  primary_key(id) : bigint
  foreign_key(prescription_id) : bigint
  foreign_key(medicine_id) : bigint
  dose_amount : decimal
  dose_unit : dose_unit_enum
  frequency : integer
  frequency_unit : frequency_unit_enum
  route : route_enum
  instructions : text
  status : medication_dose_status_enum
  created_at : timestamp
  updated_at : timestamp
}

' Medication Schedules table
table(medication_schedules) {
  primary_key(id) : bigint
  foreign_key(medication_dose_id) : bigint
  schedule_date : date
  scheduled_time : time
  status : schedule_status_enum
  taken_at : timestamp
  skipped_at : timestamp
  notes : text
  created_at : timestamp
  updated_at : timestamp
}

' Notifications table
table(notifications) {
  primary_key(id) : bigint
  foreign_key(user_id) : bigint
  title : varchar(255)
  message : text
  notification_type : notification_type_enum
  status : notification_status_enum
  scheduled_at : timestamp
  sent_at : timestamp
  read_at : timestamp
  retry_count : integer
  is_active : boolean
  created_at : timestamp
  updated_at : timestamp
}

' Audit Logs table
table(audit_logs) {
  primary_key(id) : bigint
  foreign_key(user_id) : bigint
  action : audit_action_enum
  entity_type : varchar(100)
  entity_id : bigint
  description : varchar(500)
  old_values : text
  new_values : text
  severity : audit_severity_enum
  ip_address : varchar(45)
  user_agent : varchar(500)
  created_at : timestamp
  foreign_key(anonymous_user_id) : uuid
}

' Relationships
users ||--o{ family_members : "has"
families ||--o{ family_members : "contains"
users ||--o{ medicines : "owns"
users ||--o{ prescriptions : "has"
users ||--o{ prescriptions : "prescribes"
users ||--o{ notifications : "receives"
users ||--o{ audit_logs : "performs"
users ||--o{ anonymous_sessions : "migrates_to"
anonymous_sessions ||--o{ medicines : "owns_anonymous"
anonymous_sessions ||--o{ prescriptions : "has_anonymous"
anonymous_sessions ||--o{ audit_logs : "performs_anonymous"
prescriptions ||--o{ medication_doses : "contains"
medication_doses ||--o{ medication_schedules : "schedules"
medicines ||--o{ medication_doses : "used_in"

' Anonymous user relationship
users ||--o| anonymous_sessions : "soft_fk"

@enduml 