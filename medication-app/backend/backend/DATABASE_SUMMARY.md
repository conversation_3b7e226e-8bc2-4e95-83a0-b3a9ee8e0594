# 📊 Medication Database Schema Summary

## 🏗️ Database Overview

**Total Tables:** 17  
**Total Columns:** ~200+  
**Database:** PostgreSQL 15.13  
**Migration Version:** V28 (Latest)

---

## 📋 Core Tables

### 👥 **User Management**

| Table                | Columns | PK  | FK  | Description                            |
| -------------------- | ------- | --- | --- | -------------------------------------- |
| `users`              | 13      | 1   | 0   | User accounts (registered & anonymous) |
| `anonymous_sessions` | 11      | 1   | 2   | Anonymous user sessions (id: BIGSERIAL) |
| `families`           | 6       | 1   | 0   | Family groups                          |
| `family_members`     | 9       | 1   | 2   | Family membership                      |

### 💊 **Medication Management**

| Table                    | Columns | PK  | FK  | Description                   |
| ------------------------ | ------- | --- | --- | ----------------------------- |
| `medicines`              | 19      | 1   | 4   | Medicine inventory            |
| `prescriptions`          | 21      | 1   | 3   | Medical prescriptions         |
| `medication_doses`       | 13      | 1   | 1   | Prescription doses            |
| `medication_schedules`   | 10      | 1   | 1   | Medication schedules          |
| `prescription_items`     | 11      | 1   | 2   | Prescription line items       |
| `prescription_medicines` | 4       | 2   | 2   | Medicine-prescription mapping |

### 🔔 **Notification System**

| Table           | Columns | PK  | FK  | Description        |
| --------------- | ------- | --- | --- | ------------------ |
| `notifications` | 13      | 1   | 1   | User notifications |

### 📅 **Scheduling System**

| Table                        | Columns | PK  | FK  | Description       |
| ---------------------------- | ------- | --- | --- | ----------------- |
| `schedule_patterns`          | 10      | 1   | 1   | Schedule patterns |
| `schedule_pattern_weekdays`  | 3       | 2   | 1   | Weekly patterns   |
| `schedule_pattern_monthdays` | 3       | 2   | 1   | Monthly patterns  |

### 📊 **Audit & Reference**

| Table            | Columns | PK  | FK  | Description             |
| ---------------- | ------- | --- | --- | ----------------------- |
| `audit_logs`     | 13      | 1   | 1   | System audit trail      |
| `medicine_types` | 4       | 1   | 0   | Medicine type reference |
| `medicine_units` | 4       | 1   | 0   | Unit reference          |

---

## 🔑 Key Features

### ✅ **Anonymous User Support**

- `users.user_type`: `ANONYMOUS`, `REGISTERED`, `PREMIUM`
- `users.anonymous_user_id`: UUID for anonymous users
- `anonymous_sessions`: Session management for anonymous users
- Soft foreign keys with `ON DELETE SET NULL`

### ✅ **International Standards**

- All column names in English
- Standard data types (UUID, timestamps, enums)
- Proper foreign key constraints

### ✅ **Performance Optimized**

- Indexes on frequently queried columns
- Composite indexes for complex queries
- Partitioning-ready structure

### ✅ **Audit & Compliance**

- Complete audit trail with `audit_logs`
- User action tracking
- Data change history

---

## 🎯 Enum Types

### **User Management**

- `user_type_enum`: `ANONYMOUS`, `REGISTERED`, `PREMIUM`
- `user_role_enum`: `ADMIN`, `USER`, `DOCTOR`, `PHARMACIST`
- `family_role_enum`: `OWNER`, `ADMIN`, `MEMBER`
- `anonymous_session_status_enum`: `ACTIVE`, `EXPIRED`, `MIGRATED`, `CLEANED`

### **Medication**

- `prescription_status_enum`: `ACTIVE`, `COMPLETED`, `CANCELLED`, `EXPIRED`
- `medication_dose_status_enum`: `ACTIVE`, `COMPLETED`, `CANCELLED`
- `schedule_status_enum`: `PENDING`, `TAKEN`, `SKIPPED`
- `dose_unit_enum`: `MG`, `MCG`, `ML`, `TABLET`, `CAPSULE`
- `route_enum`: `ORAL`, `INJECTION`, `TOPICAL`, `INHALATION`

### **Notifications**

- `notification_type_enum`: `REMINDER`, `ALERT`, `INFO`
- `notification_status_enum`: `PENDING`, `SENT`, `READ`

### **Audit**

- `audit_action_enum`: `CREATE`, `UPDATE`, `DELETE`, `LOGIN`, `LOGOUT`, `MIGRATE`
- `audit_severity_enum`: `LOW`, `MEDIUM`, `HIGH`, `CRITICAL`

---

## 🔄 Migration History

| Version | Description                      | Applied |
| ------- | -------------------------------- | ------- |
| V1-V23  | Initial schema setup             | ✅      |
| V24     | Anonymous user support           | ✅      |
| V25     | Enum types for anonymous support | ✅      |
| V26     | Rename Vietnamese columns        | ✅      |
| V27     | Full English column names        | ✅      |
| V28     | Fix anonymous_sessions.id to BIGSERIAL | ✅      |

---

## 🚀 Ready for Development

### **Next Steps:**

1. ✅ **Database Schema** - Complete
2. 🔄 **JPA Entities** - Ready to create
3. 🔄 **Repository Layer** - Ready to implement
4. 🔄 **Service Layer** - Ready to implement
5. 🔄 **Controller Layer** - Ready to implement

### **Key Benefits:**

- **Scalable**: Supports both anonymous and registered users
- **Portable**: Works with any PostgreSQL-compatible database
- **Maintainable**: Clean, documented schema
- **Secure**: Proper foreign keys and constraints
- **Performant**: Optimized indexes and structure

---

_Generated on: 2025-07-28_  
_Database Version: V28_  
_Total Migration Scripts: 28_
 