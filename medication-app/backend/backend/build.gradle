plugins {
	id 'java'
	id 'org.springframework.boot' version '3.3.0'
	id 'io.spring.dependency-management' version '1.1.5'
	id 'org.flywaydb.flyway' version '10.10.0'
}

group = 'com.medication'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

repositories {
	mavenCentral()
}

dependencies {
	// Spring Boot Starters
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-starter-cache'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-mail'
	implementation 'jakarta.mail:jakarta.mail-api'
	implementation 'com.sun.mail:jakarta.mail:2.0.1'
	
	// Database & Migration
	runtimeOnly 'org.postgresql:postgresql'
	implementation 'org.flywaydb:flyway-core'
	implementation 'org.flywaydb:flyway-database-postgresql'
	
	// JWT Security
	implementation 'io.jsonwebtoken:jjwt-api:0.12.3'
	runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.3'
	runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.3'
	
	// Lombok
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	
	// Redis Cache (disabled for now)
	// implementation 'org.springframework.boot:spring-boot-starter-data-redis'
	
	// API Documentation
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0'
	
	// Utilities
	implementation 'org.apache.commons:commons-lang3:3.12.0'
	implementation 'commons-io:commons-io:2.11.0'
	
	// Testing
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.security:spring-security-test'
	testImplementation 'org.testcontainers:postgresql:1.19.3'
	testImplementation 'org.testcontainers:junit-jupiter:1.19.3'
	testRuntimeOnly 'com.h2database:h2'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

// Flyway configuration
flyway {
	url = '**********************************************'
	user = 'dongtran'
	password = ''
	locations = ['classpath:db/migration']
	baselineOnMigrate = true
	outOfOrder = true
	driver = 'org.postgresql.Driver'
}

tasks.named('test') {
	useJUnitPlatform()
}
