#!/bin/bash

echo "Testing server..."

# Test health endpoint
echo "1. Health check:"
curl -s http://localhost:8081/actuator/health | head -5

echo -e "\n2. Swagger UI check:"
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:8081/swagger-ui/index.html

echo -e "\n3. OpenAPI docs check:"
curl -s http://localhost:8081/v3/api-docs | grep -o '"bearerAuth"' | head -1

echo -e "\n4. Protected endpoint without token:"
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:8081/api/medicines

echo -e "\n5. Register test user:"
curl -s -X POST http://localhost:8081/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","fullName":"Test User"}' | head -3

echo -e "\n6. Login test user:"
curl -s -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' | head -5

echo -e "\nDone!"
