#!/bin/bash

# Simple test script for Medication Schedule APIs

BASE_URL="http://localhost:8081"
ANONYMOUS_USER_ID="550e8400-e29b-41d4-a716-446655440000"

echo "🧪 Testing Medication Schedule APIs"
echo "=================================="

# Test 1: Health check
echo "1. Testing health endpoint..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" "$BASE_URL/actuator/health"

# Test 2: Get all anonymous schedules
echo "2. Testing GET /api/anonymous/medication-schedules..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules?page=0&size=10"

# Test 3: Get schedules by day
echo "3. Testing GET /api/anonymous/medication-schedules/by-day/1..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/by-day/1"

# Test 4: Get schedules by time range
echo "4. Testing GET /api/anonymous/medication-schedules/by-time-range..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/by-time-range?startTime=08:00&endTime=18:00"

# Test 5: Get schedules by dose unit
echo "5. Testing GET /api/anonymous/medication-schedules/by-dose-unit/mg..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/by-dose-unit/mg"

# Test 6: Get schedules by dose range
echo "6. Testing GET /api/anonymous/medication-schedules/by-dose-range..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/by-dose-range?minDose=1.0&maxDose=100.0"

# Test 7: Search schedules
echo "7. Testing GET /api/anonymous/medication-schedules/search..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/search?searchText=morning"

# Test 8: Count schedules
echo "8. Testing GET /api/anonymous/medication-schedules/count..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/count"

# Test 9: Get active schedules
echo "9. Testing GET /api/anonymous/medication-schedules/active..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/active?page=0&size=10"

# Test 10: Invalid day of week (should return 400)
echo "10. Testing invalid day of week (should return 400)..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/by-day/8"

# Test 11: Invalid time range (should return 400)
echo "11. Testing invalid time range (should return 400)..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/by-time-range?startTime=18:00&endTime=08:00"

# Test 12: Empty search text (should return 400)
echo "12. Testing empty search text (should return 400)..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/search?searchText="

# Test 13: Invalid dose range (should return 400)
echo "13. Testing invalid dose range (should return 400)..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  -H "X-Anonymous-User-Id: $ANONYMOUS_USER_ID" \
  "$BASE_URL/api/anonymous/medication-schedules/by-dose-range?minDose=100.0&maxDose=1.0"

# Test 14: Registered user endpoint without JWT (should return 401/403)
echo "14. Testing registered user endpoint without JWT (should return 401/403)..."
curl -s -o /dev/null -w "HTTP %{http_code}\n" \
  "$BASE_URL/api/medication-schedules"

echo ""
echo "✅ All tests completed!"
