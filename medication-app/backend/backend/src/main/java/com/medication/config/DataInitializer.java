package com.medication.config;

import com.medication.entity.MedicineType;
import com.medication.repository.domain.medicine.MedicineTypeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * Data initializer để tạo sample data
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {

    private final MedicineTypeRepository medicineTypeRepository;

    @Override
    public void run(String... args) throws Exception {
        log.info("Initializing sample data...");
        
        // Tạo medicine types mẫu nếu chưa có
        if (medicineTypeRepository.count() == 0) {
            createSampleMedicineTypes();
        }
        
        log.info("Sample data initialization completed");
    }

    private void createSampleMedicineTypes() {
        log.info("Creating sample medicine types...");
        
        MedicineType tablet = new MedicineType();
        tablet.setTypeName("Tablet");
        medicineTypeRepository.save(tablet);
        
        MedicineType capsule = new MedicineType();
        capsule.setTypeName("Capsule");
        medicineTypeRepository.save(capsule);
        
        MedicineType syrup = new MedicineType();
        syrup.setTypeName("Syrup");
        medicineTypeRepository.save(syrup);
        
        MedicineType injection = new MedicineType();
        injection.setTypeName("Injection");
        medicineTypeRepository.save(injection);
        
        MedicineType cream = new MedicineType();
        cream.setTypeName("Cream");
        medicineTypeRepository.save(cream);
        
        log.info("Created {} sample medicine types", medicineTypeRepository.count());
    }
} 