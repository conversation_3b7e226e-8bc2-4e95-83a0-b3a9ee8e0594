package com.medication.constant;

/**
 * Business codes cho API responses
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public final class BusinessCode {
    
    // Success codes
    public static final String SUCCESS = "SUCCESS";
    public static final String CREATED = "CREATED";
    public static final String NO_CONTENT = "NO_CONTENT";
    
    // Error codes - General
    public static final String BAD_REQUEST = "BAD_REQUEST";
    public static final String VALIDATION_ERROR = "VALIDATION_ERROR";
    public static final String NOT_FOUND = "NOT_FOUND";
    public static final String CONFLICT = "CONFLICT";
    public static final String INTERNAL_ERROR = "INTERNAL_ERROR";
    public static final String INVALID_REQUEST = "INVALID_REQUEST";
    
    // Error codes - Anonymous Session specific
    public static final String SESSION_NOT_FOUND = "SESSION_NOT_FOUND";
    public static final String SESSION_CREATED = "SESSION_CREATED";
    public static final String SESSION_UPDATED = "SESSION_UPDATED";
    public static final String SESSION_EXPIRED = "SESSION_EXPIRED";
    public static final String SESSION_MIGRATED = "SESSION_MIGRATED";
    public static final String SESSION_ALREADY_MIGRATED = "SESSION_ALREADY_MIGRATED";
    public static final String SESSION_ACTIVITY_UPDATED = "SESSION_ACTIVITY_UPDATED";
    
    // Error codes - User specific
    public static final String USER_NOT_FOUND = "USER_NOT_FOUND";
    public static final String USER_CREATED = "USER_CREATED";
    public static final String USER_UPDATED = "USER_UPDATED";
    public static final String USER_ALREADY_EXISTS = "USER_ALREADY_EXISTS";
    
    // Error codes - Authentication
    public static final String AUTH_INVALID_CREDENTIALS = "AUTH_INVALID_CREDENTIALS";
    public static final String AUTH_TOKEN_EXPIRED = "AUTH_TOKEN_EXPIRED";
    public static final String AUTH_UNAUTHORIZED = "AUTH_UNAUTHORIZED";
    public static final String AUTH_FORBIDDEN = "AUTH_FORBIDDEN";
    
    // Error codes - Medicine specific
    public static final String MEDICINE_NOT_FOUND = "MEDICINE_NOT_FOUND";
    public static final String MEDICINE_CREATED = "MEDICINE_CREATED";
    public static final String MEDICINE_UPDATED = "MEDICINE_UPDATED";
    public static final String MEDICINE_DELETED = "MEDICINE_DELETED";
    
    // Error codes - Prescription specific
    public static final String PRESCRIPTION_NOT_FOUND = "PRESCRIPTION_NOT_FOUND";
    public static final String PRESCRIPTION_CREATED = "PRESCRIPTION_CREATED";
    public static final String PRESCRIPTION_UPDATED = "PRESCRIPTION_UPDATED";
    public static final String PRESCRIPTION_EXPIRED = "PRESCRIPTION_EXPIRED";
    
    private BusinessCode() {
        // Prevent instantiation
    }
}