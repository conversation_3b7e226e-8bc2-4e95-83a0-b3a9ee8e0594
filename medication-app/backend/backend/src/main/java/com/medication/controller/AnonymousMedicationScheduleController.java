package com.medication.controller;

import com.medication.dto.request.CreateMedicationScheduleRequest;
import com.medication.dto.request.UpdateMedicationScheduleRequest;
import com.medication.dto.response.ApiResponse;
import com.medication.dto.response.MedicationScheduleResponse;
import com.medication.dto.response.MedicationScheduleStatsResponse;
import com.medication.entity.MedicationSchedule;
import com.medication.enums.DoseUnit;
import com.medication.service.domain.schedule.MedicationScheduleBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.util.List;
import java.util.UUID;

/**
 * Controller for anonymous user medication schedule operations
 * Handles CRUD operations for medication schedules without user authentication
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/anonymous/medication-schedules")
@RequiredArgsConstructor
@Tag(name = "Anonymous Medication Schedule", description = "Medication schedule operations for anonymous users")
public class AnonymousMedicationScheduleController {

    private final MedicationScheduleBusinessService medicationScheduleBusinessService;

    @PostMapping
    @Operation(summary = "Create medication schedule", description = "Create a new medication schedule for anonymous user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Medication schedule created successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<MedicationScheduleResponse>> createMedicationSchedule(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Medication schedule creation request", required = true)
            @Valid @RequestBody CreateMedicationScheduleRequest request) {
        
        log.info("Creating medication schedule for anonymous user: {}", anonymousUserId);
        
        MedicationSchedule createdSchedule = medicationScheduleBusinessService
                .createMedicationScheduleForAnonymousUser(request, anonymousUserId);
        
        MedicationScheduleResponse response = medicationScheduleBusinessService.convertToResponse(createdSchedule);
        
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.created(response, "Medication schedule created successfully"));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get medication schedule by ID", description = "Retrieve a specific medication schedule by ID for anonymous user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedule found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medication schedule not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<MedicationScheduleResponse>> getMedicationSchedule(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Medication schedule ID", required = true)
            @PathVariable Long id) {
        
        log.debug("Getting medication schedule {} for anonymous user: {}", id, anonymousUserId);
        
        MedicationSchedule schedule = medicationScheduleBusinessService
                .findMedicationScheduleByIdForAnonymousUser(id, anonymousUserId);
        
        MedicationScheduleResponse response = medicationScheduleBusinessService.convertToResponse(schedule);
        
        return ResponseEntity.ok(ApiResponse.success(response, "Medication schedule found successfully"));
    }

    @GetMapping
    @Operation(summary = "Get medication schedules with pagination", description = "Retrieve medication schedules for anonymous user with pagination and sorting")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules retrieved successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid pagination parameters"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<Page<MedicationScheduleResponse>>> getMedicationSchedules(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size", example = "10")
            @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field", example = "dayOfWeek")
            @RequestParam(defaultValue = "dayOfWeek") String sortBy,
            @Parameter(description = "Sort direction", example = "asc")
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        log.debug("Getting medication schedules for anonymous user: {} with pagination", anonymousUserId);
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<MedicationSchedule> schedulePage = medicationScheduleBusinessService
                .getMedicationSchedulesForAnonymousUser(anonymousUserId, pageable);
        
        Page<MedicationScheduleResponse> responsePage = schedulePage
                .map(medicationScheduleBusinessService::convertToResponse);
        
        return ResponseEntity.ok(ApiResponse.success(responsePage, "Medication schedules found successfully"));
    }

    @GetMapping("/by-prescription-item/{prescriptionItemId}")
    @Operation(summary = "Get medication schedules by prescription item", description = "Retrieve medication schedules for a specific prescription item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> getMedicationSchedulesByPrescriptionItem(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Prescription item ID", required = true)
            @PathVariable Long prescriptionItemId) {
        
        log.debug("Getting medication schedules for prescription item {} and anonymous user: {}", prescriptionItemId, anonymousUserId);
        
        List<MedicationSchedule> schedules = medicationScheduleBusinessService
                .getMedicationSchedulesByPrescriptionItemIdForAnonymousUser(prescriptionItemId, anonymousUserId);
        
        List<MedicationScheduleResponse> responses = schedules.stream()
                .map(medicationScheduleBusinessService::convertToResponse)
                .toList();
        
        return ResponseEntity.ok(ApiResponse.success(responses, "Medication schedules found successfully"));
    }

    @GetMapping("/by-day/{dayOfWeek}")
    @Operation(summary = "Get medication schedules by day of week", description = "Retrieve medication schedules for a specific day of week (1=Monday, 7=Sunday)")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid day of week"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> getMedicationSchedulesByDayOfWeek(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Day of week (1=Monday, 7=Sunday)", required = true)
            @PathVariable Integer dayOfWeek) {
        
        log.debug("Getting medication schedules for day {} and anonymous user: {}", dayOfWeek, anonymousUserId);
        
        if (dayOfWeek < 1 || dayOfWeek > 7) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("Day of week must be between 1 and 7"));
        }

        List<MedicationSchedule> schedules = medicationScheduleBusinessService
                .getMedicationSchedulesByDayOfWeekForAnonymousUser(anonymousUserId, dayOfWeek);

        List<MedicationScheduleResponse> responses = schedules.stream()
                .map(medicationScheduleBusinessService::convertToResponse)
                .toList();

        return ResponseEntity.ok(ApiResponse.success(responses, "Medication schedules found successfully"));
    }

    @GetMapping("/by-time-range")
    @Operation(summary = "Get medication schedules by time range", description = "Retrieve medication schedules within a specific time range")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid time range"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> getMedicationSchedulesByTimeRange(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Start time (HH:mm format)", required = true, example = "08:00")
            @RequestParam String startTime,
            @Parameter(description = "End time (HH:mm format)", required = true, example = "18:00")
            @RequestParam String endTime) {
        
        log.debug("Getting medication schedules for time range {} - {} and anonymous user: {}", startTime, endTime, anonymousUserId);
        
        try {
            LocalTime start = LocalTime.parse(startTime);
            LocalTime end = LocalTime.parse(endTime);
            
            if (start.isAfter(end)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "INVALID_TIME_RANGE", "Start time must be before end time"));
            }
            
            List<MedicationSchedule> schedules = medicationScheduleBusinessService
                    .getMedicationSchedulesByTimeRangeForAnonymousUser(anonymousUserId, start, end);
            
            List<MedicationScheduleResponse> responses = schedules.stream()
                    .map(medicationScheduleBusinessService::convertToResponse)
                    .toList();
            
            return ResponseEntity.ok(ApiResponse.success(responses, "Medication schedules found successfully"));
            
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "INVALID_TIME_FORMAT", "Invalid time format. Use HH:mm format"));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update medication schedule", description = "Update an existing medication schedule for anonymous user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedule updated successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medication schedule not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<MedicationScheduleResponse>> updateMedicationSchedule(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Medication schedule ID", required = true)
            @PathVariable Long id,
            @Parameter(description = "Medication schedule update request", required = true)
            @Valid @RequestBody UpdateMedicationScheduleRequest request) {
        
        log.info("Updating medication schedule {} for anonymous user: {}", id, anonymousUserId);
        
        MedicationSchedule updatedSchedule = medicationScheduleBusinessService
                .updateMedicationScheduleForAnonymousUser(id, request, anonymousUserId);
        
        MedicationScheduleResponse response = medicationScheduleBusinessService.convertToResponse(updatedSchedule);
        
        return ResponseEntity.ok(ApiResponse.success(response, "Medication schedule updated successfully"));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete medication schedule", description = "Delete a medication schedule for anonymous user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedule deleted successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medication schedule not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<String>> deleteMedicationSchedule(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Medication schedule ID", required = true)
            @PathVariable Long id) {
        
        log.info("Deleting medication schedule {} for anonymous user: {}", id, anonymousUserId);
        
        medicationScheduleBusinessService.deleteMedicationScheduleForAnonymousUser(id, anonymousUserId);
        
        return ResponseEntity.ok(ApiResponse.success("Medication schedule deleted successfully"));
    }

    @GetMapping("/by-dose-unit/{doseUnit}")
    @Operation(summary = "Get medication schedules by dose unit", description = "Retrieve medication schedules for a specific dose unit")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> getMedicationSchedulesByDoseUnit(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Dose unit", required = true)
            @PathVariable DoseUnit doseUnit) {

        log.debug("Getting medication schedules for dose unit {} and anonymous user: {}", doseUnit, anonymousUserId);

        List<MedicationSchedule> schedules = medicationScheduleBusinessService
                .getMedicationSchedulesByDoseUnitForAnonymousUser(anonymousUserId, doseUnit);

        List<MedicationScheduleResponse> responses = schedules.stream()
                .map(medicationScheduleBusinessService::convertToResponse)
                .toList();

        return ResponseEntity.ok(ApiResponse.success(responses, "Medication schedules found successfully"));
    }

    @GetMapping("/by-dose-range")
    @Operation(summary = "Get medication schedules by dose amount range", description = "Retrieve medication schedules within a specific dose amount range")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid dose range"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> getMedicationSchedulesByDoseRange(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Minimum dose amount", required = true)
            @RequestParam Double minDose,
            @Parameter(description = "Maximum dose amount", required = true)
            @RequestParam Double maxDose) {

        log.debug("Getting medication schedules for dose range {}-{} and anonymous user: {}", minDose, maxDose, anonymousUserId);

        if (minDose > maxDose) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("Minimum dose must be less than or equal to maximum dose"));
        }

        List<MedicationSchedule> schedules = medicationScheduleBusinessService
                .getMedicationSchedulesByDoseAmountRangeForAnonymousUser(anonymousUserId, minDose, maxDose);

        List<MedicationScheduleResponse> responses = schedules.stream()
                .map(medicationScheduleBusinessService::convertToResponse)
                .toList();

        return ResponseEntity.ok(ApiResponse.success(responses, "Medication schedules found successfully"));
    }

    @GetMapping("/search")
    @Operation(summary = "Search medication schedules by notes", description = "Search medication schedules by notes content")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid search parameters"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> searchMedicationSchedulesByNotes(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Search text in notes", required = true)
            @RequestParam String searchText) {

        log.debug("Searching medication schedules for text '{}' and anonymous user: {}", searchText, anonymousUserId);

        if (searchText == null || searchText.trim().isEmpty()) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("Search text cannot be empty"));
        }

        List<MedicationSchedule> schedules = medicationScheduleBusinessService
                .searchMedicationSchedulesByNotesForAnonymousUser(anonymousUserId, searchText.trim());

        List<MedicationScheduleResponse> responses = schedules.stream()
                .map(medicationScheduleBusinessService::convertToResponse)
                .toList();

        return ResponseEntity.ok(ApiResponse.success(responses, "Medication schedules found successfully"));
    }

    @GetMapping("/count")
    @Operation(summary = "Count medication schedules", description = "Get total count of medication schedules for anonymous user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Count retrieved successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<Long>> countMedicationSchedules(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {

        log.debug("Counting medication schedules for anonymous user: {}", anonymousUserId);

        long count = medicationScheduleBusinessService.countActiveSchedulesForAnonymousUser(anonymousUserId);

        return ResponseEntity.ok(ApiResponse.success(count, "Schedule count retrieved successfully"));
    }

    @GetMapping("/active")
    @Operation(summary = "Get active medication schedules", description = "Retrieve only active medication schedules for anonymous user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Active medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<Page<MedicationScheduleResponse>>> getActiveMedicationSchedules(
            @Parameter(description = "Anonymous user ID", required = true)
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size", example = "10")
            @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field", example = "dayOfWeek")
            @RequestParam(defaultValue = "dayOfWeek") String sortBy,
            @Parameter(description = "Sort direction", example = "asc")
            @RequestParam(defaultValue = "asc") String sortDir) {

        log.debug("Getting active medication schedules for anonymous user: {} with pagination", anonymousUserId);

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<MedicationSchedule> schedulePage = medicationScheduleBusinessService
                .getActiveMedicationSchedulesForAnonymousUser(anonymousUserId, pageable);

        Page<MedicationScheduleResponse> responsePage = schedulePage
                .map(medicationScheduleBusinessService::convertToResponse);

        return ResponseEntity.ok(ApiResponse.success(responsePage, "Active medication schedules found successfully"));
    }

    // ==================== ANALYTICS ENDPOINTS ====================

    @GetMapping("/stats")
    @Operation(summary = "Get medication schedule statistics", description = "Get comprehensive statistics for anonymous user's medication schedules")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid anonymous user ID"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<MedicationScheduleStatsResponse>> getMedicationScheduleStats(
            @RequestHeader("X-Anonymous-User-Id")
            @Parameter(description = "Anonymous user ID", required = true) UUID anonymousUserId) {

        log.info("Getting medication schedule statistics for anonymous user: {}", anonymousUserId);

        MedicationScheduleStatsResponse stats = medicationScheduleBusinessService
                .getMedicationScheduleStatsForAnonymousUser(anonymousUserId);

        return ResponseEntity.ok(ApiResponse.success(stats, "Medication schedule statistics retrieved successfully"));
    }
}
