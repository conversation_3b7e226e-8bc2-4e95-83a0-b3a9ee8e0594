package com.medication.controller;

import com.medication.dto.request.CreateMedicineRequest;
import com.medication.dto.request.UpdateMedicineRequest;
import com.medication.dto.response.ApiResponse;
import com.medication.dto.response.MedicineResponse;
import com.medication.dto.response.MedicineStatsResponse;
import com.medication.entity.Medicine;
import com.medication.service.domain.medicine.MedicineBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * REST Controller cho Anonymous Medicine
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/v1/anonymous/medicines")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Anonymous Medicines", description = "API quản lý medicines cho anonymous users")
public class AnonymousMedicineController {
    
    private final MedicineBusinessService medicineBusinessService;
    
    /**
     * Tạo medicine mới cho anonymous user
     */
    @Operation(summary = "Tạo medicine mới", 
               description = "Tạo một medicine mới cho anonymous user")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseEntity<ApiResponse<MedicineResponse>> createMedicine(
            @Valid @RequestBody CreateMedicineRequest request,
            @Parameter(description = "Anonymous User ID") @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Creating medicine for anonymous user ID: {}", anonymousUserId);
        
        Medicine medicine = medicineBusinessService.createMedicineForAnonymousUser(request, anonymousUserId);
        MedicineResponse response = MedicineResponse.from(medicine);
        
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(201, "MEDICINE_CREATED", "Medicine created successfully", response));
    }
    
    /**
     * Lấy medicine theo ID cho anonymous user
     */
    @Operation(summary = "Lấy medicine theo ID",
               description = "Lấy thông tin chi tiết của medicine theo ID")
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<MedicineResponse>> getMedicineById(
            @Parameter(description = "Medicine ID") @PathVariable Long id,
            @Parameter(description = "Anonymous User ID") @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Getting medicine ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        Medicine medicine = medicineBusinessService.findMedicineByIdAndAnonymousUserId(id, anonymousUserId);
        MedicineResponse response = MedicineResponse.from(medicine);
        
        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_FOUND", "Medicine found successfully", response));
    }
    
    /**
     * Cập nhật medicine cho anonymous user
     */
    @Operation(summary = "Cập nhật medicine",
               description = "Cập nhật thông tin của medicine")
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<MedicineResponse>> updateMedicine(
            @Parameter(description = "Medicine ID") @PathVariable Long id,
            @Valid @RequestBody UpdateMedicineRequest request,
            @Parameter(description = "Anonymous User ID") @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Updating medicine ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        Medicine medicine = medicineBusinessService.updateMedicineForAnonymousUser(id, request, anonymousUserId);
        MedicineResponse response = MedicineResponse.from(medicine);
        
        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_UPDATED", "Medicine updated successfully", response));
    }
    
    /**
     * Xóa medicine cho anonymous user
     */
    @Operation(summary = "Xóa medicine",
               description = "Xóa medicine theo ID")
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteMedicine(
            @Parameter(description = "Medicine ID") @PathVariable Long id,
            @Parameter(description = "Anonymous User ID") @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Deleting medicine ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        medicineBusinessService.deleteMedicineForAnonymousUser(id, anonymousUserId);
        
        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_DELETED", "Medicine deleted successfully", null));
    }
    
    /**
     * Lấy danh sách medicines cho anonymous user với pagination
     */
    @Operation(summary = "Lấy danh sách medicines",
               description = "Lấy danh sách medicines với pagination")
    @GetMapping
    public ResponseEntity<ApiResponse<Page<MedicineResponse>>> getMedicines(
            @Parameter(description = "Anonymous User ID") @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            Pageable pageable) {
        
        log.info("Getting medicines for anonymous user ID: {} with pagination", anonymousUserId);
        
        Page<Medicine> medicines = medicineBusinessService.getMedicinesForAnonymousUser(anonymousUserId, pageable);
        Page<MedicineResponse> responses = medicines.map(MedicineResponse::from);
        
        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINES_FOUND", "Medicines found successfully", responses));
    }
    
    /**
     * Lấy danh sách active medicines cho anonymous user
     */
    @Operation(summary = "Lấy danh sách active medicines",
               description = "Lấy danh sách medicines đang hoạt động")
    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<MedicineResponse>>> getActiveMedicines(
            @Parameter(description = "Anonymous User ID") @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Getting active medicines for anonymous user ID: {}", anonymousUserId);
        
        List<Medicine> medicines = medicineBusinessService.getActiveMedicinesForAnonymousUser(anonymousUserId);
        List<MedicineResponse> responses = medicines.stream().map(MedicineResponse::from).toList();
        
        return ResponseEntity.ok(ApiResponse.success(200, "ACTIVE_MEDICINES_FOUND", "Active medicines found successfully", responses));
    }
    
    /**
     * Tìm kiếm medicines theo tên cho anonymous user
     */
    @Operation(summary = "Tìm kiếm medicines theo tên",
               description = "Tìm kiếm medicines theo tên (case insensitive)")
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<MedicineResponse>>> searchMedicinesByName(
            @Parameter(description = "Medicine name") @RequestParam String name,
            @Parameter(description = "Anonymous User ID") @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Searching medicines by name '{}' for anonymous user ID: {}", name, anonymousUserId);
        
        List<Medicine> medicines = medicineBusinessService.searchMedicinesByNameForAnonymousUser(name, anonymousUserId);
        List<MedicineResponse> responses = medicines.stream().map(MedicineResponse::from).toList();
        
        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINES_SEARCHED", "Medicines searched successfully", responses));
    }
    
    /**
     * Lấy medicines sắp hết hạn cho anonymous user
     */
    @Operation(summary = "Lấy medicines sắp hết hạn",
               description = "Lấy danh sách medicines sắp hết hạn trước ngày chỉ định")
    @GetMapping("/expiring")
    public ResponseEntity<ApiResponse<List<MedicineResponse>>> getMedicinesExpiringSoon(
            @Parameter(description = "Expiry date") @RequestParam LocalDate expiryDate,
            @Parameter(description = "Anonymous User ID") @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Getting medicines expiring before {} for anonymous user ID: {}", expiryDate, anonymousUserId);
        
        List<Medicine> medicines = medicineBusinessService.getMedicinesExpiringSoonForAnonymousUser(anonymousUserId, expiryDate);
        List<MedicineResponse> responses = medicines.stream().map(MedicineResponse::from).toList();
        
        return ResponseEntity.ok(ApiResponse.success(200, "EXPIRING_MEDICINES_FOUND", "Expiring medicines found successfully", responses));
    }
    
    /**
     * Lấy medicines có số lượng thấp cho anonymous user
     */
    @Operation(summary = "Lấy medicines có số lượng thấp",
               description = "Lấy danh sách medicines có số lượng dưới ngưỡng chỉ định")
    @GetMapping("/low-quantity")
    public ResponseEntity<ApiResponse<List<MedicineResponse>>> getMedicinesWithLowQuantity(
            @Parameter(description = "Quantity threshold") @RequestParam(defaultValue = "5") Integer threshold,
            @Parameter(description = "Anonymous User ID") @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Getting medicines with quantity <= {} for anonymous user ID: {}", threshold, anonymousUserId);
        
        List<Medicine> medicines = medicineBusinessService.getMedicinesWithLowQuantityForAnonymousUser(anonymousUserId, threshold);
        List<MedicineResponse> responses = medicines.stream().map(MedicineResponse::from).toList();
        
        return ResponseEntity.ok(ApiResponse.success(200, "LOW_QUANTITY_MEDICINES_FOUND", "Low quantity medicines found successfully", responses));
    }
    
    /**
     * Lấy thống kê medicines cho anonymous user
     */
    @Operation(summary = "Lấy thống kê medicines",
               description = "Lấy thống kê tổng quan về medicines")
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<MedicineStatsResponse>> getMedicineStats(
            @Parameter(description = "Anonymous User ID") @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Getting medicine statistics for anonymous user ID: {}", anonymousUserId);
        
        MedicineStatsResponse stats = medicineBusinessService.getMedicineStatsForAnonymousUser(anonymousUserId);
        
        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_STATS_FOUND", "Medicine statistics found successfully", stats));
    }
} 