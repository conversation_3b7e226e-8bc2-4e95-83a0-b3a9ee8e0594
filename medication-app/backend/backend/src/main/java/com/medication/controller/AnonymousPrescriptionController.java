package com.medication.controller;

import com.medication.dto.request.CreatePrescriptionRequest;
import com.medication.dto.request.UpdatePrescriptionRequest;
import com.medication.dto.response.ApiResponse;
import com.medication.dto.response.PrescriptionResponse;
import com.medication.dto.response.PrescriptionStatsResponse;
import com.medication.entity.Prescription;
import com.medication.enums.PrescriptionStatus;
import com.medication.service.domain.prescription.PrescriptionBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * REST Controller cho Prescription API (Anonymous Users)
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/v1/anonymous/prescriptions")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Anonymous Prescriptions", description = "API quản lý prescriptions cho anonymous users")
public class AnonymousPrescriptionController {
    
    private final PrescriptionBusinessService prescriptionBusinessService;
    
    // ==================== CRUD OPERATIONS ====================
    
    @Operation(summary = "Tạo prescription mới", description = "Tạo prescription mới cho anonymous user")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Prescription created successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid input data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping
    public ResponseEntity<ApiResponse<PrescriptionResponse>> createPrescription(
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Valid @RequestBody CreatePrescriptionRequest request) {
        
        log.info("Creating prescription for anonymous user ID: {}", anonymousUserId);
        
        Prescription prescription = prescriptionBusinessService.createPrescriptionForAnonymousUser(request, anonymousUserId);
        PrescriptionResponse response = PrescriptionResponse.from(prescription);
        
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(201, "PRESCRIPTION_CREATED", "Prescription created successfully", response));
    }
    
    @Operation(summary = "Lấy prescription theo ID", description = "Lấy thông tin chi tiết prescription theo ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Prescription found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PrescriptionResponse>> getPrescription(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Getting prescription ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        Prescription prescription = prescriptionBusinessService.findPrescriptionByIdForAnonymousUser(id, anonymousUserId);
        PrescriptionResponse response = PrescriptionResponse.from(prescription);
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_FOUND", "Prescription found successfully", response));
    }
    
    @Operation(summary = "Lấy danh sách prescriptions", description = "Lấy danh sách prescriptions với pagination")
    @GetMapping
    public ResponseEntity<ApiResponse<Page<PrescriptionResponse>>> getPrescriptions(
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        log.info("Getting prescriptions for anonymous user ID: {} with pagination", anonymousUserId);
        
        Sort sort = sortDir.equalsIgnoreCase("asc") ? Sort.by(sortBy).ascending() : Sort.by(sortBy).descending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Prescription> prescriptions = prescriptionBusinessService.getPrescriptionsForAnonymousUser(anonymousUserId, pageable);
        Page<PrescriptionResponse> responses = prescriptions.map(PrescriptionResponse::from);
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTIONS_FOUND", "Prescriptions found successfully", responses));
    }
    
    @Operation(summary = "Cập nhật prescription", description = "Cập nhật thông tin prescription")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Prescription updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription not found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<PrescriptionResponse>> updatePrescription(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Valid @RequestBody UpdatePrescriptionRequest request) {
        
        log.info("Updating prescription ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        Prescription prescription = prescriptionBusinessService.updatePrescriptionForAnonymousUser(id, request, anonymousUserId);
        PrescriptionResponse response = PrescriptionResponse.from(prescription);
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_UPDATED", "Prescription updated successfully", response));
    }
    
    @Operation(summary = "Xóa prescription", description = "Xóa prescription theo ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Prescription deleted successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription not found")
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deletePrescription(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Deleting prescription ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        prescriptionBusinessService.deletePrescriptionForAnonymousUser(id, anonymousUserId);
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_DELETED", "Prescription deleted successfully", null));
    }
    
    // ==================== SEARCH AND FILTER OPERATIONS ====================
    
    @Operation(summary = "Tìm prescriptions theo doctor name", description = "Tìm prescriptions theo tên bác sĩ")
    @GetMapping("/search/doctor")
    public ResponseEntity<ApiResponse<List<PrescriptionResponse>>> searchByDoctorName(
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Doctor name", required = true) @RequestParam String doctorName) {
        
        log.info("Searching prescriptions by doctor name: {} for anonymous user ID: {}", doctorName, anonymousUserId);
        
        List<Prescription> prescriptions = prescriptionBusinessService.searchByDoctorNameForAnonymousUser(anonymousUserId, doctorName);
        List<PrescriptionResponse> responses = prescriptions.stream()
                .map(PrescriptionResponse::from)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTIONS_FOUND", "Prescriptions found successfully", responses));
    }
    
    @Operation(summary = "Tìm prescriptions theo diagnosis", description = "Tìm prescriptions theo chẩn đoán")
    @GetMapping("/search/diagnosis")
    public ResponseEntity<ApiResponse<List<PrescriptionResponse>>> searchByDiagnosis(
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Diagnosis", required = true) @RequestParam String diagnosis) {
        
        log.info("Searching prescriptions by diagnosis: {} for anonymous user ID: {}", diagnosis, anonymousUserId);
        
        List<Prescription> prescriptions = prescriptionBusinessService.searchByDiagnosisForAnonymousUser(anonymousUserId, diagnosis);
        List<PrescriptionResponse> responses = prescriptions.stream()
                .map(PrescriptionResponse::from)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTIONS_FOUND", "Prescriptions found successfully", responses));
    }
    
    @Operation(summary = "Tìm prescriptions theo date range", description = "Tìm prescriptions theo khoảng thời gian")
    @GetMapping("/search/date-range")
    public ResponseEntity<ApiResponse<List<PrescriptionResponse>>> findByDateRange(
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Start date", required = true) 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "End date", required = true) 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("Finding prescriptions by date range: {} to {} for anonymous user ID: {}", startDate, endDate, anonymousUserId);
        
        List<Prescription> prescriptions = prescriptionBusinessService.findByDateRangeForAnonymousUser(anonymousUserId, startDate, endDate);
        List<PrescriptionResponse> responses = prescriptions.stream()
                .map(PrescriptionResponse::from)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTIONS_FOUND", "Prescriptions found successfully", responses));
    }
    
    // ==================== UPDATE OPERATIONS ====================
    
    @Operation(summary = "Cập nhật compliance rate", description = "Cập nhật tỷ lệ tuân thủ điều trị")
    @PatchMapping("/{id}/compliance")
    public ResponseEntity<ApiResponse<Void>> updateComplianceRate(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Compliance rate", required = true) @RequestParam Double complianceRate,
            @Parameter(description = "Taken doses", required = true) @RequestParam Integer takenDoses) {
        
        log.info("Updating compliance rate for prescription ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        prescriptionBusinessService.updateComplianceRate(id, complianceRate, takenDoses);
        
        return ResponseEntity.ok(ApiResponse.success(200, "COMPLIANCE_UPDATED", "Compliance rate updated successfully", null));
    }
    
    @Operation(summary = "Cập nhật status", description = "Cập nhật trạng thái prescription")
    @PatchMapping("/{id}/status")
    public ResponseEntity<ApiResponse<Void>> updateStatus(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Status", required = true) @RequestParam PrescriptionStatus status) {
        
        log.info("Updating status for prescription ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        prescriptionBusinessService.updateStatus(id, status);
        
        return ResponseEntity.ok(ApiResponse.success(200, "STATUS_UPDATED", "Status updated successfully", null));
    }
    
    @Operation(summary = "Cập nhật active status", description = "Cập nhật trạng thái active của prescription")
    @PatchMapping("/{id}/active")
    public ResponseEntity<ApiResponse<Void>> updateActiveStatus(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Is active", required = true) @RequestParam Boolean isActive) {
        
        log.info("Updating active status for prescription ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        prescriptionBusinessService.updateActiveStatus(id, isActive);
        
        return ResponseEntity.ok(ApiResponse.success(200, "ACTIVE_STATUS_UPDATED", "Active status updated successfully", null));
    }
    
    // ==================== STATISTICS OPERATIONS ====================
    
    @Operation(summary = "Lấy thống kê prescriptions", description = "Lấy thống kê prescriptions cho anonymous user")
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<PrescriptionStatsResponse>> getPrescriptionStats(
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Getting prescription stats for anonymous user ID: {}", anonymousUserId);
        
        PrescriptionStatsResponse stats = prescriptionBusinessService.getPrescriptionStatsForAnonymousUser(anonymousUserId);
        
        return ResponseEntity.ok(ApiResponse.success(200, "STATS_FOUND", "Prescription stats found successfully", stats));
    }
} 