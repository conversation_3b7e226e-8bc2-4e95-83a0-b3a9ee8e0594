package com.medication.controller;

import com.medication.dto.request.CreatePrescriptionItemRequest;
import com.medication.dto.request.UpdatePrescriptionItemRequest;
import com.medication.dto.response.ApiResponse;
import com.medication.dto.response.PrescriptionItemResponse;
import com.medication.entity.PrescriptionItem;
import com.medication.service.domain.prescription.PrescriptionItemBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Controller cho PrescriptionItem operations cho anonymous users
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/v1/anonymous/prescription-items")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Anonymous Prescription Items", description = "API cho quản lý prescription items của anonymous users")
public class AnonymousPrescriptionItemController {

    private final PrescriptionItemBusinessService prescriptionItemBusinessService;

    // ==================== CRUD OPERATIONS ====================

    @Operation(summary = "Tạo prescription item mới", description = "Tạo prescription item mới cho anonymous user")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Prescription item created successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription or Medicine not found")
    })
    @PostMapping
    public ResponseEntity<ApiResponse<PrescriptionItemResponse>> createPrescriptionItem(
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Valid @RequestBody CreatePrescriptionItemRequest request) {
        
        log.info("Creating prescription item for anonymous user ID: {}", anonymousUserId);
        
        PrescriptionItem prescriptionItem = prescriptionItemBusinessService.createPrescriptionItemForAnonymousUser(request, anonymousUserId);
        PrescriptionItemResponse response = PrescriptionItemResponse.fromWithBusinessLogic(prescriptionItem, prescriptionItemBusinessService);
        
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(201, "PRESCRIPTION_ITEM_CREATED", "Prescription item created successfully", response));
    }

    @Operation(summary = "Lấy prescription item theo ID", description = "Lấy thông tin prescription item theo ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Prescription item found successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription item not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PrescriptionItemResponse>> getPrescriptionItem(
            @Parameter(description = "Prescription Item ID", required = true) @PathVariable Long id,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Getting prescription item ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        PrescriptionItem prescriptionItem = prescriptionItemBusinessService.findPrescriptionItemByIdForAnonymousUser(id, anonymousUserId);
        PrescriptionItemResponse response = PrescriptionItemResponse.fromWithBusinessLogic(prescriptionItem, prescriptionItemBusinessService);
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_ITEM_FOUND", "Prescription item found successfully", response));
    }
    
    @Operation(summary = "Lấy danh sách prescription items", description = "Lấy danh sách prescription items với pagination")
    @GetMapping
    public ResponseEntity<ApiResponse<Page<PrescriptionItemResponse>>> getPrescriptionItems(
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        log.info("Getting prescription items for anonymous user ID: {} with pagination", anonymousUserId);
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<PrescriptionItem> prescriptionItems = prescriptionItemBusinessService.getPrescriptionItemsForAnonymousUser(anonymousUserId, pageable);
        Page<PrescriptionItemResponse> responses = prescriptionItems.map(item -> 
            PrescriptionItemResponse.fromWithBusinessLogic(item, prescriptionItemBusinessService));
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_ITEMS_FOUND", "Prescription items found successfully", responses));
    }

    @Operation(summary = "Lấy prescription items theo prescription ID", description = "Lấy danh sách prescription items theo prescription ID")
    @GetMapping("/by-prescription/{prescriptionId}")
    public ResponseEntity<ApiResponse<List<PrescriptionItemResponse>>> getPrescriptionItemsByPrescriptionId(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long prescriptionId,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Getting prescription items for prescription ID: {} and anonymous user ID: {}", prescriptionId, anonymousUserId);
        
        List<PrescriptionItem> prescriptionItems = prescriptionItemBusinessService.getPrescriptionItemsByPrescriptionIdForAnonymousUser(prescriptionId, anonymousUserId);
        List<PrescriptionItemResponse> responses = prescriptionItems.stream()
                .map(item -> PrescriptionItemResponse.fromWithBusinessLogic(item, prescriptionItemBusinessService))
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_ITEMS_FOUND", "Prescription items found successfully", responses));
    }

    @Operation(summary = "Lấy prescription items theo prescription ID với pagination", description = "Lấy danh sách prescription items theo prescription ID với pagination")
    @GetMapping("/by-prescription/{prescriptionId}/paginated")
    public ResponseEntity<ApiResponse<Page<PrescriptionItemResponse>>> getPrescriptionItemsByPrescriptionIdPaginated(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long prescriptionId,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        log.info("Getting prescription items for prescription ID: {} and anonymous user ID: {} with pagination", prescriptionId, anonymousUserId);
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<PrescriptionItem> prescriptionItems = prescriptionItemBusinessService.getPrescriptionItemsByPrescriptionIdForAnonymousUser(prescriptionId, anonymousUserId, pageable);
        Page<PrescriptionItemResponse> responses = prescriptionItems.map(item -> 
            PrescriptionItemResponse.fromWithBusinessLogic(item, prescriptionItemBusinessService));
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_ITEMS_FOUND", "Prescription items found successfully", responses));
    }

    @Operation(summary = "Cập nhật prescription item", description = "Cập nhật thông tin prescription item")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Prescription item updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription item not found")
    })
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<PrescriptionItemResponse>> updatePrescriptionItem(
            @Parameter(description = "Prescription Item ID", required = true) @PathVariable Long id,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId,
            @Valid @RequestBody UpdatePrescriptionItemRequest request) {
        
        log.info("Updating prescription item ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        PrescriptionItem prescriptionItem = prescriptionItemBusinessService.updatePrescriptionItemForAnonymousUser(id, request, anonymousUserId);
        PrescriptionItemResponse response = PrescriptionItemResponse.fromWithBusinessLogic(prescriptionItem, prescriptionItemBusinessService);
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_ITEM_UPDATED", "Prescription item updated successfully", response));
    }
    
    @Operation(summary = "Xóa prescription item", description = "Xóa prescription item theo ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Prescription item deleted successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription item not found")
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deletePrescriptionItem(
            @Parameter(description = "Prescription Item ID", required = true) @PathVariable Long id,
            @Parameter(description = "Anonymous User ID", required = true) 
            @RequestHeader("X-Anonymous-User-Id") UUID anonymousUserId) {
        
        log.info("Deleting prescription item ID: {} for anonymous user ID: {}", id, anonymousUserId);
        
        prescriptionItemBusinessService.deletePrescriptionItemForAnonymousUser(id, anonymousUserId);
        
        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_ITEM_DELETED", "Prescription item deleted successfully", null));
    }
}
