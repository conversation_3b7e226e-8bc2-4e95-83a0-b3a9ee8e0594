package com.medication.controller;

import com.medication.constant.BusinessCode;
import com.medication.dto.request.CreateAnonymousSessionRequest;
import com.medication.dto.request.UpdateAnonymousSessionRequest;
import com.medication.dto.response.AnonymousSessionResponse;
import com.medication.dto.response.AnonymousSessionStatsResponse;
import com.medication.entity.AnonymousSession;
import com.medication.security.SecurityUtils;
import com.medication.service.domain.session.AnonymousSessionBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * REST Controller cho AnonymousSession
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/v1/anonymous-sessions")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Anonymous Sessions", description = "API quản lý anonymous sessions cho ứng dụng mobile")
public class AnonymousSessionController {
    
    private final AnonymousSessionBusinessService anonymousSessionBusinessService;
    
    /**
     * Tạo anonymous session mới
     */
    @Operation(summary = "Tạo anonymous session mới", 
               description = "Tạo một anonymous session mới cho device")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Session được tạo thành công"),
        @ApiResponse(responseCode = "400", description = "Request không hợp lệ"),
        @ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseEntity<com.medication.dto.response.ApiResponse<AnonymousSessionResponse>> createAnonymousSession(
            @Valid @RequestBody CreateAnonymousSessionRequest request) {
        
        log.info("Creating anonymous session for device: {}", request.getDeviceId());
        
        AnonymousSession session = anonymousSessionBusinessService.createAnonymousSession(request);
        AnonymousSessionResponse response = AnonymousSessionResponse.from(session);
        
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(com.medication.dto.response.ApiResponse.success(201, BusinessCode.SESSION_CREATED, "Anonymous session created successfully", response));
    }
    
    /**
     * Lấy thông tin session theo anonymous user ID
     */
    @Operation(summary = "Lấy thông tin session", 
               description = "Lấy thông tin chi tiết của anonymous session")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Lấy thông tin thành công"),
        @ApiResponse(responseCode = "404", description = "Session không tồn tại"),
        @ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping("/{anonymousUserId}")
    public ResponseEntity<com.medication.dto.response.ApiResponse<AnonymousSessionResponse>> getSession(
            @Parameter(description = "Anonymous User ID", required = true)
            @PathVariable UUID anonymousUserId) {
        
        log.debug("Getting session for anonymous user ID: {}", anonymousUserId);
        
        AnonymousSession session = anonymousSessionBusinessService.findSessionByAnonymousUserId(anonymousUserId);
        AnonymousSessionResponse response = AnonymousSessionResponse.from(session);
        
        return ResponseEntity.ok(com.medication.dto.response.ApiResponse.success(200, BusinessCode.SUCCESS, "Session retrieved successfully", response));
    }
    
    /**
     * Cập nhật session
     */
    @Operation(summary = "Cập nhật session", 
               description = "Cập nhật thông tin của anonymous session")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Cập nhật thành công"),
        @ApiResponse(responseCode = "404", description = "Session không tồn tại"),
        @ApiResponse(responseCode = "400", description = "Request không hợp lệ"),
        @ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @PutMapping("/{anonymousUserId}")
    public ResponseEntity<com.medication.dto.response.ApiResponse<AnonymousSessionResponse>> updateSession(
            @Parameter(description = "Anonymous User ID", required = true)
            @PathVariable UUID anonymousUserId,
            @Valid @RequestBody UpdateAnonymousSessionRequest request) {
        
        log.info("Updating session for anonymous user ID: {}", anonymousUserId);
        
        AnonymousSession session = anonymousSessionBusinessService.updateSession(anonymousUserId, request);
        AnonymousSessionResponse response = AnonymousSessionResponse.from(session);
        
        return ResponseEntity.ok(com.medication.dto.response.ApiResponse.success(200, BusinessCode.SESSION_UPDATED, "Session updated successfully", response));
    }
    
    /**
     * Cập nhật last activity
     */
    @Operation(summary = "Cập nhật last activity", 
               description = "Cập nhật thời gian hoạt động cuối cùng của session")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Cập nhật thành công"),
        @ApiResponse(responseCode = "404", description = "Session không tồn tại"),
        @ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @PatchMapping("/{anonymousUserId}/activity")
    public ResponseEntity<com.medication.dto.response.ApiResponse<Object>> updateLastActivity(
            @Parameter(description = "Anonymous User ID", required = true)
            @PathVariable UUID anonymousUserId) {
        
        log.debug("Updating last activity for anonymous user ID: {}", anonymousUserId);
        
        anonymousSessionBusinessService.updateLastActivity(anonymousUserId);
        
        return ResponseEntity.ok(com.medication.dto.response.ApiResponse.success(200, BusinessCode.SESSION_ACTIVITY_UPDATED, "Session activity updated successfully", null));
    }
    
    /**
     * Kiểm tra session có tồn tại không
     */
    @Operation(summary = "Kiểm tra session tồn tại", 
               description = "Kiểm tra xem anonymous session có tồn tại không")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Session tồn tại"),
        @ApiResponse(responseCode = "404", description = "Session không tồn tại")
    })
    @GetMapping("/{anonymousUserId}/exists")
    public ResponseEntity<com.medication.dto.response.ApiResponse<Object>> checkSessionExists(
            @Parameter(description = "Anonymous User ID", required = true)
            @PathVariable UUID anonymousUserId) {
        
        log.debug("Checking if session exists for anonymous user ID: {}", anonymousUserId);
        
        // Sử dụng exception handling thay vì manual check
        anonymousSessionBusinessService.findSessionByAnonymousUserId(anonymousUserId);
        
        return ResponseEntity.ok(com.medication.dto.response.ApiResponse.noContent());
    }
    
    /**
     * Lấy tất cả sessions theo device ID
     */
    @Operation(summary = "Lấy sessions theo device ID", 
               description = "Lấy tất cả sessions của một device")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Lấy danh sách thành công"),
        @ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping("/device/{deviceId}")
    public ResponseEntity<com.medication.dto.response.ApiResponse<List<AnonymousSessionResponse>>> getSessionsByDeviceId(
            @Parameter(description = "Device ID", required = true)
            @PathVariable String deviceId) {
        
        log.debug("Getting sessions for device ID: {}", deviceId);
        
        List<AnonymousSession> sessions = anonymousSessionBusinessService.findSessionsByDeviceId(deviceId);
        List<AnonymousSessionResponse> responses = sessions.stream()
                .map(AnonymousSessionResponse::from)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(com.medication.dto.response.ApiResponse.success(responses));
    }
    
    /**
     * Lấy tất cả active sessions
     */
    @Operation(summary = "Lấy active sessions", 
               description = "Lấy tất cả sessions đang hoạt động")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Lấy danh sách thành công"),
        @ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping("/active")
    public ResponseEntity<com.medication.dto.response.ApiResponse<List<AnonymousSessionResponse>>> getActiveSessions() {
        
        log.debug("Getting all active sessions");
        
        List<AnonymousSession> sessions = anonymousSessionBusinessService.findActiveSessions();
        List<AnonymousSessionResponse> responses = sessions.stream()
                .map(AnonymousSessionResponse::from)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(com.medication.dto.response.ApiResponse.success(responses));
    }
    
    /**
     * Expire session
     */
    @Operation(summary = "Expire session", 
               description = "Đánh dấu session là expired")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Expire thành công"),
        @ApiResponse(responseCode = "404", description = "Session không tồn tại"),
        @ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @PatchMapping("/{anonymousUserId}/expire")
    public ResponseEntity<com.medication.dto.response.ApiResponse<AnonymousSessionResponse>> expireSession(
            @Parameter(description = "Anonymous User ID", required = true)
            @PathVariable UUID anonymousUserId) {
        
        log.info("Expiring session: {}", anonymousUserId);
        
        AnonymousSession session = anonymousSessionBusinessService.expireSession(anonymousUserId);
        AnonymousSessionResponse response = AnonymousSessionResponse.from(session);
        
        return ResponseEntity.ok(com.medication.dto.response.ApiResponse.success(response));
    }
    
    /**
     * Migrate session sang user
     */
    @Operation(summary = "Migrate session sang user", 
               description = "Chuyển anonymous session sang registered user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Migrate thành công"),
        @ApiResponse(responseCode = "404", description = "Session không tồn tại"),
        @ApiResponse(responseCode = "400", description = "Session đã được migrate"),
        @ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @PatchMapping("/{anonymousUserId}/migrate")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<com.medication.dto.response.ApiResponse<AnonymousSessionResponse>> migrateSession(
            @Parameter(description = "Anonymous User ID", required = true)
            @PathVariable UUID anonymousUserId) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Migrating session {} to user ID: {}", anonymousUserId, userId);

        AnonymousSession session = anonymousSessionBusinessService.migrateSessionToUser(anonymousUserId, userId);
        AnonymousSessionResponse response = AnonymousSessionResponse.from(session);

        return ResponseEntity.ok(com.medication.dto.response.ApiResponse.success(response));
    }
    
    /**
     * Lấy thống kê sessions
     */
    @Operation(summary = "Lấy thống kê sessions", 
               description = "Lấy thống kê về số lượng sessions")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Lấy thống kê thành công"),
        @ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping("/stats")
    public ResponseEntity<com.medication.dto.response.ApiResponse<AnonymousSessionStatsResponse>> getSessionStats() {
        
        log.debug("Getting session statistics");
        
        AnonymousSessionStatsResponse stats = anonymousSessionBusinessService.getSessionStats();
        
        return ResponseEntity.ok(com.medication.dto.response.ApiResponse.success(stats));
    }
} 