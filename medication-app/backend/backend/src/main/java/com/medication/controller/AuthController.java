package com.medication.controller;

import com.medication.dto.request.LoginRequest;
import com.medication.dto.request.RegisterRequest;
import com.medication.dto.request.RefreshTokenRequest;
import com.medication.dto.request.VerifyOtpRequest;
import com.medication.dto.response.ApiResponse;
import com.medication.dto.response.LoginResponse;
import com.medication.service.AuthenticationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * Controller xử lý authentication endpoints
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Authentication", description = "API xử lý đăng nhập, đăng ký và xác thực")
public class AuthController {

    private final AuthenticationService authenticationService;

    /**
     * User login
     */
    @Operation(summary = "Đăng nhập", 
               description = "Đăng nhập với email và password, trả về JWT tokens")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Đăng nhập thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Thông tin đăng nhập không hợp lệ"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Email hoặc mật khẩu không đúng")
    })
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<LoginResponse>> login(
            @Valid @RequestBody LoginRequest request) {
        
        log.info("Login request for email: {}", request.getEmail());
        
        LoginResponse response = authenticationService.login(request);
        
        return ResponseEntity.ok(
            ApiResponse.success(200, "LOGIN_SUCCESS", "Đăng nhập thành công", response)
        );
    }

    /**
     * User registration - Step 1: Send OTP
     */
    @Operation(summary = "Đăng ký tài khoản - Bước 1", 
               description = "Gửi OTP verification email để đăng ký tài khoản mới")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "OTP đã được gửi"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Thông tin đăng ký không hợp lệ"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "409", description = "Email đã được sử dụng")
    })
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<Void>> register(
            @Valid @RequestBody RegisterRequest request) {
        
        log.info("Registration request for email: {}", request.getEmail());
        
        authenticationService.register(request);
        
        return ResponseEntity.ok(
            ApiResponse.success(200, "OTP_SENT", "Mã OTP đã được gửi đến email của bạn", null)
        );
    }

    /**
     * User registration - Step 2: Verify OTP and create account
     */
    @Operation(summary = "Đăng ký tài khoản - Bước 2", 
               description = "Xác thực OTP và tạo tài khoản mới")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Tài khoản được tạo thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "OTP không hợp lệ hoặc đã hết hạn")
    })
    @PostMapping("/verify-otp")
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseEntity<ApiResponse<LoginResponse>> verifyOtp(
            @Valid @RequestBody VerifyOtpRequest request) {
        
        log.info("OTP verification request for email: {}", request.getEmail());
        
        LoginResponse response = authenticationService.verifyOtpAndCreateUser(request);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(
            ApiResponse.success(201, "ACCOUNT_CREATED", "Tài khoản được tạo thành công", response)
        );
    }

    /**
     * Refresh access token
     */
    @Operation(summary = "Refresh token", 
               description = "Tạo access token mới từ refresh token")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Token được refresh thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Refresh token không hợp lệ")
    })
    @PostMapping("/refresh-token")
    public ResponseEntity<ApiResponse<LoginResponse>> refreshToken(
            @Valid @RequestBody RefreshTokenRequest request) {
        
        log.info("Token refresh request");
        
        LoginResponse response = authenticationService.refreshToken(request);
        
        return ResponseEntity.ok(
            ApiResponse.success(200, "TOKEN_REFRESHED", "Token được refresh thành công", response)
        );
    }

    /**
     * User logout
     */
    @Operation(summary = "Đăng xuất", 
               description = "Đăng xuất user (invalidate token)")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Đăng xuất thành công")
    })
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Void>> logout(HttpServletRequest request) {
        
        // Get current authentication
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null) {
            log.info("Logout request for user: {}", authentication.getName());
            
            // Clear security context
            SecurityContextHolder.clearContext();
            
            // TODO: Add token to blacklist if needed
            // For now, we rely on token expiration
        }
        
        return ResponseEntity.ok(
            ApiResponse.success(200, "LOGOUT_SUCCESS", "Đăng xuất thành công", null)
        );
    }

    /**
     * Get current user info
     */
    @Operation(summary = "Thông tin user hiện tại", 
               description = "Lấy thông tin user từ JWT token")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Lấy thông tin thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Chưa đăng nhập")
    })
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<LoginResponse.UserInfo>> getCurrentUser() {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(
                ApiResponse.error(401, "UNAUTHORIZED", "Chưa đăng nhập", null)
            );
        }

        // Extract user info from authentication
        if (authentication.getPrincipal() instanceof com.medication.security.UserDetailsAdapter userDetails) {
            com.medication.entity.User user = userDetails.getUser();
            
            LoginResponse.UserInfo userInfo = LoginResponse.UserInfo.builder()
                    .id(user.getId())
                    .email(user.getEmail())
                    .fullName(user.getFullName())
                    .phoneNumber(user.getPhoneNumber())
                    .role(user.getRole())
                    .userType(user.getUserType())
                    .isActive(user.getIsActive())
                    .emailVerified(user.getEmailVerified())
                    .createdAt(user.getCreatedAt())
                    .updatedAt(user.getUpdatedAt())
                    .build();

            return ResponseEntity.ok(
                ApiResponse.success(200, "USER_INFO", "Thông tin user", userInfo)
            );
        }

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(
            ApiResponse.error(401, "UNAUTHORIZED", "Thông tin xác thực không hợp lệ", null)
        );
    }
}
