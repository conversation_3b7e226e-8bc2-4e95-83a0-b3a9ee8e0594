package com.medication.controller;

import com.medication.dto.request.CreateMedicationScheduleRequest;
import com.medication.dto.request.UpdateMedicationScheduleRequest;
import com.medication.dto.response.ApiResponse;
import com.medication.dto.response.MedicationScheduleResponse;
import com.medication.dto.response.MedicationScheduleStatsResponse;
import com.medication.entity.MedicationSchedule;
import com.medication.enums.DoseUnit;
import com.medication.security.SecurityUtils;
import com.medication.service.domain.schedule.MedicationScheduleBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.util.List;

/**
 * Controller for registered user medication schedule operations
 * Handles CRUD operations for medication schedules with user authentication
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/medication-schedules")
@RequiredArgsConstructor
@Tag(name = "Medication Schedule", description = "Medication schedule operations for registered users")
@SecurityRequirement(name = "bearerAuth")
public class MedicationScheduleController {

    private final MedicationScheduleBusinessService medicationScheduleBusinessService;

    @PostMapping
    @Operation(summary = "Create medication schedule", description = "Create a new medication schedule for registered user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Medication schedule created successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MedicationScheduleResponse>> createMedicationSchedule(
            @Parameter(description = "Medication schedule creation request", required = true)
            @Valid @RequestBody CreateMedicationScheduleRequest request) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Creating medication schedule for user: {}", userId);

        MedicationSchedule createdSchedule = medicationScheduleBusinessService
                .createMedicationScheduleForUser(request, userId);

        MedicationScheduleResponse response = medicationScheduleBusinessService.convertToResponse(createdSchedule);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.created(response, "Medication schedule created successfully"));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get medication schedule by ID", description = "Retrieve a specific medication schedule by ID for registered user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedule found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medication schedule not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MedicationScheduleResponse>> getMedicationSchedule(
            @Parameter(description = "Medication schedule ID", required = true)
            @PathVariable Long id) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Getting medication schedule {} for user: {}", id, userId);

        MedicationSchedule schedule = medicationScheduleBusinessService
                .findMedicationScheduleByIdForUser(id, userId);

        MedicationScheduleResponse response = medicationScheduleBusinessService.convertToResponse(schedule);

        return ResponseEntity.ok(ApiResponse.success(response, "Medication schedule found successfully"));
    }

    @GetMapping
    @Operation(summary = "Get medication schedules with pagination", description = "Retrieve medication schedules for registered user with pagination and sorting")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules retrieved successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid pagination parameters"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<MedicationScheduleResponse>>> getMedicationSchedules(
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size", example = "10")
            @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field", example = "dayOfWeek")
            @RequestParam(defaultValue = "dayOfWeek") String sortBy,
            @Parameter(description = "Sort direction", example = "asc")
            @RequestParam(defaultValue = "asc") String sortDir) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Getting medication schedules for user: {} with pagination", userId);

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<MedicationSchedule> schedulePage = medicationScheduleBusinessService
                .getMedicationSchedulesForUser(userId, pageable);

        Page<MedicationScheduleResponse> responsePage = schedulePage
                .map(medicationScheduleBusinessService::convertToResponse);

        return ResponseEntity.ok(ApiResponse.success(responsePage, "MEDICATION_SCHEDULES_FOUND",
                "Medication schedules found successfully"));
    }

    @GetMapping("/by-prescription-item/{prescriptionItemId}")
    @Operation(summary = "Get medication schedules by prescription item", description = "Retrieve medication schedules for a specific prescription item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> getMedicationSchedulesByPrescriptionItem(
            @Parameter(description = "Prescription item ID", required = true)
            @PathVariable Long prescriptionItemId) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Getting medication schedules for prescription item {} and user: {}", prescriptionItemId, userId);

        List<MedicationSchedule> schedules = medicationScheduleBusinessService
                .getMedicationSchedulesByPrescriptionItemIdForUser(prescriptionItemId, userId);

        List<MedicationScheduleResponse> responses = schedules.stream()
                .map(medicationScheduleBusinessService::convertToResponse)
                .toList();

        return ResponseEntity.ok(ApiResponse.success(responses, "MEDICATION_SCHEDULES_FOUND",
                "Medication schedules found successfully"));
    }

    @GetMapping("/by-day/{dayOfWeek}")
    @Operation(summary = "Get medication schedules by day of week", description = "Retrieve medication schedules for a specific day of week (1=Monday, 7=Sunday)")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid day of week"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> getMedicationSchedulesByDayOfWeek(
            @Parameter(description = "Day of week (1=Monday, 7=Sunday)", required = true)
            @PathVariable Integer dayOfWeek) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Getting medication schedules for day {} and user: {}", dayOfWeek, userId);

        if (dayOfWeek < 1 || dayOfWeek > 7) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "INVALID_DAY_OF_WEEK", "Day of week must be between 1 and 7"));
        }

        List<MedicationSchedule> schedules = medicationScheduleBusinessService
                .getMedicationSchedulesByDayOfWeekForUser(userId, dayOfWeek);

        List<MedicationScheduleResponse> responses = schedules.stream()
                .map(medicationScheduleBusinessService::convertToResponse)
                .toList();

        return ResponseEntity.ok(ApiResponse.success(responses, "MEDICATION_SCHEDULES_FOUND",
                "Medication schedules found successfully"));
    }

    @GetMapping("/by-time-range")
    @Operation(summary = "Get medication schedules by time range", description = "Retrieve medication schedules within a specific time range")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid time range"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> getMedicationSchedulesByTimeRange(
            @Parameter(description = "Start time (HH:mm format)", required = true, example = "08:00")
            @RequestParam String startTime,
            @Parameter(description = "End time (HH:mm format)", required = true, example = "18:00")
            @RequestParam String endTime) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.debug("Getting medication schedules for time range {} - {} and user: {}", startTime, endTime, userId);
        
        try {
            LocalTime start = LocalTime.parse(startTime);
            LocalTime end = LocalTime.parse(endTime);
            
            if (start.isAfter(end)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "INVALID_TIME_RANGE", "Start time must be before end time"));
            }
            
            List<MedicationSchedule> schedules = medicationScheduleBusinessService
                    .getMedicationSchedulesByTimeRangeForUser(userId, start, end);
            
            List<MedicationScheduleResponse> responses = schedules.stream()
                    .map(medicationScheduleBusinessService::convertToResponse)
                    .toList();
            
            return ResponseEntity.ok(ApiResponse.success(responses, "MEDICATION_SCHEDULES_FOUND", 
                    "Medication schedules found successfully"));
            
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "INVALID_TIME_FORMAT", "Invalid time format. Use HH:mm format"));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update medication schedule", description = "Update an existing medication schedule for registered user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedule updated successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medication schedule not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MedicationScheduleResponse>> updateMedicationSchedule(
            @Parameter(description = "Medication schedule ID", required = true)
            @PathVariable Long id,
            @Parameter(description = "Medication schedule update request", required = true)
            @Valid @RequestBody UpdateMedicationScheduleRequest request) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Updating medication schedule {} for user: {}", id, userId);
        
        MedicationSchedule updatedSchedule = medicationScheduleBusinessService
                .updateMedicationScheduleForUser(id, request, userId);
        
        MedicationScheduleResponse response = medicationScheduleBusinessService.convertToResponse(updatedSchedule);
        
        return ResponseEntity.ok(ApiResponse.success(response, "MEDICATION_SCHEDULE_UPDATED", 
                "Medication schedule updated successfully"));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete medication schedule", description = "Delete a medication schedule for registered user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedule deleted successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medication schedule not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteMedicationSchedule(
            @Parameter(description = "Medication schedule ID", required = true)
            @PathVariable Long id) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Deleting medication schedule {} for user: {}", id, userId);

        medicationScheduleBusinessService.deleteMedicationScheduleForUser(id, userId);

        return ResponseEntity.ok(ApiResponse.success("Medication schedule deleted successfully"));
    }

    @GetMapping("/by-dose-unit/{doseUnit}")
    @Operation(summary = "Get medication schedules by dose unit", description = "Retrieve medication schedules for a specific dose unit")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> getMedicationSchedulesByDoseUnit(
            @Parameter(description = "User ID from JWT token", hidden = true)
            @RequestAttribute("userId") Long userId,
            @Parameter(description = "Dose unit", required = true)
            @PathVariable DoseUnit doseUnit) {

        log.debug("Getting medication schedules for dose unit {} and user: {}", doseUnit, userId);

        List<MedicationSchedule> schedules = medicationScheduleBusinessService
                .getMedicationSchedulesByDoseUnitForUser(userId, doseUnit);

        List<MedicationScheduleResponse> responses = schedules.stream()
                .map(medicationScheduleBusinessService::convertToResponse)
                .toList();

        return ResponseEntity.ok(ApiResponse.success(responses, "Medication schedules found successfully"));
    }

    @GetMapping("/by-dose-range")
    @Operation(summary = "Get medication schedules by dose amount range", description = "Retrieve medication schedules within a specific dose amount range")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid dose range"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> getMedicationSchedulesByDoseRange(
            @Parameter(description = "User ID from JWT token", hidden = true)
            @RequestAttribute("userId") Long userId,
            @Parameter(description = "Minimum dose amount", required = true)
            @RequestParam Double minDose,
            @Parameter(description = "Maximum dose amount", required = true)
            @RequestParam Double maxDose) {

        log.debug("Getting medication schedules for dose range {}-{} and user: {}", minDose, maxDose, userId);

        if (minDose > maxDose) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("Minimum dose must be less than or equal to maximum dose"));
        }

        List<MedicationSchedule> schedules = medicationScheduleBusinessService
                .getMedicationSchedulesByDoseAmountRangeForUser(userId, minDose, maxDose);

        List<MedicationScheduleResponse> responses = schedules.stream()
                .map(medicationScheduleBusinessService::convertToResponse)
                .toList();

        return ResponseEntity.ok(ApiResponse.success(responses, "Medication schedules found successfully"));
    }

    @GetMapping("/search")
    @Operation(summary = "Search medication schedules by notes", description = "Search medication schedules by notes content")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid search parameters"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<MedicationScheduleResponse>>> searchMedicationSchedulesByNotes(
            @Parameter(description = "User ID from JWT token", hidden = true)
            @RequestAttribute("userId") Long userId,
            @Parameter(description = "Search text in notes", required = true)
            @RequestParam String searchText) {

        log.debug("Searching medication schedules for text '{}' and user: {}", searchText, userId);

        if (searchText == null || searchText.trim().isEmpty()) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.badRequest("Search text cannot be empty"));
        }

        List<MedicationSchedule> schedules = medicationScheduleBusinessService
                .searchMedicationSchedulesByNotesForUser(userId, searchText.trim());

        List<MedicationScheduleResponse> responses = schedules.stream()
                .map(medicationScheduleBusinessService::convertToResponse)
                .toList();

        return ResponseEntity.ok(ApiResponse.success(responses, "Medication schedules found successfully"));
    }

    @GetMapping("/count")
    @Operation(summary = "Count medication schedules", description = "Get total count of medication schedules for authenticated user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Count retrieved successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<Long>> countMedicationSchedules(
            @Parameter(description = "User ID from JWT token", hidden = true)
            @RequestAttribute("userId") Long userId) {

        log.debug("Counting medication schedules for user: {}", userId);

        long count = medicationScheduleBusinessService.countActiveSchedulesForUser(userId);

        return ResponseEntity.ok(ApiResponse.success(count, "Schedule count retrieved successfully"));
    }

    @GetMapping("/active")
    @Operation(summary = "Get active medication schedules", description = "Retrieve only active medication schedules for authenticated user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Active medication schedules found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<Page<MedicationScheduleResponse>>> getActiveMedicationSchedules(
            @Parameter(description = "User ID from JWT token", hidden = true)
            @RequestAttribute("userId") Long userId,
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size", example = "10")
            @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field", example = "dayOfWeek")
            @RequestParam(defaultValue = "dayOfWeek") String sortBy,
            @Parameter(description = "Sort direction", example = "asc")
            @RequestParam(defaultValue = "asc") String sortDir) {

        log.debug("Getting active medication schedules for user: {} with pagination", userId);

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<MedicationSchedule> schedulePage = medicationScheduleBusinessService
                .getActiveMedicationSchedulesForUser(userId, pageable);

        Page<MedicationScheduleResponse> responsePage = schedulePage
                .map(medicationScheduleBusinessService::convertToResponse);

        return ResponseEntity.ok(ApiResponse.success(responsePage, "Active medication schedules found successfully"));
    }

    // ==================== ANALYTICS ENDPOINTS ====================

    @GetMapping("/stats")
    @Operation(summary = "Get medication schedule statistics", description = "Get comprehensive statistics for authenticated user's medication schedules")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Unauthorized - Invalid or missing access token"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MedicationScheduleStatsResponse>> getMedicationScheduleStats() {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Getting medication schedule statistics for user: {}", userId);

        MedicationScheduleStatsResponse stats = medicationScheduleBusinessService
                .getMedicationScheduleStatsForUser(userId);

        return ResponseEntity.ok(ApiResponse.success(stats, "Medication schedule statistics retrieved successfully"));
    }


}
