package com.medication.controller;

import com.medication.dto.request.CreateMedicineRequest;
import com.medication.dto.request.UpdateMedicineRequest;
import com.medication.dto.response.ApiResponse;
import com.medication.dto.response.MedicineResponse;
import com.medication.dto.response.MedicineStatsResponse;
import com.medication.entity.Medicine;
import com.medication.security.SecurityUtils;
import com.medication.service.domain.medicine.MedicineBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * REST Controller cho Medicine
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/v1/medicines")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Medicines", description = "API quản lý medicines cho registered users")
@SecurityRequirement(name = "bearerAuth")
public class MedicineController {
    
    private final MedicineBusinessService medicineBusinessService;
    
    /**
     * Tạo medicine mới cho registered user
     */
    @Operation(summary = "Tạo medicine mới", 
               description = "Tạo một medicine mới cho registered user")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Medicine được tạo thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Request không hợp lệ"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "409", description = "Medicine đã tồn tại"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MedicineResponse>> createMedicine(
            @Valid @RequestBody CreateMedicineRequest request) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Creating medicine for user ID: {}", userId);

        Medicine medicine = medicineBusinessService.createMedicineForUser(request, userId);
        MedicineResponse response = MedicineResponse.from(medicine);
        
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(201, "MEDICINE_CREATED", "Medicine created successfully", response));
    }
    
    /**
     * Lấy medicine theo ID cho registered user
     */
    @Operation(summary = "Lấy medicine theo ID",
               description = "Lấy thông tin chi tiết của medicine theo ID")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Lấy medicine thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medicine không tồn tại"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MedicineResponse>> getMedicineById(
            @Parameter(description = "Medicine ID") @PathVariable Long id) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Getting medicine ID: {} for user ID: {}", id, userId);

        Medicine medicine = medicineBusinessService.findMedicineByIdAndUserId(id, userId);
        MedicineResponse response = MedicineResponse.from(medicine);

        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_FOUND", "Medicine found successfully", response));
    }
    
    /**
     * Cập nhật medicine cho registered user
     */
    @Operation(summary = "Cập nhật medicine",
               description = "Cập nhật thông tin của medicine")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medicine được cập nhật thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Request không hợp lệ"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medicine không tồn tại"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MedicineResponse>> updateMedicine(
            @Parameter(description = "Medicine ID") @PathVariable Long id,
            @Valid @RequestBody UpdateMedicineRequest request) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Updating medicine ID: {} for user ID: {}", id, userId);

        Medicine medicine = medicineBusinessService.updateMedicineForUser(id, request, userId);
        MedicineResponse response = MedicineResponse.from(medicine);

        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_UPDATED", "Medicine updated successfully", response));
    }
    
    /**
     * Xóa medicine cho registered user
     */
    @Operation(summary = "Xóa medicine",
               description = "Xóa medicine theo ID")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Medicine được xóa thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medicine không tồn tại"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteMedicine(
            @Parameter(description = "Medicine ID") @PathVariable Long id) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Deleting medicine ID: {} for user ID: {}", id, userId);

        medicineBusinessService.deleteMedicineForUser(id, userId);

        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_DELETED", "Medicine deleted successfully", null));
    }
    
    /**
     * Lấy danh sách medicines cho registered user với pagination
     */
    @Operation(summary = "Lấy danh sách medicines",
               description = "Lấy danh sách medicines với pagination")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Lấy danh sách thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<MedicineResponse>>> getMedicines(
            Pageable pageable) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Getting medicines for user ID: {} with pagination", userId);

        Page<Medicine> medicines = medicineBusinessService.getMedicinesForUser(userId, pageable);
        Page<MedicineResponse> responses = medicines.map(MedicineResponse::from);

        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINES_FOUND", "Medicines found successfully", responses));
    }
    
    /**
     * Lấy danh sách active medicines cho registered user
     */
    @Operation(summary = "Lấy danh sách active medicines",
               description = "Lấy danh sách medicines đang hoạt động")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Lấy danh sách thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping("/active")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<MedicineResponse>>> getActiveMedicines() {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Getting active medicines for user ID: {}", userId);

        List<Medicine> medicines = medicineBusinessService.getActiveMedicinesForUser(userId);
        List<MedicineResponse> responses = medicines.stream().map(MedicineResponse::from).toList();

        return ResponseEntity.ok(ApiResponse.success(200, "ACTIVE_MEDICINES_FOUND", "Active medicines found successfully", responses));
    }
    
    /**
     * Tìm kiếm medicines theo tên cho registered user
     */
    @Operation(summary = "Tìm kiếm medicines theo tên",
               description = "Tìm kiếm medicines theo tên (case insensitive)")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Tìm kiếm thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Tên medicine không hợp lệ"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping("/search")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<MedicineResponse>>> searchMedicinesByName(
            @Parameter(description = "Medicine name") @RequestParam String name) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Searching medicines by name '{}' for user ID: {}", name, userId);

        List<Medicine> medicines = medicineBusinessService.searchMedicinesByNameForUser(name, userId);
        List<MedicineResponse> responses = medicines.stream().map(MedicineResponse::from).toList();

        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINES_SEARCHED", "Medicines searched successfully", responses));
    }
    
    /**
     * Lấy medicines sắp hết hạn cho registered user
     */
    @Operation(summary = "Lấy medicines sắp hết hạn",
               description = "Lấy danh sách medicines sắp hết hạn trước ngày chỉ định")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Lấy danh sách thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping("/expiring")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<MedicineResponse>>> getMedicinesExpiringSoon(
            @Parameter(description = "Expiry date") @RequestParam LocalDate expiryDate) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Getting medicines expiring before {} for user ID: {}", expiryDate, userId);

        List<Medicine> medicines = medicineBusinessService.getMedicinesExpiringSoonForUser(userId, expiryDate);
        List<MedicineResponse> responses = medicines.stream().map(MedicineResponse::from).toList();

        return ResponseEntity.ok(ApiResponse.success(200, "EXPIRING_MEDICINES_FOUND", "Expiring medicines found successfully", responses));
    }
    
    /**
     * Lấy medicines có số lượng thấp cho registered user
     */
    @Operation(summary = "Lấy medicines có số lượng thấp",
               description = "Lấy danh sách medicines có số lượng dưới ngưỡng chỉ định")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Lấy danh sách thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Ngưỡng số lượng không hợp lệ"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping("/low-quantity")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<MedicineResponse>>> getMedicinesWithLowQuantity(
            @Parameter(description = "Quantity threshold") @RequestParam(defaultValue = "5") Integer threshold) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Getting medicines with quantity <= {} for user ID: {}", threshold, userId);

        List<Medicine> medicines = medicineBusinessService.getMedicinesWithLowQuantityForUser(userId, threshold);
        List<MedicineResponse> responses = medicines.stream().map(MedicineResponse::from).toList();

        return ResponseEntity.ok(ApiResponse.success(200, "LOW_QUANTITY_MEDICINES_FOUND", "Low quantity medicines found successfully", responses));
    }
    
    /**
     * Cập nhật số lượng medicine
     */
    @Operation(summary = "Cập nhật số lượng medicine",
               description = "Cập nhật số lượng của medicine")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Số lượng được cập nhật thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Số lượng không hợp lệ"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medicine không tồn tại"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @PatchMapping("/{id}/quantity")
    public ResponseEntity<ApiResponse<Void>> updateMedicineQuantity(
            @Parameter(description = "Medicine ID") @PathVariable Long id,
            @Parameter(description = "New quantity") @RequestParam Integer quantity) {
        
        log.info("Updating quantity to {} for medicine ID: {}", quantity, id);
        
        medicineBusinessService.updateMedicineQuantity(id, quantity);
        
        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_QUANTITY_UPDATED", "Medicine quantity updated successfully", null));
    }
    
    /**
     * Cập nhật trạng thái active của medicine
     */
    @Operation(summary = "Cập nhật trạng thái active",
               description = "Cập nhật trạng thái active của medicine")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Trạng thái được cập nhật thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Trạng thái không hợp lệ"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Medicine không tồn tại"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @PatchMapping("/{id}/active")
    public ResponseEntity<ApiResponse<Void>> updateMedicineActiveStatus(
            @Parameter(description = "Medicine ID") @PathVariable Long id,
            @Parameter(description = "Active status") @RequestParam Boolean isActive) {
        
        log.info("Updating active status to {} for medicine ID: {}", isActive, id);
        
        medicineBusinessService.updateMedicineActiveStatus(id, isActive);
        
        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_ACTIVE_STATUS_UPDATED", "Medicine active status updated successfully", null));
    }
    
    /**
     * Lấy thống kê medicines cho registered user
     */
    @Operation(summary = "Lấy thống kê medicines",
               description = "Lấy thống kê tổng quan về medicines")
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Lấy thống kê thành công"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Lỗi server")
    })
    @GetMapping("/stats")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<MedicineStatsResponse>> getMedicineStats() {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Getting medicine statistics for user ID: {}", userId);

        MedicineStatsResponse stats = medicineBusinessService.getMedicineStatsForUser(userId);

        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_STATS_FOUND", "Medicine statistics found successfully", stats));
    }
} 