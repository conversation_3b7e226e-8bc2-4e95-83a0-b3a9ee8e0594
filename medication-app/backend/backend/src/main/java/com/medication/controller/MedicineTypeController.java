package com.medication.controller;

import com.medication.dto.response.ApiResponse;
import com.medication.entity.MedicineType;
import com.medication.repository.domain.medicine.MedicineTypeRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * REST Controller cho MedicineType
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/v1/medicine-types")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Medicine Types", description = "API quản lý medicine types")
public class MedicineTypeController {
    
    private final MedicineTypeRepository medicineTypeRepository;
    
    /**
     * <PERSON><PERSON>y danh sách tất cả medicine types
     */
    @Operation(summary = "Lấy danh sách medicine types",
               description = "Lấy danh sách tất cả medicine types")
    @GetMapping
    public ResponseEntity<ApiResponse<List<MedicineType>>> getAllMedicineTypes() {
        log.info("Getting all medicine types");
        
        List<MedicineType> medicineTypes = medicineTypeRepository.findAll();
        
        return ResponseEntity.ok(ApiResponse.success(200, "MEDICINE_TYPES_FOUND", "Medicine types found successfully", medicineTypes));
    }
} 