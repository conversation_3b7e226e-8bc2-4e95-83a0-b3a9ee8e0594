package com.medication.controller;

import com.medication.dto.request.CreatePrescriptionRequest;
import com.medication.dto.request.UpdatePrescriptionRequest;
import com.medication.dto.response.ApiResponse;
import com.medication.dto.response.PrescriptionResponse;
import com.medication.dto.response.PrescriptionStatsResponse;
import com.medication.entity.Prescription;
import com.medication.enums.PrescriptionStatus;
import com.medication.security.SecurityUtils;
import com.medication.service.domain.prescription.PrescriptionBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * REST Controller cho Prescription API (Registered Users)
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/v1/prescriptions")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Prescriptions", description = "API quản lý prescriptions cho registered users")
@SecurityRequirement(name = "bearerAuth")
public class PrescriptionController {
    
    private final PrescriptionBusinessService prescriptionBusinessService;
    
    // ==================== CRUD OPERATIONS ====================
    
    @Operation(summary = "Tạo prescription mới", description = "Tạo prescription mới cho registered user")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Prescription created successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid input data"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PrescriptionResponse>> createPrescription(
            @Valid @RequestBody CreatePrescriptionRequest request) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Creating prescription for user ID: {}", userId);

        Prescription prescription = prescriptionBusinessService.createPrescriptionForUser(request, userId);
        PrescriptionResponse response = PrescriptionResponse.from(prescription);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(201, "PRESCRIPTION_CREATED", "Prescription created successfully", response));
    }
    
    @Operation(summary = "Lấy prescription theo ID", description = "Lấy thông tin chi tiết prescription theo ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Prescription found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PrescriptionResponse>> getPrescription(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Getting prescription ID: {} for user ID: {}", id, userId);

        Prescription prescription = prescriptionBusinessService.findPrescriptionByIdForUser(id, userId);
        PrescriptionResponse response = PrescriptionResponse.from(prescription);

        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_FOUND", "Prescription found successfully", response));
    }
    
    @Operation(summary = "Lấy danh sách prescriptions", description = "Lấy danh sách prescriptions với pagination")
    @GetMapping
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<PrescriptionResponse>>> getPrescriptions(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Getting prescriptions for user ID: {} with pagination", userId);

        Sort sort = sortDir.equalsIgnoreCase("asc") ? Sort.by(sortBy).ascending() : Sort.by(sortBy).descending();
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<Prescription> prescriptions = prescriptionBusinessService.getPrescriptionsForUser(userId, pageable);
        Page<PrescriptionResponse> responses = prescriptions.map(PrescriptionResponse::from);

        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTIONS_FOUND", "Prescriptions found successfully", responses));
    }
    
    @Operation(summary = "Cập nhật prescription", description = "Cập nhật thông tin prescription")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Prescription updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription not found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PrescriptionResponse>> updatePrescription(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id,
            @Valid @RequestBody UpdatePrescriptionRequest request) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Updating prescription ID: {} for user ID: {}", id, userId);

        Prescription prescription = prescriptionBusinessService.updatePrescriptionForUser(id, request, userId);
        PrescriptionResponse response = PrescriptionResponse.from(prescription);

        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_UPDATED", "Prescription updated successfully", response));
    }
    
    @Operation(summary = "Xóa prescription", description = "Xóa prescription theo ID")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Prescription deleted successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Prescription not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deletePrescription(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Deleting prescription ID: {} for user ID: {}", id, userId);

        prescriptionBusinessService.deletePrescriptionForUser(id, userId);

        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTION_DELETED", "Prescription deleted successfully", null));
    }
    
    // ==================== SEARCH AND FILTER OPERATIONS ====================
    
    @Operation(summary = "Tìm prescriptions theo doctor name", description = "Tìm prescriptions theo tên bác sĩ")
    @GetMapping("/search/doctor")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<PrescriptionResponse>>> searchByDoctorName(
            @Parameter(description = "Doctor name", required = true) @RequestParam String doctorName) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Searching prescriptions by doctor name: {} for user ID: {}", doctorName, userId);

        List<Prescription> prescriptions = prescriptionBusinessService.searchByDoctorName(userId, doctorName);
        List<PrescriptionResponse> responses = prescriptions.stream()
                .map(PrescriptionResponse::from)
                .collect(Collectors.toList());

        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTIONS_FOUND", "Prescriptions found successfully", responses));
    }
    
    @Operation(summary = "Tìm prescriptions theo diagnosis", description = "Tìm prescriptions theo chẩn đoán")
    @GetMapping("/search/diagnosis")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<PrescriptionResponse>>> searchByDiagnosis(
            @Parameter(description = "Diagnosis", required = true) @RequestParam String diagnosis) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Searching prescriptions by diagnosis: {} for user ID: {}", diagnosis, userId);

        List<Prescription> prescriptions = prescriptionBusinessService.searchByDiagnosis(userId, diagnosis);
        List<PrescriptionResponse> responses = prescriptions.stream()
                .map(PrescriptionResponse::from)
                .collect(Collectors.toList());

        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTIONS_FOUND", "Prescriptions found successfully", responses));
    }
    
    @Operation(summary = "Tìm prescriptions theo date range", description = "Tìm prescriptions theo khoảng thời gian")
    @GetMapping("/search/date-range")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<PrescriptionResponse>>> findByDateRange(
            @Parameter(description = "Start date", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "End date", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Finding prescriptions by date range: {} to {} for user ID: {}", startDate, endDate, userId);

        List<Prescription> prescriptions = prescriptionBusinessService.findByDateRange(userId, startDate, endDate);
        List<PrescriptionResponse> responses = prescriptions.stream()
                .map(PrescriptionResponse::from)
                .collect(Collectors.toList());

        return ResponseEntity.ok(ApiResponse.success(200, "PRESCRIPTIONS_FOUND", "Prescriptions found successfully", responses));
    }
    
    // ==================== UPDATE OPERATIONS ====================
    
    @Operation(summary = "Cập nhật compliance rate", description = "Cập nhật tỷ lệ tuân thủ điều trị")
    @PatchMapping("/{id}/compliance")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateComplianceRate(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id,
            @Parameter(description = "Compliance rate", required = true) @RequestParam Double complianceRate,
            @Parameter(description = "Taken doses", required = true) @RequestParam Integer takenDoses) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Updating compliance rate for prescription ID: {} for user ID: {}", id, userId);

        prescriptionBusinessService.updateComplianceRate(id, complianceRate, takenDoses);

        return ResponseEntity.ok(ApiResponse.success(200, "COMPLIANCE_UPDATED", "Compliance rate updated successfully", null));
    }
    
    @Operation(summary = "Cập nhật status", description = "Cập nhật trạng thái prescription")
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateStatus(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id,
            @Parameter(description = "Status", required = true) @RequestParam PrescriptionStatus status) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Updating status for prescription ID: {} for user ID: {}", id, userId);

        prescriptionBusinessService.updateStatus(id, status);

        return ResponseEntity.ok(ApiResponse.success(200, "STATUS_UPDATED", "Status updated successfully", null));
    }
    
    @Operation(summary = "Cập nhật active status", description = "Cập nhật trạng thái active của prescription")
    @PatchMapping("/{id}/active")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateActiveStatus(
            @Parameter(description = "Prescription ID", required = true) @PathVariable Long id,
            @Parameter(description = "Is active", required = true) @RequestParam Boolean isActive) {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Updating active status for prescription ID: {} for user ID: {}", id, userId);

        prescriptionBusinessService.updateActiveStatus(id, isActive);

        return ResponseEntity.ok(ApiResponse.success(200, "ACTIVE_STATUS_UPDATED", "Active status updated successfully", null));
    }
    
    // ==================== STATISTICS OPERATIONS ====================
    
    @Operation(summary = "Lấy thống kê prescriptions", description = "Lấy thống kê prescriptions cho user")
    @GetMapping("/stats")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PrescriptionStatsResponse>> getPrescriptionStats() {

        Long userId = SecurityUtils.getCurrentUserId();
        log.info("Getting prescription stats for user ID: {}", userId);

        PrescriptionStatsResponse stats = prescriptionBusinessService.getPrescriptionStatsForUser(userId);

        return ResponseEntity.ok(ApiResponse.success(200, "STATS_FOUND", "Prescription stats found successfully", stats));
    }
} 