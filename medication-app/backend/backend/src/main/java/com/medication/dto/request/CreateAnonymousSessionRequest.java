package com.medication.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request DTO để tạo anonymous session mới
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateAnonymousSessionRequest {
    
    /**
     * Device ID - để track device sử dụng
     */
    @NotBlank(message = "Device ID không được để trống")
    @Size(max = 100, message = "Device ID không được quá 100 ký tự")
    private String deviceId;
    
    /**
     * App version - để track version app
     */
    @Size(max = 20, message = "App version không được quá 20 ký tự")
    private String appVersion;
} 