package com.medication.dto.request;

import com.medication.enums.DoseUnit;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;

/**
 * Request DTO for creating a new medication schedule
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateMedicationScheduleRequest {

    @NotNull(message = "Prescription item ID is required")
    @Positive(message = "Prescription item ID must be positive")
    private Long prescriptionItemId;

    @NotNull(message = "Day of week is required")
    @Min(value = 1, message = "Day of week must be between 1 and 7")
    @Max(value = 7, message = "Day of week must be between 1 and 7")
    private Integer dayOfWeek; // 1=Monday, 7=Sunday

    @NotNull(message = "Time is required")
    private LocalTime time;

    @NotNull(message = "Dose amount is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Dose amount must be positive")
    @DecimalMax(value = "9999.99", message = "Dose amount must not exceed 9999.99")
    private Double doseAmount;

    @NotNull(message = "Dose unit is required")
    private DoseUnit doseUnit;

    @Size(max = 500, message = "Notes must not exceed 500 characters")
    private String notes;

    @Builder.Default
    private Boolean isActive = true;

    /**
     * Validate day of week value
     */
    @AssertTrue(message = "Day of week must be between 1 (Monday) and 7 (Sunday)")
    public boolean isDayOfWeekValid() {
        return dayOfWeek == null || (dayOfWeek >= 1 && dayOfWeek <= 7);
    }

    /**
     * Validate time is not null and reasonable
     */
    @AssertTrue(message = "Time must be a valid time of day")
    public boolean isTimeValid() {
        return time != null;
    }

    /**
     * Validate dose amount is reasonable
     */
    @AssertTrue(message = "Dose amount must be a reasonable value")
    public boolean isDoseAmountReasonable() {
        return doseAmount == null || (doseAmount > 0 && doseAmount <= 9999.99);
    }

    /**
     * Validate notes length
     */
    @AssertTrue(message = "Notes must not exceed 500 characters")
    public boolean isNotesLengthValid() {
        return notes == null || notes.length() <= 500;
    }

    /**
     * Get day of week name for display
     */
    public String getDayOfWeekName() {
        if (dayOfWeek == null) return "Unknown";
        
        switch (dayOfWeek) {
            case 1: return "Monday";
            case 2: return "Tuesday";
            case 3: return "Wednesday";
            case 4: return "Thursday";
            case 5: return "Friday";
            case 6: return "Saturday";
            case 7: return "Sunday";
            default: return "Unknown";
        }
    }

    /**
     * Get formatted time string
     */
    public String getFormattedTime() {
        return time != null ? time.toString() : "";
    }

    /**
     * Get dose description
     */
    public String getDoseDescription() {
        if (doseAmount == null || doseUnit == null) {
            return "No dose specified";
        }
        return doseAmount + " " + doseUnit.name();
    }

    /**
     * Check if this is a morning schedule (before 12:00)
     */
    public boolean isMorningSchedule() {
        return time != null && time.isBefore(LocalTime.NOON);
    }

    /**
     * Check if this is an evening schedule (after 18:00)
     */
    public boolean isEveningSchedule() {
        return time != null && time.isAfter(LocalTime.of(18, 0));
    }

    /**
     * Check if this is a weekend schedule (Saturday or Sunday)
     */
    public boolean isWeekendSchedule() {
        return dayOfWeek != null && (dayOfWeek == 6 || dayOfWeek == 7);
    }

    /**
     * Check if this is a weekday schedule (Monday to Friday)
     */
    public boolean isWeekdaySchedule() {
        return dayOfWeek != null && dayOfWeek >= 1 && dayOfWeek <= 5;
    }

    /**
     * Get schedule summary for display
     */
    public String getScheduleSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (dayOfWeek != null) {
            summary.append(getDayOfWeekName());
        }
        
        if (time != null) {
            if (summary.length() > 0) summary.append(" at ");
            summary.append(getFormattedTime());
        }
        
        if (doseAmount != null && doseUnit != null) {
            if (summary.length() > 0) summary.append(" - ");
            summary.append(getDoseDescription());
        }
        
        return summary.toString();
    }

    /**
     * Validate the entire request
     */
    public boolean isValid() {
        return prescriptionItemId != null && prescriptionItemId > 0 &&
               isDayOfWeekValid() &&
               isTimeValid() &&
               isDoseAmountReasonable() &&
               doseUnit != null &&
               isNotesLengthValid();
    }

    /**
     * Get validation errors
     */
    public String getValidationErrors() {
        StringBuilder errors = new StringBuilder();
        
        if (prescriptionItemId == null || prescriptionItemId <= 0) {
            errors.append("Prescription item ID is required and must be positive. ");
        }
        
        if (!isDayOfWeekValid()) {
            errors.append("Day of week must be between 1 and 7. ");
        }
        
        if (!isTimeValid()) {
            errors.append("Time is required. ");
        }
        
        if (!isDoseAmountReasonable()) {
            errors.append("Dose amount must be positive and reasonable. ");
        }
        
        if (doseUnit == null) {
            errors.append("Dose unit is required. ");
        }
        
        if (!isNotesLengthValid()) {
            errors.append("Notes must not exceed 500 characters. ");
        }
        
        return errors.toString().trim();
    }

    /**
     * Create a copy with updated values
     */
    public CreateMedicationScheduleRequest withUpdatedTime(LocalTime newTime) {
        return CreateMedicationScheduleRequest.builder()
                .prescriptionItemId(this.prescriptionItemId)
                .dayOfWeek(this.dayOfWeek)
                .time(newTime)
                .doseAmount(this.doseAmount)
                .doseUnit(this.doseUnit)
                .notes(this.notes)
                .isActive(this.isActive)
                .build();
    }

    /**
     * Create a copy with updated dose
     */
    public CreateMedicationScheduleRequest withUpdatedDose(Double newDoseAmount, DoseUnit newDoseUnit) {
        return CreateMedicationScheduleRequest.builder()
                .prescriptionItemId(this.prescriptionItemId)
                .dayOfWeek(this.dayOfWeek)
                .time(this.time)
                .doseAmount(newDoseAmount)
                .doseUnit(newDoseUnit)
                .notes(this.notes)
                .isActive(this.isActive)
                .build();
    }
}
