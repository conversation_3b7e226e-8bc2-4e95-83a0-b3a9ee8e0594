package com.medication.dto.request;

import com.medication.enums.DoseUnit;
import com.medication.enums.Route;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for creating a new prescription item
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreatePrescriptionItemRequest {

    @NotNull(message = "Prescription ID is required")
    private Long prescriptionId;

    @NotNull(message = "Medicine ID is required")
    private Long medicineId;

    @NotNull(message = "Dose amount is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Dose amount must be positive")
    private Double doseAmount;

    @NotNull(message = "Dose unit is required")
    private DoseUnit doseUnit;

    @Min(value = 1, message = "Quantity must be at least 1")
    @Builder.Default
    private Integer quantity = 1;

    @Size(max = 2000, message = "Instructions must not exceed 2000 characters")
    private String instructions;

    @NotNull(message = "Route is required")
    private Route route;

    @NotNull(message = "Duration days is required")
    @Min(value = 1, message = "Duration days must be at least 1")
    private Integer durationDays;
}
