package com.medication.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.DecimalMax;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO for creating a new prescription
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreatePrescriptionRequest {

    @NotBlank(message = "Doctor name is required")
    @Size(max = 100, message = "Doctor name must not exceed 100 characters")
    private String doctorName;

    @NotNull(message = "Prescription date is required")
    private LocalDate prescriptionDate;

    @NotNull(message = "Start date is required")
    private LocalDate startDate;

    @NotNull(message = "End date is required")
    private LocalDate endDate;

    @NotBlank(message = "Diagnosis is required")
    @Size(max = 500, message = "Diagnosis must not exceed 500 characters")
    private String diagnosis;

    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    private String notes;

    private Long patientId;

    private Long prescribedById;

    private Long familyId;

    @DecimalMin(value = "0.0", inclusive = true, message = "Compliance rate must be at least 0")
    @DecimalMax(value = "100.0", inclusive = true, message = "Compliance rate cannot exceed 100")
    private BigDecimal complianceRate = BigDecimal.ZERO;

    private Integer totalDoses = 0;

    private Integer takenDoses = 0;

    private Boolean isActive = true;
} 