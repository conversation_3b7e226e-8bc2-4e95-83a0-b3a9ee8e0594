package com.medication.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Request DTO cho user registration
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RegisterRequest {

    /**
     * Email của user
     */
    @NotBlank(message = "Email không được để trống")
    @Email(message = "Email không đúng định dạng")
    @Size(max = 255, message = "Email không được quá 255 ký tự")
    private String email;

    /**
     * Password của user
     */
    @NotBlank(message = "Password không được để trống")
    @Size(min = 6, max = 100, message = "Password phải từ 6 đến 100 ký tự")
    private String password;

    /**
     * Confirm password
     */
    @NotBlank(message = "Confirm password không được để trống")
    private String confirmPassword;

    /**
     * Tên đầy đủ của user
     */
    @NotBlank(message = "Tên đầy đủ không được để trống")
    @Size(max = 100, message = "Tên đầy đủ không được quá 100 ký tự")
    private String fullName;

    /**
     * Số điện thoại (optional)
     */
    @Size(max = 20, message = "Số điện thoại không được quá 20 ký tự")
    private String phoneNumber;

    /**
     * Anonymous user ID để migrate data (optional)
     */
    private UUID anonymousUserId;
}
