package com.medication.dto.request;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request DTO để cập nhật anonymous session
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateAnonymousSessionRequest {
    
    /**
     * App version - để track version app
     */
    @Size(max = 20, message = "App version không được quá 20 ký tự")
    private String appVersion;
    
    /**
     * Trạng thái đồng bộ data
     */
    private Boolean dataSynced;
} 