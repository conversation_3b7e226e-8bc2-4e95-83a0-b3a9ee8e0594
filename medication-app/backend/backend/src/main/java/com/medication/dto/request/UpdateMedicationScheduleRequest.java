package com.medication.dto.request;

import com.medication.enums.DoseUnit;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;

/**
 * Request DTO for updating an existing medication schedule
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateMedicationScheduleRequest {

    @Min(value = 1, message = "Day of week must be between 1 and 7")
    @Max(value = 7, message = "Day of week must be between 1 and 7")
    private Integer dayOfWeek; // 1=Monday, 7=Sunday

    private LocalTime time;

    @DecimalMin(value = "0.0", inclusive = false, message = "Dose amount must be positive")
    @DecimalMax(value = "9999.99", message = "Dose amount must not exceed 9999.99")
    private Double doseAmount;

    private DoseUnit doseUnit;

    @Size(max = 500, message = "Notes must not exceed 500 characters")
    private String notes;

    private Boolean isActive;

    /**
     * Validate day of week value if provided
     */
    @AssertTrue(message = "Day of week must be between 1 (Monday) and 7 (Sunday)")
    public boolean isDayOfWeekValid() {
        return dayOfWeek == null || (dayOfWeek >= 1 && dayOfWeek <= 7);
    }

    /**
     * Validate dose amount is reasonable if provided
     */
    @AssertTrue(message = "Dose amount must be a reasonable value")
    public boolean isDoseAmountReasonable() {
        return doseAmount == null || (doseAmount > 0 && doseAmount <= 9999.99);
    }

    /**
     * Validate notes length if provided
     */
    @AssertTrue(message = "Notes must not exceed 500 characters")
    public boolean isNotesLengthValid() {
        return notes == null || notes.length() <= 500;
    }

    /**
     * Check if any field is provided for update
     */
    public boolean hasUpdates() {
        return dayOfWeek != null || 
               time != null || 
               doseAmount != null || 
               doseUnit != null || 
               notes != null || 
               isActive != null;
    }

    /**
     * Get day of week name for display
     */
    public String getDayOfWeekName() {
        if (dayOfWeek == null) return null;
        
        switch (dayOfWeek) {
            case 1: return "Monday";
            case 2: return "Tuesday";
            case 3: return "Wednesday";
            case 4: return "Thursday";
            case 5: return "Friday";
            case 6: return "Saturday";
            case 7: return "Sunday";
            default: return "Unknown";
        }
    }

    /**
     * Get formatted time string
     */
    public String getFormattedTime() {
        return time != null ? time.toString() : null;
    }

    /**
     * Get dose description
     */
    public String getDoseDescription() {
        if (doseAmount == null && doseUnit == null) {
            return null;
        }
        
        StringBuilder desc = new StringBuilder();
        if (doseAmount != null) {
            desc.append(doseAmount);
        }
        if (doseUnit != null) {
            if (desc.length() > 0) desc.append(" ");
            desc.append(doseUnit.name());
        }
        
        return desc.toString();
    }

    /**
     * Check if this is a morning schedule (before 12:00)
     */
    public Boolean isMorningSchedule() {
        return time != null ? time.isBefore(LocalTime.NOON) : null;
    }

    /**
     * Check if this is an evening schedule (after 18:00)
     */
    public Boolean isEveningSchedule() {
        return time != null ? time.isAfter(LocalTime.of(18, 0)) : null;
    }

    /**
     * Check if this is a weekend schedule (Saturday or Sunday)
     */
    public Boolean isWeekendSchedule() {
        return dayOfWeek != null ? (dayOfWeek == 6 || dayOfWeek == 7) : null;
    }

    /**
     * Check if this is a weekday schedule (Monday to Friday)
     */
    public Boolean isWeekdaySchedule() {
        return dayOfWeek != null ? (dayOfWeek >= 1 && dayOfWeek <= 5) : null;
    }

    /**
     * Get update summary for display
     */
    public String getUpdateSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (dayOfWeek != null) {
            summary.append("Day: ").append(getDayOfWeekName());
        }
        
        if (time != null) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("Time: ").append(getFormattedTime());
        }
        
        if (doseAmount != null || doseUnit != null) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("Dose: ").append(getDoseDescription());
        }
        
        if (notes != null) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("Notes updated");
        }
        
        if (isActive != null) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("Status: ").append(isActive ? "Active" : "Inactive");
        }
        
        return summary.toString();
    }

    /**
     * Validate the entire request
     */
    public boolean isValid() {
        return isDayOfWeekValid() &&
               isDoseAmountReasonable() &&
               isNotesLengthValid() &&
               hasUpdates();
    }

    /**
     * Get validation errors
     */
    public String getValidationErrors() {
        StringBuilder errors = new StringBuilder();
        
        if (!hasUpdates()) {
            errors.append("At least one field must be provided for update. ");
        }
        
        if (!isDayOfWeekValid()) {
            errors.append("Day of week must be between 1 and 7. ");
        }
        
        if (!isDoseAmountReasonable()) {
            errors.append("Dose amount must be positive and reasonable. ");
        }
        
        if (!isNotesLengthValid()) {
            errors.append("Notes must not exceed 500 characters. ");
        }
        
        return errors.toString().trim();
    }

    /**
     * Create a copy with only time update
     */
    public static UpdateMedicationScheduleRequest timeOnly(LocalTime newTime) {
        return UpdateMedicationScheduleRequest.builder()
                .time(newTime)
                .build();
    }

    /**
     * Create a copy with only dose update
     */
    public static UpdateMedicationScheduleRequest doseOnly(Double newDoseAmount, DoseUnit newDoseUnit) {
        return UpdateMedicationScheduleRequest.builder()
                .doseAmount(newDoseAmount)
                .doseUnit(newDoseUnit)
                .build();
    }

    /**
     * Create a copy with only day update
     */
    public static UpdateMedicationScheduleRequest dayOnly(Integer newDayOfWeek) {
        return UpdateMedicationScheduleRequest.builder()
                .dayOfWeek(newDayOfWeek)
                .build();
    }

    /**
     * Create a copy with only notes update
     */
    public static UpdateMedicationScheduleRequest notesOnly(String newNotes) {
        return UpdateMedicationScheduleRequest.builder()
                .notes(newNotes)
                .build();
    }

    /**
     * Create a copy with only status update
     */
    public static UpdateMedicationScheduleRequest statusOnly(Boolean newIsActive) {
        return UpdateMedicationScheduleRequest.builder()
                .isActive(newIsActive)
                .build();
    }

    /**
     * Merge with another update request
     */
    public UpdateMedicationScheduleRequest mergeWith(UpdateMedicationScheduleRequest other) {
        if (other == null) return this;
        
        return UpdateMedicationScheduleRequest.builder()
                .dayOfWeek(other.dayOfWeek != null ? other.dayOfWeek : this.dayOfWeek)
                .time(other.time != null ? other.time : this.time)
                .doseAmount(other.doseAmount != null ? other.doseAmount : this.doseAmount)
                .doseUnit(other.doseUnit != null ? other.doseUnit : this.doseUnit)
                .notes(other.notes != null ? other.notes : this.notes)
                .isActive(other.isActive != null ? other.isActive : this.isActive)
                .build();
    }
}
