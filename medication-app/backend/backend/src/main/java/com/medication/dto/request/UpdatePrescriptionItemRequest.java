package com.medication.dto.request;

import com.medication.enums.DoseUnit;
import com.medication.enums.Route;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for updating an existing prescription item
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdatePrescriptionItemRequest {

    private Long medicineId;

    @DecimalMin(value = "0.0", inclusive = false, message = "Dose amount must be positive")
    private Double doseAmount;

    private DoseUnit doseUnit;

    @Min(value = 1, message = "Quantity must be at least 1")
    private Integer quantity;

    @Size(max = 2000, message = "Instructions must not exceed 2000 characters")
    private String instructions;

    private Route route;

    @Min(value = 1, message = "Duration days must be at least 1")
    private Integer durationDays;
}
