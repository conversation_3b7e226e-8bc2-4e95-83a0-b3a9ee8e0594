package com.medication.dto.response;

import com.medication.entity.AnonymousSession;
import com.medication.enums.AnonymousSessionStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Response DTO cho anonymous session
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AnonymousSessionResponse {
    
    private Long id;
    private UUID anonymousUserId;
    private String deviceId;
    private String appVersion;
    private LocalDateTime createdAt;
    private LocalDateTime lastActivityAt;
    private Boolean dataSynced;
    private Long migratedToUserId;
    private LocalDateTime migratedAt;
    private AnonymousSessionStatus status;
    
    /**
     * Static factory method để convert từ entity sang response
     */
    public static AnonymousSessionResponse from(AnonymousSession session) {
        return AnonymousSessionResponse.builder()
            .id(session.getId())
            .anonymousUserId(session.getAnonymousUserId())
            .deviceId(session.getDeviceId())
            .appVersion(session.getAppVersion())
            .createdAt(session.getCreatedAt())
            .lastActivityAt(session.getLastActivityAt())
            .dataSynced(session.getDataSynced())
            .migratedToUserId(session.getMigratedToUserId())
            .migratedAt(session.getMigratedAt())
            .status(session.getStatus())
            .build();
    }
} 