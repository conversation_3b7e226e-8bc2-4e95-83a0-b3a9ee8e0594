package com.medication.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO cho response thống kê anonymous sessions
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AnonymousSessionStatsResponse {
    
    /**
     * Tổng số sessions
     */
    private long totalSessions;
    
    /**
     * Số active sessions
     */
    private long activeSessions;
    
    /**
     * Số expired sessions
     */
    private long expiredSessions;
    
    /**
     * Số migrated sessions
     */
    private long migratedSessions;
    
    /**
     * Số sessions được tạo trong 24h qua
     */
    private long sessionsCreatedLast24h;
    
    /**
     * Số sessions hoạt động trong 24h qua
     */
    private long sessionsActiveLast24h;
    
    /**
     * Thời gian thống kê
     */
    private LocalDateTime timestamp;
    
    /**
     * Static factory method để tạo stats response
     */
    public static AnonymousSessionStatsResponse of(
            long totalSessions,
            long activeSessions,
            long expiredSessions,
            long migratedSessions,
            long sessionsCreatedLast24h,
            long sessionsActiveLast24h) {
        
        return AnonymousSessionStatsResponse.builder()
            .totalSessions(totalSessions)
            .activeSessions(activeSessions)
            .expiredSessions(expiredSessions)
            .migratedSessions(migratedSessions)
            .sessionsCreatedLast24h(sessionsCreatedLast24h)
            .sessionsActiveLast24h(sessionsActiveLast24h)
            .timestamp(LocalDateTime.now())
            .build();
    }
} 