package com.medication.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.time.Instant;

/**
 * Response wrapper chuẩn RESTful API với HTTP code và Business code
 * 
 * <AUTHOR> Team
 * @version 2.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    
    /**
     * Trạng thái thành công
     */
    private boolean success;
    
    /**
     * HTTP status code (200, 400, 500,...)
     */
    private int statusCode;
    
    /**
     * Business code do hệ thống định nghĩa (ví dụ: USER_NOT_FOUND, SESSION_CREATED)
     */
    private String code;
    
    /**
     * Message mô tả
     */
    private String message;
    
    /**
     * Data payload
     */
    private T data;
    
    /**
     * Timestamp
     */
    private Instant timestamp;
    
    /**
     * Success response với full parameters
     */
    public static <T> ApiResponse<T> success(int statusCode, String code, String message, T data) {
        return ApiResponse.<T>builder()
                .success(true)
                .statusCode(statusCode)
                .code(code)
                .message(message)
                .data(data)
                .timestamp(Instant.now())
                .build();
    }
    
    /**
     * Error response với full parameters
     */
    public static <T> ApiResponse<T> error(int statusCode, String code, String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .statusCode(statusCode)
                .code(code)
                .message(message)
                .timestamp(Instant.now())
                .build();
    }
    
    /**
     * Error response với data
     */
    public static <T> ApiResponse<T> error(int statusCode, String code, String message, T data) {
        return ApiResponse.<T>builder()
                .success(false)
                .statusCode(statusCode)
                .code(code)
                .message(message)
                .data(data)
                .timestamp(Instant.now())
                .build();
    }
    
    // Convenience methods cho các use cases phổ biến
    
    /**
     * Success response đơn giản với data
     */
    public static <T> ApiResponse<T> success(T data) {
        return success(200, "SUCCESS", "OK", data);
    }
    
    /**
     * Success response với message custom
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return success(200, "SUCCESS", message, data);
    }

    /**
     * Success response với custom code và message
     */
    public static <T> ApiResponse<T> success(T data, String code, String message) {
        return success(200, code, message, data);
    }
    
    /**
     * Created response (201)
     */
    public static <T> ApiResponse<T> created(T data) {
        return success(201, "CREATED", "Created successfully", data);
    }
    
    /**
     * Created response với message custom
     */
    public static <T> ApiResponse<T> created(T data, String message) {
        return success(201, "CREATED", message, data);
    }
    
    /**
     * No content response (204)
     */
    public static <T> ApiResponse<T> noContent() {
        return success(204, "NO_CONTENT", "No content", null);
    }
    
    /**
     * Not found error (404)
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return error(404, "NOT_FOUND", message);
    }
    
    /**
     * Bad request error (400)
     */
    public static <T> ApiResponse<T> badRequest(String message) {
        return error(400, "BAD_REQUEST", message);
    }
    
    /**
     * Bad request error với validation data (400)
     */
    public static <T> ApiResponse<T> badRequest(String message, T data) {
        return error(400, "VALIDATION_ERROR", message, data);
    }

    /**
     * Error response với custom code và message
     */
    public static <T> ApiResponse<T> error(String code, String message, org.springframework.http.HttpStatus status) {
        return error(status.value(), code, message);
    }
    
    /**
     * Internal server error (500)
     */
    public static <T> ApiResponse<T> internalServerError(String message) {
        return error(500, "INTERNAL_ERROR", message);
    }
    
    /**
     * Conflict error (409)
     */
    public static <T> ApiResponse<T> conflict(String message) {
        return error(409, "CONFLICT", message);
    }
} 