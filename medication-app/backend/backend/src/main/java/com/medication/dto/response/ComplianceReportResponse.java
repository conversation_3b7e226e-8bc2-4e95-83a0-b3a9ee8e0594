package com.medication.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * DTO for medication compliance report response
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ComplianceReportResponse {

    /**
     * Thời gian bắt đầu báo cáo
     */
    private LocalDate startDate;
    
    /**
     * Thời gian kết thúc báo cáo
     */
    private LocalDate endDate;
    
    /**
     * Tổng số ngày trong báo cáo
     */
    private long totalDays;
    
    /**
     * Tỷ lệ tuân thủ tổng thể (%)
     */
    private double overallComplianceRate;
    
    /**
     * Tổng số doses được scheduled
     */
    private long totalScheduledDoses;
    
    /**
     * Tổng số doses đã taken
     */
    private long totalTakenDoses;
    
    /**
     * Tổng số doses bị missed
     */
    private long totalMissedDoses;
    
    /**
     * Compliance rate theo ngày
     * Key: Date (YYYY-MM-DD), Value: compliance rate (%)
     */
    private Map<String, Double> dailyComplianceRates;
    
    /**
     * Compliance rate theo medicine
     */
    private List<MedicineComplianceData> medicineCompliance;
    
    /**
     * Compliance rate theo dose unit
     */
    private Map<String, ComplianceByDoseUnit> complianceByDoseUnit;
    
    /**
     * Thời gian tạo báo cáo
     */
    private LocalDateTime generatedAt;

    /**
     * Inner class for medicine compliance data
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MedicineComplianceData {
        private String medicineName;
        private long scheduledDoses;
        private long takenDoses;
        private long missedDoses;
        private double complianceRate;
    }

    /**
     * Inner class for compliance by dose unit
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ComplianceByDoseUnit {
        private String doseUnit;
        private long scheduledDoses;
        private long takenDoses;
        private long missedDoses;
        private double complianceRate;
    }

    /**
     * Static factory method to create ComplianceReportResponse
     */
    public static ComplianceReportResponse of(
            LocalDate startDate,
            LocalDate endDate,
            long totalDays,
            double overallComplianceRate,
            long totalScheduledDoses,
            long totalTakenDoses,
            long totalMissedDoses,
            Map<String, Double> dailyComplianceRates,
            List<MedicineComplianceData> medicineCompliance,
            Map<String, ComplianceByDoseUnit> complianceByDoseUnit) {
        
        return ComplianceReportResponse.builder()
            .startDate(startDate)
            .endDate(endDate)
            .totalDays(totalDays)
            .overallComplianceRate(overallComplianceRate)
            .totalScheduledDoses(totalScheduledDoses)
            .totalTakenDoses(totalTakenDoses)
            .totalMissedDoses(totalMissedDoses)
            .dailyComplianceRates(dailyComplianceRates)
            .medicineCompliance(medicineCompliance)
            .complianceByDoseUnit(complianceByDoseUnit)
            .generatedAt(LocalDateTime.now())
            .build();
    }
}
