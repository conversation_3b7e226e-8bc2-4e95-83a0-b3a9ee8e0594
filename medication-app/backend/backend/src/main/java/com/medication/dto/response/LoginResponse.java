package com.medication.dto.response;

import com.medication.entity.User;
import com.medication.enums.UserRole;
import com.medication.enums.UserType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Response DTO cho user login
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LoginResponse {

    /**
     * Access token
     */
    private String accessToken;

    /**
     * Refresh token
     */
    private String refreshToken;

    /**
     * Token type (Bearer)
     */
    @Builder.Default
    private String tokenType = "Bearer";

    /**
     * Token expiration time in seconds
     */
    private Long expiresIn;

    /**
     * User information
     */
    private UserInfo user;

    /**
     * User information nested class
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UserInfo {
        private Long id;
        private String email;
        private String fullName;
        private String phoneNumber;
        private UserRole role;
        private UserType userType;
        private Boolean isActive;
        private Boolean emailVerified;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    /**
     * Factory method để tạo LoginResponse từ User entity
     */
    public static LoginResponse from(User user, String accessToken, String refreshToken, Long expiresIn) {
        UserInfo userInfo = UserInfo.builder()
                .id(user.getId())
                .email(user.getEmail())
                .fullName(user.getFullName())
                .phoneNumber(user.getPhoneNumber())
                .role(user.getRole())
                .userType(user.getUserType())
                .isActive(user.getIsActive())
                .emailVerified(user.getEmailVerified())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .build();

        return LoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(expiresIn)
                .user(userInfo)
                .build();
    }
}
