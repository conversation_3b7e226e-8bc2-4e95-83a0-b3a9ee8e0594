package com.medication.dto.response;

import com.medication.enums.DoseUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Response DTO for medication schedule data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicationScheduleResponse {

    private Long id;
    private Long prescriptionItemId;
    private Integer dayOfWeek; // 1=Monday, 7=Sunday
    private LocalTime time;
    private Double doseAmount;
    private DoseUnit doseUnit;
    private String notes;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Additional computed fields for better UX
    private String dayOfWeekName;
    private String formattedTime;
    private String doseDescription;
    private String scheduleSummary;
    private Boolean isMorningSchedule;
    private Boolean isEveningSchedule;
    private Boolean isWeekendSchedule;
    private Boolean isWeekdaySchedule;

    /**
     * Get day of week name for display
     */
    public String getDayOfWeekName() {
        if (dayOfWeek == null) return "Unknown";
        
        switch (dayOfWeek) {
            case 1: return "Monday";
            case 2: return "Tuesday";
            case 3: return "Wednesday";
            case 4: return "Thursday";
            case 5: return "Friday";
            case 6: return "Saturday";
            case 7: return "Sunday";
            default: return "Unknown";
        }
    }

    /**
     * Get Vietnamese day of week name
     */
    public String getDayOfWeekNameVi() {
        if (dayOfWeek == null) return "Không xác định";
        
        switch (dayOfWeek) {
            case 1: return "Thứ 2";
            case 2: return "Thứ 3";
            case 3: return "Thứ 4";
            case 4: return "Thứ 5";
            case 5: return "Thứ 6";
            case 6: return "Thứ 7";
            case 7: return "Chủ nhật";
            default: return "Không xác định";
        }
    }

    /**
     * Get formatted time string
     */
    public String getFormattedTime() {
        return time != null ? time.toString() : "";
    }

    /**
     * Get 12-hour formatted time
     */
    public String getFormattedTime12Hour() {
        if (time == null) return "";
        
        int hour = time.getHour();
        int minute = time.getMinute();
        String amPm = hour >= 12 ? "PM" : "AM";
        
        if (hour == 0) {
            hour = 12;
        } else if (hour > 12) {
            hour -= 12;
        }
        
        return String.format("%d:%02d %s", hour, minute, amPm);
    }

    /**
     * Get dose description
     */
    public String getDoseDescription() {
        if (doseAmount == null || doseUnit == null) {
            return "No dose specified";
        }
        return doseAmount + " " + doseUnit.name();
    }

    /**
     * Get dose description with unit label
     */
    public String getDoseDescriptionWithLabel() {
        if (doseAmount == null || doseUnit == null) {
            return "No dose specified";
        }
        
        String unitLabel = doseUnit.name().toLowerCase();
        return doseAmount + " " + unitLabel + (doseAmount > 1 ? "s" : "");
    }

    /**
     * Check if this is a morning schedule (before 12:00)
     */
    public Boolean getIsMorningSchedule() {
        return time != null ? time.isBefore(LocalTime.NOON) : null;
    }

    /**
     * Check if this is an afternoon schedule (12:00 - 18:00)
     */
    public Boolean getIsAfternoonSchedule() {
        if (time == null) return null;
        return !time.isBefore(LocalTime.NOON) && !time.isAfter(LocalTime.of(18, 0));
    }

    /**
     * Check if this is an evening schedule (after 18:00)
     */
    public Boolean getIsEveningSchedule() {
        return time != null ? time.isAfter(LocalTime.of(18, 0)) : null;
    }

    /**
     * Check if this is a weekend schedule (Saturday or Sunday)
     */
    public Boolean getIsWeekendSchedule() {
        return dayOfWeek != null ? (dayOfWeek == 6 || dayOfWeek == 7) : null;
    }

    /**
     * Check if this is a weekday schedule (Monday to Friday)
     */
    public Boolean getIsWeekdaySchedule() {
        return dayOfWeek != null ? (dayOfWeek >= 1 && dayOfWeek <= 5) : null;
    }

    /**
     * Get time period description
     */
    public String getTimePeriod() {
        if (time == null) return "Unknown";
        
        if (getIsMorningSchedule()) return "Morning";
        if (getIsAfternoonSchedule()) return "Afternoon";
        if (getIsEveningSchedule()) return "Evening";
        return "Night";
    }

    /**
     * Get Vietnamese time period description
     */
    public String getTimePeriodVi() {
        if (time == null) return "Không xác định";
        
        if (getIsMorningSchedule()) return "Sáng";
        if (getIsAfternoonSchedule()) return "Chiều";
        if (getIsEveningSchedule()) return "Tối";
        return "Đêm";
    }

    /**
     * Get schedule summary for display
     */
    public String getScheduleSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (dayOfWeek != null) {
            summary.append(getDayOfWeekName());
        }
        
        if (time != null) {
            if (summary.length() > 0) summary.append(" at ");
            summary.append(getFormattedTime12Hour());
        }
        
        if (doseAmount != null && doseUnit != null) {
            if (summary.length() > 0) summary.append(" - ");
            summary.append(getDoseDescription());
        }
        
        return summary.toString();
    }

    /**
     * Get Vietnamese schedule summary
     */
    public String getScheduleSummaryVi() {
        StringBuilder summary = new StringBuilder();
        
        if (dayOfWeek != null) {
            summary.append(getDayOfWeekNameVi());
        }
        
        if (time != null) {
            if (summary.length() > 0) summary.append(" lúc ");
            summary.append(getFormattedTime());
        }
        
        if (doseAmount != null && doseUnit != null) {
            if (summary.length() > 0) summary.append(" - ");
            summary.append(getDoseDescription());
        }
        
        return summary.toString();
    }

    /**
     * Get status description
     */
    public String getStatusDescription() {
        return Boolean.TRUE.equals(isActive) ? "Active" : "Inactive";
    }

    /**
     * Get Vietnamese status description
     */
    public String getStatusDescriptionVi() {
        return Boolean.TRUE.equals(isActive) ? "Đang hoạt động" : "Không hoạt động";
    }

    /**
     * Get detailed description
     */
    public String getDetailedDescription() {
        StringBuilder desc = new StringBuilder();
        
        desc.append("Schedule: ").append(getScheduleSummary());
        
        if (notes != null && !notes.trim().isEmpty()) {
            desc.append(" | Notes: ").append(notes);
        }
        
        desc.append(" | Status: ").append(getStatusDescription());
        
        return desc.toString();
    }

    /**
     * Check if schedule is for today (based on current day of week)
     */
    public Boolean isForToday() {
        if (dayOfWeek == null) return null;
        
        java.time.DayOfWeek today = java.time.LocalDate.now().getDayOfWeek();
        int todayValue = today.getValue(); // Monday = 1, Sunday = 7
        
        return dayOfWeek.equals(todayValue);
    }

    /**
     * Check if schedule time has passed today
     */
    public Boolean hasPassedToday() {
        if (time == null || !Boolean.TRUE.equals(isForToday())) {
            return null;
        }
        
        return LocalTime.now().isAfter(time);
    }

    /**
     * Get next occurrence description
     */
    public String getNextOccurrenceDescription() {
        if (dayOfWeek == null || time == null) {
            return "Unknown";
        }
        
        if (Boolean.TRUE.equals(isForToday()) && Boolean.FALSE.equals(hasPassedToday())) {
            return "Today at " + getFormattedTime12Hour();
        }
        
        return "Next " + getDayOfWeekName() + " at " + getFormattedTime12Hour();
    }

    /**
     * Create a builder with computed fields
     */
    public static MedicationScheduleResponseBuilder builderWithComputedFields() {
        return new MedicationScheduleResponseBuilder() {
            @Override
            public MedicationScheduleResponse build() {
                MedicationScheduleResponse response = super.build();
                
                // Set computed fields
                response.dayOfWeekName = response.getDayOfWeekName();
                response.formattedTime = response.getFormattedTime();
                response.doseDescription = response.getDoseDescription();
                response.scheduleSummary = response.getScheduleSummary();
                response.isMorningSchedule = response.getIsMorningSchedule();
                response.isEveningSchedule = response.getIsEveningSchedule();
                response.isWeekendSchedule = response.getIsWeekendSchedule();
                response.isWeekdaySchedule = response.getIsWeekdaySchedule();
                
                return response;
            }
        };
    }
}
