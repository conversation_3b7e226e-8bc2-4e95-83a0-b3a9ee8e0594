package com.medication.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO for medication schedule statistics response
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MedicationScheduleStatsResponse {

    /**
     * Tổng số medication schedules
     */
    private long totalSchedules;
    
    /**
     * Số active schedules
     */
    private long activeSchedules;
    
    /**
     * Số inactive schedules
     */
    private long inactiveSchedules;
    
    /**
     * Tổng số doses đã được scheduled
     */
    private long totalScheduledDoses;
    
    /**
     * Số doses đã được taken (compliance tracking)
     */
    private long takenDoses;
    
    /**
     * Số doses bị missed
     */
    private long missedDoses;
    
    /**
     * Tỷ lệ tuân thủ (compliance rate) - percentage
     */
    private double complianceRate;

    /**
     * Tỷ lệ uống đúng giờ (on-time rate) - percentage
     */
    private double onTimeRate;

    /**
     * <PERSON>ố doses được uống đúng giờ
     */
    private long onTimeDoses;

    /**
     * Số doses được uống muộn
     */
    private long lateDoses;

    /**
     * Số schedules được tạo trong 7 ngày qua
     */
    private long schedulesCreatedLast7Days;
    
    /**
     * Số schedules được tạo trong 30 ngày qua
     */
    private long schedulesCreatedLast30Days;
    
    /**
     * Thống kê theo dose unit
     * Key: DoseUnit name, Value: count
     */
    private Map<String, Long> schedulesByDoseUnit;
    
    /**
     * Thống kê theo ngày trong tuần
     * Key: Day name (MONDAY, TUESDAY, etc.), Value: count
     */
    private Map<String, Long> schedulesByDayOfWeek;

    /**
     * Thống kê theo tuần
     * Key: Week (YYYY-WW format), Value: count
     */
    private Map<String, Long> schedulesByWeek;

    /**
     * Thống kê theo tháng
     * Key: Month (YYYY-MM format), Value: count
     */
    private Map<String, Long> schedulesByMonth;

    /**
     * Thời gian thống kê
     */
    private LocalDateTime timestamp;

    /**
     * Static factory method to create MedicationScheduleStatsResponse
     */
    public static MedicationScheduleStatsResponse of(
            long totalSchedules,
            long activeSchedules,
            long inactiveSchedules,
            long totalScheduledDoses,
            long takenDoses,
            long missedDoses,
            double complianceRate,
            double onTimeRate,
            long onTimeDoses,
            long lateDoses,
            long schedulesCreatedLast7Days,
            long schedulesCreatedLast30Days,
            Map<String, Long> schedulesByDoseUnit,
            Map<String, Long> schedulesByDayOfWeek,
            Map<String, Long> schedulesByWeek,
            Map<String, Long> schedulesByMonth) {
        
        return MedicationScheduleStatsResponse.builder()
            .totalSchedules(totalSchedules)
            .activeSchedules(activeSchedules)
            .inactiveSchedules(inactiveSchedules)
            .totalScheduledDoses(totalScheduledDoses)
            .takenDoses(takenDoses)
            .missedDoses(missedDoses)
            .complianceRate(complianceRate)
            .onTimeRate(onTimeRate)
            .onTimeDoses(onTimeDoses)
            .lateDoses(lateDoses)
            .schedulesCreatedLast7Days(schedulesCreatedLast7Days)
            .schedulesCreatedLast30Days(schedulesCreatedLast30Days)
            .schedulesByDoseUnit(schedulesByDoseUnit)
            .schedulesByDayOfWeek(schedulesByDayOfWeek)
            .schedulesByWeek(schedulesByWeek)
            .schedulesByMonth(schedulesByMonth)
            .timestamp(LocalDateTime.now())
            .build();
    }
}
