package com.medication.dto.response;

import com.medication.entity.Medicine;
import com.medication.enums.UserType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for medicine response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MedicineResponse {

    private Long id;
    private String name;
    private String dosage;
    private String unit;
    private Integer quantity;
    private BigDecimal price;
    private String manufacturer;
    private String activeIngredient;
    private LocalDate expiryDate;
    private String batchNumber;
    private Long medicineTypeId;
    private String medicineTypeName;
    private Long familyId;
    private Long userId;
    private UserType userType;
    private UUID anonymousUserId;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * Static factory method to create MedicineResponse from Medicine entity
     */
    public static MedicineResponse from(Medicine medicine) {
        return MedicineResponse.builder()
            .id(medicine.getId())
            .name(medicine.getName())
            .dosage(medicine.getDosage())
            .unit(medicine.getUnit())
            .quantity(medicine.getQuantity())
            .price(medicine.getPrice())
            .manufacturer(medicine.getManufacturer())
            .activeIngredient(medicine.getActiveIngredient())
            .expiryDate(medicine.getExpiryDate())
            .batchNumber(medicine.getBatchNumber())
            .medicineTypeId(medicine.getMedicineType() != null ? medicine.getMedicineType().getId() : null)
            .medicineTypeName(medicine.getMedicineType() != null ? medicine.getMedicineType().getTypeName() : null)
            .familyId(medicine.getFamily() != null ? medicine.getFamily().getId() : null)
            .userId(medicine.getUser() != null ? medicine.getUser().getId() : null)
            .userType(medicine.getUserType())
            .anonymousUserId(medicine.getAnonymousUserId())
            .isActive(medicine.getIsActive())
            .createdAt(medicine.getCreatedAt())
            .updatedAt(medicine.getUpdatedAt())
            .build();
    }
} 