package com.medication.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for medicine statistics response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MedicineStatsResponse {

    private long totalMedicines;
    private long activeMedicines;
    private long lowQuantityMedicines;
    private long expiringMedicines;

    /**
     * Static factory method to create MedicineStatsResponse
     */
    public static MedicineStatsResponse of(long totalMedicines, long activeMedicines, 
                                         long lowQuantityMedicines, long expiringMedicines) {
        return MedicineStatsResponse.builder()
            .totalMedicines(totalMedicines)
            .activeMedicines(activeMedicines)
            .lowQuantityMedicines(lowQuantityMedicines)
            .expiringMedicines(expiringMedicines)
            .build();
    }
} 