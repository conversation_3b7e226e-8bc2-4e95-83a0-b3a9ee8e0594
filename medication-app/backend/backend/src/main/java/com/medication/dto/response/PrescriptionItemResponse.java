package com.medication.dto.response;

import com.medication.entity.PrescriptionItem;
import com.medication.enums.DoseUnit;
import com.medication.enums.Route;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for prescription item response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PrescriptionItemResponse {

    private Long id;
    private Long prescriptionId;
    private Long medicineId;
    private String medicineName;
    private String medicineActiveIngredient;
    private String medicineManufacturer;
    private Double doseAmount;
    private DoseUnit doseUnit;
    private Integer quantity;
    private String instructions;
    private Route route;
    private Integer durationDays;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Business logic fields
    private Integer expectedTotalDoses;
    private Integer actualTotalDoses;
    private Double complianceRate;
    private Boolean isCompleted;
    private Boolean hasActiveSchedules;
    private Integer remainingDays;
    private Double progressPercentage;
    private Boolean isOverdue;
    private String durationDescription;

    /**
     * Static factory method to convert from entity to response
     */
    public static PrescriptionItemResponse from(PrescriptionItem prescriptionItem) {
        return PrescriptionItemResponse.builder()
            .id(prescriptionItem.getId())
            .prescriptionId(prescriptionItem.getPrescription() != null ? prescriptionItem.getPrescription().getId() : null)
            .medicineId(prescriptionItem.getMedicine() != null ? prescriptionItem.getMedicine().getId() : null)
            .medicineName(prescriptionItem.getMedicine() != null ? prescriptionItem.getMedicine().getName() : null)
            .medicineActiveIngredient(prescriptionItem.getMedicine() != null ? prescriptionItem.getMedicine().getActiveIngredient() : null)
            .medicineManufacturer(prescriptionItem.getMedicine() != null ? prescriptionItem.getMedicine().getManufacturer() : null)
            .doseAmount(prescriptionItem.getDoseAmount())
            .doseUnit(prescriptionItem.getDoseUnit())
            .quantity(prescriptionItem.getQuantity())
            .instructions(prescriptionItem.getInstructions())
            .route(prescriptionItem.getRoute())
            .durationDays(prescriptionItem.getDurationDays())
            .createdAt(prescriptionItem.getCreatedAt())
            .updatedAt(prescriptionItem.getUpdatedAt())
            .expectedTotalDoses(prescriptionItem.getExpectedTotalDoses())
            .actualTotalDoses(prescriptionItem.getActualTotalDoses())
            .build();
    }
    
    /**
     * Static factory method with business service calculations
     */
    public static PrescriptionItemResponse fromWithBusinessLogic(PrescriptionItem prescriptionItem, 
            com.medication.service.domain.prescription.PrescriptionItemBusinessService businessService) {
        PrescriptionItemResponse response = from(prescriptionItem);
        
        // Add business logic calculations
        response.setComplianceRate(businessService.getComplianceRate(prescriptionItem));
        response.setIsCompleted(businessService.isCompleted(prescriptionItem));
        response.setHasActiveSchedules(businessService.hasActiveSchedules(prescriptionItem));
        response.setRemainingDays(businessService.getRemainingDays(prescriptionItem));
        response.setProgressPercentage(businessService.getProgressPercentage(prescriptionItem));
        response.setIsOverdue(businessService.isOverdue(prescriptionItem));
        response.setDurationDescription(businessService.getDurationDescription(prescriptionItem));
        
        return response;
    }
}
