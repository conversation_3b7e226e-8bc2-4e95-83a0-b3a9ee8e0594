package com.medication.dto.response;

import com.medication.entity.Prescription;
import com.medication.enums.PrescriptionStatus;
import com.medication.enums.UserType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for prescription response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PrescriptionResponse {

    private Long id;
    private String doctorName;
    private LocalDate prescriptionDate;
    private LocalDate startDate;
    private LocalDate endDate;
    private String diagnosis;
    private String notes;
    private Long patientId;
    private String patientName;
    private Long prescribedById;
    private String prescribedByName;
    private UserType userType;
    private UUID anonymousUserId;
    private Long familyId;
    private String familyName;
    private PrescriptionStatus status;
    private BigDecimal complianceRate;
    private Integer totalDoses;
    private Integer takenDoses;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * Static factory method to convert from entity to response
     */
    public static PrescriptionResponse from(Prescription prescription) {
        return PrescriptionResponse.builder()
            .id(prescription.getId())
            .doctorName(prescription.getDoctorName())
            .prescriptionDate(prescription.getPrescriptionDate())
            .startDate(prescription.getStartDate())
            .endDate(prescription.getEndDate())
            .diagnosis(prescription.getDiagnosis())
            .notes(prescription.getNotes())
            .patientId(prescription.getPatient() != null ? prescription.getPatient().getId() : null)
            .patientName(prescription.getPatient() != null ? prescription.getPatient().getFullName() : null)
            .prescribedById(prescription.getPrescribedBy() != null ? prescription.getPrescribedBy().getId() : null)
            .prescribedByName(prescription.getPrescribedBy() != null ? prescription.getPrescribedBy().getFullName() : null)
            .userType(prescription.getUserType())
            .anonymousUserId(prescription.getAnonymousUserId())
            .familyId(prescription.getFamily() != null ? prescription.getFamily().getId() : null)
            .familyName(prescription.getFamily() != null ? prescription.getFamily().getName() : null)
            .status(prescription.getStatus())
            .complianceRate(prescription.getComplianceRate())
            .totalDoses(prescription.getTotalDoses())
            .takenDoses(prescription.getTakenDoses())
            .isActive(prescription.getIsActive())
            .createdAt(prescription.getCreatedAt())
            .updatedAt(prescription.getUpdatedAt())
            .build();
    }
} 