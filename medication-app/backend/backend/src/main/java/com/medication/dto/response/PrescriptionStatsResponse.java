package com.medication.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for prescription statistics response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PrescriptionStatsResponse {

    private long totalPrescriptions;
    private long activePrescriptions;
    private long completedPrescriptions;
    private long expiredPrescriptions;
    private long lowCompliancePrescriptions;
    private double averageComplianceRate;
    private long prescriptionsCreatedLast30Days;
    private long prescriptionsExpiringNext7Days;

    /**
     * Static factory method to create PrescriptionStatsResponse
     */
    public static PrescriptionStatsResponse of(long totalPrescriptions, long activePrescriptions, 
                                             long completedPrescriptions, long expiredPrescriptions,
                                             long lowCompliancePrescriptions, double averageComplianceRate,
                                             long prescriptionsCreatedLast30Days, long prescriptionsExpiringNext7Days) {
        return PrescriptionStatsResponse.builder()
            .totalPrescriptions(totalPrescriptions)
            .activePrescriptions(activePrescriptions)
            .completedPrescriptions(completedPrescriptions)
            .expiredPrescriptions(expiredPrescriptions)
            .lowCompliancePrescriptions(lowCompliancePrescriptions)
            .averageComplianceRate(averageComplianceRate)
            .prescriptionsCreatedLast30Days(prescriptionsCreatedLast30Days)
            .prescriptionsExpiringNext7Days(prescriptionsExpiringNext7Days)
            .build();
    }
} 