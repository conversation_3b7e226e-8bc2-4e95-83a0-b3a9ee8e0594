package com.medication.entity;

import com.medication.enums.AnonymousSessionStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity quản lý anonymous user sessions
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-12-01
 */
@Entity
@Table(name = "anonymous_sessions", indexes = {
    @Index(name = "idx_anonymous_sessions_user_id", columnList = "anonymous_user_id"),
    @Index(name = "idx_anonymous_sessions_last_activity", columnList = "last_activity_at"),
    @Index(name = "idx_anonymous_sessions_migrated", columnList = "migrated_to_user_id"),
    @Index(name = "idx_anonymous_sessions_status", columnList = "status"),
    @Index(name = "idx_anonymous_sessions_device", columnList = "device_id")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class AnonymousSession {
    
    /**
     * Primary key - Long auto increment
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;
    
    /**
     * Anonymous user ID - UUID để client sử dụng
     * Unique constraint để đảm bảo không trùng lặp
     */
    @Column(name = "anonymous_user_id", nullable = false, unique = true, columnDefinition = "UUID")
    private UUID anonymousUserId;
    
    /**
     * Device ID - để track device sử dụng
     */
    @Column(name = "device_id", length = 100)
    private String deviceId;
    
    /**
     * App version - để track version app
     */
    @Column(name = "app_version", length = 20)
    private String appVersion;
    
    /**
     * Thời gian tạo session
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * Thời gian hoạt động cuối cùng
     * Được update mỗi khi user thực hiện action
     */
    @UpdateTimestamp
    @Column(name = "last_activity_at", nullable = false)
    private LocalDateTime lastActivityAt;
    
    /**
     * Trạng thái đồng bộ data
     * true: data đã được sync với server
     * false: data chỉ lưu local
     */
    @Column(name = "data_synced", nullable = false)
    private Boolean dataSynced = false;
    
    /**
     * User ID mà session này đã migrate sang
     * null: chưa migrate
     * not null: đã migrate sang user này
     */
    @Column(name = "migrated_to_user_id")
    private Long migratedToUserId;
    
    /**
     * Thời gian migrate
     */
    @Column(name = "migrated_at")
    private LocalDateTime migratedAt;
    
    /**
     * Trạng thái session
     */
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, columnDefinition = "anonymous_session_status_enum")
    private AnonymousSessionStatus status = AnonymousSessionStatus.ACTIVE;
    

    
    // Business logic đã được chuyển ra AnonymousSessionBusinessService
    
    @PrePersist
    protected void onCreate() {
        // Chỉ set default cho fields không có default value
        if (status == null) {
            status = AnonymousSessionStatus.ACTIVE;
        }
        if (dataSynced == null) {
            dataSynced = false;
        }
        // @CreationTimestamp và @UpdateTimestamp tự động handle
    }
    
    // Business logic đã được chuyển ra AnonymousSessionBusinessService
    // AnonymousSessionStatus enum đã được tách ra thành file riêng: com.medication.enums.AnonymousSessionStatus
} 