package com.medication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;
import com.medication.enums.AuditAction;
import com.medication.enums.AuditSeverity;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

@Entity
@Table(name = "audit_logs", indexes = {
    @Index(name = "idx_audit_user", columnList = "user_id"),
    @Index(name = "idx_audit_entity_type", columnList = "entity_type"),
    @Index(name = "idx_audit_entity_id", columnList = "entity_id"),
    @Index(name = "idx_audit_created", columnList = "created_at"),
    @Index(name = "idx_audit_severity", columnList = "severity"),
    @Index(name = "idx_audit_action", columnList = "action"),
    @Index(name = "idx_audit_anonymous_user_id", columnList = "anonymous_user_id")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class AuditLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    @EqualsAndHashCode.Include
    private User user;
    
    @Column(name = "anonymous_user_id", columnDefinition = "UUID")
    private UUID anonymousUserId;

    @NotNull(message = "Action is required")
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "action", nullable = false, length = 100, columnDefinition = "audit_action_enum")
    @EqualsAndHashCode.Include
    private AuditAction action;

    @NotBlank(message = "Entity type is required")
    @Size(max = 100, message = "Entity type must not exceed 100 characters")
    @Column(name = "entity_type", nullable = false, length = 100)
    private String entityType;

    @Column(name = "entity_id")
    private Long entityId;

    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Column(name = "description", length = 1000)
    private String description;

    @Column(name = "old_values", columnDefinition = "TEXT")
    private String oldValues;

    @Column(name = "new_values", columnDefinition = "TEXT")
    private String newValues;

    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "severity", nullable = false, length = 50, columnDefinition = "audit_severity_enum")
    private AuditSeverity severity = AuditSeverity.INFO;

    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    @Column(name = "user_agent", length = 500)
    private String userAgent;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
        // Chỉ set default cho fields không có default value
        if (severity == null) {
            severity = AuditSeverity.INFO;
        }
        if (action != null && severity == AuditSeverity.INFO) {
            // Auto-set severity based on action
            severity = action.getDefaultSeverity();
        }
        // @CreationTimestamp tự động handle
    }
    
    // Business logic has been moved to AuditLogBusinessService
} 