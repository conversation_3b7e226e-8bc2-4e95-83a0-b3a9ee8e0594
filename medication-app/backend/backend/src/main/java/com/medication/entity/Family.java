package com.medication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "families", indexes = {
    @Index(name = "idx_family_name", columnList = "name"),
    @Index(name = "idx_family_active", columnList = "is_active")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class Family {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;

    @NotBlank(message = "Family name is required")
    @Size(max = 100, message = "Family name must not exceed 100 characters")
    @Column(name = "name", nullable = false, length = 100)
    @EqualsAndHashCode.Include
    private String name;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    // ✅ Cascade PERSIST + MERGE cho phép tạo đồng thời Family + Members
    // ⚠️ Không dùng REMOVE để tránh xoá nhầm dữ liệu
    // 🟢 orphanRemoval = true nếu muốn remove member khi bị xoá khỏi danh sách
    @OneToMany(mappedBy = "family", cascade = {CascadeType.PERSIST, CascadeType.MERGE}, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<FamilyMember> members = new ArrayList<>();

    // ❌ Không cascade – tránh xoá nhầm Medicine khi xoá Family
    @OneToMany(mappedBy = "family", fetch = FetchType.LAZY)
    private List<Medicine> medicines = new ArrayList<>();

    // ❌ Không cascade – đơn thuốc có thể lưu lại độc lập
    @OneToMany(mappedBy = "family", fetch = FetchType.LAZY)
    private List<Prescription> prescriptions = new ArrayList<>();

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        // Chỉ set default cho fields không có default value
        if (isActive == null) {
            isActive = true;
        }
        // @CreationTimestamp và @UpdateTimestamp tự động handle
    }

    @PreUpdate
    protected void onUpdate() {
        // @UpdateTimestamp tự động handle
    }
    
    // Business logic has been moved to FamilyBusinessService
} 