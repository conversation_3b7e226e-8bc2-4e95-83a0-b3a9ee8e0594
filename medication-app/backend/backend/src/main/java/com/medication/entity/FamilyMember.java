package com.medication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import com.medication.enums.FamilyRole;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

@Entity
@Table(name = "family_members", indexes = {
    @Index(name = "idx_family_member_user", columnList = "user_id"),
    @Index(name = "idx_family_member_family", columnList = "family_id"),
    @Index(name = "idx_family_member_role", columnList = "role"),
    @Index(name = "idx_family_member_active", columnList = "is_active")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class FamilyMember {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;

    @NotNull(message = "User is required")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", nullable = false)
    @EqualsAndHashCode.Include
    private User user;

    @NotNull(message = "Family is required")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "family_id", nullable = false)
    @EqualsAndHashCode.Include
    private Family family;

    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false, length = 50, columnDefinition = "family_role_enum")
    private FamilyRole role = FamilyRole.MEMBER;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        // Chỉ set default cho fields không có default value
        if (isActive == null) {
            isActive = true;
        }
        if (role == null) {
            role = FamilyRole.MEMBER;
        }
        // @CreationTimestamp và @UpdateTimestamp tự động handle
    }

    @PreUpdate
    protected void onUpdate() {
        // @UpdateTimestamp tự động handle
    }
    
    // Business logic has been moved to FamilyMemberBusinessService

    // FamilyRole enum đã được tách ra thành file riêng: com.medication.enums.FamilyRole
} 