package com.medication.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import com.medication.enums.DoseUnit;
import com.medication.enums.DoseStatus;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

@Entity
@Table(name = "medication_doses", indexes = {
    @Index(name = "idx_medication_doses_schedule_id", columnList = "medication_schedule_id"),
    @Index(name = "idx_medication_doses_scheduled_date", columnList = "scheduled_date"),
    @Index(name = "idx_medication_doses_status", columnList = "status"),
    @Index(name = "idx_medication_doses_schedule_date", columnList = "medication_schedule_id, scheduled_date"),
    @Index(name = "idx_medication_doses_status_date", columnList = "status, scheduled_date")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MedicationDose {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @JsonBackReference
    @NotNull(message = "Medication schedule is required")
    @ManyToOne(fetch = FetchType.LAZY, optional = false, cascade = {})
    @JoinColumn(name = "medication_schedule_id", nullable = false)
    private MedicationSchedule medicationSchedule;
    
    @NotNull(message = "Scheduled date is required")
    @Column(name = "scheduled_date", nullable = false)
    private LocalDateTime scheduledDate;
    
    @Column(name = "taken_date")
    private LocalDateTime takenDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 50)
    private DoseStatus status = DoseStatus.PENDING;
    
    // Expected dose (mapped from MedicationSchedule for adherence calculation)
    @Column(name = "expected_dose_amount")
    private Double expectedDoseAmount;
    
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "expected_dose_unit", columnDefinition = "dose_unit_enum")
    private DoseUnit expectedDoseUnit;
    
    // Actual dose (required for TAKEN/PARTIAL status)
    @DecimalMin(value = "0.0", inclusive = false, message = "Actual dose must be positive")
    @Column(name = "actual_dose_amount")
    private Double actualDoseAmount;
    
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "actual_dose_unit", columnDefinition = "dose_unit_enum")
    private DoseUnit actualDoseUnit;
    
    @Size(max = 500, message = "Notes must not exceed 500 characters")
    @Column(name = "notes", length = 500)
    private String notes;
    
    @Size(max = 100, message = "Reason must not exceed 100 characters")
    @Column(name = "missed_reason", length = 100)
    private String missedReason;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        if (status == null) status = DoseStatus.PENDING;
        // Note: Expected dose mapping should be handled in service layer
        // to avoid lazy loading issues
    }
    
    // Validation methods - using ValidationUtils for business logic
    @AssertTrue(message = "Actual dose is required when status is TAKEN or PARTIAL")
    public boolean isActualDoseValid() {
        return com.medication.util.ValidationUtils.isActualDoseValid(status, actualDoseAmount, actualDoseUnit);
    }
    
    @AssertTrue(message = "Expected dose unit must be compatible with actual dose unit for adherence calculation")
    public boolean isDoseUnitCompatible() {
        return com.medication.util.ValidationUtils.isDoseUnitCompatible(expectedDoseUnit, actualDoseUnit);
    }
    
    // DoseStatus enum đã được tách ra thành file riêng: com.medication.enums.DoseStatus
    
    // Business logic has been moved to MedicationDoseBusinessService
} 