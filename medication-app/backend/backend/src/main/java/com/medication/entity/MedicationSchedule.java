package com.medication.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import org.springframework.lang.Nullable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import com.medication.enums.DoseUnit;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

@Entity
@Table(name = "medication_schedules", indexes = {
    @Index(name = "idx_medication_schedules_prescription_item_id", columnList = "prescription_item_id"),
    @Index(name = "idx_medication_schedules_day_of_week", columnList = "day_of_week"),
    @Index(name = "idx_medication_schedules_time", columnList = "time"),
    @Index(name = "idx_medication_schedules_is_active", columnList = "is_active"),
    @Index(name = "idx_medication_schedules_composite", columnList = "prescription_item_id, day_of_week, time"),
    @Index(name = "idx_medication_schedules_notification", columnList = "day_of_week, time, is_active"),
    @Index(name = "idx_schedule_item_active", columnList = "prescription_item_id, is_active")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MedicationSchedule {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @JsonBackReference
    @NotNull(message = "Prescription item is required")
    @ManyToOne(fetch = FetchType.LAZY, optional = false, cascade = {})
    @JoinColumn(name = "prescription_item_id", nullable = false)
    private PrescriptionItem prescriptionItem;
    
    @NotNull(message = "Day of week is required")
    @Min(1) @Max(7)
    @Column(name = "day_of_week", nullable = false)
    private Integer dayOfWeek; // 1=Monday, 7=Sunday
    
    @NotNull(message = "Time is required")
    @Column(name = "time", nullable = false)
    private LocalTime time;
    
    @NotNull(message = "Dose amount is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Dose amount must be positive")
    @Column(name = "dose_amount", nullable = false)
    private Double doseAmount;
    
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "dose_unit", nullable = false, columnDefinition = "dose_unit_enum")
    private DoseUnit doseUnit;
    
    @Nullable
    @Size(max = 500, message = "Notes must not exceed 500 characters")
    @Column(name = "notes", length = 500)
    private String notes;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Relationships for tracking compliance
    @OneToMany(mappedBy = "medicationSchedule", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<MedicationDose> medicationDoses = new ArrayList<>();
    
    // Relationship for flexible scheduling patterns
    @OneToMany(mappedBy = "medicationSchedule", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<SchedulePattern> schedulePatterns = new ArrayList<>();
    
    @PrePersist
    protected void onCreate() {
        if (isActive == null) isActive = true;
    }
    
    // Business logic đã được chuyển ra MedicationScheduleBusinessService
    
    /**
     * Get time formatted
     */
    public String getTimeFormatted() {
        return time != null ? time.toString() : "";
    }
} 