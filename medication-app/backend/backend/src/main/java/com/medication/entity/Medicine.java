package com.medication.entity;

import com.medication.enums.UserType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Builder;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcType;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "medicines", indexes = {
    @Index(name = "idx_medicine_name", columnList = "name"),
    @Index(name = "idx_medicine_user", columnList = "user_id"),
    @Index(name = "idx_medicine_family", columnList = "family_id"),
    @Index(name = "idx_medicine_type", columnList = "medicine_type_id"),
    @Index(name = "idx_medicine_active", columnList = "is_active"),
    @Index(name = "idx_medicine_expiry", columnList = "expiry_date"),
    @Index(name = "idx_medicine_user_type", columnList = "user_type"),
    @Index(name = "idx_medicine_anonymous_id", columnList = "anonymous_user_id")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class Medicine {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;
    
    @NotBlank(message = "Medicine name is required")
    @Size(max = 200, message = "Medicine name must not exceed 200 characters")
    @Column(name = "name", nullable = false, length = 200)
    @EqualsAndHashCode.Include
    private String name;
    
    @Size(max = 100, message = "Dosage must not exceed 100 characters")
    @Column(name = "dosage", length = 100)
    private String dosage;
    
    @Size(max = 50, message = "Unit must not exceed 50 characters")
    @Column(name = "unit", length = 50)
    private String unit;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unit_id")
    private MedicineUnit unitEntity;
    
    @NotNull(message = "Quantity is required")
    @Column(name = "quantity", nullable = false)
    private Integer quantity = 0;
    
    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price;
    
    @Size(max = 200, message = "Manufacturer must not exceed 200 characters")
    @Column(name = "manufacturer", length = 200)
    private String manufacturer;
    
    @Size(max = 500, message = "Active ingredient must not exceed 500 characters")
    @Column(name = "active_ingredient", length = 500)
    private String activeIngredient;
    
    @Column(name = "expiry_date")
    private LocalDate expiryDate;
    
    @Column(name = "batch_number", length = 100)
    private String batchNumber;
    
    @NotNull(message = "Medicine type is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "medicine_type_id", nullable = false)
    private MedicineType medicineType;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "family_id", nullable = true)
    private Family family;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false, columnDefinition = "user_type_enum")
    private UserType userType = UserType.REGISTERED;
    
    @Column(name = "anonymous_user_id", columnDefinition = "UUID")
    private UUID anonymousUserId;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @OneToMany(mappedBy = "medicine", fetch = FetchType.LAZY)
    private List<PrescriptionItem> prescriptionItems = new ArrayList<>();
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        // Chỉ set default cho fields không có default value
        if (quantity == null) {
            quantity = 0;
        }
        if (userType == null) {
            userType = UserType.REGISTERED;
        }
        if (isActive == null) {
            isActive = true;
        }
        // @CreationTimestamp và @UpdateTimestamp tự động handle
    }
    
    // Business logic đã được chuyển ra MedicineBusinessService
    
    @PreUpdate
    protected void onUpdate() {
        // @UpdateTimestamp tự động handle
    }
} 