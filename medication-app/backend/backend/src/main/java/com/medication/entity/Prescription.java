package com.medication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.Digits;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcType;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.validation.constraints.AssertTrue;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import com.medication.enums.UserType;
import com.medication.enums.PrescriptionStatus;
import java.util.UUID;

@Entity
@Table(name = "prescriptions", indexes = {
    @Index(name = "idx_prescription_patient", columnList = "patient_id"),
    @Index(name = "idx_prescription_prescribed_by", columnList = "prescribed_by"),
    @Index(name = "idx_prescription_family", columnList = "family_id"),
    @Index(name = "idx_prescription_status", columnList = "status"),
    @Index(name = "idx_prescription_active", columnList = "is_active"),
    @Index(name = "idx_prescription_user_type", columnList = "user_type"),
    @Index(name = "idx_prescription_anonymous_id", columnList = "anonymous_user_id")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@SQLRestriction("deleted_at IS NULL")  // Soft delete filter
public class Prescription {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Doctor name is required")
    @Size(max = 100, message = "Doctor name must not exceed 100 characters")
    @Column(name = "doctor_name", nullable = false, length = 100)
    private String doctorName;
    
    @NotNull(message = "Prescription date is required")
    @Column(name = "prescription_date", nullable = false)
    private LocalDate prescriptionDate;
    
    @NotNull(message = "Start date is required")
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;
    
    @NotNull(message = "End date is required")
    @Column(name = "end_date", nullable = false)
    private LocalDate endDate;
    
    @NotBlank(message = "Diagnosis is required")
    @Size(max = 500, message = "Diagnosis must not exceed 500 characters")
    @Column(name = "diagnosis", nullable = false, length = 500)
    private String diagnosis;
    
    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    @Column(name = "notes", length = 1000)
    private String notes;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id")
    private User patient;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "prescribed_by")
    private User prescribedBy;
    
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false, columnDefinition = "user_type_enum")
    private UserType userType = UserType.REGISTERED;
    
    @Column(name = "anonymous_user_id", columnDefinition = "UUID")
    private UUID anonymousUserId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "family_id", nullable = true)
    private Family family;
    
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 50, columnDefinition = "prescription_status_enum")
    private PrescriptionStatus status = PrescriptionStatus.ACTIVE;
    
    @DecimalMin(value = "0.0", inclusive = true, message = "Compliance rate must be at least 0")
    @DecimalMax(value = "100.0", inclusive = true, message = "Compliance rate cannot exceed 100")
    @Digits(integer = 3, fraction = 2, message = "Compliance rate must have max 3 digits and 2 decimals")
    @Column(name = "compliance_rate", precision = 5, scale = 2)
    private BigDecimal complianceRate = BigDecimal.ZERO;
    
    @Column(name = "total_doses")
    private Integer totalDoses = 0;
    
    @Column(name = "taken_doses")
    private Integer takenDoses = 0;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    // Soft delete fields
    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;
    
    @Size(max = 100, message = "Deleted by must not exceed 100 characters")
    @Column(name = "deleted_by", length = 100)
    private String deletedBy;
    
    @JsonManagedReference
    @OneToMany(mappedBy = "prescription", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PrescriptionItem> prescriptionItems = new ArrayList<>();
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        // Chỉ set default cho fields không có default value
        if (prescriptionItems == null) {
            prescriptionItems = new ArrayList<>();
        }
        if (userType == null) {
            userType = UserType.REGISTERED;
        }
    }
    
    // Business logic đã được chuyển ra PrescriptionBusinessService
    
    @PreUpdate
    protected void onUpdate() {
        // @UpdateTimestamp tự động handle updatedAt
    }
    
    // Custom validation methods
    @AssertTrue(message = "Start date must be before or equal to end date")
    public boolean isDateRangeValid() {
        return com.medication.util.ValidationUtils.isDateRangeValid(startDate, endDate);
    }
    
    @AssertTrue(message = "Taken doses cannot exceed total doses")
    public boolean isDosesValid() {
        return com.medication.util.ValidationUtils.isDosesValid(takenDoses, totalDoses);
    }
    
    @AssertTrue(message = "Prescription date must be before or equal to start date")
    public boolean isPrescriptionDateValid() {
        return com.medication.util.ValidationUtils.isPrescriptionDateValid(prescriptionDate, startDate);
    }
    
    // Business logic đã được chuyển ra PrescriptionBusinessService
    // PrescriptionStatus enum đã được tách ra thành file riêng: com.medication.enums.PrescriptionStatus
} 