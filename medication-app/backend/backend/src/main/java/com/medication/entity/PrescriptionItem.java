package com.medication.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import com.medication.enums.Route;
import com.medication.enums.DoseUnit;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

@Entity
@Table(name = "prescription_items")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrescriptionItem {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @JsonBackReference
    @NotNull(message = "Prescription is required")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "prescription_id", nullable = false)
    private Prescription prescription;
    
    @NotNull(message = "Medicine is required")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "medicine_id", nullable = false)
    private Medicine medicine;
    
    @NotNull(message = "Dose amount is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Dose amount must be positive")
    @Column(name = "dose_amount", nullable = false)
    private Double doseAmount;
    
    @NotNull(message = "Dose unit is required")
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "dose_unit", nullable = false, columnDefinition = "dose_unit_enum")
    private DoseUnit doseUnit;
    
    @Min(value = 1, message = "Quantity must be at least 1")
    @Column(name = "quantity")
    private Integer quantity = 1;
    
    @Size(max = 2000, message = "Instructions must not exceed 2000 characters")
    @Column(name = "instructions", columnDefinition = "TEXT")
    private String instructions;
    
    @NotNull(message = "Route is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "route", nullable = false, length = 50)
    private Route route;
    
    @NotNull(message = "Duration days is required")
    @Min(value = 1, message = "Duration days must be at least 1")
    @Column(name = "duration_days", nullable = false)
    private Integer durationDays;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Relationships
    @OneToMany(mappedBy = "prescriptionItem", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<MedicationSchedule> medicationSchedules = new ArrayList<>();
    
    @PrePersist
    protected void onCreate() {
        if (quantity == null) quantity = 1;
    }
    
    // Business logic has been moved to PrescriptionItemBusinessService
    
    // Validation methods - using ValidationUtils for business logic
    @AssertTrue(message = "Dose unit must be compatible with route")
    public boolean isDoseUnitCompatibleWithRoute() {
        return com.medication.util.ValidationUtils.isDoseUnitCompatibleWithRoute(doseUnit, route);
    }
    
    @AssertTrue(message = "Weight-based doses require patient weight for calculation")
    public boolean isWeightBasedDoseValid() {
        return com.medication.util.ValidationUtils.isWeightBasedDoseValid(doseUnit);
    }
    
    /**
     * Get expected total doses
     */
    public Integer getExpectedTotalDoses() {
        return com.medication.util.PrescriptionUtils.getExpectedTotalDoses(this);
    }
    
    /**
     * Get actual total doses
     */
    public Integer getActualTotalDoses() {
        return com.medication.util.PrescriptionUtils.getActualTotalDoses(this);
    }
} 