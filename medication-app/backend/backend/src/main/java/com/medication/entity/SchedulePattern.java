package com.medication.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import com.medication.enums.RepeatType;
import com.medication.enums.DayOfWeekCode;

@Entity
@Table(name = "schedule_patterns")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SchedulePattern {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @JsonBackReference
    @NotNull(message = "Medication schedule is required")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "medication_schedule_id", nullable = false)
    private MedicationSchedule medicationSchedule;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "repeat_type", nullable = false, length = 50)
    private RepeatType repeatType;
    
    @ElementCollection(fetch = FetchType.EAGER)
    @Enumerated(EnumType.STRING)
    @CollectionTable(
            name = "schedule_pattern_weekdays",
            joinColumns = @JoinColumn(name = "pattern_id"),
            indexes = @Index(name = "idx_pattern_weekdays", columnList = "pattern_id, weekday")
    )
    @Column(name = "weekday", length = 10)
    private List<DayOfWeekCode> weekDays = new ArrayList<>();
    
    @Min(value = 1, message = "Interval days must be at least 1")
    @Column(name = "interval_days")
    private Integer intervalDays;
    
    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "schedule_pattern_monthdays", 
        joinColumns = @JoinColumn(name = "pattern_id"),
        indexes = @Index(name = "idx_pattern_monthdays", columnList = "pattern_id, day_of_month")
    )
    @Column(name = "day_of_month")
    private List<Integer> daysOfMonth = new ArrayList<>();
    
    @NotNull(message = "Start date is required")
    @Column(name = "start_date", nullable = false)
    private LocalDateTime startDate;
    
    @Column(name = "end_date")
    private LocalDateTime endDate;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @Size(max = 500, message = "Notes must not exceed 500 characters")
    @Column(name = "notes", length = 500)
    private String notes;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        isActive = Boolean.TRUE.equals(isActive);
    }
    
    // RepeatType enum đã được tách ra thành file riêng: com.medication.enums.RepeatType
    // DayOfWeekCode enum đã được tách ra thành file riêng: com.medication.enums.DayOfWeekCode
    
    // Validation methods - using ValidationUtils for business logic
    @AssertTrue(message = "Week days are required for WEEKLY, CUSTOM_DAYS, WEEKDAYS, WEEKENDS")
    public boolean isWeekDaysValid() {
        return com.medication.util.ValidationUtils.isWeekDaysValid(repeatType, weekDays);
    }

    @AssertTrue(message = "Interval days are required for EVERY_N_DAYS")
    public boolean isIntervalDaysValid() {
        return com.medication.util.ValidationUtils.isIntervalDaysValid(repeatType, intervalDays);
    }

    @AssertTrue(message = "Days of month are required for MONTHLY repeat type")
    public boolean isDaysOfMonthValid() {
        return com.medication.util.ValidationUtils.isDaysOfMonthValid(repeatType, daysOfMonth);
    }

    @AssertTrue(message = "End date must be after start date")
    public boolean isDateRangeValid() {
        return com.medication.util.ValidationUtils.isDateRangeValid(startDate, endDate);
    }

    @AssertTrue(message = "Start date is required for ONE_TIME repeat type")
    public boolean isOneTimeValid() {
        return com.medication.util.ValidationUtils.isOneTimeValid(repeatType, startDate);
    }
    
    // Business logic has been moved to SchedulePatternBusinessService
    
    /**
     * Check if pattern is active today
     */
    public boolean isActiveToday() {
        return com.medication.util.ScheduleUtils.isPatternActiveToday(this);
    }
    
    /**
     * Get pattern description
     */
    public String getPatternDescription() {
        return com.medication.util.ScheduleUtils.getPatternDescription(this);
    }
} 