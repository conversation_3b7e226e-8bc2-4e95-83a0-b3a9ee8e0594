package com.medication.entity;

import com.medication.enums.UserType;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcType;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import com.medication.enums.UserRole;

@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_user_email", columnList = "email"),
    @Index(name = "idx_user_role", columnList = "role"),
    @Index(name = "idx_user_active", columnList = "is_active"),
    @Index(name = "idx_user_created", columnList = "created_at"),
    @Index(name = "idx_user_type", columnList = "user_type"),
    @Index(name = "idx_user_anonymous_id", columnList = "anonymous_user_id")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class User {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;
    
    @Email(message = "Invalid email format")
    @Column(name = "email", unique = true, length = 255)
    @EqualsAndHashCode.Include
    private String email;
    
    @Size(min = 6, max = 100, message = "Password must be between 6 and 100 characters")
    @Column(name = "password", length = 100)
    private String password;
    
    @Size(max = 100, message = "Full name must not exceed 100 characters")
    @Column(name = "full_name", length = 100)
    private String fullName;
    
    @Size(max = 20, message = "Phone number must not exceed 20 characters")
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;
    
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "role", length = 50, columnDefinition = "user_role_enum")
    private UserRole role = UserRole.USER;
    
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false, columnDefinition = "user_type_enum")
    private UserType userType = UserType.REGISTERED;
    
    @Column(name = "anonymous_user_id", columnDefinition = "UUID")
    private UUID anonymousUserId;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "email_verified")
    private Boolean emailVerified = false;
    
    @Column(name = "last_login_at")
    private LocalDateTime lastLoginAt;
    
    @JsonIgnore
    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
    private List<FamilyMember> familyMembers = new ArrayList<>();
    
    @JsonIgnore
    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
    private List<Medicine> medicines = new ArrayList<>();
    
    @JsonIgnore
    @OneToMany(mappedBy = "prescribedBy", fetch = FetchType.LAZY)
    private List<Prescription> prescribedPrescriptions = new ArrayList<>();
    
    @JsonIgnore
    @OneToMany(mappedBy = "patient", fetch = FetchType.LAZY)
    private List<Prescription> patientPrescriptions = new ArrayList<>();
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        // Chỉ set default cho fields không có default value
        if (role == null) {
            role = UserRole.USER;
        }
        if (userType == null) {
            userType = UserType.REGISTERED;
        }
        if (isActive == null) {
            isActive = true;
        }
        if (emailVerified == null) {
            emailVerified = false;
        }
        // @CreationTimestamp và @UpdateTimestamp tự động handle
    }
    
    // Business logic đã được chuyển ra UserBusinessService
    
    // UserDetails implementation đã được chuyển ra UserBusinessService
    // UserRole enum đã được tách ra thành file riêng: com.medication.enums.UserRole
} 