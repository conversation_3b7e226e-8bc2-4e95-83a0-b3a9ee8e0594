package com.medication.enums;

/**
 * Enum định nghĩa trạng thái của anonymous session
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-12-01
 */
public enum AnonymousSessionStatus {
    
    /**
     * Session đang hoạt động
     * - User c<PERSON> thể sử dụng app bình thường
     * - Data được lưu trữ local
     * - <PERSON><PERSON> thể migrate sang registered user
     */
    ACTIVE("ACTIVE", "Đang hoạt động"),
    
    /**
     * Session đã hết hạn (30 ngày)
     * - User không thể sử dụng app
     * - Data vẫn được giữ lại để migration
     * - Cần đăng ký để tiếp tục sử dụng
     */
    EXPIRED("EXPIRED", "Đã hết hạn"),
    
    /**
     * Session đã được migrate sang registered user
     * - Data đã được chuyển sang tài khoản chính thức
     * - Session không còn hoạt động
     * - <PERSON><PERSON> thể cleanup sau một thời gian
     */
    MIGRATED("MIGRATED", "Đ<PERSON> migrate"),
    
    /**
     * Session đã được cleanup
     * - Data đã được xóa hoàn toàn
     * - Không thể khôi phục
     * - Chỉ dùng cho audit purposes
     */
    CLEANED("CLEANED", "Đã cleanup");
    
    private final String code;
    private final String description;
    
    AnonymousSessionStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Kiểm tra xem session có đang hoạt động không
     */
    public boolean isActive() {
        return this == ACTIVE;
    }
    
    /**
     * Kiểm tra xem session có thể sử dụng không
     */
    public boolean canUse() {
        return this == ACTIVE;
    }
    
    /**
     * Kiểm tra xem session có thể migrate không
     */
    public boolean canMigrate() {
        return this == ACTIVE || this == EXPIRED;
    }
    
    /**
     * Kiểm tra xem session có thể cleanup không
     */
    public boolean canCleanup() {
        return this == MIGRATED;
    }
    
    /**
     * Kiểm tra xem session có thể expire không
     */
    public boolean canExpire() {
        return this == ACTIVE;
    }
    
    /**
     * Lấy trạng thái mặc định cho session mới
     */
    public static AnonymousSessionStatus getDefault() {
        return ACTIVE;
    }
    
    /**
     * Tìm AnonymousSessionStatus theo code
     */
    public static AnonymousSessionStatus fromCode(String code) {
        for (AnonymousSessionStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid AnonymousSessionStatus code: " + code);
    }
    
    @Override
    public String toString() {
        return code;
    }
} 