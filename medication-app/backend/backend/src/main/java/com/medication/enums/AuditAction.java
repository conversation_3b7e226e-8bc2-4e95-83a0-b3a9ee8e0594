package com.medication.enums;

/**
 * Enumeration of audit actions
 * 
 * <AUTHOR>
 * @since December 2024
 */
public enum AuditAction {
    CREATE("CREATE", "Tạo mới"),
    UPDATE("UPDATE", "<PERSON><PERSON><PERSON> nhật"),
    DELETE("DELETE", "Xóa"),
    LOGIN("LOGI<PERSON>", "Đăng nhập"),
    LOGOUT("LOGOUT", "Đăng xuất"),
    EXPORT("EXPORT", "Xuất dữ liệu"),
    IMPORT("IMPORT", "Nhập dữ liệu"),
    BACKUP("<PERSON><PERSON><PERSON><PERSON>", "Sao lưu"),
    RESTORE("RESTORE", "<PERSON>h<PERSON><PERSON> phục"),
    PASSWORD_CHANGE("PASSWORD_CHANGE", "Đổi mật khẩu"),
    PROFILE_UPDATE("PROFILE_UPDATE", "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hồ sơ"),
    MEDICINE_ADD("MEDICINE_ADD", "Thêm thuốc"),
    MEDICINE_UPDATE("MEDICINE_UPDATE", "<PERSON><PERSON><PERSON> nhật thuốc"),
    MEDICINE_DELETE("MEDICINE_DELETE", "Xóa thuốc"),
    PRESCRIPTION_CREATE("PRESCRIPTION_CREATE", "Tạo đơn thuốc"),
    PRESCRIPTION_UPDATE("PRESCRIPTION_UPDATE", "Cập nhật đơn thuốc"),
    PRESCRIPTION_DELETE("PRESCRIPTION_DELETE", "Xóa đơn thuốc"),
    FAMILY_CREATE("FAMILY_CREATE", "Tạo gia đình"),
    FAMILY_UPDATE("FAMILY_UPDATE", "Cập nhật gia đình"),
    FAMILY_DELETE("FAMILY_DELETE", "Xóa gia đình"),
    MEMBER_ADD("MEMBER_ADD", "Thêm thành viên"),
    MEMBER_REMOVE("MEMBER_REMOVE", "Xóa thành viên"),
    NOTIFICATION_SEND("NOTIFICATION_SEND", "Gửi thông báo"),
    SYSTEM_CONFIG("SYSTEM_CONFIG", "Cấu hình hệ thống");
    
    private final String code;
    private final String label;
    
    AuditAction(String code, String label) {
        this.code = code;
        this.label = label;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getLabel() {
        return label;
    }
    
    /**
     * Check if this is a CRUD operation
     */
    public boolean isCrudOperation() {
        return this == CREATE || this == UPDATE || this == DELETE;
    }
    
    /**
     * Check if this is an authentication action
     */
    public boolean isAuthenticationAction() {
        return this == LOGIN || this == LOGOUT || this == PASSWORD_CHANGE;
    }
    
    /**
     * Check if this is a data operation
     */
    public boolean isDataOperation() {
        return this == EXPORT || this == IMPORT || this == BACKUP || this == RESTORE;
    }
    
    /**
     * Check if this is a medicine operation
     */
    public boolean isMedicineOperation() {
        return this == MEDICINE_ADD || this == MEDICINE_UPDATE || this == MEDICINE_DELETE;
    }
    
    /**
     * Check if this is a prescription operation
     */
    public boolean isPrescriptionOperation() {
        return this == PRESCRIPTION_CREATE || this == PRESCRIPTION_UPDATE || this == PRESCRIPTION_DELETE;
    }
    
    /**
     * Check if this is a family operation
     */
    public boolean isFamilyOperation() {
        return this == FAMILY_CREATE || this == FAMILY_UPDATE || this == FAMILY_DELETE;
    }
    
    /**
     * Check if this is a member operation
     */
    public boolean isMemberOperation() {
        return this == MEMBER_ADD || this == MEMBER_REMOVE;
    }
    
    /**
     * Check if this is a critical action
     */
    public boolean isCriticalAction() {
        return this == DELETE || this == FAMILY_DELETE || this == PRESCRIPTION_DELETE || 
               this == MEDICINE_DELETE || this == PASSWORD_CHANGE || this == SYSTEM_CONFIG;
    }
    
    /**
     * Get severity level for this action
     */
    public AuditSeverity getDefaultSeverity() {
        if (isCriticalAction()) {
            return AuditSeverity.CRITICAL;
        } else if (this == UPDATE || this == MEDICINE_UPDATE || this == PRESCRIPTION_UPDATE) {
            return AuditSeverity.WARNING;
        } else if (this == LOGIN || this == LOGOUT) {
            return AuditSeverity.INFO;
        } else {
            return AuditSeverity.INFO;
        }
    }
} 