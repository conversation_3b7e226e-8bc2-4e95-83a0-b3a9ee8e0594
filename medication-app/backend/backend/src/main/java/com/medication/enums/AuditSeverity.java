package com.medication.enums;

/**
 * Enumeration of audit severity levels
 * 
 * <AUTHOR>
 * @since December 2024
 */
public enum AuditSeverity {
    INFO("INFO", "Thông tin", 1),
    WARNING("WARNING", "Cảnh báo", 2),
    ERROR("ERROR", "Lỗi", 3),
    CRITICAL("CRITIC<PERSON>", "Nghiêm trọng", 4);
    
    private final String code;
    private final String label;
    private final int level;
    
    AuditSeverity(String code, String label, int level) {
        this.code = code;
        this.label = label;
        this.level = level;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getLabel() {
        return label;
    }
    
    public int getLevel() {
        return level;
    }
    
    /**
     * Check if this severity is higher than or equal to the given severity
     */
    public boolean isHigherThanOrEqual(AuditSeverity other) {
        return this.level >= other.level;
    }
    
    /**
     * Check if this severity is critical
     */
    public boolean isCritical() {
        return this == CRITICAL;
    }
    
    /**
     * Check if this severity is error or higher
     */
    public boolean isErrorOrHigher() {
        return this.level >= ERROR.level;
    }
    
    /**
     * Check if this severity is warning or higher
     */
    public boolean isWarningOrHigher() {
        return this.level >= WARNING.level;
    }
    
    /**
     * Get severity by level
     */
    public static AuditSeverity getByLevel(int level) {
        for (AuditSeverity severity : values()) {
            if (severity.level == level) {
                return severity;
            }
        }
        return INFO; // Default
    }
} 