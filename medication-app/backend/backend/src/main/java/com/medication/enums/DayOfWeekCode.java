package com.medication.enums;

import java.time.DayOfWeek;

/**
 * Enum định nghĩa các mã ngày trong tuần
 */
public enum DayOfWeekCode {
    MON("Thứ 2", DayOfWeek.MONDAY, 1),
    TUE("Thứ 3", DayOfWeek.TUESDAY, 2),
    WED("Thứ 4", DayOfWeek.WEDNESDAY, 3),
    THU("Thứ 5", DayOfWeek.THURSDAY, 4),
    FRI("Thứ 6", DayOfWeek.FRIDAY, 5),
    SAT("Thứ 7", DayOfWeek.SATURDAY, 6),
    SUN("Chủ nhật", DayOfWeek.SUNDAY, 7);

    private final String displayName;
    private final DayOfWeek dayOfWeek;
    private final int dayNumber;

    DayOfWeekCode(String displayName, DayOfWeek dayOfWeek, int dayNumber) {
        this.displayName = displayName;
        this.dayOfWeek = dayOfWeek;
        this.dayNumber = dayNumber;
    }

    public String getDisplayName() {
        return displayName;
    }

    public DayOfWeek getDayOfWeek() {
        return dayOfWeek;
    }

    public int getDayNumber() {
        return dayNumber;
    }
    
    /**
     * Chuyển đổi string thành DayOfWeekCode enum
     * @param codeString String cần chuyển đổi
     * @return DayOfWeekCode enum tương ứng, null nếu không tìm thấy
     */
    public static DayOfWeekCode fromString(String codeString) {
        if (codeString == null || codeString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return DayOfWeekCode.valueOf(codeString.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * Chuyển đổi từ DayOfWeek thành DayOfWeekCode
     * @param dayOfWeek DayOfWeek cần chuyển đổi
     * @return DayOfWeekCode enum tương ứng
     */
    public static DayOfWeekCode fromDayOfWeek(DayOfWeek dayOfWeek) {
        if (dayOfWeek == null) {
            return null;
        }
        
        switch (dayOfWeek) {
            case MONDAY:
                return MON;
            case TUESDAY:
                return TUE;
            case WEDNESDAY:
                return WED;
            case THURSDAY:
                return THU;
            case FRIDAY:
                return FRI;
            case SATURDAY:
                return SAT;
            case SUNDAY:
                return SUN;
            default:
                return null;
        }
    }
    
    /**
     * Kiểm tra xem có phải là ngày trong tuần không (Thứ 2-6)
     * @return true nếu là ngày trong tuần
     */
    public boolean isWeekday() {
        return this == MON || this == TUE || this == WED || this == THU || this == FRI;
    }
    
    /**
     * Kiểm tra xem có phải là cuối tuần không (Thứ 7, Chủ nhật)
     * @return true nếu là cuối tuần
     */
    public boolean isWeekend() {
        return this == SAT || this == SUN;
    }
    
    /**
     * Lấy tên ngắn gọn
     * @return String tên ngắn gọn
     */
    public String getShortName() {
        return this.name();
    }
    
    /**
     * Lấy tên đầy đủ bằng tiếng Anh
     * @return String tên đầy đủ
     */
    public String getFullName() {
        switch (this) {
            case MON:
                return "Monday";
            case TUE:
                return "Tuesday";
            case WED:
                return "Wednesday";
            case THU:
                return "Thursday";
            case FRI:
                return "Friday";
            case SAT:
                return "Saturday";
            case SUN:
                return "Sunday";
            default:
                return "Unknown";
        }
    }
    
    /**
     * Lấy mô tả chi tiết
     * @return String mô tả
     */
    public String getDescription() {
        return displayName + " (" + getFullName() + ")";
    }
} 