package com.medication.enums;

/**
 * Enum định nghĩa các trạng thái của liều thuốc
 */
public enum DoseStatus {
    PENDING("Chờ uống", "pending"),
    TAKEN("Đ<PERSON> uống", "taken"),
    MISSED("Bỏ lỡ", "missed"),
    SKIPPED("Bỏ qua", "skipped"),
    PARTIAL("Uống một phần", "partial");
    
    private final String label;
    private final String jsonValue;
    
    DoseStatus(String label, String jsonValue) {
        this.label = label;
        this.jsonValue = jsonValue;
    }
    
    public String getLabel() {
        return label;
    }
    
    public String getJsonValue() {
        return jsonValue;
    }
    
    /**
     * Chuyển đổi string thành DoseStatus enum
     * @param statusString String cần chuyển đổi
     * @return DoseStatus enum tương ứng, null nếu không tìm thấy
     */
    public static DoseStatus fromString(String statusString) {
        if (statusString == null || statusString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return DoseStatus.valueOf(statusString.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * Chuyển đổi từ JSON value thành DoseStatus enum
     * @param jsonValue JSON value cần chuyển đổi
     * @return DoseStatus enum tương ứng, null nếu không tìm thấy
     */
    public static DoseStatus fromJsonValue(String jsonValue) {
        if (jsonValue == null || jsonValue.trim().isEmpty()) {
            return null;
        }
        
        for (DoseStatus status : DoseStatus.values()) {
            if (status.getJsonValue().equals(jsonValue.toLowerCase())) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái đã hoàn thành không
     * @return true nếu là TAKEN hoặc PARTIAL
     */
    public boolean isCompleted() {
        return this == TAKEN || this == PARTIAL;
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái bỏ lỡ không
     * @return true nếu là MISSED hoặc SKIPPED
     */
    public boolean isMissed() {
        return this == MISSED || this == SKIPPED;
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái chờ không
     * @return true nếu là PENDING
     */
    public boolean isPending() {
        return this == PENDING;
    }
    
    /**
     * Kiểm tra xem có cần actual dose không
     * @return true nếu là TAKEN hoặc PARTIAL
     */
    public boolean requiresActualDose() {
        return this == TAKEN || this == PARTIAL;
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái thành công không
     * @return true nếu là TAKEN
     */
    public boolean isSuccessful() {
        return this == TAKEN;
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái thất bại không
     * @return true nếu là MISSED hoặc SKIPPED
     */
    public boolean isFailed() {
        return this == MISSED || this == SKIPPED;
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái một phần không
     * @return true nếu là PARTIAL
     */
    public boolean isPartial() {
        return this == PARTIAL;
    }
    
    /**
     * Lấy mô tả chi tiết của trạng thái
     * @return String mô tả
     */
    public String getDescription() {
        switch (this) {
            case PENDING:
                return "Liều thuốc chưa được uống, đang chờ đến thời gian";
            case TAKEN:
                return "Liều thuốc đã được uống đầy đủ";
            case MISSED:
                return "Liều thuốc đã bị bỏ lỡ do quên hoặc không có sẵn";
            case SKIPPED:
                return "Liều thuốc đã được bỏ qua có chủ ý";
            case PARTIAL:
                return "Liều thuốc đã được uống một phần";
            default:
                return "Không có mô tả";
        }
    }
    
    /**
     * Lấy màu sắc hiển thị cho trạng thái
     * @return String mã màu
     */
    public String getColor() {
        switch (this) {
            case PENDING:
                return "#ffc107"; // Yellow
            case TAKEN:
                return "#28a745"; // Green
            case MISSED:
                return "#dc3545"; // Red
            case SKIPPED:
                return "#6c757d"; // Gray
            case PARTIAL:
                return "#fd7e14"; // Orange
            default:
                return "#6c757d"; // Gray
        }
    }
    
    /**
     * Lấy icon hiển thị cho trạng thái
     * @return String tên icon
     */
    public String getIcon() {
        switch (this) {
            case PENDING:
                return "clock";
            case TAKEN:
                return "check-circle";
            case MISSED:
                return "times-circle";
            case SKIPPED:
                return "ban";
            case PARTIAL:
                return "minus-circle";
            default:
                return "question-circle";
        }
    }
    
    /**
     * Lấy adherence rate cho trạng thái
     * @return Double adherence rate (0.0 - 1.0)
     */
    public double getAdherenceRate() {
        switch (this) {
            case TAKEN:
                return 1.0;
            case PARTIAL:
                return 0.5;
            case PENDING:
            case MISSED:
            case SKIPPED:
                return 0.0;
            default:
                return 0.0;
        }
    }
} 