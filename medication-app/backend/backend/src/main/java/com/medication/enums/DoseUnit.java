package com.medication.enums;



/**
 * Enumeration of dose units for medication prescriptions
 * 
 * <AUTHOR>
 * @since December 2024
 */
public enum DoseUnit {
    // Mass units - match PostgreSQL enum values
    MILLIGRAM("mg", "Milligram", DoseUnitCategory.MASS, null),
    MICROGRAM("mcg", "Microgram", DoseUnitCategory.MASS, null),
    GRAM("g", "Gram", DoseUnitCategory.MASS, null),

    // Volume units - match PostgreSQL enum values
    MILLILITER("ml", "Milliliter", DoseUnitCategory.VOLUME, null),
    LITER("l", "Liter", DoseUnitCategory.VOLUME, null),

    // Count units - match PostgreSQL enum values
    TABLET("viên", "Viên thuốc", DoseUnitCategory.COUNT, 500.0), // Default 500mg per tablet
    CAPSULE("viên nang", "Viên nang", DoseUnitCategory.COUNT, 500.0), // Default 500mg per capsule
    PILL("viên", "Viên thuốc", DoseUnitCategory.COUNT, 500.0), // Default 500mg per pill
    DROP("giọt", "Giọt", DoseUnitCategory.COUNT, 20.0), // Default 20mg per drop
    PUFF("nhát", "Nhát xịt", DoseUnitCategory.COUNT, 100.0), // Default 100mcg per puff
    SPRAY("xịt", "Xịt", DoseUnitCategory.COUNT, 100.0), // Default 100mcg per spray
    PATCH("miếng dán", "Miếng dán", DoseUnitCategory.COUNT, 25.0), // Default 25mg per patch
    SUPPOSITORY("viên đặt", "Viên đặt", DoseUnitCategory.COUNT, 250.0), // Default 250mg per suppository
    INHALATION("hít", "Hít", DoseUnitCategory.COUNT, 100.0), // Default 100mcg per inhalation

    // Special units - match PostgreSQL enum values
    TEASPOON("tsp", "Teaspoon", DoseUnitCategory.VOLUME, null),
    TABLESPOON("tbsp", "Tablespoon", DoseUnitCategory.VOLUME, null),
    CUP("cup", "Cup", DoseUnitCategory.VOLUME, null),
    UNIT("unit", "Unit", DoseUnitCategory.COUNT, null),
    INTERNATIONAL_UNIT("IU", "International Unit", DoseUnitCategory.COUNT, null),
    APPLICATION("lần", "Application", DoseUnitCategory.COUNT, null),

    // Weight-based units - match PostgreSQL enum values
    MG_PER_KG("mg/kg", "Milligram per Kilogram", DoseUnitCategory.MASS, null),
    MCG_PER_KG("mcg/kg", "Microgram per Kilogram", DoseUnitCategory.MASS, null),
    UNITS_PER_KG("units/kg", "Units per Kilogram", DoseUnitCategory.COUNT, null);
    
    private final String abbreviation;
    private final String description;
    private final DoseUnitCategory category;
    private final Double defaultMultiplier; // mg/mcg equivalent for count units
    
    DoseUnit(String abbreviation, String description, DoseUnitCategory category, Double defaultMultiplier) {
        this.abbreviation = abbreviation;
        this.description = description;
        this.category = category;
        this.defaultMultiplier = defaultMultiplier;
    }
    
    public String getAbbreviation() {
        return abbreviation;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Get display label for UI
     */
    public String getLabel() {
        return String.format("%s (%s)", description, abbreviation);
    }
    
    /**
     * Get the category of this unit
     */
    public DoseUnitCategory getCategory() {
        return category;
    }
    
    /**
     * Get the default multiplier (mg/mcg equivalent for count units)
     */
    public Double getDefaultMultiplier() {
        return defaultMultiplier;
    }
    
    /**
     * Check if this unit has a default multiplier
     */
    public boolean hasDefaultMultiplier() {
        return defaultMultiplier != null;
    }
    
    /**
     * Check if this unit is compatible with another unit for conversion
     */
    public boolean isCompatibleWith(DoseUnit other) {
        return this.category == other.category;
    }
    
    /**
     * Get conversion factor to another unit (if possible)
     */
    public Double getConversionFactor(DoseUnit targetUnit) {
        if (!isCompatibleWith(targetUnit)) return null;
        if (this == targetUnit) return 1.0;
        
        // Only MASS category supports conversion
        if (this.category != DoseUnitCategory.MASS) return null;
        
        // Basic mass conversions
        switch (this) {
            case GRAM:
                switch (targetUnit) {
                    case MILLIGRAM: return 1000.0;
                    case MICROGRAM: return 1000000.0;
                }
                break;
            case MILLIGRAM:
                switch (targetUnit) {
                    case GRAM: return 0.001;
                    case MICROGRAM: return 1000.0;
                }
                break;
            case MICROGRAM:
                switch (targetUnit) {
                    case GRAM: return 0.000001;
                    case MILLIGRAM: return 0.001;
                }
                break;
        }
        
        return null; // No conversion available
    }
    
    /**
     * Check if this unit supports conversion
     */
    public boolean supportsConversion() {
        return this.category.supportsConversion();
    }
    
    /**
     * Get all compatible units
     */
    public DoseUnit[] getCompatibleUnits() {
        return this.category.getUnits();
    }
    
    /**
     * Check if this is a mass unit
     */
    public boolean isMassUnit() {
        return this.category == DoseUnitCategory.MASS;
    }
    
    /**
     * Check if this is a volume unit
     */
    public boolean isVolumeUnit() {
        return this.category == DoseUnitCategory.VOLUME;
    }
    
    /**
     * Check if this is a count unit
     */
    public boolean isCountUnit() {
        return this.category == DoseUnitCategory.COUNT;
    }
    
    /**
     * Get display text with default multiplier (e.g., "1 viên nang (500mg)")
     */
    public String getDisplayTextWithMultiplier(Double amount) {
        if (amount == null) return getLabel();
        
        if (hasDefaultMultiplier()) {
            return String.format("%.0f %s (%.0f%s)", 
                amount, 
                description, 
                amount * defaultMultiplier, 
                getMultiplierUnit().getAbbreviation());
        }
        
        return String.format("%.2f %s", amount, abbreviation);
    }
    
    /**
     * Get display text with custom multiplier
     */
    public String getDisplayTextWithCustomMultiplier(Double amount, Double customMultiplier, DoseUnit multiplierUnit) {
        if (amount == null || customMultiplier == null || multiplierUnit == null) {
            return getDisplayText(amount);
        }
        
        return String.format("%.0f %s (%.0f%s)", 
            amount, 
            description, 
            amount * customMultiplier, 
            multiplierUnit.getAbbreviation());
    }
    
    /**
     * Get simple display text
     */
    public String getDisplayText(Double amount) {
        if (amount == null) return getLabel();
        
        if (amount == Math.floor(amount)) {
            return String.format("%.0f %s", amount, abbreviation);
        }
        
        return String.format("%.2f %s", amount, abbreviation);
    }
    
    /**
     * Get the unit for multiplier (mg for most cases, mcg for some)
     */
    public DoseUnit getMultiplierUnit() {
        // Most count units use mg as multiplier
        if (this == PUFF || this == SPRAY || this == INHALATION) return MICROGRAM; // Puff/spray/inhalation usually in mcg
        return MILLIGRAM; // Default to mg
    }
    
    /**
     * Convert count unit to mass equivalent using default multiplier
     */
    public Double toMassEquivalent(Double amount) {
        if (!hasDefaultMultiplier() || amount == null) return null;
        return amount * defaultMultiplier;
    }
    
    /**
     * Convert count unit to mass equivalent using custom multiplier
     */
    public Double toMassEquivalent(Double amount, Double customMultiplier) {
        if (amount == null || customMultiplier == null) return null;
        return amount * customMultiplier;
    }
} 