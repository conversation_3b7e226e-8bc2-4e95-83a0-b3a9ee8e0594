package com.medication.enums;

/**
 * Enumeration of dose unit categories for medication prescriptions
 * 
 * <AUTHOR>
 * @since December 2024
 */
public enum DoseUnitCategory {
    MASS("Khối lượng"),
    VOLUME("Thể tích"),
    COUNT("Số lượng"),
    OTHER("Khác");
    
    private final String label;
    
    DoseUnitCategory(String label) {
        this.label = label;
    }
    
    public String getLabel() {
        return label;
    }
    
    /**
     * Get description for the category
     */
    public String getDescription() {
        switch (this) {
            case MASS:
                return "Đơn vị đo khối lượng (mg, mcg, g)";
            case VOLUME:
                return "Đơn vị đo thể tích (ml)";
            case COUNT:
                return "Đơn vị đếm số lượng (viên, giọt, nhát)";
            case OTHER:
                return "Đơn vị khác";
            default:
                return "Không xác định";
        }
    }
    
    /**
     * Check if this category supports conversion
     */
    public boolean supportsConversion() {
        return this == MASS || this == VOLUME;
    }
    
    /**
     * Get all units in this category
     */
    public DoseUnit[] getUnits() {
        switch (this) {
            case MASS:
                return new DoseUnit[]{DoseUnit.MILLIGRAM, DoseUnit.MICROGRAM, DoseUnit.GRAM, DoseUnit.MG_PER_KG, DoseUnit.MCG_PER_KG};
            case VOLUME:
                return new DoseUnit[]{DoseUnit.MILLILITER, DoseUnit.LITER, DoseUnit.TEASPOON, DoseUnit.TABLESPOON, DoseUnit.CUP};
            case COUNT:
                return new DoseUnit[]{DoseUnit.TABLET, DoseUnit.CAPSULE, DoseUnit.PILL, DoseUnit.DROP, DoseUnit.PUFF,
                                     DoseUnit.SPRAY, DoseUnit.PATCH, DoseUnit.SUPPOSITORY, DoseUnit.INHALATION,
                                     DoseUnit.UNIT, DoseUnit.INTERNATIONAL_UNIT, DoseUnit.APPLICATION, DoseUnit.UNITS_PER_KG};
            case OTHER:
                return new DoseUnit[]{};
            default:
                return new DoseUnit[]{};
        }
    }
} 