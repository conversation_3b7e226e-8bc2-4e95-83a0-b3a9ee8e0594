package com.medication.enums;

/**
 * Enum định nghĩa các vai trò trong gia đình
 */
public enum FamilyRole {
    OWNER("Chủ gia đình", "owner"),
    ADMIN("Quản trị viên", "admin"),
    MEMBER("Thành viên", "member");
    
    private final String label;
    private final String jsonValue;
    
    FamilyRole(String label, String jsonValue) {
        this.label = label;
        this.jsonValue = jsonValue;
    }
    
    public String getLabel() {
        return label;
    }
    
    public String getJsonValue() {
        return jsonValue;
    }
    
    /**
     * Chuyển đổi string thành FamilyRole enum
     * @param roleString String cần chuyển đổi
     * @return FamilyRole enum tương ứng, null nếu không tìm thấy
     */
    public static FamilyRole fromString(String roleString) {
        if (roleString == null || roleString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return FamilyRole.valueOf(roleString.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * Chuyển đổi từ JSON value thành FamilyRole enum
     * @param jsonValue JSON value cần chuyển đổi
     * @return FamilyRole enum tương ứng, null nếu không tìm thấy
     */
    public static FamilyRole fromJsonValue(String jsonValue) {
        if (jsonValue == null || jsonValue.trim().isEmpty()) {
            return null;
        }
        
        for (FamilyRole role : FamilyRole.values()) {
            if (role.getJsonValue().equals(jsonValue.toLowerCase())) {
                return role;
            }
        }
        return null;
    }
    
    /**
     * Kiểm tra xem có phải là chủ gia đình không
     * @return true nếu là OWNER
     */
    public boolean isOwner() {
        return this == OWNER;
    }
    
    /**
     * Kiểm tra xem có phải là admin không
     * @return true nếu là ADMIN
     */
    public boolean isAdmin() {
        return this == ADMIN;
    }
    
    /**
     * Kiểm tra xem có phải là thành viên thường không
     * @return true nếu là MEMBER
     */
    public boolean isMember() {
        return this == MEMBER;
    }
    
    /**
     * Kiểm tra xem có quyền quản trị không
     * @return true nếu là OWNER hoặc ADMIN
     */
    public boolean hasAdminRights() {
        return this == OWNER || this == ADMIN;
    }
    
    /**
     * Kiểm tra xem có quyền cao nhất không
     * @return true nếu là OWNER
     */
    public boolean hasOwnerRights() {
        return this == OWNER;
    }
    
    /**
     * Kiểm tra xem có thể quản lý thành viên khác không
     * @return true nếu có quyền admin
     */
    public boolean canManageMembers() {
        return hasAdminRights();
    }
    
    /**
     * Kiểm tra xem có thể quản lý cài đặt gia đình không
     * @return true nếu là owner
     */
    public boolean canManageFamilySettings() {
        return hasOwnerRights();
    }
    
    /**
     * Kiểm tra xem có thể xem thông tin gia đình không
     * @return true cho tất cả roles
     */
    public boolean canViewFamilyInfo() {
        return true; // Tất cả thành viên đều có thể xem
    }
    
    /**
     * Lấy mô tả chi tiết của vai trò
     * @return String mô tả
     */
    public String getDescription() {
        switch (this) {
            case OWNER:
                return "Chủ gia đình - Có toàn quyền quản lý gia đình, thành viên và cài đặt";
            case ADMIN:
                return "Quản trị viên - Có quyền quản lý thành viên và một số cài đặt";
            case MEMBER:
                return "Thành viên - Có quyền xem thông tin và tham gia hoạt động gia đình";
            default:
                return "Không có mô tả";
        }
    }
    
    /**
     * Lấy màu sắc hiển thị cho vai trò
     * @return String mã màu
     */
    public String getColor() {
        switch (this) {
            case OWNER:
                return "#dc3545"; // Red
            case ADMIN:
                return "#fd7e14"; // Orange
            case MEMBER:
                return "#28a745"; // Green
            default:
                return "#6c757d"; // Gray
        }
    }
    
    /**
     * Lấy icon hiển thị cho vai trò
     * @return String tên icon
     */
    public String getIcon() {
        switch (this) {
            case OWNER:
                return "crown";
            case ADMIN:
                return "shield-alt";
            case MEMBER:
                return "user";
            default:
                return "user-circle";
        }
    }
    
    /**
     * Lấy level của vai trò (cao hơn = quyền lớn hơn)
     * @return Integer level
     */
    public int getLevel() {
        switch (this) {
            case OWNER:
                return 3;
            case ADMIN:
                return 2;
            case MEMBER:
                return 1;
            default:
                return 0;
        }
    }
    
    /**
     * Kiểm tra xem có thể thay đổi vai trò của role khác không
     * @param targetRole Vai trò đích
     * @return true nếu có thể thay đổi
     */
    public boolean canChangeRole(FamilyRole targetRole) {
        if (targetRole == null) return false;
        
        int currentLevel = this.getLevel();
        int targetLevel = targetRole.getLevel();
        
        // Chỉ có thể thay đổi vai trò có level thấp hơn hoặc bằng
        return currentLevel > targetLevel;
    }
    
    /**
     * Lấy danh sách vai trò có thể được gán bởi role hiện tại
     * @return Array các FamilyRole có thể gán
     */
    public FamilyRole[] getAssignableRoles() {
        switch (this) {
            case OWNER:
                return new FamilyRole[]{ADMIN, MEMBER};
            case ADMIN:
                return new FamilyRole[]{MEMBER};
            case MEMBER:
                return new FamilyRole[]{};
            default:
                return new FamilyRole[]{};
        }
    }
} 