package com.medication.enums;

/**
 * Enumeration of notification statuses
 * 
 * <AUTHOR>
 * @since December 2024
 */
public enum NotificationStatus {
    PENDING("Chờ gửi", "pending"),
    SENT("Đã gửi", "sent"),
    FAILED("Gửi thất bại", "failed"),
    CANCELLED("Đã hủy", "cancelled");
    
    private final String label;
    private final String code;
    
    NotificationStatus(String label, String code) {
        this.label = label;
        this.code = code;
    }
    
    public String getLabel() {
        return label;
    }
    
    public String getCode() {
        return code;
    }
    
    /**
     * Check if notification is pending
     */
    public boolean isPending() {
        return this == PENDING;
    }
    
    /**
     * Check if notification is sent
     */
    public boolean isSent() {
        return this == SENT;
    }
    
    /**
     * Check if notification failed
     */
    public boolean isFailed() {
        return this == FAILED;
    }
    
    /**
     * Check if notification is cancelled
     */
    public boolean isCancelled() {
        return this == CANCELLED;
    }
    
    /**
     * Check if notification is in final state (cannot be changed)
     */
    public boolean isFinal() {
        return this == SENT || this == CANCELLED;
    }
    
    /**
     * Check if notification can be retried
     */
    public boolean canRetry() {
        return this == FAILED;
    }
    
    /**
     * Check if notification is active (not cancelled)
     */
    public boolean isActive() {
        return this != CANCELLED;
    }
} 