package com.medication.enums;

/**
 * Enumeration of notification types
 * 
 * <AUTHOR>
 * @since December 2024
 */
public enum NotificationType {
    INFO("Thông tin", "info"),
    WARNING("Cảnh báo", "warning"),
    ERROR("Lỗi", "error"),
    REMINDER("Nhắc nhở", "reminder"),
    ALERT("Báo động", "alert");
    
    private final String label;
    private final String icon;
    
    NotificationType(String label, String icon) {
        this.label = label;
        this.icon = icon;
    }
    
    public String getLabel() {
        return label;
    }
    
    public String getIcon() {
        return icon;
    }
    
    /**
     * Check if this is an urgent notification
     */
    public boolean isUrgent() {
        return this == ERROR || this == ALERT;
    }
    
    /**
     * Check if this is a reminder notification
     */
    public boolean isReminder() {
        return this == REMINDER;
    }
    
    /**
     * Check if this is an informational notification
     */
    public boolean isInfo() {
        return this == INFO;
    }
    
    /**
     * Check if this is a warning notification
     */
    public boolean isWarning() {
        return this == WARNING;
    }
    
    /**
     * Check if this is an error notification
     */
    public boolean isError() {
        return this == ERROR;
    }
    
    /**
     * Check if this is an alert notification
     */
    public boolean isAlert() {
        return this == ALERT;
    }
    
    /**
     * Get priority level (higher = more important)
     */
    public int getPriority() {
        switch (this) {
            case ERROR:
                return 5;
            case ALERT:
                return 4;
            case WARNING:
                return 3;
            case REMINDER:
                return 2;
            case INFO:
                return 1;
            default:
                return 0;
        }
    }
} 