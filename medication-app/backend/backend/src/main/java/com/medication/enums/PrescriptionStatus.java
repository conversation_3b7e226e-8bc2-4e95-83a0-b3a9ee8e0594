package com.medication.enums;

/**
 * Enum định nghĩa các trạng thái của đơn thuốc
 */
public enum PrescriptionStatus {
    ACTIVE("Đang sử dụng"),
    COMPLETED("<PERSON>àn thành"),
    CANCELLED("Đ<PERSON> hủy"),
    EXPIRED("Hết hạn");
    
    private final String label;
    
    PrescriptionStatus(String label) {
        this.label = label;
    }
    
    public String getLabel() {
        return label;
    }
    
    /**
     * Chuyển đổi string thành PrescriptionStatus enum
     * @param statusString String cần chuyển đổi
     * @return PrescriptionStatus enum tương ứng, null nếu không tìm thấy
     */
    public static PrescriptionStatus fromString(String statusString) {
        if (statusString == null || statusString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return PrescriptionStatus.valueOf(statusString.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái hoạt động không
     * @return true nếu là ACTIVE
     */
    public boolean isActive() {
        return this == ACTIVE;
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái kết thúc không
     * @return true nếu là COMPLETED, CANCELLED, hoặc EXPIRED
     */
    public boolean isTerminated() {
        return this == COMPLETED || this == CANCELLED || this == EXPIRED;
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái có thể chỉnh sửa không
     * @return true nếu là ACTIVE
     */
    public boolean isEditable() {
        return this == ACTIVE;
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái có thể hủy không
     * @return true nếu là ACTIVE hoặc COMPLETED
     */
    public boolean isCancellable() {
        return this == ACTIVE || this == COMPLETED;
    }
    
    /**
     * Lấy mô tả chi tiết của trạng thái
     * @return String mô tả
     */
    public String getDescription() {
        switch (this) {
            case ACTIVE:
                return "Đơn thuốc đang được sử dụng và có hiệu lực";
            case COMPLETED:
                return "Đơn thuốc đã được hoàn thành theo đúng lịch trình";
            case CANCELLED:
                return "Đơn thuốc đã bị hủy bỏ";
            case EXPIRED:
                return "Đơn thuốc đã hết hạn sử dụng";
            default:
                return "Không có mô tả";
        }
    }
    
    /**
     * Lấy màu sắc hiển thị cho trạng thái
     * @return String mã màu
     */
    public String getColor() {
        switch (this) {
            case ACTIVE:
                return "#28a745"; // Green
            case COMPLETED:
                return "#17a2b8"; // Blue
            case CANCELLED:
                return "#dc3545"; // Red
            case EXPIRED:
                return "#ffc107"; // Yellow
            default:
                return "#6c757d"; // Gray
        }
    }
    
    /**
     * Lấy icon hiển thị cho trạng thái
     * @return String tên icon
     */
    public String getIcon() {
        switch (this) {
            case ACTIVE:
                return "check-circle";
            case COMPLETED:
                return "flag-checkered";
            case CANCELLED:
                return "times-circle";
            case EXPIRED:
                return "clock";
            default:
                return "question-circle";
        }
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái thành công không
     * @return true nếu là COMPLETED
     */
    public boolean isSuccessful() {
        return this == COMPLETED;
    }
    
    /**
     * Kiểm tra xem có phải là trạng thái thất bại không
     * @return true nếu là CANCELLED hoặc EXPIRED
     */
    public boolean isFailed() {
        return this == CANCELLED || this == EXPIRED;
    }
} 