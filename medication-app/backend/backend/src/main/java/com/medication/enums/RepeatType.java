package com.medication.enums;

/**
 * Enum định nghĩa các loại lặp lại của lịch trình thuốc
 */
public enum RepeatType {
    ONE_TIME("Một lần"),
    DAILY("Hàng ngày"),
    WEEKLY("Hàng tuần"),
    EVERY_N_DAYS("Mỗi N ngày"),
    CUSTOM_DAYS("Ngày tùy chỉnh"),
    MONTHLY("Hàng tháng"),
    WEEKDAYS("Ngày trong tuần"),
    WEEKENDS("Cuối tuần");

    private final String label;

    RepeatType(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }
    
    /**
     * Chuyển đổi string thành RepeatType enum
     * @param typeString String cần chuyển đổi
     * @return RepeatType enum tương ứng, null nếu không tìm thấy
     */
    public static RepeatType fromString(String typeString) {
        if (typeString == null || typeString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return RepeatType.valueOf(typeString.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * Kiểm tra xem có cần weekdays không
     * @return true nếu cần weekdays
     */
    public boolean requiresWeekDays() {
        return this == WEEKLY || this == CUSTOM_DAYS || this == WEEKDAYS || this == WEEKENDS;
    }
    
    /**
     * Kiểm tra xem có cần interval days không
     * @return true nếu cần interval days
     */
    public boolean requiresIntervalDays() {
        return this == EVERY_N_DAYS;
    }
    
    /**
     * Kiểm tra xem có cần days of month không
     * @return true nếu cần days of month
     */
    public boolean requiresDaysOfMonth() {
        return this == MONTHLY;
    }
    
    /**
     * Kiểm tra xem có cần end date không
     * @return true nếu cần end date
     */
    public boolean requiresEndDate() {
        return this != ONE_TIME;
    }
    
    /**
     * Lấy mô tả chi tiết của loại lặp lại
     * @return String mô tả
     */
    public String getDescription() {
        switch (this) {
            case ONE_TIME:
                return "Thuốc chỉ uống một lần duy nhất";
            case DAILY:
                return "Thuốc uống hàng ngày";
            case WEEKLY:
                return "Thuốc uống hàng tuần vào các ngày được chọn";
            case EVERY_N_DAYS:
                return "Thuốc uống cách N ngày một lần";
            case CUSTOM_DAYS:
                return "Thuốc uống vào các ngày tùy chỉnh";
            case MONTHLY:
                return "Thuốc uống hàng tháng vào các ngày được chọn";
            case WEEKDAYS:
                return "Thuốc uống vào các ngày trong tuần (Thứ 2-6)";
            case WEEKENDS:
                return "Thuốc uống vào cuối tuần (Thứ 7, Chủ nhật)";
            default:
                return "Không có mô tả";
        }
    }
    
    /**
     * Kiểm tra xem có phải là lịch trình đơn giản không
     * @return true nếu là ONE_TIME hoặc DAILY
     */
    public boolean isSimpleSchedule() {
        return this == ONE_TIME || this == DAILY;
    }
    
    /**
     * Kiểm tra xem có phải là lịch trình phức tạp không
     * @return true nếu cần cấu hình thêm
     */
    public boolean isComplexSchedule() {
        return requiresWeekDays() || requiresIntervalDays() || requiresDaysOfMonth();
    }
} 