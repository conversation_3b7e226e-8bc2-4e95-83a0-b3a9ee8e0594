package com.medication.enums;

/**
 * Enumeration of medication administration routes
 * 
 * <AUTHOR>
 * @since December 2024
 */
public enum Route {
    ORAL("Uống"),
    INJECTION("Tiêm"),
    TOPICAL("Bôi ngoài da"),
    INHALATION("Hít"),
    RECTAL("Đặt hậu môn"),
    VAGINAL("Đặt âm đạo"),
    OPHTHALMIC("Nhỏ mắt"),
    OTIC("Nhỏ tai"),
    NASAL("Nhỏ mũi");
    
    private final String label;
    
    Route(String label) {
        this.label = label;
    }
    
    public String getLabel() {
        return label;
    }
    
    /**
     * Get description for the route
     */
    public String getDescription() {
        switch (this) {
            case ORAL:
                return "Thuốc uống qua đường miệng";
            case INJECTION:
                return "Thuốc tiêm qua đường tiêm";
            case TOPICAL:
                return "Thuốc bôi ngoài da";
            case INHALATION:
                return "Thuốc hít qua đường hô hấp";
            case RECTAL:
                return "Thuốc đặt qua đường hậu môn";
            case VAGINAL:
                return "Thuốc đặt qua đường âm đạo";
            case OPHTHALMIC:
                return "Thuốc nhỏ mắt";
            case OTIC:
                return "Thuốc nhỏ tai";
            case NASAL:
                return "Thuốc nhỏ mũi";
            default:
                return "Không xác định";
        }
    }
    
    /**
     * Check if this route is oral
     */
    public boolean isOral() {
        return this == ORAL;
    }
    
    /**
     * Check if this route is injection
     */
    public boolean isInjection() {
        return this == INJECTION;
    }
    
    /**
     * Check if this route is topical
     */
    public boolean isTopical() {
        return this == TOPICAL;
    }
    
    /**
     * Check if this route is inhalation
     */
    public boolean isInhalation() {
        return this == INHALATION;
    }
    
    /**
     * Check if this route is rectal
     */
    public boolean isRectal() {
        return this == RECTAL;
    }
    
    /**
     * Check if this route is vaginal
     */
    public boolean isVaginal() {
        return this == VAGINAL;
    }
    
    /**
     * Check if this route is ophthalmic
     */
    public boolean isOphthalmic() {
        return this == OPHTHALMIC;
    }
    
    /**
     * Check if this route is otic
     */
    public boolean isOtic() {
        return this == OTIC;
    }
    
    /**
     * Check if this route is nasal
     */
    public boolean isNasal() {
        return this == NASAL;
    }
    
    /**
     * Check if this route requires special handling
     */
    public boolean requiresSpecialHandling() {
        return this == INJECTION || this == RECTAL || this == VAGINAL;
    }
    
    /**
     * Check if this route is for external use only
     */
    public boolean isExternalUseOnly() {
        return this == TOPICAL || this == OPHTHALMIC || this == OTIC || this == NASAL;
    }
} 