package com.medication.enums;

/**
 * Enum định nghĩa các vai trò của người dùng trong hệ thống
 */
public enum UserRole {
    USER,       // Người dùng thường
    ADMIN,      // Quản trị viên
    DOCTOR,     // <PERSON><PERSON><PERSON> sĩ
    PHARMACIST, // Dượ<PERSON> sĩ
    SUPER_ADMIN; // Siêu quản trị viên
    
    /**
     * Chuyển đổi string thành UserRole enum
     * @param roleString String cần chuyển đổi
     * @return UserRole enum tương ứng, null nếu không tìm thấy
     */
    public static UserRole fromString(String roleString) {
        if (roleString == null || roleString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return UserRole.valueOf(roleString.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * Kiểm tra xem role có phải là admin không
     * @return true nếu là ADMIN hoặc SUPER_ADMIN
     */
    public boolean isAdmin() {
        return this == ADMIN || this == SUPER_ADMIN;
    }
    
    /**
     * Kiểm tra xem role có phải là super admin không
     * @return true nếu là SUPER_ADMIN
     */
    public boolean isSuperAdmin() {
        return this == SUPER_ADMIN;
    }
    
    /**
     * Kiểm tra xem role có phải là medical staff không
     * @return true nếu là DOCTOR hoặc PHARMACIST
     */
    public boolean isMedicalStaff() {
        return this == DOCTOR || this == PHARMACIST;
    }
    
    /**
     * Lấy tên hiển thị của role
     * @return String tên hiển thị
     */
    public String getDisplayName() {
        switch (this) {
            case USER:
                return "Người dùng";
            case ADMIN:
                return "Quản trị viên";
            case DOCTOR:
                return "Bác sĩ";
            case PHARMACIST:
                return "Dược sĩ";
            case SUPER_ADMIN:
                return "Siêu quản trị viên";
            default:
                return "Không xác định";
        }
    }
    
    /**
     * Lấy mô tả của role
     * @return String mô tả
     */
    public String getDescription() {
        switch (this) {
            case USER:
                return "Người dùng thường, có thể quản lý thuốc và đơn thuốc cá nhân";
            case ADMIN:
                return "Quản trị viên, có thể quản lý người dùng và hệ thống";
            case DOCTOR:
                return "Bác sĩ, có thể kê đơn và quản lý bệnh nhân";
            case PHARMACIST:
                return "Dược sĩ, có thể quản lý thuốc và tư vấn";
            case SUPER_ADMIN:
                return "Siêu quản trị viên, có toàn quyền trên hệ thống";
            default:
                return "Không có mô tả";
        }
    }
} 