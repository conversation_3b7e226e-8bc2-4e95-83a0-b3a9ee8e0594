package com.medication.enums;

/**
 * Enum định nghĩa các loại user trong hệ thống
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-12-01
 */
public enum UserType {
    
    /**
     * User ẩn danh - không đăng ký tài khoản
     * - Sử dụng local storage
     * - Không có family management
     * - Không có multi-device sync
     * - Session có thời hạn 30 ngày
     */
    ANONYMOUS("ANONYMOUS", "User ẩn danh"),
    
    /**
     * User đã đăng ký - có tài khoản chính thức
     * - Có family management
     * - Có multi-device sync
     * - Có cloud backup
     * - Không có giới hạn thời gian
     */
    REGISTERED("REGISTERED", "User đã đăng ký"),
    
    /**
     * User premium - tính năng cao cấp (future)
     * - Tất cả tính năng của REGISTERED
     * - Advanced analytics
     * - Priority support
     * - Custom features
     */
    PREMIUM("PREMIUM", "User premium");
    
    private final String code;
    private final String description;
    
    UserType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Kiểm tra xem user type có phải là anonymous không
     */
    public boolean isAnonymous() {
        return this == ANONYMOUS;
    }
    
    /**
     * Kiểm tra xem user type có phải là registered không
     */
    public boolean isRegistered() {
        return this == REGISTERED;
    }
    
    /**
     * Kiểm tra xem user type có phải là premium không
     */
    public boolean isPremium() {
        return this == PREMIUM;
    }
    
    /**
     * Kiểm tra xem user type có thể migrate data không
     */
    public boolean canMigrateData() {
        return this == ANONYMOUS;
    }
    
    /**
     * Lấy user type mặc định cho user mới
     */
    public static UserType getDefault() {
        return REGISTERED;
    }
    
    /**
     * Tìm UserType theo code
     */
    public static UserType fromCode(String code) {
        for (UserType userType : values()) {
            if (userType.code.equals(code)) {
                return userType;
            }
        }
        throw new IllegalArgumentException("Invalid UserType code: " + code);
    }
    
    @Override
    public String toString() {
        return code;
    }
} 