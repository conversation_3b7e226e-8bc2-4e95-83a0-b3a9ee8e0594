# Exception Package Structure

## 📁 Overview

Package này được tổ chức theo Clean Architecture principles với cấu trúc rõ ràng cho từng domain và loại exception.

## 🏗️ Directory Structure

```
exception/
├── base/                           # Base exception classes
│   ├── BaseException.java          # Abstract base for all exceptions
│   └── BusinessException.java      # Business logic exceptions
├── handler/                        # Exception handlers
│   ├── GlobalExceptionHandler.java # Global exception handler
│   └── ValidationExceptionHandler.java # Validation specific handler
├── domain/                         # Domain-specific exceptions
│   ├── user/                       # User domain exceptions
│   │   └── UserNotFoundException.java
│   ├── session/                    # Session domain exceptions
│   │   ├── AnonymousSessionNotFoundException.java
│   │   └── AnonymousSessionAlreadyMigratedException.java
│   ├── medicine/                   # Medicine domain exceptions
│   │   └── MedicineNotFoundException.java
│   └── prescription/               # Prescription domain exceptions
│       └── PrescriptionNotFoundException.java
├── common/                         # Common exceptions
│   ├── ValidationException.java    # Validation errors
│   ├── AuthenticationException.java # Authentication errors
│   └── AuthorizationException.java # Authorization errors
└── error/                          # Error response models
    └── ErrorResponse.java          # Standard error response
```

## 🎯 Usage Examples

### Creating Domain Exceptions

```java
// User domain
public class UserNotFoundException extends BusinessException {
    public UserNotFoundException(Long userId) {
        super(HttpStatus.NOT_FOUND, BusinessCode.USER_NOT_FOUND,
              "User not found with ID: " + userId);
    }
}
```

### Using Common Exceptions

```java
// Validation
throw new ValidationException("Invalid input data");

// Authentication
throw new AuthenticationException("Invalid credentials");

// Authorization
throw new AuthorizationException("Access denied");
```

### Error Response Format

```json
{
  "success": false,
  "statusCode": 404,
  "code": "USER_NOT_FOUND",
  "message": "User not found with ID: 123",
  "timestamp": "2025-08-02T06:58:06.866887Z"
}
```

## 🔧 Best Practices

1. **Always extend BusinessException** for domain-specific exceptions
2. **Use appropriate HTTP status codes** (404 for not found, 400 for bad request, etc.)
3. **Provide meaningful error messages** that help developers and users
4. **Use BusinessCode constants** for consistent error codes
5. **Include field errors** for validation exceptions
6. **Log exceptions appropriately** in handlers

## 📝 Adding New Exceptions

### For New Domain

1. Create domain directory: `exception/domain/newdomain/`
2. Create exception class extending `BusinessException`
3. Add to appropriate handler if needed

### For Common Exceptions

1. Add to `exception/common/` directory
2. Extend `BusinessException` or `BaseException`
3. Update handlers if needed

## 🚀 Benefits

- **Scalability**: Easy to add new domains and exceptions
- **Maintainability**: Clear organization and separation of concerns
- **Consistency**: Standardized error handling across the application
- **Clean Architecture**: Follows domain-driven design principles
- **Team Collaboration**: Multiple developers can work on different domains
