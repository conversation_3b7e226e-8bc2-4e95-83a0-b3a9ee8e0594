package com.medication.exception.base;

import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * Base exception class cho tất cả custom exceptions trong application
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Getter
public abstract class BaseException extends RuntimeException {
    
    private final HttpStatus httpStatus;
    private final String errorCode;
    private final String timestamp;

    public BaseException(HttpStatus httpStatus, String errorCode, String message) {
        super(message);
        this.httpStatus = httpStatus;
        this.errorCode = errorCode;
        this.timestamp = java.time.LocalDateTime.now().toString();
    }

    public BaseException(HttpStatus httpStatus, String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.httpStatus = httpStatus;
        this.errorCode = errorCode;
        this.timestamp = java.time.LocalDateTime.now().toString();
    }
} 