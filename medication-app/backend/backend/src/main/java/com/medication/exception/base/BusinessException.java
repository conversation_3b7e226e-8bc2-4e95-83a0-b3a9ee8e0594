package com.medication.exception.base;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public abstract class BusinessException extends RuntimeException {
    private final HttpStatus httpStatus;
    private final String businessCode;

    public BusinessException(HttpStatus httpStatus, String businessCode, String message) {
        super(message);
        this.httpStatus = httpStatus;
        this.businessCode = businessCode;
    }
} 