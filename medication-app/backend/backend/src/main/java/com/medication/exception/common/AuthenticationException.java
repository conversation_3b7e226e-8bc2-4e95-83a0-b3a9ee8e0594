package com.medication.exception.common;

import com.medication.constant.BusinessCode;
import com.medication.exception.base.BusinessException;
import org.springframework.http.HttpStatus;

/**
 * Exception được throw khi có lỗi authentication
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AuthenticationException extends BusinessException {
    
    public AuthenticationException(String message) {
        super(HttpStatus.UNAUTHORIZED, BusinessCode.AUTH_UNAUTHORIZED, message);
    }

    public AuthenticationException(String message, Throwable cause) {
        super(HttpStatus.UNAUTHORIZED, BusinessCode.AUTH_UNAUTHORIZED, message);
        initCause(cause);
    }

    public AuthenticationException() {
        super(HttpStatus.UNAUTHORIZED, BusinessCode.AUTH_UNAUTHORIZED, "Authentication required");
    }
} 