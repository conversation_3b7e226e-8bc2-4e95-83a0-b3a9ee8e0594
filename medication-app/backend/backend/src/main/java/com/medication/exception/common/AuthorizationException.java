package com.medication.exception.common;

import com.medication.constant.BusinessCode;
import com.medication.exception.base.BusinessException;
import org.springframework.http.HttpStatus;

/**
 * Exception được throw khi có lỗi authorization
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AuthorizationException extends BusinessException {
    
    public AuthorizationException(String message) {
        super(HttpStatus.FORBIDDEN, BusinessCode.AUTH_FORBIDDEN, message);
    }

    public AuthorizationException(String message, Throwable cause) {
        super(HttpStatus.FORBIDDEN, BusinessCode.AUTH_FORBIDDEN, message);
        initCause(cause);
    }

    public AuthorizationException() {
        super(HttpStatus.FORBIDDEN, BusinessCode.AUTH_FORBIDDEN, "Access denied");
    }
} 