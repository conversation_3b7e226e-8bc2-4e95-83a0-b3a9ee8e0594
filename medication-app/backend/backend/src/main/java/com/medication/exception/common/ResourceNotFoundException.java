package com.medication.exception.common;

import com.medication.constant.BusinessCode;
import com.medication.exception.base.BusinessException;
import org.springframework.http.HttpStatus;

/**
 * Exception được throw khi resource không tồn tại
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class ResourceNotFoundException extends BusinessException {
    
    public ResourceNotFoundException(String message) {
        super(HttpStatus.NOT_FOUND, BusinessCode.NOT_FOUND, message);
    }

    public ResourceNotFoundException(String resourceType, Long id) {
        super(HttpStatus.NOT_FOUND, BusinessCode.NOT_FOUND,
              resourceType + " not found with ID: " + id);
    }

    public ResourceNotFoundException(String resourceType, String identifier) {
        super(HttpStatus.NOT_FOUND, BusinessCode.NOT_FOUND,
              resourceType + " not found with identifier: " + identifier);
    }

    public ResourceNotFoundException(String message, Throwable cause) {
        super(HttpStatus.NOT_FOUND, BusinessCode.NOT_FOUND, message);
        initCause(cause);
    }
}
