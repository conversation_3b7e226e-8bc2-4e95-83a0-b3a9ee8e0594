package com.medication.exception.common;

import com.medication.constant.BusinessCode;
import com.medication.exception.base.BusinessException;
import org.springframework.http.HttpStatus;

import java.util.Map;

/**
 * Exception được throw khi có lỗi validation
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class ValidationException extends BusinessException {
    
    private final Map<String, String> fieldErrors;

    public ValidationException(String message) {
        super(HttpStatus.BAD_REQUEST, BusinessCode.VALIDATION_ERROR, message);
        this.fieldErrors = null;
    }

    public ValidationException(String message, Map<String, String> fieldErrors) {
        super(HttpStatus.BAD_REQUEST, BusinessCode.VALIDATION_ERROR, message);
        this.fieldErrors = fieldErrors;
    }

    public ValidationException(Map<String, String> fieldErrors) {
        super(HttpStatus.BAD_REQUEST, BusinessCode.VALIDATION_ERROR, "Validation failed");
        this.fieldErrors = fieldErrors;
    }

    public Map<String, String> getFieldErrors() {
        return fieldErrors;
    }

    public boolean hasFieldErrors() {
        return fieldErrors != null && !fieldErrors.isEmpty();
    }
} 