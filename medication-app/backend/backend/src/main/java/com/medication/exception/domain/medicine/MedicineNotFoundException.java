package com.medication.exception.domain.medicine;

/**
 * Exception thrown when medicine is not found
 */
public class MedicineNotFoundException extends RuntimeException {

    public MedicineNotFoundException(String message) {
        super(message);
    }

    public MedicineNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public MedicineNotFoundException(Long medicineId) {
        super("Medicine not found with ID: " + medicineId);
    }
} 