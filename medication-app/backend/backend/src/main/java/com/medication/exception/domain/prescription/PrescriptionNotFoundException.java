package com.medication.exception.domain.prescription;

import java.util.UUID;

/**
 * Exception thrown when a prescription is not found
 */
public class PrescriptionNotFoundException extends RuntimeException {
    
    public PrescriptionNotFoundException(Long id) {
        super("Prescription not found with ID: " + id);
    }
    
    public PrescriptionNotFoundException(UUID anonymousUserId) {
        super("Prescription not found for anonymous user ID: " + anonymousUserId);
    }
    
    public PrescriptionNotFoundException(String message) {
        super(message);
    }
} 