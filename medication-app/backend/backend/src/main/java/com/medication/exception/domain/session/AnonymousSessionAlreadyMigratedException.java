package com.medication.exception.domain.session;

import com.medication.constant.BusinessCode;
import com.medication.exception.base.BusinessException;
import org.springframework.http.HttpStatus;

import java.util.UUID;

/**
 * Exception được throw khi anonymous session đã được migrate
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AnonymousSessionAlreadyMigratedException extends BusinessException {
    
    public AnonymousSessionAlreadyMigratedException(String message) {
        super(HttpStatus.CONFLICT, BusinessCode.SESSION_ALREADY_MIGRATED, message);
    }
    
    public AnonymousSessionAlreadyMigratedException(UUID anonymousUserId, Long userId) {
        super(HttpStatus.CONFLICT, BusinessCode.SESSION_ALREADY_MIGRATED, 
              "Anonymous session " + anonymousUserId + " already migrated to user: " + userId);
    }
    
    public AnonymousSessionAlreadyMigratedException(String message, Throwable cause) {
        super(HttpStatus.CONFLICT, BusinessCode.SESSION_ALREADY_MIGRATED, message);
        initCause(cause);
    }
} 