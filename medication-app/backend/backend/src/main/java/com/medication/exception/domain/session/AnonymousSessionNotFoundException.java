package com.medication.exception.domain.session;

import com.medication.constant.BusinessCode;
import com.medication.exception.base.BusinessException;
import org.springframework.http.HttpStatus;

import java.util.UUID;

/**
 * Exception đượ<PERSON> throw khi không tìm thấy anonymous session
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AnonymousSessionNotFoundException extends BusinessException {
    
    public AnonymousSessionNotFoundException(String message) {
        super(HttpStatus.NOT_FOUND, BusinessCode.SESSION_NOT_FOUND, message);
    }
    
    public AnonymousSessionNotFoundException(UUID anonymousUserId) {
        super(HttpStatus.NOT_FOUND, BusinessCode.SESSION_NOT_FOUND, 
              "Anonymous session not found with ID: " + anonymousUserId);
    }
    
    public AnonymousSessionNotFoundException(String message, Throwable cause) {
        super(HttpStatus.NOT_FOUND, BusinessCode.SESSION_NOT_FOUND, message);
        initCause(cause);
    }
} 