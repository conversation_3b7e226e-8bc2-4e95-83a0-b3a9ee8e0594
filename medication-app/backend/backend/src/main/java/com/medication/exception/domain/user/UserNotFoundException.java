package com.medication.exception.domain.user;

import com.medication.constant.BusinessCode;
import com.medication.exception.base.BusinessException;
import org.springframework.http.HttpStatus;

/**
 * Exception được throw khi user không tồn tại
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class UserNotFoundException extends BusinessException {
    
    public UserNotFoundException(String message) {
        super(HttpStatus.NOT_FOUND, BusinessCode.USER_NOT_FOUND, message);
    }
    
    public UserNotFoundException(Long userId) {
        super(HttpStatus.NOT_FOUND, BusinessCode.USER_NOT_FOUND, 
              "User not found with ID: " + userId);
    }
    
    public UserNotFoundException(String message, Throwable cause) {
        super(HttpStatus.NOT_FOUND, BusinessCode.USER_NOT_FOUND, message);
        initCause(cause);
    }
} 