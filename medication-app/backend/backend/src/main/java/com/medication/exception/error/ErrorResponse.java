package com.medication.exception.error;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Map;

/**
 * Standard error response model cho tất cả API errors
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorResponse {
    
    private boolean success;
    private int statusCode;
    private String code;
    private String message;
    private String timestamp;
    private Map<String, String> fieldErrors;
    private String path;
    private String method;

    public static ErrorResponse of(int statusCode, String code, String message) {
        return ErrorResponse.builder()
                .success(false)
                .statusCode(statusCode)
                .code(code)
                .message(message)
                .timestamp(Instant.now().toString())
                .build();
    }

    public static ErrorResponse of(int statusCode, String code, String message, Map<String, String> fieldErrors) {
        return ErrorResponse.builder()
                .success(false)
                .statusCode(statusCode)
                .code(code)
                .message(message)
                .timestamp(Instant.now().toString())
                .fieldErrors(fieldErrors)
                .build();
    }

    public static ErrorResponse of(int statusCode, String code, String message, String path, String method) {
        return ErrorResponse.builder()
                .success(false)
                .statusCode(statusCode)
                .code(code)
                .message(message)
                .timestamp(Instant.now().toString())
                .path(path)
                .method(method)
                .build();
    }
} 