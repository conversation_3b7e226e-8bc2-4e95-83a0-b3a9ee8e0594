package com.medication.exception.handler;

import com.medication.constant.BusinessCode;
import com.medication.dto.response.ApiResponse;
import com.medication.exception.base.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    private <T> ResponseEntity<ApiResponse<T>> buildError(HttpStatus status, String code, String message, T data) {
        return ResponseEntity.status(status)
                .body(ApiResponse.error(status.value(), code, message, data));
    }

    private ResponseEntity<ApiResponse<Object>> buildError(HttpStatus status, String code, String message) {
        return buildError(status, code, message, null);
    }

    /**
     * Xử lý tất cả BusinessException
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(BusinessException ex) {
        log.warn("Business exception: {}", ex.getMessage());
        return buildError(ex.getHttpStatus(), ex.getBusinessCode(), ex.getMessage());
    }

    /**
     * Xử lý lỗi validation @Valid
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleValidationErrors(MethodArgumentNotValidException ex) {
        log.warn("Validation error: {}", ex.getMessage());

        Map<String, String> fieldErrors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            fieldErrors.put(fieldName, errorMessage);
        });

        return buildError(HttpStatus.BAD_REQUEST, BusinessCode.VALIDATION_ERROR, "Validation failed", fieldErrors);
    }

    /**
     * Xử lý khi gọi sai HTTP method
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiResponse<Object>> handleMethodNotSupported(HttpRequestMethodNotSupportedException ex) {
        log.warn("Method not supported: {}", ex.getMessage());
        return buildError(HttpStatus.METHOD_NOT_ALLOWED, BusinessCode.INVALID_REQUEST, "HTTP method not supported");
    }

    /**
     * Xử lý khi gửi sai Content-Type
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<ApiResponse<Object>> handleMediaTypeNotSupported(HttpMediaTypeNotSupportedException ex) {
        log.warn("Media type not supported: {}", ex.getMessage());
        return buildError(HttpStatus.UNSUPPORTED_MEDIA_TYPE, BusinessCode.INVALID_REQUEST, "Content-Type not supported");
    }

    /**
     * Xử lý lỗi không mong muốn
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(Exception ex) {
        log.error("Unexpected error occurred", ex);
        return buildError(HttpStatus.INTERNAL_SERVER_ERROR, BusinessCode.INTERNAL_ERROR, "An unexpected error occurred");
    }

    /**
     * Handle method argument type mismatch (e.g., invalid enum values)
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleTypeMismatch(MethodArgumentTypeMismatchException ex) {
        log.warn("Type mismatch for parameter '{}': {}", ex.getName(), ex.getMessage());

        String message = String.format("Invalid value '%s' for parameter '%s'", ex.getValue(), ex.getName());

        // Special handling for enum types
        Class<?> requiredType = ex.getRequiredType();
        if (requiredType != null && requiredType.isEnum()) {
            Object[] enumConstants = requiredType.getEnumConstants();
            StringBuilder validValues = new StringBuilder();
            for (int i = 0; i < enumConstants.length; i++) {
                if (i > 0) validValues.append(", ");
                validValues.append(enumConstants[i]);
            }
            message += ". Valid values are: " + validValues.toString();
        }

        return ResponseEntity.badRequest()
                .body(ApiResponse.badRequest(message));
    }
}