package com.medication.exception.handler;

import com.medication.dto.response.ApiResponse;
import com.medication.exception.common.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * Handler cho validation exceptions
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Slf4j
@RestControllerAdvice
public class ValidationExceptionHandler {

    /**
     * Xử lý ValidationException
     */
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleValidationException(ValidationException ex) {
        log.warn("Validation exception: {}", ex.getMessage());
        
        if (ex.hasFieldErrors()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(HttpStatus.BAD_REQUEST.value(), 
                                          ex.getBusinessCode(), 
                                          ex.getMessage(), 
                                          ex.getFieldErrors()));
        } else {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(HttpStatus.BAD_REQUEST.value(), 
                                          ex.getBusinessCode(), 
                                          ex.getMessage(), 
                                          null));
        }
    }

    /**
     * Xử lý MethodArgumentNotValidException (Spring validation)
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleMethodArgumentNotValid(MethodArgumentNotValidException ex) {
        log.warn("Method argument not valid: {}", ex.getMessage());

        Map<String, String> fieldErrors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            fieldErrors.put(fieldName, errorMessage);
        });

        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(HttpStatus.BAD_REQUEST.value(), 
                                      "VALIDATION_ERROR", 
                                      "Validation failed", 
                                      fieldErrors));
    }
} 