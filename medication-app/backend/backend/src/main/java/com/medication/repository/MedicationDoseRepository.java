package com.medication.repository;

import com.medication.entity.MedicationDose;
import com.medication.enums.DoseStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Repository interface for MedicationDose entity
 * Provides data access methods for real compliance tracking
 */
@Repository
public interface MedicationDoseRepository extends JpaRepository<MedicationDose, Long> {

    // ==================== BASIC QUERIES ====================
    
    /**
     * Find doses by medication schedule ID
     */
    List<MedicationDose> findByMedicationSchedule_Id(Long scheduleId);
    
    /**
     * Find doses by status
     */
    List<MedicationDose> findByStatus(DoseStatus status);
    
    /**
     * Find doses by scheduled date range
     */
    List<MedicationDose> findByScheduledDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    // ==================== COMPLIANCE ANALYTICS FOR REGISTERED USERS ====================
    
    /**
     * Count total scheduled doses for registered user
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId")
    long countTotalScheduledDosesByUserId(@Param("userId") Long userId);
    
    /**
     * Count doses by status for registered user
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND md.status = :status")
    long countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") DoseStatus status);
    
    /**
     * Count doses by multiple statuses for registered user
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND md.status IN :statuses")
    long countByUserIdAndStatusIn(@Param("userId") Long userId, @Param("statuses") List<DoseStatus> statuses);
    
    /**
     * Count on-time doses for registered user (taken within tolerance minutes)
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId " +
           "AND md.status IN ('TAKEN', 'PARTIAL') " +
           "AND md.takenDate IS NOT NULL " +
           "AND ABS(EXTRACT(EPOCH FROM md.takenDate) - EXTRACT(EPOCH FROM md.scheduledDate)) <= :toleranceMinutes * 60")
    long countOnTimeDosesByUserId(@Param("userId") Long userId, @Param("toleranceMinutes") int toleranceMinutes);
    
    /**
     * Count late doses for registered user (taken after tolerance minutes)
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId " +
           "AND md.status IN ('TAKEN', 'PARTIAL') " +
           "AND md.takenDate IS NOT NULL " +
           "AND (EXTRACT(EPOCH FROM md.takenDate) - EXTRACT(EPOCH FROM md.scheduledDate)) > :toleranceMinutes * 60")
    long countLateDosesByUserId(@Param("userId") Long userId, @Param("toleranceMinutes") int toleranceMinutes);

    // ==================== COMPLIANCE ANALYTICS FOR ANONYMOUS USERS ====================
    
    /**
     * Count total scheduled doses for anonymous user
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId")
    long countTotalScheduledDosesByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId);
    
    /**
     * Count doses by status for anonymous user
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND md.status = :status")
    long countByAnonymousUserIdAndStatus(@Param("anonymousUserId") UUID anonymousUserId, @Param("status") DoseStatus status);
    
    /**
     * Count doses by multiple statuses for anonymous user
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND md.status IN :statuses")
    long countByAnonymousUserIdAndStatusIn(@Param("anonymousUserId") UUID anonymousUserId, @Param("statuses") List<DoseStatus> statuses);
    
    /**
     * Count on-time doses for anonymous user (taken within tolerance minutes)
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId " +
           "AND md.status IN ('TAKEN', 'PARTIAL') " +
           "AND md.takenDate IS NOT NULL " +
           "AND ABS(EXTRACT(EPOCH FROM md.takenDate) - EXTRACT(EPOCH FROM md.scheduledDate)) <= :toleranceMinutes * 60")
    long countOnTimeDosesByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId, @Param("toleranceMinutes") int toleranceMinutes);
    
    /**
     * Count late doses for anonymous user (taken after tolerance minutes)
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId " +
           "AND md.status IN ('TAKEN', 'PARTIAL') " +
           "AND md.takenDate IS NOT NULL " +
           "AND (EXTRACT(EPOCH FROM md.takenDate) - EXTRACT(EPOCH FROM md.scheduledDate)) > :toleranceMinutes * 60")
    long countLateDosesByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId, @Param("toleranceMinutes") int toleranceMinutes);

    // ==================== TIME-BASED ANALYTICS ====================
    
    /**
     * Count doses created in last N days for registered user
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId " +
           "AND md.scheduledDate >= :startDate")
    long countByUserIdAndScheduledDateAfter(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate);
    
    /**
     * Count doses created in last N days for anonymous user
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId " +
           "AND md.scheduledDate >= :startDate")
    long countByAnonymousUserIdAndScheduledDateAfter(@Param("anonymousUserId") UUID anonymousUserId, @Param("startDate") LocalDateTime startDate);

    // ==================== DETAILED ANALYTICS ====================
    
    /**
     * Get compliance statistics for registered user
     * Simplified version without complex CASE statements
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId")
    Long getTotalDosesByUserId(@Param("userId") Long userId);

    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND md.status IN ('TAKEN', 'PARTIAL')")
    Long getTakenDosesByUserId(@Param("userId") Long userId);

    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND md.status IN ('MISSED', 'SKIPPED')")
    Long getMissedDosesByUserId(@Param("userId") Long userId);

    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND md.status IN ('TAKEN', 'PARTIAL') " +
           "AND md.takenDate IS NOT NULL " +
           "AND ABS(EXTRACT(EPOCH FROM md.takenDate) - EXTRACT(EPOCH FROM md.scheduledDate)) <= :toleranceMinutes * 60")
    Long getOnTimeDosesByUserId(@Param("userId") Long userId, @Param("toleranceMinutes") int toleranceMinutes);
    
    /**
     * Get compliance statistics for anonymous user
     * Simplified version without complex CASE statements
     */
    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId")
    Long getTotalDosesByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId);

    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND md.status IN ('TAKEN', 'PARTIAL')")
    Long getTakenDosesByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId);

    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND md.status IN ('MISSED', 'SKIPPED')")
    Long getMissedDosesByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId);

    @Query("SELECT COUNT(md) FROM MedicationDose md " +
           "JOIN md.medicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND md.status IN ('TAKEN', 'PARTIAL') " +
           "AND md.takenDate IS NOT NULL " +
           "AND ABS(EXTRACT(EPOCH FROM md.takenDate) - EXTRACT(EPOCH FROM md.scheduledDate)) <= :toleranceMinutes * 60")
    Long getOnTimeDosesByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId, @Param("toleranceMinutes") int toleranceMinutes);
}
