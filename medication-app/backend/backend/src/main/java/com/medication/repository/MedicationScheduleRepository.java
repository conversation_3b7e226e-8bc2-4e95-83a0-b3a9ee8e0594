package com.medication.repository;

import com.medication.entity.MedicationSchedule;
import com.medication.enums.DoseUnit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for MedicationSchedule entity
 * Provides data access methods for both anonymous and registered users
 */
@Repository
public interface MedicationScheduleRepository extends JpaRepository<MedicationSchedule, Long> {

    // ==================== BASIC CRUD OPERATIONS ====================
    
    /**
     * Find medication schedule by ID for registered user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE ms.id = :scheduleId AND p.patient.id = :userId")
    Optional<MedicationSchedule> findByIdAndUserId(@Param("scheduleId") Long scheduleId,
                                                   @Param("userId") Long userId);

    /**
     * Find medication schedule by ID for anonymous user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE ms.id = :scheduleId AND p.anonymousUserId = :anonymousUserId")
    Optional<MedicationSchedule> findByIdAndAnonymousUserId(@Param("scheduleId") Long scheduleId, 
                                                            @Param("anonymousUserId") UUID anonymousUserId);

    // ==================== FIND BY PRESCRIPTION ITEM ====================
    
    /**
     * Find all medication schedules by prescription item ID for registered user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE pi.id = :prescriptionItemId AND p.patient.id = :userId " +
           "ORDER BY ms.dayOfWeek ASC, ms.time ASC")
    List<MedicationSchedule> findByPrescriptionItemIdAndUserId(@Param("prescriptionItemId") Long prescriptionItemId,
                                                               @Param("userId") Long userId);

    /**
     * Find all medication schedules by prescription item ID for anonymous user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE pi.id = :prescriptionItemId AND p.anonymousUserId = :anonymousUserId " +
           "ORDER BY ms.dayOfWeek ASC, ms.time ASC")
    List<MedicationSchedule> findByPrescriptionItemIdAndAnonymousUserId(@Param("prescriptionItemId") Long prescriptionItemId,
                                                                        @Param("anonymousUserId") UUID anonymousUserId);

    /**
     * Find active medication schedules by prescription item ID for registered user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE pi.id = :prescriptionItemId AND p.patient.id = :userId AND ms.isActive = true " +
           "ORDER BY ms.dayOfWeek ASC, ms.time ASC")
    List<MedicationSchedule> findActiveByPrescriptionItemIdAndUserId(@Param("prescriptionItemId") Long prescriptionItemId,
                                                                     @Param("userId") Long userId);

    /**
     * Find active medication schedules by prescription item ID for anonymous user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE pi.id = :prescriptionItemId AND p.anonymousUserId = :anonymousUserId AND ms.isActive = true " +
           "ORDER BY ms.dayOfWeek ASC, ms.time ASC")
    List<MedicationSchedule> findActiveByPrescriptionItemIdAndAnonymousUserId(@Param("prescriptionItemId") Long prescriptionItemId,
                                                                              @Param("anonymousUserId") UUID anonymousUserId);

    // ==================== FIND BY USER WITH PAGINATION ====================
    
    /**
     * Find all medication schedules for registered user with pagination
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId")
    Page<MedicationSchedule> findByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * Find all medication schedules for anonymous user with pagination
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId")
    Page<MedicationSchedule> findByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId, Pageable pageable);

    /**
     * Find active medication schedules for registered user with pagination
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND ms.isActive = true")
    Page<MedicationSchedule> findActiveByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * Find active medication schedules for anonymous user with pagination
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND ms.isActive = true")
    Page<MedicationSchedule> findActiveByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId, Pageable pageable);

    // ==================== FIND BY DAY OF WEEK ====================
    
    /**
     * Find medication schedules by day of week for registered user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND ms.dayOfWeek = :dayOfWeek AND ms.isActive = true " +
           "ORDER BY ms.time ASC")
    List<MedicationSchedule> findByUserIdAndDayOfWeek(@Param("userId") Long userId, 
                                                      @Param("dayOfWeek") Integer dayOfWeek);

    /**
     * Find medication schedules by day of week for anonymous user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND ms.dayOfWeek = :dayOfWeek AND ms.isActive = true " +
           "ORDER BY ms.time ASC")
    List<MedicationSchedule> findByAnonymousUserIdAndDayOfWeek(@Param("anonymousUserId") UUID anonymousUserId, 
                                                               @Param("dayOfWeek") Integer dayOfWeek);

    // ==================== FIND BY TIME RANGE ====================
    
    /**
     * Find medication schedules by time range for registered user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND ms.time BETWEEN :startTime AND :endTime AND ms.isActive = true " +
           "ORDER BY ms.time ASC")
    List<MedicationSchedule> findByUserIdAndTimeBetween(@Param("userId") Long userId,
                                                        @Param("startTime") LocalTime startTime,
                                                        @Param("endTime") LocalTime endTime);

    /**
     * Find medication schedules by time range for anonymous user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND ms.time BETWEEN :startTime AND :endTime AND ms.isActive = true " +
           "ORDER BY ms.time ASC")
    List<MedicationSchedule> findByAnonymousUserIdAndTimeBetween(@Param("anonymousUserId") UUID anonymousUserId,
                                                                 @Param("startTime") LocalTime startTime,
                                                                 @Param("endTime") LocalTime endTime);

    // ==================== FIND BY DOSE CRITERIA ====================
    
    /**
     * Find medication schedules by dose unit for registered user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND ms.doseUnit = :doseUnit AND ms.isActive = true")
    List<MedicationSchedule> findByUserIdAndDoseUnit(@Param("userId") Long userId, 
                                                     @Param("doseUnit") DoseUnit doseUnit);

    /**
     * Find medication schedules by dose unit for anonymous user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND ms.doseUnit = :doseUnit AND ms.isActive = true")
    List<MedicationSchedule> findByAnonymousUserIdAndDoseUnit(@Param("anonymousUserId") UUID anonymousUserId, 
                                                              @Param("doseUnit") DoseUnit doseUnit);

    /**
     * Find medication schedules by dose amount range for registered user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND ms.doseAmount BETWEEN :minDose AND :maxDose AND ms.isActive = true")
    List<MedicationSchedule> findByUserIdAndDoseAmountBetween(@Param("userId") Long userId,
                                                              @Param("minDose") Double minDose,
                                                              @Param("maxDose") Double maxDose);

    /**
     * Find medication schedules by dose amount range for anonymous user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND ms.doseAmount BETWEEN :minDose AND :maxDose AND ms.isActive = true")
    List<MedicationSchedule> findByAnonymousUserIdAndDoseAmountBetween(@Param("anonymousUserId") UUID anonymousUserId,
                                                                       @Param("minDose") Double minDose,
                                                                       @Param("maxDose") Double maxDose);

    // ==================== SEARCH BY NOTES ====================
    
    /**
     * Find medication schedules by notes containing text for registered user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND LOWER(ms.notes) LIKE LOWER(CONCAT('%', :searchText, '%')) AND ms.isActive = true")
    List<MedicationSchedule> findByUserIdAndNotesContainingIgnoreCase(@Param("userId") Long userId,
                                                                      @Param("searchText") String searchText);

    /**
     * Find medication schedules by notes containing text for anonymous user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND LOWER(ms.notes) LIKE LOWER(CONCAT('%', :searchText, '%')) AND ms.isActive = true")
    List<MedicationSchedule> findByAnonymousUserIdAndNotesContainingIgnoreCase(@Param("anonymousUserId") UUID anonymousUserId,
                                                                               @Param("searchText") String searchText);

    // ==================== COUNT OPERATIONS ====================
    
    /**
     * Count medication schedules by prescription item ID for registered user
     */
    @Query("SELECT COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE pi.id = :prescriptionItemId AND p.patient.id = :userId")
    long countByPrescriptionItemIdAndUserId(@Param("prescriptionItemId") Long prescriptionItemId,
                                            @Param("userId") Long userId);

    /**
     * Count medication schedules by prescription item ID for anonymous user
     */
    @Query("SELECT COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE pi.id = :prescriptionItemId AND p.anonymousUserId = :anonymousUserId")
    long countByPrescriptionItemIdAndAnonymousUserId(@Param("prescriptionItemId") Long prescriptionItemId,
                                                     @Param("anonymousUserId") UUID anonymousUserId);

    /**
     * Count active medication schedules for registered user
     */
    @Query("SELECT COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND ms.isActive = true")
    long countActiveByUserId(@Param("userId") Long userId);

    /**
     * Count active medication schedules for anonymous user
     */
    @Query("SELECT COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND ms.isActive = true")
    long countActiveByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId);

    // ==================== EXISTS OPERATIONS ====================
    
    /**
     * Check if medication schedule exists for prescription item and registered user
     */
    @Query("SELECT CASE WHEN COUNT(ms) > 0 THEN true ELSE false END FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE pi.id = :prescriptionItemId AND p.patient.id = :userId")
    boolean existsByPrescriptionItemIdAndUserId(@Param("prescriptionItemId") Long prescriptionItemId,
                                                @Param("userId") Long userId);

    /**
     * Check if medication schedule exists for prescription item and anonymous user
     */
    @Query("SELECT CASE WHEN COUNT(ms) > 0 THEN true ELSE false END FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE pi.id = :prescriptionItemId AND p.anonymousUserId = :anonymousUserId")
    boolean existsByPrescriptionItemIdAndAnonymousUserId(@Param("prescriptionItemId") Long prescriptionItemId,
                                                         @Param("anonymousUserId") UUID anonymousUserId);

    // ==================== DATE RANGE OPERATIONS ====================
    
    /**
     * Find medication schedules created between dates for registered user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND ms.createdAt BETWEEN :startDate AND :endDate")
    List<MedicationSchedule> findByUserIdAndCreatedAtBetween(@Param("userId") Long userId,
                                                             @Param("startDate") LocalDateTime startDate,
                                                             @Param("endDate") LocalDateTime endDate);

    /**
     * Find medication schedules created between dates for anonymous user
     */
    @Query("SELECT ms FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND ms.createdAt BETWEEN :startDate AND :endDate")
    List<MedicationSchedule> findByAnonymousUserIdAndCreatedAtBetween(@Param("anonymousUserId") UUID anonymousUserId,
                                                                      @Param("startDate") LocalDateTime startDate,
                                                                      @Param("endDate") LocalDateTime endDate);

    // ==================== ANALYTICS & STATISTICS OPERATIONS ====================

    /**
     * Count total medication schedules for registered user
     */
    @Query("SELECT COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId")
    long countByUserId(@Param("userId") Long userId);

    /**
     * Count total medication schedules for anonymous user
     */
    @Query("SELECT COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId")
    long countByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId);

    /**
     * Count inactive medication schedules for registered user
     */
    @Query("SELECT COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND ms.isActive = false")
    long countInactiveByUserId(@Param("userId") Long userId);

    /**
     * Count inactive medication schedules for anonymous user
     */
    @Query("SELECT COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND ms.isActive = false")
    long countInactiveByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId);

    /**
     * Count schedules created in last N days for registered user
     */
    @Query("SELECT COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId AND ms.createdAt >= :sinceDate")
    long countByUserIdAndCreatedAtAfter(@Param("userId") Long userId,
                                        @Param("sinceDate") LocalDateTime sinceDate);

    /**
     * Count schedules created in last N days for anonymous user
     */
    @Query("SELECT COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId AND ms.createdAt >= :sinceDate")
    long countByAnonymousUserIdAndCreatedAtAfter(@Param("anonymousUserId") UUID anonymousUserId,
                                                 @Param("sinceDate") LocalDateTime sinceDate);

    /**
     * Get schedule count by dose unit for registered user
     */
    @Query("SELECT ms.doseUnit, COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId " +
           "GROUP BY ms.doseUnit")
    List<Object[]> countByUserIdGroupByDoseUnit(@Param("userId") Long userId);

    /**
     * Get schedule count by dose unit for anonymous user
     */
    @Query("SELECT ms.doseUnit, COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId " +
           "GROUP BY ms.doseUnit")
    List<Object[]> countByAnonymousUserIdGroupByDoseUnit(@Param("anonymousUserId") UUID anonymousUserId);

    /**
     * Get schedule count by day of week for registered user
     */
    @Query("SELECT ms.dayOfWeek, COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId " +
           "GROUP BY ms.dayOfWeek " +
           "ORDER BY ms.dayOfWeek")
    List<Object[]> countByUserIdGroupByDayOfWeek(@Param("userId") Long userId);

    /**
     * Get schedule count by day of week for anonymous user
     */
    @Query("SELECT ms.dayOfWeek, COUNT(ms) FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId " +
           "GROUP BY ms.dayOfWeek " +
           "ORDER BY ms.dayOfWeek")
    List<Object[]> countByAnonymousUserIdGroupByDayOfWeek(@Param("anonymousUserId") UUID anonymousUserId);

    /**
     * Get schedule count by week for registered user
     * Returns year and week as separate values
     */
    @Query("SELECT EXTRACT(YEAR FROM ms.createdAt), EXTRACT(WEEK FROM ms.createdAt), COUNT(ms) " +
           "FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId " +
           "GROUP BY EXTRACT(YEAR FROM ms.createdAt), EXTRACT(WEEK FROM ms.createdAt) " +
           "ORDER BY EXTRACT(YEAR FROM ms.createdAt), EXTRACT(WEEK FROM ms.createdAt)")
    List<Object[]> countByUserIdGroupByWeek(@Param("userId") Long userId);

    /**
     * Get schedule count by week for anonymous user
     * Returns year and week as separate values
     */
    @Query("SELECT EXTRACT(YEAR FROM ms.createdAt), EXTRACT(WEEK FROM ms.createdAt), COUNT(ms) " +
           "FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId " +
           "GROUP BY EXTRACT(YEAR FROM ms.createdAt), EXTRACT(WEEK FROM ms.createdAt) " +
           "ORDER BY EXTRACT(YEAR FROM ms.createdAt), EXTRACT(WEEK FROM ms.createdAt)")
    List<Object[]> countByAnonymousUserIdGroupByWeek(@Param("anonymousUserId") UUID anonymousUserId);

    /**
     * Get schedule count by month for registered user
     * Returns year and month as separate values
     */
    @Query("SELECT EXTRACT(YEAR FROM ms.createdAt), EXTRACT(MONTH FROM ms.createdAt), COUNT(ms) " +
           "FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.patient.id = :userId " +
           "GROUP BY EXTRACT(YEAR FROM ms.createdAt), EXTRACT(MONTH FROM ms.createdAt) " +
           "ORDER BY EXTRACT(YEAR FROM ms.createdAt), EXTRACT(MONTH FROM ms.createdAt)")
    List<Object[]> countByUserIdGroupByMonth(@Param("userId") Long userId);

    /**
     * Get schedule count by month for anonymous user
     * Returns year and month as separate values
     */
    @Query("SELECT EXTRACT(YEAR FROM ms.createdAt), EXTRACT(MONTH FROM ms.createdAt), COUNT(ms) " +
           "FROM MedicationSchedule ms " +
           "JOIN ms.prescriptionItem pi " +
           "JOIN pi.prescription p " +
           "WHERE p.anonymousUserId = :anonymousUserId " +
           "GROUP BY EXTRACT(YEAR FROM ms.createdAt), EXTRACT(MONTH FROM ms.createdAt) " +
           "ORDER BY EXTRACT(YEAR FROM ms.createdAt), EXTRACT(MONTH FROM ms.createdAt)")
    List<Object[]> countByAnonymousUserIdGroupByMonth(@Param("anonymousUserId") UUID anonymousUserId);

}
