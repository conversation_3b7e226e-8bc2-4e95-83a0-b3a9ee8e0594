# Repository Package Structure

## 📁 Overview

Package này được tổ chức theo Clean Architecture principles với domain-based organization cho từng business domain, tương ứng với service package.

## 🏗️ Directory Structure

```
repository/
├── domain/                           # Domain-specific repositories
│   ├── user/                         # User domain
│   │   └── UserRepository.java
│   ├── session/                      # Session domain
│   │   └── AnonymousSessionRepository.java
│   ├── prescription/                 # Prescription domain
│   │   └── PrescriptionRepository.java
│   ├── medicine/                     # Medicine domain
│   │   └── MedicineRepository.java
│   ├── family/                       # Family domain
│   │   └── FamilyRepository.java
│   └── notification/                 # Notification domain
│       └── NotificationRepository.java
└── common/                           # Common repositories
    └── AuditLogRepository.java
```

## 🎯 Domain Organization

### **User Domain**

- **UserRepository**: Data access for User entity
  - `findByEmail(String email)`: Find user by email
  - `existsByEmail(String email)`: Check if user exists by email

### **Session Domain**

- **AnonymousSessionRepository**: Data access for AnonymousSession entity
  - `findByAnonymousUserId(UUID)`: Find session by anonymous user ID
  - `findByDeviceId(String)`: Find sessions by device ID
  - `findByStatus(AnonymousSessionStatus)`: Find sessions by status
  - `findByMigratedToUserId(Long)`: Find sessions by migrated user ID
  - `findByMigratedToUserIdIsNull()`: Find sessions not yet migrated
  - `existsByAnonymousUserId(UUID)`: Check if session exists
  - `countByCreatedAtAfter(LocalDateTime)`: Count sessions created after date
  - `countByLastActivityAtAfter(LocalDateTime)`: Count sessions with recent activity
  - `countByDeviceId(String)`: Count sessions by device ID
  - `updateLastActivity(UUID, LocalDateTime)`: Update last activity time
  - `findInactiveSessions(LocalDateTime, AnonymousSessionStatus)`: Find inactive sessions for cleanup
  - Custom queries for date ranges and statistics

### **Prescription Domain**

- **PrescriptionRepository**: Data access for Prescription entity
  - Basic CRUD operations via JpaRepository
  - TODO: Add domain-specific query methods

### **Medicine Domain**

- **MedicineRepository**: Data access for Medicine entity
  - Basic CRUD operations via JpaRepository
  - TODO: Add domain-specific query methods

### **Family Domain**

- **FamilyRepository**: Data access for Family entity
  - Basic CRUD operations via JpaRepository
  - TODO: Add domain-specific query methods

### **Notification Domain**

- **NotificationRepository**: Data access for Notification entity
  - Basic CRUD operations via JpaRepository
  - TODO: Add domain-specific query methods

### **Common Repositories**

- **AuditLogRepository**: Data access for AuditLog entity
  - Basic CRUD operations via JpaRepository
  - TODO: Add audit-specific query methods

## 🔧 Usage Examples

### **Using Domain Repositories**

```java
@Service
public class UserService {

    private final UserRepository userRepository;

    public User findByEmail(String email) {
        return userRepository.findByEmail(email)
            .orElseThrow(() -> new UserNotFoundException("User not found"));
    }

    public boolean userExists(String email) {
        return userRepository.existsByEmail(email);
    }
}
```

### **Using Session Repository**

```java
@Service
public class SessionService {

    private final AnonymousSessionRepository sessionRepository;

    public AnonymousSession findByAnonymousUserId(UUID anonymousUserId) {
        return sessionRepository.findByAnonymousUserId(anonymousUserId)
            .orElseThrow(() -> new AnonymousSessionNotFoundException("Session not found"));
    }

    public List<AnonymousSession> findActiveSessions() {
        return sessionRepository.findByStatus(AnonymousSessionStatus.ACTIVE);
    }

    public void updateLastActivity(UUID anonymousUserId) {
        sessionRepository.updateLastActivity(anonymousUserId, LocalDateTime.now());
    }

    public long getSessionStats() {
        return sessionRepository.countByCreatedAtAfter(LocalDateTime.now().minusDays(30));
    }
}
```

### **Using Custom Queries**

```java
@Service
public class SessionAnalyticsService {

    private final AnonymousSessionRepository sessionRepository;

    public List<AnonymousSession> getSessionsCreatedBetween(LocalDateTime start, LocalDateTime end) {
        return sessionRepository.findSessionsCreatedBetween(start, end);
    }

    public List<AnonymousSession> getExpiredSessions() {
        LocalDateTime expiryDate = LocalDateTime.now().minusDays(7);
        return sessionRepository.findExpiredSessions(expiryDate);
    }

    public void cleanupInactiveSessions() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
        List<AnonymousSession> inactiveSessions = sessionRepository.findInactiveSessions(
            cutoffTime, AnonymousSessionStatus.ACTIVE);
        // Process cleanup logic
    }
}
```

## 🔧 Best Practices

1. **Domain Separation**: Each domain has its own package with related repositories
2. **Method Naming**: Use Spring Data JPA naming conventions
3. **Custom Queries**: Use @Query for complex queries
4. **Modifying Queries**: Use @Modifying for update/delete operations
5. **Parameter Binding**: Use @Param for named parameters
6. **Return Types**: Use appropriate return types (Optional, List, etc.)
7. **Documentation**: Document all custom methods with JavaDoc

## 📝 Adding New Repositories

### **For New Domain**

1. Create domain directory: `repository/domain/newdomain/`
2. Create repository interface: `NewDomainRepository.java`
3. Extend JpaRepository with appropriate entity and ID type
4. Add domain-specific query methods
5. Update this README

### **For Common Repositories**

1. Add to `repository/common/` directory
2. Ensure it's truly common across domains
3. Update this README

## 🚀 Benefits

- **Scalability**: Easy to add new domains and repositories
- **Maintainability**: Clear organization and separation of concerns
- **Clean Architecture**: Follows domain-driven design principles
- **Team Collaboration**: Multiple developers can work on different domains
- **Testability**: Repositories can be easily unit tested
- **Consistency**: Aligns with service package structure

## 🔄 Migration Notes

This structure was refactored from a flat organization to domain-based organization. All existing repositories have been moved to appropriate domains and package declarations have been updated.

### **Key Changes:**

- Repositories moved to domain-specific packages
- Package declarations updated
- Imports updated in service classes
- Enhanced AnonymousSessionRepository with comprehensive methods
- Added proper documentation and JavaDoc

### **Breaking Changes:**

- Import statements need to be updated in classes that use these repositories
- Package declarations changed for all moved repositories

## 🔗 Related Packages

- **Service Package**: Corresponding business logic services
- **Entity Package**: JPA entities that repositories manage
- **DTO Package**: Data transfer objects for API responses
