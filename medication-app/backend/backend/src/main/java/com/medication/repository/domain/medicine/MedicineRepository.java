package com.medication.repository.domain.medicine;

import com.medication.entity.Medicine;
import com.medication.enums.UserType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Repository for Medicine entity
 */
@Repository
public interface MedicineRepository extends JpaRepository<Medicine, Long> {
    
    /**
     * Find medicines by user ID
     */
    List<Medicine> findByUser_Id(Long userId);
    
    /**
     * Find medicines by user ID with pagination
     */
    Page<Medicine> findByUser_Id(Long userId, Pageable pageable);
    
    /**
     * Find medicines by anonymous user ID
     */
    List<Medicine> findByAnonymousUserId(UUID anonymousUserId);
    
    /**
     * Find medicines by anonymous user ID with pagination
     */
    Page<Medicine> findByAnonymousUserId(UUID anonymousUserId, Pageable pageable);
    
    /**
     * Find medicines by user type
     */
    List<Medicine> findByUserType(UserType userType);
    
    /**
     * Find medicines by user type with pagination
     */
    Page<Medicine> findByUserType(UserType userType, Pageable pageable);
    
    /**
     * Find medicines by user ID and user type
     */
    List<Medicine> findByUser_IdAndUserType(Long userId, UserType userType);
    
    /**
     * Find medicines by anonymous user ID and user type
     */
    List<Medicine> findByAnonymousUserIdAndUserType(UUID anonymousUserId, UserType userType);
    
    /**
     * Find active medicines by user ID
     */
    List<Medicine> findByUser_IdAndIsActiveTrue(Long userId);
    
    /**
     * Find active medicines by anonymous user ID
     */
    List<Medicine> findByAnonymousUserIdAndIsActiveTrue(UUID anonymousUserId);
    
    /**
     * Find medicines by family ID
     */
    List<Medicine> findByFamilyId(Long familyId);
    
    /**
     * Find medicines by family ID with pagination
     */
    Page<Medicine> findByFamilyId(Long familyId, Pageable pageable);
    
    /**
     * Find medicines by medicine type ID
     */
    List<Medicine> findByMedicineTypeId(Long medicineTypeId);
    
    /**
     * Find medicines by medicine type ID with pagination
     */
    Page<Medicine> findByMedicineTypeId(Long medicineTypeId, Pageable pageable);
    
    /**
     * Find medicines by name (case insensitive)
     */
    List<Medicine> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find medicines by name and user ID (case insensitive)
     */
    List<Medicine> findByNameContainingIgnoreCaseAndUser_Id(String name, Long userId);
    
    /**
     * Find medicines by name and anonymous user ID (case insensitive)
     */
    List<Medicine> findByNameContainingIgnoreCaseAndAnonymousUserId(String name, UUID anonymousUserId);
    
    /**
     * Find medicines expiring before specific date
     */
    List<Medicine> findByExpiryDateBefore(LocalDate date);
    
    /**
     * Find medicines expiring before specific date by user ID
     */
    List<Medicine> findByExpiryDateBeforeAndUser_Id(LocalDate date, Long userId);
    
    /**
     * Find medicines expiring before specific date by anonymous user ID
     */
    List<Medicine> findByExpiryDateBeforeAndAnonymousUserId(LocalDate date, UUID anonymousUserId);
    
    /**
     * Find medicines with low quantity (below threshold)
     */
    @Query("SELECT m FROM Medicine m WHERE m.quantity <= :threshold AND m.isActive = true")
    List<Medicine> findMedicinesWithLowQuantity(@Param("threshold") Integer threshold);
    
    /**
     * Find medicines with low quantity by user ID
     */
    @Query("SELECT m FROM Medicine m WHERE m.quantity <= :threshold AND m.user.id = :userId AND m.isActive = true")
    List<Medicine> findMedicinesWithLowQuantityByUserId(@Param("threshold") Integer threshold, @Param("userId") Long userId);
    
    /**
     * Find medicines with low quantity by anonymous user ID
     */
    @Query("SELECT m FROM Medicine m WHERE m.quantity <= :threshold AND m.anonymousUserId = :anonymousUserId AND m.isActive = true")
    List<Medicine> findMedicinesWithLowQuantityByAnonymousUserId(@Param("threshold") Integer threshold, @Param("anonymousUserId") UUID anonymousUserId);
    
    /**
     * Count medicines by user ID
     */
    long countByUser_Id(Long userId);
    
    /**
     * Count medicines by anonymous user ID
     */
    long countByAnonymousUserId(UUID anonymousUserId);
    
    /**
     * Count active medicines by user ID
     */
    long countByUser_IdAndIsActiveTrue(Long userId);
    
    /**
     * Count active medicines by anonymous user ID
     */
    long countByAnonymousUserIdAndIsActiveTrue(UUID anonymousUserId);
    
    /**
     * Count medicines by family ID
     */
    long countByFamilyId(Long familyId);
    
    /**
     * Count medicines by medicine type ID
     */
    long countByMedicineTypeId(Long medicineTypeId);
    
    /**
     * Check if medicine exists by name and user ID
     */
    boolean existsByNameAndUser_Id(String name, Long userId);
    
    /**
     * Check if medicine exists by name and anonymous user ID
     */
    boolean existsByNameAndAnonymousUserId(String name, UUID anonymousUserId);
    
    /**
     * Find medicines by manufacturer
     */
    List<Medicine> findByManufacturerContainingIgnoreCase(String manufacturer);
    
    /**
     * Find medicines by active ingredient
     */
    List<Medicine> findByActiveIngredientContainingIgnoreCase(String activeIngredient);
    
    /**
     * Find medicines by batch number
     */
    List<Medicine> findByBatchNumber(String batchNumber);
    
    /**
     * Find medicines by price range
     */
    @Query("SELECT m FROM Medicine m WHERE m.price BETWEEN :minPrice AND :maxPrice AND m.isActive = true")
    List<Medicine> findMedicinesByPriceRange(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice);
    
    /**
     * Find medicines by price range and user ID
     */
    @Query("SELECT m FROM Medicine m WHERE m.price BETWEEN :minPrice AND :maxPrice AND m.user.id = :userId AND m.isActive = true")
    List<Medicine> findMedicinesByPriceRangeAndUserId(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice, @Param("userId") Long userId);
    
    /**
     * Find medicines by price range and anonymous user ID
     */
    @Query("SELECT m FROM Medicine m WHERE m.price BETWEEN :minPrice AND :maxPrice AND m.anonymousUserId = :anonymousUserId AND m.isActive = true")
    List<Medicine> findMedicinesByPriceRangeAndAnonymousUserId(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice, @Param("anonymousUserId") UUID anonymousUserId);
    
    /**
     * Update medicine quantity
     */
    @Modifying
    @Query("UPDATE Medicine m SET m.quantity = :quantity WHERE m.id = :id")
    int updateQuantity(@Param("id") Long id, @Param("quantity") Integer quantity);
    
    /**
     * Update medicine active status
     */
    @Modifying
    @Query("UPDATE Medicine m SET m.isActive = :isActive WHERE m.id = :id")
    int updateActiveStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);
    
    /**
     * Find medicines created between dates
     */
    @Query("SELECT m FROM Medicine m WHERE m.createdAt BETWEEN :startDate AND :endDate")
    List<Medicine> findMedicinesCreatedBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    /**
     * Find medicines created between dates by user ID
     */
    @Query("SELECT m FROM Medicine m WHERE m.createdAt BETWEEN :startDate AND :endDate AND m.user.id = :userId")
    List<Medicine> findMedicinesCreatedBetweenByUserId(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("userId") Long userId);
    
    /**
     * Find medicines created between dates by anonymous user ID
     */
    @Query("SELECT m FROM Medicine m WHERE m.createdAt BETWEEN :startDate AND :endDate AND m.anonymousUserId = :anonymousUserId")
    List<Medicine> findMedicinesCreatedBetweenByAnonymousUserId(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("anonymousUserId") UUID anonymousUserId);
} 