package com.medication.repository.domain.medicine;

import com.medication.entity.MedicineType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Repository for MedicineType entity
 */
@Repository
public interface MedicineTypeRepository extends JpaRepository<MedicineType, Long> {
    
    /**
     * Find medicine type by type name
     */
    MedicineType findByTypeName(String typeName);
    
    /**
     * Check if medicine type exists by type name
     */
    boolean existsByTypeName(String typeName);
} 