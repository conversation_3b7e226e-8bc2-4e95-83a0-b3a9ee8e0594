package com.medication.repository.domain.prescription;

import com.medication.entity.PrescriptionItem;
import com.medication.enums.Route;
import com.medication.enums.DoseUnit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository cho PrescriptionItem entity
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Repository
public interface PrescriptionItemRepository extends JpaRepository<PrescriptionItem, Long> {
    
    // ==================== BASIC QUERIES ====================
    
    /**
     * Tìm prescription items theo prescription ID
     */
    List<PrescriptionItem> findByPrescription_Id(Long prescriptionId);
    
    /**
     * Tìm prescription items theo prescription ID với pagination
     */
    Page<PrescriptionItem> findByPrescription_Id(Long prescriptionId, Pageable pageable);
    
    /**
     * Tìm prescription item theo ID và prescription ID
     */
    Optional<PrescriptionItem> findByIdAndPrescription_Id(Long id, Long prescriptionId);
    
    /**
     * Tìm prescription items theo medicine ID
     */
    List<PrescriptionItem> findByMedicine_Id(Long medicineId);
    
    /**
     * Tìm prescription items theo medicine ID với pagination
     */
    Page<PrescriptionItem> findByMedicine_Id(Long medicineId, Pageable pageable);
    
    // ==================== REGISTERED USER QUERIES ====================
    
    /**
     * Tìm prescription items theo patient ID (registered user)
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.prescription.patient.id = :userId")
    List<PrescriptionItem> findByPatientId(@Param("userId") Long userId);
    
    /**
     * Tìm prescription items theo patient ID với pagination
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.prescription.patient.id = :userId")
    Page<PrescriptionItem> findByPatientId(@Param("userId") Long userId, Pageable pageable);
    
    /**
     * Tìm prescription item theo ID và patient ID
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.id = :id AND pi.prescription.patient.id = :userId")
    Optional<PrescriptionItem> findByIdAndPatientId(@Param("id") Long id, @Param("userId") Long userId);
    
    /**
     * Tìm prescription items theo prescription ID và patient ID
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.prescription.id = :prescriptionId AND pi.prescription.patient.id = :userId")
    List<PrescriptionItem> findByPrescriptionIdAndPatientId(@Param("prescriptionId") Long prescriptionId, @Param("userId") Long userId);
    
    /**
     * Tìm prescription items theo prescription ID và patient ID với pagination
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.prescription.id = :prescriptionId AND pi.prescription.patient.id = :userId")
    Page<PrescriptionItem> findByPrescriptionIdAndPatientId(@Param("prescriptionId") Long prescriptionId, @Param("userId") Long userId, Pageable pageable);
    
    // ==================== ANONYMOUS USER QUERIES ====================
    
    /**
     * Tìm prescription items theo anonymous user ID
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.prescription.anonymousUserId = :anonymousUserId")
    List<PrescriptionItem> findByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId);
    
    /**
     * Tìm prescription items theo anonymous user ID với pagination
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.prescription.anonymousUserId = :anonymousUserId")
    Page<PrescriptionItem> findByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId, Pageable pageable);
    
    /**
     * Tìm prescription item theo ID và anonymous user ID
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.id = :id AND pi.prescription.anonymousUserId = :anonymousUserId")
    Optional<PrescriptionItem> findByIdAndAnonymousUserId(@Param("id") Long id, @Param("anonymousUserId") UUID anonymousUserId);
    
    /**
     * Tìm prescription items theo prescription ID và anonymous user ID
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.prescription.id = :prescriptionId AND pi.prescription.anonymousUserId = :anonymousUserId")
    List<PrescriptionItem> findByPrescriptionIdAndAnonymousUserId(@Param("prescriptionId") Long prescriptionId, @Param("anonymousUserId") UUID anonymousUserId);
    
    /**
     * Tìm prescription items theo prescription ID và anonymous user ID với pagination
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.prescription.id = :prescriptionId AND pi.prescription.anonymousUserId = :anonymousUserId")
    Page<PrescriptionItem> findByPrescriptionIdAndAnonymousUserId(@Param("prescriptionId") Long prescriptionId, @Param("anonymousUserId") UUID anonymousUserId, Pageable pageable);
    
    // ==================== SEARCH AND FILTER QUERIES ====================
    
    /**
     * Tìm prescription items theo route
     */
    List<PrescriptionItem> findByRoute(Route route);
    
    /**
     * Tìm prescription items theo dose unit
     */
    List<PrescriptionItem> findByDoseUnit(DoseUnit doseUnit);
    
    /**
     * Tìm prescription items theo duration days
     */
    List<PrescriptionItem> findByDurationDays(Integer durationDays);
    
    /**
     * Tìm prescription items theo duration days range
     */
    List<PrescriptionItem> findByDurationDaysBetween(Integer minDays, Integer maxDays);
    
    /**
     * Tìm prescription items theo dose amount range
     */
    List<PrescriptionItem> findByDoseAmountBetween(Double minDose, Double maxDose);
    
    /**
     * Tìm prescription items theo medicine name (case insensitive)
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE LOWER(pi.medicine.name) LIKE LOWER(CONCAT('%', :medicineName, '%'))")
    List<PrescriptionItem> findByMedicineNameContainingIgnoreCase(@Param("medicineName") String medicineName);
    
    /**
     * Tìm prescription items theo instructions (case insensitive)
     */
    List<PrescriptionItem> findByInstructionsContainingIgnoreCase(String instructions);
    
    // ==================== COUNT QUERIES ====================
    
    /**
     * Đếm prescription items theo prescription ID
     */
    long countByPrescription_Id(Long prescriptionId);
    
    /**
     * Đếm prescription items theo patient ID
     */
    @Query("SELECT COUNT(pi) FROM PrescriptionItem pi WHERE pi.prescription.patient.id = :userId")
    long countByPatientId(@Param("userId") Long userId);
    
    /**
     * Đếm prescription items theo anonymous user ID
     */
    @Query("SELECT COUNT(pi) FROM PrescriptionItem pi WHERE pi.prescription.anonymousUserId = :anonymousUserId")
    long countByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId);
    
    /**
     * Đếm prescription items theo medicine ID
     */
    long countByMedicine_Id(Long medicineId);
    
    /**
     * Đếm prescription items theo route
     */
    long countByRoute(Route route);
    
    /**
     * Đếm prescription items theo dose unit
     */
    long countByDoseUnit(DoseUnit doseUnit);
    
    // ==================== EXISTENCE QUERIES ====================
    
    /**
     * Kiểm tra tồn tại prescription item theo prescription ID
     */
    boolean existsByPrescription_Id(Long prescriptionId);
    
    /**
     * Kiểm tra tồn tại prescription item theo patient ID
     */
    @Query("SELECT COUNT(pi) > 0 FROM PrescriptionItem pi WHERE pi.prescription.patient.id = :userId")
    boolean existsByPatientId(@Param("userId") Long userId);
    
    /**
     * Kiểm tra tồn tại prescription item theo anonymous user ID
     */
    @Query("SELECT COUNT(pi) > 0 FROM PrescriptionItem pi WHERE pi.prescription.anonymousUserId = :anonymousUserId")
    boolean existsByAnonymousUserId(@Param("anonymousUserId") UUID anonymousUserId);
    
    /**
     * Kiểm tra tồn tại prescription item theo medicine ID
     */
    boolean existsByMedicine_Id(Long medicineId);
    
    // ==================== DATE RANGE QUERIES ====================
    
    /**
     * Tìm prescription items được tạo trong khoảng thời gian
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.createdAt BETWEEN :startDate AND :endDate")
    List<PrescriptionItem> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                                 @Param("endDate") LocalDateTime endDate);
    
    /**
     * Tìm prescription items được cập nhật trong khoảng thời gian
     */
    @Query("SELECT pi FROM PrescriptionItem pi WHERE pi.updatedAt BETWEEN :startDate AND :endDate")
    List<PrescriptionItem> findByUpdatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                                 @Param("endDate") LocalDateTime endDate);
    
    /**
     * Đếm prescription items được tạo trong khoảng thời gian
     */
    @Query("SELECT COUNT(pi) FROM PrescriptionItem pi WHERE pi.createdAt BETWEEN :startDate AND :endDate")
    long countByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                @Param("endDate") LocalDateTime endDate);
}
