package com.medication.repository.domain.prescription;

import com.medication.entity.Prescription;
import com.medication.enums.PrescriptionStatus;
import com.medication.enums.UserType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository cho Prescription entity
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Repository
public interface PrescriptionRepository extends JpaRepository<Prescription, Long> {
    
    // ==================== REGISTERED USER QUERIES ====================
    
    /**
     * Tìm prescriptions theo patient ID (registered user)
     */
    List<Prescription> findByPatient_Id(Long userId);

    /**
     * Tìm prescription theo ID và patient ID
     */
    Optional<Prescription> findByIdAndPatient_Id(Long id, Long userId);
    
    /**
     * Tìm prescriptions theo patient ID với pagination
     */
    Page<Prescription> findByPatient_Id(Long userId, Pageable pageable);
    
    /**
     * Tìm prescriptions theo patient ID và status
     */
    List<Prescription> findByPatient_IdAndStatus(Long userId, PrescriptionStatus status);
    
    /**
     * Tìm prescriptions theo patient ID và active status
     */
    List<Prescription> findByPatient_IdAndIsActive(Long userId, Boolean isActive);
    
    /**
     * Tìm prescriptions theo patient ID và date range
     */
    @Query("SELECT p FROM Prescription p WHERE p.patient.id = :userId AND p.prescriptionDate BETWEEN :startDate AND :endDate")
    List<Prescription> findByPatient_IdAndPrescriptionDateBetween(@Param("userId") Long userId, 
                                                              @Param("startDate") LocalDate startDate, 
                                                              @Param("endDate") LocalDate endDate);
    
    /**
     * Tìm prescriptions theo patient ID và doctor name
     */
    List<Prescription> findByPatient_IdAndDoctorNameContainingIgnoreCase(Long userId, String doctorName);
    
    /**
     * Tìm prescriptions theo patient ID và diagnosis
     */
    List<Prescription> findByPatient_IdAndDiagnosisContainingIgnoreCase(Long userId, String diagnosis);
    
    /**
     * Đếm prescriptions theo patient ID
     */
    long countByPatient_Id(Long userId);
    
    /**
     * Đếm prescriptions theo patient ID và status
     */
    long countByPatient_IdAndStatus(Long userId, PrescriptionStatus status);
    
    /**
     * Kiểm tra tồn tại prescription theo patient ID
     */
    boolean existsByPatient_Id(Long userId);
    
    // ==================== ANONYMOUS USER QUERIES ====================
    
    /**
     * Tìm prescriptions theo anonymous user ID
     */
    List<Prescription> findByAnonymousUserId(UUID anonymousUserId);

    /**
     * Tìm prescription theo ID và anonymous user ID
     */
    Optional<Prescription> findByIdAndAnonymousUserId(Long id, UUID anonymousUserId);
    
    /**
     * Tìm prescriptions theo anonymous user ID với pagination
     */
    Page<Prescription> findByAnonymousUserId(UUID anonymousUserId, Pageable pageable);
    
    /**
     * Tìm prescriptions theo anonymous user ID và status
     */
    List<Prescription> findByAnonymousUserIdAndStatus(UUID anonymousUserId, PrescriptionStatus status);
    
    /**
     * Tìm prescriptions theo anonymous user ID và active status
     */
    List<Prescription> findByAnonymousUserIdAndIsActive(UUID anonymousUserId, Boolean isActive);
    
    /**
     * Tìm prescriptions theo anonymous user ID và date range
     */
    @Query("SELECT p FROM Prescription p WHERE p.anonymousUserId = :anonymousUserId AND p.prescriptionDate BETWEEN :startDate AND :endDate")
    List<Prescription> findByAnonymousUserIdAndPrescriptionDateBetween(@Param("anonymousUserId") UUID anonymousUserId, 
                                                                      @Param("startDate") LocalDate startDate, 
                                                                      @Param("endDate") LocalDate endDate);
    
    /**
     * Tìm prescriptions theo anonymous user ID và doctor name
     */
    List<Prescription> findByAnonymousUserIdAndDoctorNameContainingIgnoreCase(UUID anonymousUserId, String doctorName);
    
    /**
     * Tìm prescriptions theo anonymous user ID và diagnosis
     */
    List<Prescription> findByAnonymousUserIdAndDiagnosisContainingIgnoreCase(UUID anonymousUserId, String diagnosis);
    
    /**
     * Đếm prescriptions theo anonymous user ID
     */
    long countByAnonymousUserId(UUID anonymousUserId);
    
    /**
     * Đếm prescriptions theo anonymous user ID và status
     */
    long countByAnonymousUserIdAndStatus(UUID anonymousUserId, PrescriptionStatus status);
    
    /**
     * Kiểm tra tồn tại prescription theo anonymous user ID
     */
    boolean existsByAnonymousUserId(UUID anonymousUserId);
    
    // ==================== GENERAL QUERIES ====================
    
    /**
     * Tìm prescriptions theo status
     */
    List<Prescription> findByStatus(PrescriptionStatus status);
    
    /**
     * Tìm prescriptions theo active status
     */
    List<Prescription> findByIsActive(Boolean isActive);
    
    /**
     * Tìm prescriptions theo user type
     */
    List<Prescription> findByUserType(UserType userType);
    
    /**
     * Tìm prescriptions theo family ID
     */
    List<Prescription> findByFamily_Id(Long familyId);
    
    /**
     * Tìm prescriptions theo family ID và status
     */
    List<Prescription> findByFamily_IdAndStatus(Long familyId, PrescriptionStatus status);
    
    /**
     * Tìm prescriptions theo doctor name
     */
    List<Prescription> findByDoctorNameContainingIgnoreCase(String doctorName);
    
    /**
     * Tìm prescriptions theo diagnosis
     */
    List<Prescription> findByDiagnosisContainingIgnoreCase(String diagnosis);
    
    /**
     * Tìm prescriptions theo date range
     */
    @Query("SELECT p FROM Prescription p WHERE p.prescriptionDate BETWEEN :startDate AND :endDate")
    List<Prescription> findByPrescriptionDateBetween(@Param("startDate") LocalDate startDate, 
                                                    @Param("endDate") LocalDate endDate);
    
    /**
     * Tìm prescriptions sắp hết hạn (end date trong 7 ngày tới)
     */
    @Query("SELECT p FROM Prescription p WHERE p.endDate BETWEEN :today AND :nextWeek AND p.status = 'ACTIVE'")
    List<Prescription> findExpiringPrescriptions(@Param("today") LocalDate today, 
                                                @Param("nextWeek") LocalDate nextWeek);
    
    /**
     * Tìm prescriptions đã hết hạn
     */
    @Query("SELECT p FROM Prescription p WHERE p.endDate < :today AND p.status = 'ACTIVE'")
    List<Prescription> findExpiredPrescriptions(@Param("today") LocalDate today);
    
    /**
     * Tìm prescriptions có compliance rate thấp
     */
    @Query("SELECT p FROM Prescription p WHERE p.complianceRate < :threshold AND p.status = 'ACTIVE'")
    List<Prescription> findLowCompliancePrescriptions(@Param("threshold") Double threshold);
    
    // ==================== UPDATE OPERATIONS ====================
    
    /**
     * Update compliance rate
     */
    @Modifying
    @Query("UPDATE Prescription p SET p.complianceRate = :complianceRate, p.takenDoses = :takenDoses WHERE p.id = :id")
    int updateComplianceRate(@Param("id") Long id, 
                            @Param("complianceRate") Double complianceRate, 
                            @Param("takenDoses") Integer takenDoses);
    
    /**
     * Update status
     */
    @Modifying
    @Query("UPDATE Prescription p SET p.status = :status WHERE p.id = :id")
    int updateStatus(@Param("id") Long id, @Param("status") PrescriptionStatus status);
    
    /**
     * Update active status
     */
    @Modifying
    @Query("UPDATE Prescription p SET p.isActive = :isActive WHERE p.id = :id")
    int updateActiveStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);
    
    /**
     * Soft delete prescription
     */
    @Modifying
    @Query("UPDATE Prescription p SET p.deletedAt = :deletedAt, p.deletedBy = :deletedBy WHERE p.id = :id")
    int softDelete(@Param("id") Long id, 
                   @Param("deletedAt") LocalDate deletedAt, 
                   @Param("deletedBy") String deletedBy);
    
    // ==================== STATISTICS QUERIES ====================
    
    /**
     * Đếm tổng prescriptions
     */
    long count();
    
    /**
     * Đếm prescriptions theo status
     */
    long countByStatus(PrescriptionStatus status);
    
    /**
     * Đếm prescriptions theo user type
     */
    long countByUserType(UserType userType);
    
    /**
     * Đếm prescriptions theo family ID
     */
    long countByFamily_Id(Long familyId);
    
    /**
     * Đếm prescriptions được tạo trong khoảng thời gian
     */
    @Query("SELECT COUNT(p) FROM Prescription p WHERE p.createdAt BETWEEN :startDate AND :endDate")
    long countByCreatedAtBetween(@Param("startDate") LocalDate startDate, 
                                @Param("endDate") LocalDate endDate);
} 