package com.medication.repository.domain.session;

import com.medication.entity.AnonymousSession;
import com.medication.enums.AnonymousSessionStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for AnonymousSession entity
 */
@Repository
public interface AnonymousSessionRepository extends JpaRepository<AnonymousSession, Long> {
    
    /**
     * Find session by anonymous user ID
     */
    Optional<AnonymousSession> findByAnonymousUserId(UUID anonymousUserId);
    
    /**
     * Find sessions by device ID
     */
    List<AnonymousSession> findByDeviceId(String deviceId);
    
    /**
     * Find sessions by status
     */
    List<AnonymousSession> findByStatus(AnonymousSessionStatus status);
    
    /**
     * Find sessions by migrated user ID
     */
    List<AnonymousSession> findByMigratedToUserId(Long userId);
    
    /**
     * Find sessions that haven't been migrated yet
     */
    List<AnonymousSession> findByMigratedToUserIdIsNull();
    
    /**
     * Check if session exists by anonymous user ID
     */
    boolean existsByAnonymousUserId(UUID anonymousUserId);
    
    /**
     * Count sessions created after specific date
     */
    long countByCreatedAtAfter(LocalDateTime date);
    
    /**
     * Count sessions with last activity after specific date
     */
    long countByLastActivityAtAfter(LocalDateTime date);
    
    /**
     * Count sessions by device ID
     */
    long countByDeviceId(String deviceId);
    
    /**
     * Find sessions by device ID and status
     */
    List<AnonymousSession> findByDeviceIdAndStatus(String deviceId, AnonymousSessionStatus status);
    
    /**
     * Find sessions created between dates
     */
    @Query("SELECT a FROM AnonymousSession a WHERE a.createdAt BETWEEN :startDate AND :endDate")
    List<AnonymousSession> findSessionsCreatedBetween(@Param("startDate") LocalDateTime startDate, 
                                                     @Param("endDate") LocalDateTime endDate);
    
    /**
     * Find sessions with last activity between dates
     */
    @Query("SELECT a FROM AnonymousSession a WHERE a.lastActivityAt BETWEEN :startDate AND :endDate")
    List<AnonymousSession> findSessionsActiveBetween(@Param("startDate") LocalDateTime startDate, 
                                                    @Param("endDate") LocalDateTime endDate);
    
    /**
     * Find expired sessions (last activity before specific date)
     */
    @Query("SELECT a FROM AnonymousSession a WHERE a.lastActivityAt < :expiryDate AND a.status = 'ACTIVE'")
    List<AnonymousSession> findExpiredSessions(@Param("expiryDate") LocalDateTime expiryDate);
    
    /**
     * Find inactive sessions for cleanup
     */
    @Query("SELECT s FROM AnonymousSession s WHERE s.lastActivityAt < :cutoffTime AND s.status = :status")
    List<AnonymousSession> findInactiveSessions(
        @Param("cutoffTime") LocalDateTime cutoffTime, 
        @Param("status") AnonymousSessionStatus status
    );
    
    /**
     * Update last activity time
     */
    @Modifying
    @Query("UPDATE AnonymousSession s SET s.lastActivityAt = :now WHERE s.anonymousUserId = :anonymousUserId")
    int updateLastActivity(@Param("anonymousUserId") UUID anonymousUserId, @Param("now") LocalDateTime now);
    
    /**
     * Count total sessions by status
     */
    long countByStatus(AnonymousSessionStatus status);
    
    /**
     * Find sessions by app version
     */
    List<AnonymousSession> findByAppVersion(String appVersion);
    
    /**
     * Find sessions by device ID and app version
     */
    List<AnonymousSession> findByDeviceIdAndAppVersion(String deviceId, String appVersion);
}
