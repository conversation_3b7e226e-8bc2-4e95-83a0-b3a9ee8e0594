package com.medication.repository.domain.user;

import com.medication.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository for User entity
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * Find user by email
     */
    Optional<User> findByEmail(String email);

    /**
     * Find user by email and active
     */
    Optional<User> findByEmailAndIsActiveTrue(String email);

    /**
     * Find user by ID and active
     */
    Optional<User> findByIdAndIsActiveTrue(Long id);

    /**
     * Check if user exists by email
     */
    boolean existsByEmail(String email);

    /**
     * Check if user exists by email and active
     */
    boolean existsByEmailAndIsActiveTrue(String email);
}