package com.medication.security;

import com.medication.entity.User;
import com.medication.exception.common.AuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * Utility class để lấy thông tin user từ SecurityContext
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class SecurityUtils {

    /**
     * Lấy current authenticated user
     */
    public static User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new AuthenticationException("User chưa được xác thực");
        }

        if (authentication.getPrincipal() instanceof UserDetailsAdapter userDetails) {
            return userDetails.getUser();
        }

        throw new AuthenticationException("Không thể lấy thông tin user từ authentication context");
    }

    /**
     * Lấy current user ID
     */
    public static Long getCurrentUserId() {
        return getCurrentUser().getId();
    }

    /**
     * Lấy current user email
     */
    public static String getCurrentUserEmail() {
        return getCurrentUser().getEmail();
    }

    /**
     * Kiểm tra user có role cụ thể không
     */
    public static boolean hasRole(String role) {
        try {
            User user = getCurrentUser();
            return user.getRole().name().equals(role);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Kiểm tra user có phải admin không
     */
    public static boolean isAdmin() {
        return hasRole("ADMIN");
    }

    /**
     * Kiểm tra user có authenticated không
     */
    public static boolean isAuthenticated() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && authentication.isAuthenticated() && 
               !(authentication.getPrincipal() instanceof String);
    }

    /**
     * Lấy current user (nullable)
     */
    public static User getCurrentUserOrNull() {
        try {
            return getCurrentUser();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Lấy current user ID (nullable)
     */
    public static Long getCurrentUserIdOrNull() {
        try {
            return getCurrentUserId();
        } catch (Exception e) {
            return null;
        }
    }
}
