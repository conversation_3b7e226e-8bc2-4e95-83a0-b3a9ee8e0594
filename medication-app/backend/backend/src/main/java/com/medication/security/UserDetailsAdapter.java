package com.medication.security;

import com.medication.entity.User;
import com.medication.service.domain.user.UserBusinessService;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import java.util.Collection;

/**
 * Adapter class to convert User entity to UserDetails for Spring Security
 */
public class UserDetailsAdapter implements UserDetails {

    private final User user;
    private final UserBusinessService userBusinessService;

    public UserDetailsAdapter(User user, UserBusinessService userBusinessService) {
        this.user = user;
        this.userBusinessService = userBusinessService;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return userBusinessService.getAuthorities(user);
    }

    @Override
    public String getPassword() {
        return user.getPassword();
    }

    @Override
    public String getUsername() {
        return user.getEmail();
    }

    @Override
    public boolean isAccountNonExpired() {
        return userBusinessService.isAccountNonExpired(user);
    }

    @Override
    public boolean isAccountNonLocked() {
        return userBusinessService.isAccountNonLocked(user);
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return userBusinessService.isCredentialsNonExpired(user);
    }

    @Override
    public boolean isEnabled() {
        return userBusinessService.isEnabled(user);
    }

    public User getUser() {
        return user;
    }
} 