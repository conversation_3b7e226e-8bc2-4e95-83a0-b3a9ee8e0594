package com.medication.service;

import com.medication.dto.request.LoginRequest;
import com.medication.dto.request.RegisterRequest;
import com.medication.dto.request.RefreshTokenRequest;
import com.medication.dto.request.VerifyOtpRequest;
import com.medication.dto.response.LoginResponse;
import com.medication.entity.User;
import com.medication.enums.UserRole;
import com.medication.enums.UserType;
import com.medication.exception.common.ValidationException;
import com.medication.repository.domain.user.UserRepository;
import com.medication.security.CustomUserDetailsService;
import com.medication.security.JwtUtil;
import com.medication.security.UserDetailsAdapter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service để xử lý authentication logic
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthenticationService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final AuthenticationManager authenticationManager;
    private final CustomUserDetailsService userDetailsService;
    private final EmailService emailService;
    private final OtpService otpService;

    @Value("${jwt.expiration:86400000}")
    private Long jwtExpiration;

    // Temporary storage cho pending registrations
    private final ConcurrentHashMap<String, RegisterRequest> pendingRegistrations = new ConcurrentHashMap<>();

    /**
     * User login
     */
    @Transactional(readOnly = true)
    public LoginResponse login(LoginRequest request) {
        try {
            log.info("Attempting login for email: {}", request.getEmail());

            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    request.getEmail(),
                    request.getPassword()
                )
            );

            UserDetailsAdapter userDetails = (UserDetailsAdapter) authentication.getPrincipal();
            User user = userDetails.getUser();

            // Kiểm tra user có active không
            if (!user.getIsActive()) {
                throw new ValidationException("Tài khoản đã bị vô hiệu hóa");
            }

            // Kiểm tra email đã verify chưa
            if (!user.getEmailVerified()) {
                throw new ValidationException("Email chưa được xác thực. Vui lòng xác thực email trước khi đăng nhập");
            }

            // Generate tokens
            String accessToken = jwtUtil.generateToken(userDetails, user.getId(), user.getRole().name());
            String refreshToken = jwtUtil.generateRefreshToken(userDetails, user.getId());

            // Update last login time
            user.setLastLoginAt(LocalDateTime.now());
            userRepository.save(user);

            log.info("Login successful for user: {}", user.getEmail());

            return LoginResponse.from(user, accessToken, refreshToken, jwtExpiration / 1000);

        } catch (BadCredentialsException e) {
            log.warn("Login failed - invalid credentials for email: {}", request.getEmail());
            throw new ValidationException("Email hoặc mật khẩu không đúng");
        }
    }

    /**
     * User registration - Step 1: Send OTP
     */
    @Transactional
    public void register(RegisterRequest request) {
        log.info("Starting registration process for email: {}", request.getEmail());

        // Validate request
        validateRegisterRequest(request);

        // Kiểm tra email đã tồn tại chưa
        if (userRepository.existsByEmailAndIsActiveTrue(request.getEmail())) {
            throw new ValidationException("Email đã được sử dụng");
        }

        // Lưu pending registration
        pendingRegistrations.put(request.getEmail().toLowerCase(), request);

        // Generate và gửi OTP
        String otpCode = otpService.generateOtp(request.getEmail());
        emailService.sendOtpEmail(request.getEmail(), otpCode);

        log.info("Registration OTP sent to email: {}", request.getEmail());
    }

    /**
     * User registration - Step 2: Verify OTP and create user
     */
    @Transactional
    public LoginResponse verifyOtpAndCreateUser(VerifyOtpRequest request) {
        log.info("Verifying OTP for email: {}", request.getEmail());

        // Verify OTP
        if (!otpService.verifyOtp(request.getEmail(), request.getOtpCode())) {
            throw new ValidationException("Mã OTP không đúng hoặc đã hết hạn");
        }

        // Get pending registration
        RegisterRequest registerRequest = pendingRegistrations.remove(request.getEmail().toLowerCase());
        if (registerRequest == null) {
            throw new ValidationException("Không tìm thấy thông tin đăng ký. Vui lòng đăng ký lại");
        }

        // Create user
        User user = createUserFromRegisterRequest(registerRequest);
        user = userRepository.save(user);

        // Generate tokens
        UserDetails userDetails = userDetailsService.loadUserByUsername(user.getEmail());
        String accessToken = jwtUtil.generateToken(userDetails, user.getId(), user.getRole().name());
        String refreshToken = jwtUtil.generateRefreshToken(userDetails, user.getId());

        // Send welcome email
        emailService.sendWelcomeEmail(user.getEmail(), user.getFullName());

        log.info("User registration completed successfully for email: {}", user.getEmail());

        return LoginResponse.from(user, accessToken, refreshToken, jwtExpiration / 1000);
    }

    /**
     * Refresh access token
     */
    @Transactional(readOnly = true)
    public LoginResponse refreshToken(RefreshTokenRequest request) {
        String refreshToken = request.getRefreshToken();

        try {
            // Validate refresh token
            if (!jwtUtil.validateToken(refreshToken) || !jwtUtil.isRefreshToken(refreshToken)) {
                throw new ValidationException("Refresh token không hợp lệ");
            }

            // Extract user info
            String email = jwtUtil.extractUsername(refreshToken);
            Long userId = jwtUtil.extractUserId(refreshToken);

            // Load user
            UserDetails userDetails = userDetailsService.loadUserByUsername(email);
            UserDetailsAdapter userDetailsAdapter = (UserDetailsAdapter) userDetails;
            User user = userDetailsAdapter.getUser();

            // Generate new tokens
            String newAccessToken = jwtUtil.generateToken(userDetails, userId, user.getRole().name());
            String newRefreshToken = jwtUtil.generateRefreshToken(userDetails, userId);

            log.info("Token refreshed successfully for user: {}", email);

            return LoginResponse.from(user, newAccessToken, newRefreshToken, jwtExpiration / 1000);

        } catch (Exception e) {
            log.error("Token refresh failed: {}", e.getMessage());
            throw new ValidationException("Không thể refresh token. Vui lòng đăng nhập lại");
        }
    }

    /**
     * Validate register request
     */
    private void validateRegisterRequest(RegisterRequest request) {
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new ValidationException("Mật khẩu xác nhận không khớp");
        }
    }

    /**
     * Create User entity từ RegisterRequest
     */
    private User createUserFromRegisterRequest(RegisterRequest request) {
        User user = new User();
        user.setEmail(request.getEmail().toLowerCase());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setFullName(request.getFullName());
        user.setPhoneNumber(request.getPhoneNumber());
        user.setRole(UserRole.USER);
        user.setUserType(UserType.REGISTERED);
        user.setIsActive(true);
        user.setEmailVerified(true); // Đã verify qua OTP
        user.setAnonymousUserId(request.getAnonymousUserId());
        
        return user;
    }
}
