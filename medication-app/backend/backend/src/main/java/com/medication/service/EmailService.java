package com.medication.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

/**
 * Service để gửi email
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String fromEmail;

    @Value("${app.name:Medication App}")
    private String appName;

    /**
     * Gửi OTP verification email
     */
    public void sendOtpEmail(String toEmail, String otpCode) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("<PERSON><PERSON><PERSON> thực tà<PERSON> kho<PERSON>n - " + appName);
            
            String emailBody = buildOtpEmailBody(otpCode);
            message.setText(emailBody);
            
            mailSender.send(message);
            log.info("OTP email sent successfully to: {}", toEmail);
            
        } catch (Exception e) {
            log.error("Failed to send OTP email to: {}, error: {}", toEmail, e.getMessage());
            throw new RuntimeException("Không thể gửi email xác thực. Vui lòng thử lại sau.", e);
        }
    }

    /**
     * Gửi welcome email sau khi đăng ký thành công
     */
    public void sendWelcomeEmail(String toEmail, String fullName) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("Chào mừng bạn đến với " + appName);
            
            String emailBody = buildWelcomeEmailBody(fullName);
            message.setText(emailBody);
            
            mailSender.send(message);
            log.info("Welcome email sent successfully to: {}", toEmail);
            
        } catch (Exception e) {
            log.error("Failed to send welcome email to: {}, error: {}", toEmail, e.getMessage());
            // Không throw exception vì welcome email không critical
        }
    }

    /**
     * Gửi password reset email
     */
    public void sendPasswordResetEmail(String toEmail, String resetToken) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("Đặt lại mật khẩu - " + appName);
            
            String emailBody = buildPasswordResetEmailBody(resetToken);
            message.setText(emailBody);
            
            mailSender.send(message);
            log.info("Password reset email sent successfully to: {}", toEmail);
            
        } catch (Exception e) {
            log.error("Failed to send password reset email to: {}, error: {}", toEmail, e.getMessage());
            throw new RuntimeException("Không thể gửi email đặt lại mật khẩu. Vui lòng thử lại sau.", e);
        }
    }

    /**
     * Build OTP email body
     */
    private String buildOtpEmailBody(String otpCode) {
        return String.format("""
            Xin chào,
            
            Cảm ơn bạn đã đăng ký tài khoản tại %s!
            
            Mã xác thực OTP của bạn là: %s
            
            Mã này có hiệu lực trong 10 phút.
            
            Nếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này.
            
            Trân trọng,
            Đội ngũ %s
            """, appName, otpCode, appName);
    }

    /**
     * Build welcome email body
     */
    private String buildWelcomeEmailBody(String fullName) {
        return String.format("""
            Xin chào %s,
            
            Chào mừng bạn đến với %s!
            
            Tài khoản của bạn đã được tạo thành công và đã được xác thực.
            
            Bạn có thể bắt đầu sử dụng ứng dụng để quản lý tủ thuốc gia đình của mình.
            
            Tính năng chính:
            - Quản lý thuốc và hạn sử dụng
            - Lịch uống thuốc thông minh
            - Quản lý đơn thuốc
            - Thông báo nhắc nhở
            
            Chúc bạn có trải nghiệm tuyệt vời!
            
            Trân trọng,
            Đội ngũ %s
            """, fullName, appName, appName);
    }

    /**
     * Build password reset email body
     */
    private String buildPasswordResetEmailBody(String resetToken) {
        return String.format("""
            Xin chào,
            
            Bạn đã yêu cầu đặt lại mật khẩu cho tài khoản %s.
            
            Mã đặt lại mật khẩu của bạn là: %s
            
            Mã này có hiệu lực trong 30 phút.
            
            Nếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này.
            
            Trân trọng,
            Đội ngũ %s
            """, appName, resetToken, appName);
    }
}
