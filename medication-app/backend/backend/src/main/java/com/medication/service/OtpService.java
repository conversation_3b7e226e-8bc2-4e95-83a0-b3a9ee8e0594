package com.medication.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service để quản lý OTP codes
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
@Slf4j
public class OtpService {

    private final ConcurrentHashMap<String, OtpData> otpStorage = new ConcurrentHashMap<>();
    private final SecureRandom random = new SecureRandom();
    
    // OTP có hiệu lực trong 10 phút
    private static final int OTP_EXPIRY_MINUTES = 10;
    
    // Maximum attempts cho OTP verification
    private static final int MAX_ATTEMPTS = 3;

    /**
     * Tạo và lưu OTP code cho email
     */
    public String generateOtp(String email) {
        // Tạo OTP 6 chữ số
        String otpCode = String.format("%06d", random.nextInt(1000000));
        
        // <PERSON>ưu <PERSON>TP với thời gian hết hạn
        OtpData otpData = new OtpData(
            otpCode,
            LocalDateTime.now().plusMinutes(OTP_EXPIRY_MINUTES),
            0
        );
        
        otpStorage.put(email.toLowerCase(), otpData);
        
        log.info("OTP generated for email: {}", email);
        return otpCode;
    }

    /**
     * Verify OTP code
     */
    public boolean verifyOtp(String email, String otpCode) {
        String emailKey = email.toLowerCase();
        OtpData otpData = otpStorage.get(emailKey);
        
        if (otpData == null) {
            log.warn("No OTP found for email: {}", email);
            return false;
        }
        
        // Kiểm tra số lần thử
        if (otpData.getAttempts() >= MAX_ATTEMPTS) {
            log.warn("Maximum OTP attempts exceeded for email: {}", email);
            otpStorage.remove(emailKey);
            return false;
        }
        
        // Tăng số lần thử
        otpData.incrementAttempts();
        
        // Kiểm tra hết hạn
        if (LocalDateTime.now().isAfter(otpData.getExpiryTime())) {
            log.warn("OTP expired for email: {}", email);
            otpStorage.remove(emailKey);
            return false;
        }
        
        // Kiểm tra OTP code
        if (!otpData.getOtpCode().equals(otpCode)) {
            log.warn("Invalid OTP code for email: {}", email);
            return false;
        }
        
        // OTP hợp lệ - xóa khỏi storage
        otpStorage.remove(emailKey);
        log.info("OTP verified successfully for email: {}", email);
        return true;
    }

    /**
     * Kiểm tra OTP có tồn tại và chưa hết hạn không
     */
    public boolean isOtpValid(String email) {
        String emailKey = email.toLowerCase();
        OtpData otpData = otpStorage.get(emailKey);
        
        if (otpData == null) {
            return false;
        }
        
        if (LocalDateTime.now().isAfter(otpData.getExpiryTime())) {
            otpStorage.remove(emailKey);
            return false;
        }
        
        return otpData.getAttempts() < MAX_ATTEMPTS;
    }

    /**
     * Xóa OTP cho email (dùng khi cancel registration)
     */
    public void removeOtp(String email) {
        otpStorage.remove(email.toLowerCase());
        log.info("OTP removed for email: {}", email);
    }

    /**
     * Cleanup expired OTPs (có thể chạy định kỳ)
     */
    public void cleanupExpiredOtps() {
        LocalDateTime now = LocalDateTime.now();
        otpStorage.entrySet().removeIf(entry -> {
            boolean expired = now.isAfter(entry.getValue().getExpiryTime());
            if (expired) {
                log.debug("Cleaned up expired OTP for email: {}", entry.getKey());
            }
            return expired;
        });
    }

    /**
     * Inner class để lưu OTP data
     */
    private static class OtpData {
        private final String otpCode;
        private final LocalDateTime expiryTime;
        private int attempts;

        public OtpData(String otpCode, LocalDateTime expiryTime, int attempts) {
            this.otpCode = otpCode;
            this.expiryTime = expiryTime;
            this.attempts = attempts;
        }

        public String getOtpCode() {
            return otpCode;
        }

        public LocalDateTime getExpiryTime() {
            return expiryTime;
        }

        public int getAttempts() {
            return attempts;
        }

        public void incrementAttempts() {
            this.attempts++;
        }
    }
}
