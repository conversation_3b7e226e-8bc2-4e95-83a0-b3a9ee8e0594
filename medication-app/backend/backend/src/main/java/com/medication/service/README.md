# Service Package Structure

## 📁 Overview

Package này được tổ chức theo Clean Architecture principles với domain-based organization cho từng business domain.

## 🏗️ Directory Structure

```
service/
├── domain/                           # Domain-specific services
│   ├── user/                         # User domain
│   │   ├── UserBusinessService.java
│   │   └── UserValidationService.java
│   ├── session/                      # Session domain
│   │   ├── AnonymousSessionBusinessService.java
│   │   └── SessionValidationService.java
│   ├── prescription/                 # Prescription domain
│   │   ├── PrescriptionBusinessService.java
│   │   ├── PrescriptionItemBusinessService.java
│   │   ├── PrescriptionValidationService.java
│   │   └── PrescriptionCalculationService.java
│   ├── medicine/                     # Medicine domain
│   │   ├── MedicineTypeBusinessService.java
│   │   ├── MedicineUnitBusinessService.java
│   │   └── MedicineValidationService.java
│   ├── schedule/                     # Schedule domain
│   │   ├── MedicationScheduleBusinessService.java
│   │   ├── SchedulePatternBusinessService.java
│   │   └── MedicationDoseBusinessService.java
│   ├── family/                       # Family domain
│   │   ├── FamilyBusinessService.java
│   │   └── FamilyMemberBusinessService.java
│   └── notification/                 # Notification domain
│       └── NotificationBusinessService.java
├── common/                           # Common services
│   ├── AuditLogBusinessService.java
│   └── SecurityService.java
└── util/                             # Utility services (future)
    └── (utility services will be added here)
```

## 🎯 Domain Organization

### **User Domain**

- **UserBusinessService**: Core user business logic
- **UserValidationService**: User validation logic

### **Session Domain**

- **AnonymousSessionBusinessService**: Anonymous session management
- **SessionValidationService**: Session validation logic

### **Prescription Domain**

- **PrescriptionBusinessService**: Core prescription business logic
- **PrescriptionItemBusinessService**: Prescription items management
- **PrescriptionValidationService**: Prescription validation logic
- **PrescriptionCalculationService**: Prescription calculations (compliance, costs, etc.)

### **Medicine Domain**

- **MedicineTypeBusinessService**: Medicine types management
- **MedicineUnitBusinessService**: Medicine units management
- **MedicineValidationService**: Medicine validation logic

### **Schedule Domain**

- **MedicationScheduleBusinessService**: Medication scheduling
- **SchedulePatternBusinessService**: Schedule patterns management
- **MedicationDoseBusinessService**: Medication doses management

### **Family Domain**

- **FamilyBusinessService**: Family management
- **FamilyMemberBusinessService**: Family members management

### **Notification Domain**

- **NotificationBusinessService**: Notification management

### **Common Services**

- **AuditLogBusinessService**: Audit logging across domains
- **SecurityService**: Security and authorization logic

## 🔧 Usage Examples

### **Using Domain Services**

```java
@Service
public class SomeController {

    private final UserBusinessService userService;
    private final UserValidationService userValidationService;
    private final SecurityService securityService;

    public void createUser(CreateUserRequest request) {
        // Validate
        userValidationService.validateForCreation(user);

        // Check permissions
        securityService.requireActiveAndVerified();

        // Business logic
        User user = userService.createUser(request);
    }
}
```

### **Using Validation Services**

```java
@Service
public class PrescriptionService {

    private final PrescriptionValidationService validationService;
    private final PrescriptionCalculationService calculationService;

    public void createPrescription(Prescription prescription) {
        // Validate
        validationService.validateForCreation(prescription);

        // Calculate
        calculationService.updatePrescriptionCalculations(prescription);

        // Save
        prescriptionRepository.save(prescription);
    }
}
```

### **Using Security Service**

```java
@Service
public class SomeService {

    private final SecurityService securityService;

    public void accessResource(Long resourceId) {
        // Check permissions
        securityService.validateResourceAccessById(resourceId);

        // Check role
        if (securityService.canPrescribe()) {
            // Prescription logic
        }
    }
}
```

## 🔧 Best Practices

1. **Domain Separation**: Each domain has its own package with related services
2. **Validation Separation**: Validation logic is separated into dedicated services
3. **Calculation Separation**: Complex calculations are separated into dedicated services
4. **Common Services**: Shared functionality is in common package
5. **Dependency Injection**: Use constructor injection for all dependencies
6. **Transaction Management**: Use @Transactional on business service methods
7. **Logging**: Use proper logging in all service methods

## 📝 Adding New Services

### **For New Domain**

1. Create domain directory: `service/domain/newdomain/`
2. Create business service: `NewDomainBusinessService.java`
3. Create validation service if needed: `NewDomainValidationService.java`
4. Update this README

### **For Common Services**

1. Add to `service/common/` directory
2. Ensure it's truly common across domains
3. Update this README

### **For Utility Services**

1. Add to `service/util/` directory
2. Ensure it's stateless and reusable
3. Update this README

## 🚀 Benefits

- **Scalability**: Easy to add new domains and services
- **Maintainability**: Clear organization and separation of concerns
- **Clean Architecture**: Follows domain-driven design principles
- **Team Collaboration**: Multiple developers can work on different domains
- **Testability**: Services can be easily unit tested
- **Reusability**: Common services can be reused across domains

## 🔄 Migration Notes

This structure was refactored from a flat organization to domain-based organization. All existing services have been moved to appropriate domains and package declarations have been updated.

### **Key Changes:**

- Services moved to domain-specific packages
- Package declarations updated
- Imports updated in controllers and other classes
- New validation and calculation services added
- Common services separated

### **Breaking Changes:**

- Import statements need to be updated in classes that use these services
- Package declarations changed for all moved services
