package com.medication.service.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.medication.entity.AuditLog;
import org.springframework.stereotype.Service;

@Service
public class AuditLogBusinessService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    // JSON serialization methods
    public void setOldValuesAsJson(AuditLog auditLog, Object oldData) {
        if (oldData == null) {
            auditLog.setOldValues(null);
            return;
        }
        
        try {
            auditLog.setOldValues(objectMapper.writeValueAsString(oldData));
        } catch (JsonProcessingException e) {
            auditLog.setOldValues("{\"error\": \"Failed to serialize old data\"}");
        }
    }
    
    public void setNewValuesAsJson(AuditLog auditLog, Object newData) {
        if (newData == null) {
            auditLog.setNewValues(null);
            return;
        }
        
        try {
            auditLog.setNewValues(objectMapper.writeValueAsString(newData));
        } catch (JsonProcessingException e) {
            auditLog.setNewValues("{\"error\": \"Failed to serialize new data\"}");
        }
    }
    
    public <T> T getOldValuesAsObject(AuditLog auditLog, Class<T> clazz) {
        if (auditLog.getOldValues() == null || auditLog.getOldValues().trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(auditLog.getOldValues(), clazz);
        } catch (JsonProcessingException e) {
            return null;
        }
    }
    
    public <T> T getNewValuesAsObject(AuditLog auditLog, Class<T> clazz) {
        if (auditLog.getNewValues() == null || auditLog.getNewValues().trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(auditLog.getNewValues(), clazz);
        } catch (JsonProcessingException e) {
            return null;
        }
    }
    
    // Severity check methods
    public boolean isCritical(AuditLog auditLog) {
        return auditLog.getSeverity() != null && auditLog.getSeverity().isCritical();
    }
    
    public boolean isErrorOrHigher(AuditLog auditLog) {
        return auditLog.getSeverity() != null && auditLog.getSeverity().isErrorOrHigher();
    }
    
    public boolean isWarningOrHigher(AuditLog auditLog) {
        return auditLog.getSeverity() != null && auditLog.getSeverity().isWarningOrHigher();
    }
    
    // Display methods
    public String getActionDisplayName(AuditLog auditLog) {
        return auditLog.getAction() != null ? auditLog.getAction().getLabel() : "Unknown Action";
    }
    
    public String getSeverityDisplayName(AuditLog auditLog) {
        return auditLog.getSeverity() != null ? auditLog.getSeverity().getLabel() : "Unknown Severity";
    }
    
    public String getUserDisplayName(AuditLog auditLog) {
        if (auditLog.getUser() != null && auditLog.getUser().getFullName() != null) {
            return auditLog.getUser().getFullName();
        }
        return "Unknown User";
    }
    
    public String getUserEmail(AuditLog auditLog) {
        if (auditLog.getUser() != null && auditLog.getUser().getEmail() != null) {
            return auditLog.getUser().getEmail();
        }
        return "No email";
    }
    
    // Action type check methods
    public boolean isCrudOperation(AuditLog auditLog) {
        return auditLog.getAction() != null && auditLog.getAction().isCrudOperation();
    }
    
    public boolean isAuthenticationAction(AuditLog auditLog) {
        return auditLog.getAction() != null && auditLog.getAction().isAuthenticationAction();
    }
    
    public boolean isDataOperation(AuditLog auditLog) {
        return auditLog.getAction() != null && auditLog.getAction().isDataOperation();
    }
    
    public boolean isMedicineOperation(AuditLog auditLog) {
        return auditLog.getAction() != null && auditLog.getAction().isMedicineOperation();
    }
    
    public boolean isPrescriptionOperation(AuditLog auditLog) {
        return auditLog.getAction() != null && auditLog.getAction().isPrescriptionOperation();
    }
    
    public boolean isFamilyOperation(AuditLog auditLog) {
        return auditLog.getAction() != null && auditLog.getAction().isFamilyOperation();
    }
    
    // Summary method
    public String getAuditSummary(AuditLog auditLog) {
        StringBuilder summary = new StringBuilder();
        summary.append(getActionDisplayName(auditLog));
        summary.append(" - ");
        summary.append(auditLog.getEntityType() != null ? auditLog.getEntityType() : "Unknown Entity");
        
        if (auditLog.getEntityId() != null) {
            summary.append(" (ID: ").append(auditLog.getEntityId()).append(")");
        }
        
        if (auditLog.getDescription() != null && !auditLog.getDescription().trim().isEmpty()) {
            summary.append(" - ").append(auditLog.getDescription());
        }
        
        return summary.toString();
    }
} 