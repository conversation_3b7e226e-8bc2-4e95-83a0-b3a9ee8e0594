package com.medication.service.common;

import com.medication.entity.User;
import com.medication.enums.UserRole;
import com.medication.exception.common.AuthorizationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

/**
 * Service for Security-related business logic
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class SecurityService {

    /**
     * Get current authenticated user
     */
    public User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new AuthorizationException("User not authenticated");
        }

        Object principal = authentication.getPrincipal();
        if (principal instanceof User) {
            return (User) principal;
        }

        throw new AuthorizationException("Invalid user principal");
    }

    /**
     * Check if current user has specific role
     */
    public boolean hasRole(UserRole role) {
        User currentUser = getCurrentUser();
        return currentUser.getRole() == role;
    }

    /**
     * Check if current user is admin
     */
    public boolean isAdmin() {
        User currentUser = getCurrentUser();
        return currentUser.getRole() == UserRole.ADMIN || currentUser.getRole() == UserRole.SUPER_ADMIN;
    }

    /**
     * Check if current user is doctor
     */
    public boolean isDoctor() {
        return hasRole(UserRole.DOCTOR);
    }

    /**
     * Check if current user is pharmacist
     */
    public boolean isPharmacist() {
        return hasRole(UserRole.PHARMACIST);
    }

    /**
     * Check if current user is regular user
     */
    public boolean isRegularUser() {
        return hasRole(UserRole.USER);
    }

    /**
     * Check if current user can prescribe medications
     */
    public boolean canPrescribe() {
        return isDoctor() || isPharmacist() || isAdmin();
    }

    /**
     * Check if current user can manage other users
     */
    public boolean canManageUsers() {
        return isAdmin();
    }

    /**
     * Check if current user can view audit logs
     */
    public boolean canViewAuditLogs() {
        return isAdmin();
    }

    /**
     * Validate user access to resource
     */
    public void validateResourceAccess(User resourceOwner) {
        User currentUser = getCurrentUser();

        // Admin can access all resources
        if (isAdmin()) {
            return;
        }

        // User can only access their own resources
        if (!currentUser.getId().equals(resourceOwner.getId())) {
            throw new AuthorizationException("Access denied to this resource");
        }
    }

    /**
     * Validate user access to resource by ID
     */
    public void validateResourceAccessById(Long resourceOwnerId) {
        User currentUser = getCurrentUser();

        // Admin can access all resources
        if (isAdmin()) {
            return;
        }

        // User can only access their own resources
        if (!currentUser.getId().equals(resourceOwnerId)) {
            throw new AuthorizationException("Access denied to this resource");
        }
    }

    /**
     * Check if user is active and verified
     */
    public boolean isUserActiveAndVerified() {
        User currentUser = getCurrentUser();
        return currentUser.getIsActive() && currentUser.getEmailVerified();
    }

    /**
     * Require user to be active and verified
     */
    public void requireActiveAndVerified() {
        if (!isUserActiveAndVerified()) {
            throw new AuthorizationException("User account is not active or email not verified");
        }
    }
} 