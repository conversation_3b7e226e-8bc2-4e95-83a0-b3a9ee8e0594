package com.medication.service.domain.family;

import com.medication.entity.Family;
import com.medication.entity.FamilyMember;
import com.medication.entity.Medicine;
import com.medication.entity.Prescription;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FamilyBusinessService {

    // Member management methods
    public boolean hasMembers(Family family) {
        return family.getMembers() != null && !family.getMembers().isEmpty();
    }
    
    public int getActiveMemberCount(Family family) {
        if (family.getMembers() == null) return 0;
        return (int) family.getMembers().stream()
                .filter(member -> member.getIsActive() != null && member.getIsActive())
                .count();
    }
    
    public List<FamilyMember> getActiveMembers(Family family) {
        if (family.getMembers() == null) return new ArrayList<>();
        return family.getMembers().stream()
                .filter(member -> member.getIsActive() != null && member.getIsActive())
                .collect(Collectors.toList());
    }
    
    public List<FamilyMember> getMembersByRole(Family family, String role) {
        if (family.getMembers() == null) return new ArrayList<>();
        return family.getMembers().stream()
                .filter(member -> role.equals(member.getRole()))
                .collect(Collectors.toList());
    }
    
    // Medicine management methods
    public int getMedicineCount(Family family) {
        return family.getMedicines() != null ? family.getMedicines().size() : 0;
    }
    
    public boolean hasMedicines(Family family) {
        return family.getMedicines() != null && !family.getMedicines().isEmpty();
    }
    
    public List<Medicine> getMedicinesByType(Family family, String type) {
        if (family.getMedicines() == null) return new ArrayList<>();
        return family.getMedicines().stream()
                .filter(medicine -> medicine.getMedicineType() != null && type.equals(medicine.getMedicineType().getTypeName()))
                .collect(Collectors.toList());
    }
    
    // Prescription management methods
    public int getPrescriptionCount(Family family) {
        return family.getPrescriptions() != null ? family.getPrescriptions().size() : 0;
    }
    
    public boolean hasActivePrescriptions(Family family) {
        if (family.getPrescriptions() == null) return false;
        return family.getPrescriptions().stream()
                .anyMatch(prescription -> prescription.getIsActive() != null && prescription.getIsActive());
    }
    
    public List<Prescription> getActivePrescriptions(Family family) {
        if (family.getPrescriptions() == null) return new ArrayList<>();
        return family.getPrescriptions().stream()
                .filter(prescription -> prescription.getIsActive() != null && prescription.getIsActive())
                .collect(Collectors.toList());
    }
    
    // Business logic methods
    public boolean canBeDeleted(Family family) {
        return !hasMembers(family) && getMedicineCount(family) == 0 && getPrescriptionCount(family) == 0;
    }
    
    public String getDisplayName(Family family) {
        return family.getName() != null ? family.getName() : "Unknown Family";
    }
    
    public String getDisplayDescription(Family family) {
        return family.getDescription() != null && !family.getDescription().trim().isEmpty() 
               ? family.getDescription() 
               : "No description available";
    }
} 