package com.medication.service.domain.family;

import com.medication.entity.FamilyMember;
import com.medication.enums.FamilyRole;
import org.springframework.stereotype.Service;

@Service
public class FamilyMemberBusinessService {

    // Role check methods
    public boolean isOwner(FamilyMember familyMember) {
        return familyMember.getRole() == FamilyRole.OWNER;
    }
    
    public boolean isAdmin(FamilyMember familyMember) {
        return familyMember.getRole() == FamilyRole.ADMIN;
    }
    
    public boolean isMember(FamilyMember familyMember) {
        return familyMember.getRole() == FamilyRole.MEMBER;
    }
    
    public boolean hasAdminPrivileges(FamilyMember familyMember) {
        return familyMember.getRole() == FamilyRole.OWNER || 
               familyMember.getRole() == FamilyRole.ADMIN;
    }
    
    public boolean isActive(FamilyMember familyMember) {
        return familyMember.getIsActive() != null && familyMember.getIsActive();
    }
    
    // Display methods
    public String getRoleDisplayName(FamilyMember familyMember) {
        switch (familyMember.getRole()) {
            case OWNER:
                return "Chủ gia đình";
            case ADMIN:
                return "Quản trị viên";
            case MEMBER:
                return "Thành viên";
            default:
                return "Không xác định";
        }
    }
    
    public String getRoleDescription(FamilyMember familyMember) {
        switch (familyMember.getRole()) {
            case OWNER:
                return "Có toàn quyền quản lý gia đình, có thể thêm/xóa thành viên, quản lý thuốc và đơn thuốc";
            case ADMIN:
                return "Có quyền quản lý thuốc và đơn thuốc, có thể mời thành viên mới";
            case MEMBER:
                return "Có thể xem thông tin gia đình, thuốc và đơn thuốc";
            default:
                return "Không xác định";
        }
    }
    
    public String getMemberDisplayName(FamilyMember familyMember) {
        if (familyMember.getUser() != null && familyMember.getUser().getFullName() != null) {
            return familyMember.getUser().getFullName();
        }
        return "Unknown Member";
    }
    
    public String getMemberEmail(FamilyMember familyMember) {
        if (familyMember.getUser() != null && familyMember.getUser().getEmail() != null) {
            return familyMember.getUser().getEmail();
        }
        return "No email";
    }
    
    // Permission check methods
    public boolean canManageFamily(FamilyMember familyMember) {
        return hasAdminPrivileges(familyMember);
    }
    
    public boolean canManageMedicines(FamilyMember familyMember) {
        return hasAdminPrivileges(familyMember);
    }
    
    public boolean canManagePrescriptions(FamilyMember familyMember) {
        return hasAdminPrivileges(familyMember);
    }
    
    public boolean canInviteMembers(FamilyMember familyMember) {
        return hasAdminPrivileges(familyMember);
    }
    
    public boolean canRemoveMembers(FamilyMember familyMember) {
        return familyMember.getRole() == FamilyRole.OWNER;
    }
    
    public boolean canChangeFamilySettings(FamilyMember familyMember) {
        return familyMember.getRole() == FamilyRole.OWNER;
    }
} 