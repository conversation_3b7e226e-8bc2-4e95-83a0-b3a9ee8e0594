package com.medication.service.domain.medicine;

import com.medication.dto.request.CreateMedicineRequest;
import com.medication.dto.request.UpdateMedicineRequest;
import com.medication.dto.response.MedicineStatsResponse;
import com.medication.entity.Medicine;
import com.medication.entity.MedicineType;
import com.medication.enums.UserType;
import com.medication.exception.domain.medicine.MedicineNotFoundException;
import com.medication.exception.domain.medicine.MedicineAlreadyExistsException;
import com.medication.exception.domain.medicine.InvalidMedicineDataException;
import com.medication.repository.domain.medicine.MedicineRepository;
import com.medication.repository.domain.medicine.MedicineTypeRepository;
import com.medication.repository.domain.user.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Business service cho Medicine
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class MedicineBusinessService {

    private final MedicineRepository medicineRepository;
    private final MedicineTypeRepository medicineTypeRepository;
    private final UserRepository userRepository;

    // Helper methods
    private void validateNotBlank(String value, String fieldName) {
        if (value == null || value.isBlank()) {
            throw new IllegalArgumentException(fieldName + " must not be blank");
        }
    }

    private void validateNotNull(Object value, String fieldName) {
        if (value == null) {
            throw new IllegalArgumentException(fieldName + " must not be null");
        }
    }

    private void validatePositive(Integer value, String fieldName) {
        if (value != null && value < 0) {
            throw new IllegalArgumentException(fieldName + " must be positive");
        }
    }

    private void validatePositive(BigDecimal value, String fieldName) {
        if (value != null && value.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException(fieldName + " must be positive");
        }
    }

    private void validateDateRange(LocalDate startDate, LocalDate endDate, String startFieldName, String endFieldName) {
        if (startDate != null && endDate != null && startDate.isAfter(endDate)) {
            throw new IllegalArgumentException(startFieldName + " must be before or equal to " + endFieldName);
        }
    }

    /**
     * Create new medicine for registered user
     */
    public Medicine createMedicineForUser(CreateMedicineRequest request, Long userId) {
        log.info("Creating medicine for user ID: {}", userId);

        validateCreateMedicineRequest(request);
        validateUserExists(userId);

        // Check if medicine with same name already exists for this user
        if (medicineRepository.existsByNameAndUser_Id(request.getName(), userId)) {
            throw new MedicineAlreadyExistsException("Medicine with name '" + request.getName() + "' already exists for this user");
        }

        Medicine medicine = mapToMedicine(request);
        medicine.setUserType(UserType.REGISTERED);
        medicine.setUser(userRepository.findById(userId).orElse(null));

        Medicine savedMedicine = medicineRepository.save(medicine);
        log.info("Created medicine with ID: {} for user ID: {}", savedMedicine.getId(), userId);

        return savedMedicine;
    }

    /**
     * Create new medicine for anonymous user
     */
    public Medicine createMedicineForAnonymousUser(CreateMedicineRequest request, UUID anonymousUserId) {
        log.info("Creating medicine for anonymous user ID: {}", anonymousUserId);

        validateCreateMedicineRequest(request);
        validateAnonymousUserId(anonymousUserId);

        // Check if medicine with same name already exists for this anonymous user
        if (medicineRepository.existsByNameAndAnonymousUserId(request.getName(), anonymousUserId)) {
            throw new MedicineAlreadyExistsException("Medicine with name '" + request.getName() + "' already exists for this anonymous user");
        }

        Medicine medicine = mapToMedicine(request);
        medicine.setUserType(UserType.ANONYMOUS);
        medicine.setAnonymousUserId(anonymousUserId);

        Medicine savedMedicine = medicineRepository.save(medicine);
        log.info("Created medicine with ID: {} for anonymous user ID: {}", savedMedicine.getId(), anonymousUserId);

        return savedMedicine;
    }

    /**
     * Find medicine by ID
     */
    @Transactional(readOnly = true)
    public Medicine findMedicineById(Long id) {
        log.debug("Finding medicine by ID: {}", id);
        return medicineRepository.findById(id)
                .orElseThrow(() -> new MedicineNotFoundException("Medicine not found with ID: " + id));
    }

    /**
     * Find medicine by ID and user ID (for registered users)
     */
    @Transactional(readOnly = true)
    public Medicine findMedicineByIdAndUserId(Long id, Long userId) {
        log.debug("Finding medicine by ID: {} and user ID: {}", id, userId);
        Medicine medicine = findMedicineById(id);
        
        if (!medicine.getUserType().equals(UserType.REGISTERED) || 
            !medicine.getUser().getId().equals(userId)) {
            throw new MedicineNotFoundException("Medicine not found with ID: " + id + " for user ID: " + userId);
        }
        
        return medicine;
    }

    /**
     * Find medicine by ID and anonymous user ID (for anonymous users)
     */
    @Transactional(readOnly = true)
    public Medicine findMedicineByIdAndAnonymousUserId(Long id, UUID anonymousUserId) {
        log.debug("Finding medicine by ID: {} and anonymous user ID: {}", id, anonymousUserId);
        Medicine medicine = findMedicineById(id);
        
        if (!medicine.getUserType().equals(UserType.ANONYMOUS) || 
            !medicine.getAnonymousUserId().equals(anonymousUserId)) {
            throw new MedicineNotFoundException("Medicine not found with ID: " + id + " for anonymous user ID: " + anonymousUserId);
        }
        
        return medicine;
    }

    /**
     * Update medicine for registered user
     */
    public Medicine updateMedicineForUser(Long id, UpdateMedicineRequest request, Long userId) {
        log.info("Updating medicine ID: {} for user ID: {}", id, userId);

        Medicine medicine = findMedicineByIdAndUserId(id, userId);
        updateMedicineFields(medicine, request);

        Medicine updatedMedicine = medicineRepository.save(medicine);
        log.info("Updated medicine ID: {} for user ID: {}", id, userId);

        return updatedMedicine;
    }

    /**
     * Update medicine for anonymous user
     */
    public Medicine updateMedicineForAnonymousUser(Long id, UpdateMedicineRequest request, UUID anonymousUserId) {
        log.info("Updating medicine ID: {} for anonymous user ID: {}", id, anonymousUserId);

        Medicine medicine = findMedicineByIdAndAnonymousUserId(id, anonymousUserId);
        updateMedicineFields(medicine, request);

        Medicine updatedMedicine = medicineRepository.save(medicine);
        log.info("Updated medicine ID: {} for anonymous user ID: {}", id, anonymousUserId);

        return updatedMedicine;
    }

    /**
     * Delete medicine for registered user
     */
    public void deleteMedicineForUser(Long id, Long userId) {
        log.info("Deleting medicine ID: {} for user ID: {}", id, userId);

        Medicine medicine = findMedicineByIdAndUserId(id, userId);
        medicineRepository.delete(medicine);
        
        log.info("Deleted medicine ID: {} for user ID: {}", id, userId);
    }

    /**
     * Delete medicine for anonymous user
     */
    public void deleteMedicineForAnonymousUser(Long id, UUID anonymousUserId) {
        log.info("Deleting medicine ID: {} for anonymous user ID: {}", id, anonymousUserId);

        Medicine medicine = findMedicineByIdAndAnonymousUserId(id, anonymousUserId);
        medicineRepository.delete(medicine);
        
        log.info("Deleted medicine ID: {} for anonymous user ID: {}", id, anonymousUserId);
    }

    /**
     * Get medicines for registered user with pagination
     */
    @Transactional(readOnly = true)
    public Page<Medicine> getMedicinesForUser(Long userId, Pageable pageable) {
        log.debug("Getting medicines for user ID: {} with pagination", userId);
        validateUserExists(userId);
        return medicineRepository.findByUser_Id(userId, pageable);
    }

    /**
     * Get medicines for anonymous user with pagination
     */
    @Transactional(readOnly = true)
    public Page<Medicine> getMedicinesForAnonymousUser(UUID anonymousUserId, Pageable pageable) {
        log.debug("Getting medicines for anonymous user ID: {} with pagination", anonymousUserId);
        validateAnonymousUserId(anonymousUserId);
        return medicineRepository.findByAnonymousUserId(anonymousUserId, pageable);
    }

    /**
     * Get active medicines for registered user
     */
    @Transactional(readOnly = true)
    public List<Medicine> getActiveMedicinesForUser(Long userId) {
        log.debug("Getting active medicines for user ID: {}", userId);
        validateUserExists(userId);
        return medicineRepository.findByUser_IdAndIsActiveTrue(userId);
    }

    /**
     * Get active medicines for anonymous user
     */
    @Transactional(readOnly = true)
    public List<Medicine> getActiveMedicinesForAnonymousUser(UUID anonymousUserId) {
        log.debug("Getting active medicines for anonymous user ID: {}", anonymousUserId);
        validateAnonymousUserId(anonymousUserId);
        return medicineRepository.findByAnonymousUserIdAndIsActiveTrue(anonymousUserId);
    }

    /**
     * Search medicines by name for registered user
     */
    @Transactional(readOnly = true)
    public List<Medicine> searchMedicinesByNameForUser(String name, Long userId) {
        log.debug("Searching medicines by name '{}' for user ID: {}", name, userId);
        validateNotBlank(name, "Medicine name");
        validateUserExists(userId);
        return medicineRepository.findByNameContainingIgnoreCaseAndUser_Id(name, userId);
    }

    /**
     * Search medicines by name for anonymous user
     */
    @Transactional(readOnly = true)
    public List<Medicine> searchMedicinesByNameForAnonymousUser(String name, UUID anonymousUserId) {
        log.debug("Searching medicines by name '{}' for anonymous user ID: {}", name, anonymousUserId);
        validateNotBlank(name, "Medicine name");
        validateAnonymousUserId(anonymousUserId);
        return medicineRepository.findByNameContainingIgnoreCaseAndAnonymousUserId(name, anonymousUserId);
    }

    /**
     * Get medicines expiring soon for registered user
     */
    @Transactional(readOnly = true)
    public List<Medicine> getMedicinesExpiringSoonForUser(Long userId, LocalDate expiryDate) {
        log.debug("Getting medicines expiring before {} for user ID: {}", expiryDate, userId);
        validateUserExists(userId);
        return medicineRepository.findByExpiryDateBeforeAndUser_Id(expiryDate, userId);
    }

    /**
     * Get medicines expiring soon for anonymous user
     */
    @Transactional(readOnly = true)
    public List<Medicine> getMedicinesExpiringSoonForAnonymousUser(UUID anonymousUserId, LocalDate expiryDate) {
        log.debug("Getting medicines expiring before {} for anonymous user ID: {}", expiryDate, anonymousUserId);
        validateAnonymousUserId(anonymousUserId);
        return medicineRepository.findByExpiryDateBeforeAndAnonymousUserId(expiryDate, anonymousUserId);
    }

    /**
     * Get medicines with low quantity for registered user
     */
    @Transactional(readOnly = true)
    public List<Medicine> getMedicinesWithLowQuantityForUser(Long userId, Integer threshold) {
        log.debug("Getting medicines with quantity <= {} for user ID: {}", threshold, userId);
        validateUserExists(userId);
        validatePositive(threshold, "Quantity threshold");
        return medicineRepository.findMedicinesWithLowQuantityByUserId(threshold, userId);
    }

    /**
     * Get medicines with low quantity for anonymous user
     */
    @Transactional(readOnly = true)
    public List<Medicine> getMedicinesWithLowQuantityForAnonymousUser(UUID anonymousUserId, Integer threshold) {
        log.debug("Getting medicines with quantity <= {} for anonymous user ID: {}", threshold, anonymousUserId);
        validateAnonymousUserId(anonymousUserId);
        validatePositive(threshold, "Quantity threshold");
        return medicineRepository.findMedicinesWithLowQuantityByAnonymousUserId(threshold, anonymousUserId);
    }

    /**
     * Update medicine quantity
     */
    public void updateMedicineQuantity(Long id, Integer newQuantity) {
        log.info("Updating quantity to {} for medicine ID: {}", newQuantity, id);
        validatePositive(newQuantity, "Quantity");
        
        int updatedRows = medicineRepository.updateQuantity(id, newQuantity);
        if (updatedRows == 0) {
            throw new MedicineNotFoundException("Medicine not found with ID: " + id);
        }
        
        log.info("Updated quantity to {} for medicine ID: {}", newQuantity, id);
    }

    /**
     * Update medicine active status
     */
    public void updateMedicineActiveStatus(Long id, Boolean isActive) {
        log.info("Updating active status to {} for medicine ID: {}", isActive, id);
        validateNotNull(isActive, "Active status");
        
        int updatedRows = medicineRepository.updateActiveStatus(id, isActive);
        if (updatedRows == 0) {
            throw new MedicineNotFoundException("Medicine not found with ID: " + id);
        }
        
        log.info("Updated active status to {} for medicine ID: {}", isActive, id);
    }

    /**
     * Get medicine statistics for registered user
     */
    @Transactional(readOnly = true)
    public MedicineStatsResponse getMedicineStatsForUser(Long userId) {
        log.debug("Getting medicine statistics for user ID: {}", userId);
        validateUserExists(userId);

        long totalMedicines = medicineRepository.countByUser_Id(userId);
        long activeMedicines = medicineRepository.countByUser_IdAndIsActiveTrue(userId);
        long lowQuantityMedicines = medicineRepository.findMedicinesWithLowQuantityByUserId(5, userId).size();
        long expiringMedicines = medicineRepository.findByExpiryDateBeforeAndUser_Id(LocalDate.now().plusDays(30), userId).size();

        return MedicineStatsResponse.of(
            totalMedicines,
            activeMedicines,
            lowQuantityMedicines,
            expiringMedicines
        );
    }

    /**
     * Get medicine statistics for anonymous user
     */
    @Transactional(readOnly = true)
    public MedicineStatsResponse getMedicineStatsForAnonymousUser(UUID anonymousUserId) {
        log.debug("Getting medicine statistics for anonymous user ID: {}", anonymousUserId);
        validateAnonymousUserId(anonymousUserId);

        long totalMedicines = medicineRepository.countByAnonymousUserId(anonymousUserId);
        long activeMedicines = medicineRepository.countByAnonymousUserIdAndIsActiveTrue(anonymousUserId);
        long lowQuantityMedicines = medicineRepository.findMedicinesWithLowQuantityByAnonymousUserId(5, anonymousUserId).size();
        long expiringMedicines = medicineRepository.findByExpiryDateBeforeAndAnonymousUserId(LocalDate.now().plusDays(30), anonymousUserId).size();

        return MedicineStatsResponse.of(
            totalMedicines,
            activeMedicines,
            lowQuantityMedicines,
            expiringMedicines
        );
    }

    // Private helper methods
    private void validateCreateMedicineRequest(CreateMedicineRequest request) {
        validateNotBlank(request.getName(), "Medicine name");
        validateNotNull(request.getMedicineTypeId(), "Medicine type");
        validatePositive(request.getQuantity(), "Quantity");
        
        if (request.getPrice() != null) {
            validatePositive(request.getPrice(), "Price");
        }
        
        if (request.getExpiryDate() != null) {
            validateDateRange(LocalDate.now(), request.getExpiryDate(), "Current date", "Expiry date");
        }
    }

    private void validateUserExists(Long userId) {
        if (!userRepository.existsById(userId)) {
            throw new InvalidMedicineDataException("User not found with ID: " + userId);
        }
    }

    private void validateAnonymousUserId(UUID anonymousUserId) {
        if (anonymousUserId == null) {
            throw new InvalidMedicineDataException("Anonymous user ID must not be null");
        }
    }

    private Medicine mapToMedicine(CreateMedicineRequest request) {
        MedicineType medicineType = medicineTypeRepository.findById(request.getMedicineTypeId())
                .orElseThrow(() -> new InvalidMedicineDataException("Medicine type not found with ID: " + request.getMedicineTypeId()));

        return Medicine.builder()
            .name(request.getName())
            .dosage(request.getDosage())
            .unit(request.getUnit())
            .quantity(request.getQuantity())
            .price(request.getPrice())
            .manufacturer(request.getManufacturer())
            .activeIngredient(request.getActiveIngredient())
            .expiryDate(request.getExpiryDate())
            .batchNumber(request.getBatchNumber())
            .medicineType(medicineType)
            .isActive(true)
            .build();
    }

    private void updateMedicineFields(Medicine medicine, UpdateMedicineRequest request) {
        if (request.getName() != null) {
            validateNotBlank(request.getName(), "Medicine name");
            medicine.setName(request.getName());
        }
        
        if (request.getDosage() != null) {
            medicine.setDosage(request.getDosage());
        }
        
        if (request.getUnit() != null) {
            medicine.setUnit(request.getUnit());
        }
        
        if (request.getQuantity() != null) {
            validatePositive(request.getQuantity(), "Quantity");
            medicine.setQuantity(request.getQuantity());
        }
        
        if (request.getPrice() != null) {
            validatePositive(request.getPrice(), "Price");
            medicine.setPrice(request.getPrice());
        }
        
        if (request.getManufacturer() != null) {
            medicine.setManufacturer(request.getManufacturer());
        }
        
        if (request.getActiveIngredient() != null) {
            medicine.setActiveIngredient(request.getActiveIngredient());
        }
        
        if (request.getExpiryDate() != null) {
            validateDateRange(LocalDate.now(), request.getExpiryDate(), "Current date", "Expiry date");
            medicine.setExpiryDate(request.getExpiryDate());
        }
        
        if (request.getBatchNumber() != null) {
            medicine.setBatchNumber(request.getBatchNumber());
        }
        
        if (request.getMedicineTypeId() != null) {
            MedicineType medicineType = medicineTypeRepository.findById(request.getMedicineTypeId())
                    .orElseThrow(() -> new InvalidMedicineDataException("Medicine type not found with ID: " + request.getMedicineTypeId()));
            medicine.setMedicineType(medicineType);
        }
        
        if (request.getIsActive() != null) {
            medicine.setIsActive(request.getIsActive());
        }
    }
} 