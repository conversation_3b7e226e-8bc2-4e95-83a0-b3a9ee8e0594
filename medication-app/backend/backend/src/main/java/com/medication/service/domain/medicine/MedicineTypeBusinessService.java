package com.medication.service.domain.medicine;

import com.medication.entity.MedicineType;
import org.springframework.stereotype.Service;

@Service
public class MedicineTypeBusinessService {
    
    /**
     * Check if this type is used by any medicines
     */
    public boolean isUsed(MedicineType medicineType) {
        return medicineType.getMedicines() != null && !medicineType.getMedicines().isEmpty();
    }
    
    /**
     * Get number of medicines using this type
     */
    public int getMedicineCount(MedicineType medicineType) {
        return medicineType.getMedicines() != null ? medicineType.getMedicines().size() : 0;
    }
    
    /**
     * Check if this type can be deleted (not used by any medicines)
     */
    public boolean canBeDeleted(MedicineType medicineType) {
        return !isUsed(medicineType);
    }
    
    /**
     * Get type display name
     */
    public String getDisplayName(MedicineType medicineType) {
        return medicineType.getTypeName() != null ? medicineType.getTypeName() : "Unknown Type";
    }
    
    /**
     * Check if this is a common medicine type
     */
    public boolean isCommonType(MedicineType medicineType) {
        if (medicineType.getTypeName() == null) return false;
        String lowerTypeName = medicineType.getTypeName().toLowerCase();
        return lowerTypeName.contains("tablet") || 
               lowerTypeName.contains("capsule") || 
               lowerTypeName.contains("syrup") || 
               lowerTypeName.contains("injection") ||
               lowerTypeName.contains("cream") ||
               lowerTypeName.contains("drops");
    }
    
    /**
     * Get type category based on name
     */
    public String getTypeCategory(MedicineType medicineType) {
        if (medicineType.getTypeName() == null) return "Other";
        
        String lowerTypeName = medicineType.getTypeName().toLowerCase();
        if (lowerTypeName.contains("tablet") || lowerTypeName.contains("pill")) {
            return "Solid";
        } else if (lowerTypeName.contains("syrup") || lowerTypeName.contains("liquid")) {
            return "Liquid";
        } else if (lowerTypeName.contains("injection") || lowerTypeName.contains("injectable")) {
            return "Injectable";
        } else if (lowerTypeName.contains("cream") || lowerTypeName.contains("ointment")) {
            return "Topical";
        } else if (lowerTypeName.contains("drops") || lowerTypeName.contains("eye")) {
            return "Drops";
        } else {
            return "Other";
        }
    }
    
    /**
     * Validate if type name is valid
     */
    public boolean isValidTypeName(String typeName) {
        return typeName != null && !typeName.trim().isEmpty() && typeName.length() <= 100;
    }
    
    /**
     * Check if type is active (has medicines)
     */
    public boolean isActive(MedicineType medicineType) {
        return isUsed(medicineType);
    }
    
    /**
     * Get type status description
     */
    public String getStatusDescription(MedicineType medicineType) {
        return isUsed(medicineType) ? "In Use" : "Not Used";
    }
    
    /**
     * Check if type is prescription required
     */
    public boolean isPrescriptionRequired(MedicineType medicineType) {
        if (medicineType.getTypeName() == null) return false;
        String lowerTypeName = medicineType.getTypeName().toLowerCase();
        return lowerTypeName.contains("antibiotic") || 
               lowerTypeName.contains("controlled") ||
               lowerTypeName.contains("narcotic") ||
               lowerTypeName.contains("psychotropic");
    }
    
    /**
     * Get type priority for display
     */
    public int getDisplayPriority(MedicineType medicineType) {
        if (medicineType.getTypeName() == null) return 999;
        
        String lowerTypeName = medicineType.getTypeName().toLowerCase();
        if (lowerTypeName.contains("tablet") || lowerTypeName.contains("pill")) {
            return 1;
        } else if (lowerTypeName.contains("capsule")) {
            return 2;
        } else if (lowerTypeName.contains("syrup") || lowerTypeName.contains("liquid")) {
            return 3;
        } else if (lowerTypeName.contains("injection")) {
            return 4;
        } else if (lowerTypeName.contains("cream") || lowerTypeName.contains("ointment")) {
            return 5;
        } else if (lowerTypeName.contains("drops")) {
            return 6;
        } else {
            return 999;
        }
    }
} 