package com.medication.service.domain.medicine;

import com.medication.entity.MedicineUnit;
import org.springframework.stereotype.Service;

@Service
public class MedicineUnitBusinessService {
    
    /**
     * Check if this unit is used by any medicines
     */
    public boolean isUsed(MedicineUnit medicineUnit) {
        return medicineUnit.getMedicines() != null && !medicineUnit.getMedicines().isEmpty();
    }
    
    /**
     * Get number of medicines using this unit
     */
    public int getMedicineCount(MedicineUnit medicineUnit) {
        return medicineUnit.getMedicines() != null ? medicineUnit.getMedicines().size() : 0;
    }
    
    /**
     * Check if this unit can be deleted (not used by any medicines)
     */
    public boolean canBeDeleted(MedicineUnit medicineUnit) {
        return !isUsed(medicineUnit);
    }
    
    /**
     * Get unit display name
     */
    public String getDisplayName(MedicineUnit medicineUnit) {
        return medicineUnit.getUnitName() != null ? medicineUnit.getUnitName() : "Unknown Unit";
    }
    
    /**
     * Get unit description or default
     */
    public String getDisplayDescription(MedicineUnit medicineUnit) {
        return medicineUnit.getDescription() != null && !medicineUnit.getDescription().trim().isEmpty() 
               ? medicineUnit.getDescription() 
               : "No description available";
    }
    
    /**
     * Validate if unit name is valid
     */
    public boolean isValidUnitName(String unitName) {
        return unitName != null && !unitName.trim().isEmpty() && unitName.length() <= 50;
    }
    
    /**
     * Validate if description is valid
     */
    public boolean isValidDescription(String description) {
        return description == null || description.length() <= 200;
    }
    
    /**
     * Check if unit is active (has medicines)
     */
    public boolean isActive(MedicineUnit medicineUnit) {
        return isUsed(medicineUnit);
    }
    
    /**
     * Get unit status description
     */
    public String getStatusDescription(MedicineUnit medicineUnit) {
        return isUsed(medicineUnit) ? "In Use" : "Not Used";
    }
} 