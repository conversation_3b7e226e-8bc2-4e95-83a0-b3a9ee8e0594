package com.medication.service.domain.medicine;

import com.medication.entity.Medicine;
import com.medication.entity.MedicineType;
import com.medication.entity.MedicineUnit;
import com.medication.exception.common.ValidationException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Service for Medicine validation logic
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class MedicineValidationService {

    /**
     * Validate medicine for creation
     */
    public void validateForCreation(Medicine medicine) {
        Map<String, String> errors = new HashMap<>();

        if (medicine == null) {
            throw new ValidationException("Medicine cannot be null");
        }

        if (medicine.getName() == null || medicine.getName().trim().isEmpty()) {
            errors.put("name", "Medicine name is required");
        } else if (medicine.getName().length() > 200) {
            errors.put("name", "Medicine name cannot exceed 200 characters");
        }

        if (medicine.getDosage() != null && medicine.getDosage().length() > 100) {
            errors.put("dosage", "Dosage cannot exceed 100 characters");
        }

        if (medicine.getUnit() != null && medicine.getUnit().length() > 50) {
            errors.put("unit", "Unit cannot exceed 50 characters");
        }

        if (medicine.getQuantity() == null) {
            errors.put("quantity", "Quantity is required");
        } else if (medicine.getQuantity() < 0) {
            errors.put("quantity", "Quantity cannot be negative");
        }

        if (medicine.getManufacturer() != null && medicine.getManufacturer().length() > 200) {
            errors.put("manufacturer", "Manufacturer cannot exceed 200 characters");
        }

        if (medicine.getActiveIngredient() != null && medicine.getActiveIngredient().length() > 500) {
            errors.put("activeIngredient", "Active ingredient cannot exceed 500 characters");
        }

        if (medicine.getBatchNumber() != null && medicine.getBatchNumber().length() > 100) {
            errors.put("batchNumber", "Batch number cannot exceed 100 characters");
        }

        if (medicine.getMedicineType() == null) {
            errors.put("medicineType", "Medicine type is required");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("Medicine creation validation failed", errors);
        }
    }

    /**
     * Validate medicine for update
     */
    public void validateForUpdate(Medicine medicine) {
        Map<String, String> errors = new HashMap<>();

        if (medicine == null) {
            throw new ValidationException("Medicine cannot be null");
        }

        if (medicine.getId() == null) {
            errors.put("id", "Medicine ID is required for update");
        }

        if (medicine.getName() != null && medicine.getName().trim().isEmpty()) {
            errors.put("name", "Medicine name cannot be empty");
        } else if (medicine.getName() != null && medicine.getName().length() > 200) {
            errors.put("name", "Medicine name cannot exceed 200 characters");
        }

        if (medicine.getDosage() != null && medicine.getDosage().length() > 100) {
            errors.put("dosage", "Dosage cannot exceed 100 characters");
        }

        if (medicine.getUnit() != null && medicine.getUnit().length() > 50) {
            errors.put("unit", "Unit cannot exceed 50 characters");
        }

        if (medicine.getQuantity() != null && medicine.getQuantity() < 0) {
            errors.put("quantity", "Quantity cannot be negative");
        }

        if (medicine.getManufacturer() != null && medicine.getManufacturer().length() > 200) {
            errors.put("manufacturer", "Manufacturer cannot exceed 200 characters");
        }

        if (medicine.getActiveIngredient() != null && medicine.getActiveIngredient().length() > 500) {
            errors.put("activeIngredient", "Active ingredient cannot exceed 500 characters");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("Medicine update validation failed", errors);
        }
    }

    /**
     * Validate medicine type
     */
    public void validateMedicineType(MedicineType medicineType) {
        Map<String, String> errors = new HashMap<>();

        if (medicineType == null) {
            throw new ValidationException("Medicine type cannot be null");
        }

        if (medicineType.getTypeName() == null || medicineType.getTypeName().trim().isEmpty()) {
            errors.put("typeName", "Medicine type name is required");
        } else if (medicineType.getTypeName().length() > 100) {
            errors.put("typeName", "Medicine type name cannot exceed 100 characters");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("Medicine type validation failed", errors);
        }
    }

    /**
     * Validate medicine unit
     */
    public void validateMedicineUnit(MedicineUnit medicineUnit) {
        Map<String, String> errors = new HashMap<>();

        if (medicineUnit == null) {
            throw new ValidationException("Medicine unit cannot be null");
        }

        if (medicineUnit.getUnitName() == null || medicineUnit.getUnitName().trim().isEmpty()) {
            errors.put("unitName", "Medicine unit name is required");
        } else if (medicineUnit.getUnitName().length() > 50) {
            errors.put("unitName", "Medicine unit name cannot exceed 50 characters");
        }

        if (medicineUnit.getDescription() != null && medicineUnit.getDescription().length() > 200) {
            errors.put("description", "Description cannot exceed 200 characters");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("Medicine unit validation failed", errors);
        }
    }
} 