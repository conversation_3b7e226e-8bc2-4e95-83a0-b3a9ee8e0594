package com.medication.service.domain.notification;

import com.medication.entity.Notification;
import com.medication.enums.NotificationStatus;
import com.medication.util.DateUtils;
import com.medication.util.ValidationUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.Duration;

/**
 * Service for Notification business logic
 */
@Service
@Transactional
public class NotificationBusinessService {

    /**
     * Mark notification as read
     */
    public void markAsRead(Notification notification) {
        notification.setIsRead(true);
        notification.setReadAt(LocalDateTime.now());
    }

    /**
     * Mark notification as sent
     */
    public void markAsSent(Notification notification) {
        notification.setStatus(NotificationStatus.SENT);
        notification.setSentAt(LocalDateTime.now());
    }

    /**
     * Mark notification as failed
     */
    public void markAsFailed(Notification notification) {
        notification.setStatus(NotificationStatus.FAILED);
    }

    /**
     * Mark notification as cancelled
     */
    public void markAsCancelled(Notification notification) {
        notification.setStatus(NotificationStatus.CANCELLED);
    }

    /**
     * Increment retry count
     */
    public void incrementRetryCount(Notification notification) {
        if (notification.getRetryCount() == null) {
            notification.setRetryCount(0);
        }
        notification.setRetryCount(notification.getRetryCount() + 1);
    }

    /**
     * Check if notification can be retried
     */
    public boolean canRetry(Notification notification, int maxRetries) {
        return notification.getStatus() == NotificationStatus.FAILED && 
               notification.getRetryCount() < maxRetries;
    }

    /**
     * Check if notification is urgent
     */
    public boolean isUrgent(Notification notification) {
        return notification.getType() != null && notification.getType().isUrgent();
    }

    /**
     * Check if notification is a reminder
     */
    public boolean isReminder(Notification notification) {
        return notification.getType() != null && notification.getType().isReminder();
    }

    /**
     * Check if notification is pending
     */
    public boolean isPending(Notification notification) {
        return notification.getStatus() != null && notification.getStatus().isPending();
    }

    /**
     * Check if notification is sent
     */
    public boolean isSent(Notification notification) {
        return notification.getStatus() != null && notification.getStatus().isSent();
    }

    /**
     * Check if notification failed
     */
    public boolean isFailed(Notification notification) {
        return notification.getStatus() != null && notification.getStatus().isFailed();
    }

    /**
     * Check if notification is cancelled
     */
    public boolean isCancelled(Notification notification) {
        return notification.getStatus() != null && notification.getStatus().isCancelled();
    }

    /**
     * Check if notification is in final state
     */
    public boolean isFinal(Notification notification) {
        return notification.getStatus() != null && notification.getStatus().isFinal();
    }

    /**
     * Get priority level
     */
    public int getPriority(Notification notification) {
        return notification.getType() != null ? notification.getType().getPriority() : 0;
    }

    /**
     * Check if notification is overdue (scheduled but not sent)
     */
    public boolean isOverdue(Notification notification) {
        return notification.getScheduledAt() != null && 
               LocalDateTime.now().isAfter(notification.getScheduledAt()) && 
               notification.getStatus() == NotificationStatus.PENDING;
    }

    /**
     * Get time until scheduled
     */
    public Duration getTimeUntilScheduled(Notification notification) {
        return DateUtils.getTimeUntilScheduled(notification.getScheduledAt());
    }

    /**
     * Get time since sent
     */
    public Duration getTimeSinceSent(Notification notification) {
        return DateUtils.getTimeSince(notification.getSentAt());
    }

    /**
     * Get time since read
     */
    public Duration getTimeSinceRead(Notification notification) {
        return DateUtils.getTimeSince(notification.getReadAt());
    }

    /**
     * Validate notification data
     */
    public void validateNotification(Notification notification) {
        if (!ValidationUtils.isRetryCountValid(notification.getRetryCount())) {
            throw new IllegalArgumentException("Retry count must be non-negative");
        }

        if (notification.getScheduledAt() != null && 
            notification.getCreatedAt() != null && 
            notification.getScheduledAt().isBefore(notification.getCreatedAt())) {
            throw new IllegalArgumentException("Scheduled time cannot be before creation time");
        }
    }

    /**
     * Check if notification should be processed
     */
    public boolean shouldProcess(Notification notification) {
        return notification.getStatus() == NotificationStatus.PENDING &&
               (notification.getScheduledAt() == null || 
                LocalDateTime.now().isAfter(notification.getScheduledAt()));
    }

    /**
     * Check if notification is expired (too old to process)
     */
    public boolean isExpired(Notification notification, Duration maxAge) {
        if (notification.getCreatedAt() == null) return false;
        
        Duration age = DateUtils.getTimeSince(notification.getCreatedAt());
        return age.compareTo(maxAge) > 0;
    }
} 