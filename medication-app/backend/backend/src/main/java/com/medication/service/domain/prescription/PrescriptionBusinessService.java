package com.medication.service.domain.prescription;

import com.medication.dto.request.CreatePrescriptionRequest;
import com.medication.dto.request.UpdatePrescriptionRequest;
import com.medication.dto.response.PrescriptionStatsResponse;
import com.medication.entity.Prescription;
import com.medication.entity.User;
import com.medication.entity.Family;
import com.medication.enums.PrescriptionStatus;
import com.medication.enums.UserType;
import com.medication.exception.domain.prescription.PrescriptionNotFoundException;
import com.medication.exception.domain.prescription.InvalidPrescriptionDataException;
import com.medication.exception.domain.user.UserNotFoundException;
import com.medication.repository.domain.prescription.PrescriptionRepository;
import com.medication.repository.domain.user.UserRepository;
import com.medication.repository.domain.family.FamilyRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Business service cho Prescription entity
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class PrescriptionBusinessService {
    
    private final PrescriptionRepository prescriptionRepository;
    private final UserRepository userRepository;
    private final FamilyRepository familyRepository;
    
    // ==================== REGISTERED USER METHODS ====================
    
    /**
     * Tạo prescription cho registered user
     */
    public Prescription createPrescriptionForUser(CreatePrescriptionRequest request, Long userId) {
        log.info("Creating prescription for user ID: {}", userId);
        
        validateCreateRequest(request);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + userId));
        
        Prescription prescription = mapToPrescription(request);
        prescription.setPatient(user);
        prescription.setUserType(UserType.REGISTERED);
        
        Prescription savedPrescription = prescriptionRepository.save(prescription);
        log.info("Created prescription with ID: {} for user ID: {}", savedPrescription.getId(), userId);
        
        return savedPrescription;
    }
    
    /**
     * Tìm prescription theo ID cho registered user
     */
    @Transactional(readOnly = true)
    public Prescription findPrescriptionByIdForUser(Long prescriptionId, Long userId) {
        log.debug("Finding prescription ID: {} for user ID: {}", prescriptionId, userId);
        
        Prescription prescription = prescriptionRepository.findById(prescriptionId)
                .orElseThrow(() -> new PrescriptionNotFoundException(prescriptionId));
        
        if (!prescription.getPatient().getId().equals(userId)) {
            throw new PrescriptionNotFoundException("Prescription not found for user ID: " + userId);
        }
        
        return prescription;
    }
    
    /**
     * Lấy danh sách prescriptions cho registered user với pagination
     */
    @Transactional(readOnly = true)
    public Page<Prescription> getPrescriptionsForUser(Long userId, Pageable pageable) {
        log.debug("Getting prescriptions for user ID: {} with pagination", userId);
        return prescriptionRepository.findByPatient_Id(userId, pageable);
    }
    
    /**
     * Lấy danh sách prescriptions cho registered user
     */
    @Transactional(readOnly = true)
    public List<Prescription> getAllPrescriptionsForUser(Long userId) {
        log.debug("Getting all prescriptions for user ID: {}", userId);
        return prescriptionRepository.findByPatient_Id(userId);
    }
    
    /**
     * Cập nhật prescription cho registered user
     */
    public Prescription updatePrescriptionForUser(Long prescriptionId, UpdatePrescriptionRequest request, Long userId) {
        log.info("Updating prescription ID: {} for user ID: {}", prescriptionId, userId);
        
        Prescription prescription = findPrescriptionByIdForUser(prescriptionId, userId);
        updatePrescriptionFields(prescription, request);
        
        Prescription updatedPrescription = prescriptionRepository.save(prescription);
        log.info("Updated prescription ID: {} for user ID: {}", prescriptionId, userId);
        
        return updatedPrescription;
    }
    
    /**
     * Xóa prescription cho registered user
     */
    public void deletePrescriptionForUser(Long prescriptionId, Long userId) {
        log.info("Deleting prescription ID: {} for user ID: {}", prescriptionId, userId);
        
        Prescription prescription = findPrescriptionByIdForUser(prescriptionId, userId);
        prescriptionRepository.delete(prescription);
        
        log.info("Deleted prescription ID: {} for user ID: {}", prescriptionId, userId);
    }
    
    // ==================== ANONYMOUS USER METHODS ====================
    
    /**
     * Tạo prescription cho anonymous user
     */
    public Prescription createPrescriptionForAnonymousUser(CreatePrescriptionRequest request, UUID anonymousUserId) {
        log.info("Creating prescription for anonymous user ID: {}", anonymousUserId);
        
        validateCreateRequest(request);
        
        Prescription prescription = mapToPrescription(request);
        prescription.setAnonymousUserId(anonymousUserId);
        prescription.setUserType(UserType.ANONYMOUS);
        
        Prescription savedPrescription = prescriptionRepository.save(prescription);
        log.info("Created prescription with ID: {} for anonymous user ID: {}", savedPrescription.getId(), anonymousUserId);
        
        return savedPrescription;
    }
    
    /**
     * Tìm prescription theo ID cho anonymous user
     */
    @Transactional(readOnly = true)
    public Prescription findPrescriptionByIdForAnonymousUser(Long prescriptionId, UUID anonymousUserId) {
        log.debug("Finding prescription ID: {} for anonymous user ID: {}", prescriptionId, anonymousUserId);
        
        Prescription prescription = prescriptionRepository.findById(prescriptionId)
                .orElseThrow(() -> new PrescriptionNotFoundException(prescriptionId));
        
        if (!prescription.getAnonymousUserId().equals(anonymousUserId)) {
            throw new PrescriptionNotFoundException("Prescription not found for anonymous user ID: " + anonymousUserId);
        }
        
        return prescription;
    }
    
    /**
     * Lấy danh sách prescriptions cho anonymous user với pagination
     */
    @Transactional(readOnly = true)
    public Page<Prescription> getPrescriptionsForAnonymousUser(UUID anonymousUserId, Pageable pageable) {
        log.debug("Getting prescriptions for anonymous user ID: {} with pagination", anonymousUserId);
        return prescriptionRepository.findByAnonymousUserId(anonymousUserId, pageable);
    }
    
    /**
     * Lấy danh sách prescriptions cho anonymous user
     */
    @Transactional(readOnly = true)
    public List<Prescription> getAllPrescriptionsForAnonymousUser(UUID anonymousUserId) {
        log.debug("Getting all prescriptions for anonymous user ID: {}", anonymousUserId);
        return prescriptionRepository.findByAnonymousUserId(anonymousUserId);
    }
    
    /**
     * Cập nhật prescription cho anonymous user
     */
    public Prescription updatePrescriptionForAnonymousUser(Long prescriptionId, UpdatePrescriptionRequest request, UUID anonymousUserId) {
        log.info("Updating prescription ID: {} for anonymous user ID: {}", prescriptionId, anonymousUserId);
        
        Prescription prescription = findPrescriptionByIdForAnonymousUser(prescriptionId, anonymousUserId);
        updatePrescriptionFields(prescription, request);
        
        Prescription updatedPrescription = prescriptionRepository.save(prescription);
        log.info("Updated prescription ID: {} for anonymous user ID: {}", prescriptionId, anonymousUserId);
        
        return updatedPrescription;
    }
    
    /**
     * Xóa prescription cho anonymous user
     */
    public void deletePrescriptionForAnonymousUser(Long prescriptionId, UUID anonymousUserId) {
        log.info("Deleting prescription ID: {} for anonymous user ID: {}", prescriptionId, anonymousUserId);
        
        Prescription prescription = findPrescriptionByIdForAnonymousUser(prescriptionId, anonymousUserId);
        prescriptionRepository.delete(prescription);
        
        log.info("Deleted prescription ID: {} for anonymous user ID: {}", prescriptionId, anonymousUserId);
    }
    
    // ==================== SEARCH AND FILTER METHODS ====================
    
    /**
     * Tìm prescriptions theo doctor name
     */
    @Transactional(readOnly = true)
    public List<Prescription> searchByDoctorName(Long userId, String doctorName) {
        log.debug("Searching prescriptions by doctor name: {} for user ID: {}", doctorName, userId);
        return prescriptionRepository.findByPatient_IdAndDoctorNameContainingIgnoreCase(userId, doctorName);
    }
    
    /**
     * Tìm prescriptions theo doctor name cho anonymous user
     */
    @Transactional(readOnly = true)
    public List<Prescription> searchByDoctorNameForAnonymousUser(UUID anonymousUserId, String doctorName) {
        log.debug("Searching prescriptions by doctor name: {} for anonymous user ID: {}", doctorName, anonymousUserId);
        return prescriptionRepository.findByAnonymousUserIdAndDoctorNameContainingIgnoreCase(anonymousUserId, doctorName);
    }
    
    /**
     * Tìm prescriptions theo diagnosis
     */
    @Transactional(readOnly = true)
    public List<Prescription> searchByDiagnosis(Long userId, String diagnosis) {
        log.debug("Searching prescriptions by diagnosis: {} for user ID: {}", diagnosis, userId);
        return prescriptionRepository.findByPatient_IdAndDiagnosisContainingIgnoreCase(userId, diagnosis);
    }
    
    /**
     * Tìm prescriptions theo diagnosis cho anonymous user
     */
    @Transactional(readOnly = true)
    public List<Prescription> searchByDiagnosisForAnonymousUser(UUID anonymousUserId, String diagnosis) {
        log.debug("Searching prescriptions by diagnosis: {} for anonymous user ID: {}", diagnosis, anonymousUserId);
        return prescriptionRepository.findByAnonymousUserIdAndDiagnosisContainingIgnoreCase(anonymousUserId, diagnosis);
    }
    
    /**
     * Tìm prescriptions theo date range
     */
    @Transactional(readOnly = true)
    public List<Prescription> findByDateRange(Long userId, LocalDate startDate, LocalDate endDate) {
        log.debug("Finding prescriptions by date range: {} to {} for user ID: {}", startDate, endDate, userId);
        return prescriptionRepository.findByPatient_IdAndPrescriptionDateBetween(userId, startDate, endDate);
    }
    
    /**
     * Tìm prescriptions theo date range cho anonymous user
     */
    @Transactional(readOnly = true)
    public List<Prescription> findByDateRangeForAnonymousUser(UUID anonymousUserId, LocalDate startDate, LocalDate endDate) {
        log.debug("Finding prescriptions by date range: {} to {} for anonymous user ID: {}", startDate, endDate, anonymousUserId);
        return prescriptionRepository.findByAnonymousUserIdAndPrescriptionDateBetween(anonymousUserId, startDate, endDate);
    }
    
    // ==================== UPDATE OPERATIONS ====================
    
    /**
     * Cập nhật compliance rate
     */
    public void updateComplianceRate(Long prescriptionId, Double complianceRate, Integer takenDoses) {
        log.info("Updating compliance rate for prescription ID: {}", prescriptionId);
        
        int updatedRows = prescriptionRepository.updateComplianceRate(prescriptionId, complianceRate, takenDoses);
        if (updatedRows == 0) {
            throw new PrescriptionNotFoundException(prescriptionId);
        }
        
        log.info("Updated compliance rate for prescription ID: {}", prescriptionId);
    }
    
    /**
     * Cập nhật status
     */
    public void updateStatus(Long prescriptionId, PrescriptionStatus status) {
        log.info("Updating status to {} for prescription ID: {}", status, prescriptionId);
        
        int updatedRows = prescriptionRepository.updateStatus(prescriptionId, status);
        if (updatedRows == 0) {
            throw new PrescriptionNotFoundException(prescriptionId);
        }
        
        log.info("Updated status for prescription ID: {}", prescriptionId);
    }
    
    /**
     * Cập nhật active status
     */
    public void updateActiveStatus(Long prescriptionId, Boolean isActive) {
        log.info("Updating active status to {} for prescription ID: {}", isActive, prescriptionId);
        
        int updatedRows = prescriptionRepository.updateActiveStatus(prescriptionId, isActive);
        if (updatedRows == 0) {
            throw new PrescriptionNotFoundException(prescriptionId);
        }
        
        log.info("Updated active status for prescription ID: {}", prescriptionId);
    }
    
    // ==================== STATISTICS METHODS ====================
    
    /**
     * Lấy thống kê prescriptions cho user
     */
    @Transactional(readOnly = true)
    public PrescriptionStatsResponse getPrescriptionStatsForUser(Long userId) {
        log.debug("Getting prescription stats for user ID: {}", userId);
        
        long totalPrescriptions = prescriptionRepository.countByPatient_Id(userId);
        long activePrescriptions = prescriptionRepository.countByPatient_IdAndStatus(userId, PrescriptionStatus.ACTIVE);
        long completedPrescriptions = prescriptionRepository.countByPatient_IdAndStatus(userId, PrescriptionStatus.COMPLETED);
        long expiredPrescriptions = prescriptionRepository.countByPatient_IdAndStatus(userId, PrescriptionStatus.EXPIRED);
        
        // TODO: Implement more complex statistics
        double averageComplianceRate = 0.0;
        long lowCompliancePrescriptions = 0;
        long prescriptionsCreatedLast30Days = 0;
        long prescriptionsExpiringNext7Days = 0;
        
        return PrescriptionStatsResponse.of(
            totalPrescriptions, activePrescriptions, completedPrescriptions, expiredPrescriptions,
            lowCompliancePrescriptions, averageComplianceRate, prescriptionsCreatedLast30Days, prescriptionsExpiringNext7Days
        );
    }
    
    /**
     * Lấy thống kê prescriptions cho anonymous user
     */
    @Transactional(readOnly = true)
    public PrescriptionStatsResponse getPrescriptionStatsForAnonymousUser(UUID anonymousUserId) {
        log.debug("Getting prescription stats for anonymous user ID: {}", anonymousUserId);
        
        long totalPrescriptions = prescriptionRepository.countByAnonymousUserId(anonymousUserId);
        long activePrescriptions = prescriptionRepository.countByAnonymousUserIdAndStatus(anonymousUserId, PrescriptionStatus.ACTIVE);
        long completedPrescriptions = prescriptionRepository.countByAnonymousUserIdAndStatus(anonymousUserId, PrescriptionStatus.COMPLETED);
        long expiredPrescriptions = prescriptionRepository.countByAnonymousUserIdAndStatus(anonymousUserId, PrescriptionStatus.EXPIRED);
        
        // TODO: Implement more complex statistics
        double averageComplianceRate = 0.0;
        long lowCompliancePrescriptions = 0;
        long prescriptionsCreatedLast30Days = 0;
        long prescriptionsExpiringNext7Days = 0;
        
        return PrescriptionStatsResponse.of(
            totalPrescriptions, activePrescriptions, completedPrescriptions, expiredPrescriptions,
            lowCompliancePrescriptions, averageComplianceRate, prescriptionsCreatedLast30Days, prescriptionsExpiringNext7Days
        );
    }
    
    // ==================== HELPER METHODS ====================
    
    /**
     * Map CreatePrescriptionRequest to Prescription entity
     */
    private Prescription mapToPrescription(CreatePrescriptionRequest request) {
        User patient = null;
        if (request.getPatientId() != null) {
            patient = userRepository.findById(request.getPatientId())
                    .orElseThrow(() -> new InvalidPrescriptionDataException("Patient not found with ID: " + request.getPatientId()));
        }
        
        User prescribedBy = null;
        if (request.getPrescribedById() != null) {
            prescribedBy = userRepository.findById(request.getPrescribedById())
                    .orElseThrow(() -> new InvalidPrescriptionDataException("Prescribed by user not found with ID: " + request.getPrescribedById()));
        }
        
        Family family = null;
        if (request.getFamilyId() != null) {
            family = familyRepository.findById(request.getFamilyId())
                    .orElseThrow(() -> new InvalidPrescriptionDataException("Family not found with ID: " + request.getFamilyId()));
        }
        
        return Prescription.builder()
            .doctorName(request.getDoctorName())
            .prescriptionDate(request.getPrescriptionDate())
            .startDate(request.getStartDate())
            .endDate(request.getEndDate())
            .diagnosis(request.getDiagnosis())
            .notes(request.getNotes())
            .patient(patient)
            .prescribedBy(prescribedBy)
            .family(family)
            .complianceRate(request.getComplianceRate())
            .totalDoses(request.getTotalDoses())
            .takenDoses(request.getTakenDoses())
            .isActive(request.getIsActive())
            .status(PrescriptionStatus.ACTIVE)
            .build();
    }
    
    /**
     * Update prescription fields from UpdatePrescriptionRequest
     */
    private void updatePrescriptionFields(Prescription prescription, UpdatePrescriptionRequest request) {
        if (StringUtils.hasText(request.getDoctorName())) {
            prescription.setDoctorName(request.getDoctorName());
        }
        if (request.getPrescriptionDate() != null) {
            prescription.setPrescriptionDate(request.getPrescriptionDate());
        }
        if (request.getStartDate() != null) {
            prescription.setStartDate(request.getStartDate());
        }
        if (request.getEndDate() != null) {
            prescription.setEndDate(request.getEndDate());
        }
        if (StringUtils.hasText(request.getDiagnosis())) {
            prescription.setDiagnosis(request.getDiagnosis());
        }
        if (request.getNotes() != null) {
            prescription.setNotes(request.getNotes());
        }
        if (request.getPatientId() != null) {
            User patient = userRepository.findById(request.getPatientId())
                    .orElseThrow(() -> new InvalidPrescriptionDataException("Patient not found with ID: " + request.getPatientId()));
            prescription.setPatient(patient);
        }
        if (request.getPrescribedById() != null) {
            User prescribedBy = userRepository.findById(request.getPrescribedById())
                    .orElseThrow(() -> new InvalidPrescriptionDataException("Prescribed by user not found with ID: " + request.getPrescribedById()));
            prescription.setPrescribedBy(prescribedBy);
        }
        if (request.getFamilyId() != null) {
            Family family = familyRepository.findById(request.getFamilyId())
                    .orElseThrow(() -> new InvalidPrescriptionDataException("Family not found with ID: " + request.getFamilyId()));
            prescription.setFamily(family);
        }
        if (request.getComplianceRate() != null) {
            prescription.setComplianceRate(request.getComplianceRate());
        }
        if (request.getTotalDoses() != null) {
            prescription.setTotalDoses(request.getTotalDoses());
        }
        if (request.getTakenDoses() != null) {
            prescription.setTakenDoses(request.getTakenDoses());
        }
        if (request.getIsActive() != null) {
            prescription.setIsActive(request.getIsActive());
        }
    }
    
    /**
     * Validate CreatePrescriptionRequest
     */
    private void validateCreateRequest(CreatePrescriptionRequest request) {
        validateNotBlank(request.getDoctorName(), "Doctor name");
        validateNotNull(request.getPrescriptionDate(), "Prescription date");
        validateNotNull(request.getStartDate(), "Start date");
        validateNotNull(request.getEndDate(), "End date");
        validateNotBlank(request.getDiagnosis(), "Diagnosis");
        
        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new InvalidPrescriptionDataException("Start date must be before or equal to end date");
        }
        
        if (request.getPrescriptionDate().isAfter(request.getStartDate())) {
            throw new InvalidPrescriptionDataException("Prescription date must be before or equal to start date");
        }
        
        if (request.getTakenDoses() != null && request.getTotalDoses() != null) {
            if (request.getTakenDoses() > request.getTotalDoses()) {
                throw new InvalidPrescriptionDataException("Taken doses cannot exceed total doses");
            }
        }
    }
    
    /**
     * Validate not blank
     */
    private void validateNotBlank(String value, String fieldName) {
        if (!StringUtils.hasText(value)) {
            throw new InvalidPrescriptionDataException(fieldName + " cannot be blank");
        }
    }
    
    /**
     * Validate not null
     */
    private void validateNotNull(Object value, String fieldName) {
        if (value == null) {
            throw new InvalidPrescriptionDataException(fieldName + " cannot be null");
        }
    }
} 