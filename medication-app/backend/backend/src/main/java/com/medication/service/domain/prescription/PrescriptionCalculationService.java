package com.medication.service.domain.prescription;

import com.medication.entity.Prescription;
import com.medication.entity.PrescriptionItem;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * Service for Prescription calculation logic
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class PrescriptionCalculationService {

    /**
     * Calculate compliance rate for prescription
     */
    public BigDecimal calculateComplianceRate(Prescription prescription) {
        if (prescription.getTotalDoses() == null || prescription.getTotalDoses() == 0) {
            return BigDecimal.ZERO;
        }

        if (prescription.getTakenDoses() == null) {
            prescription.setTakenDoses(0);
        }

        double rate = (double) prescription.getTakenDoses() / prescription.getTotalDoses() * 100;
        return BigDecimal.valueOf(rate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Calculate remaining doses
     */
    public Integer calculateRemainingDoses(Prescription prescription) {
        if (prescription.getTotalDoses() == null) {
            return 0;
        }

        int takenDoses = prescription.getTakenDoses() != null ? prescription.getTakenDoses() : 0;
        return Math.max(0, prescription.getTotalDoses() - takenDoses);
    }

    /**
     * Calculate progress percentage
     */
    public BigDecimal calculateProgressPercentage(Prescription prescription) {
        if (prescription.getStartDate() == null || prescription.getEndDate() == null) {
            return BigDecimal.ZERO;
        }

        long totalDays = ChronoUnit.DAYS.between(prescription.getStartDate(), prescription.getEndDate());
        if (totalDays <= 0) {
            return BigDecimal.valueOf(100);
        }

        long elapsedDays = ChronoUnit.DAYS.between(prescription.getStartDate(), LocalDate.now());
        double percentage = Math.min(100.0, Math.max(0.0, (double) elapsedDays / totalDays * 100));
        return BigDecimal.valueOf(percentage).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Calculate remaining days
     */
    public Integer calculateRemainingDays(Prescription prescription) {
        if (prescription.getEndDate() == null) {
            return null;
        }

        long days = ChronoUnit.DAYS.between(LocalDate.now(), prescription.getEndDate());
        return Math.max(0, (int) days);
    }

    /**
     * Calculate total cost for prescription
     */
    public BigDecimal calculateTotalCost(Prescription prescription) {
        if (prescription.getPrescriptionItems() == null || prescription.getPrescriptionItems().isEmpty()) {
            return BigDecimal.ZERO;
        }

        return prescription.getPrescriptionItems().stream()
                .map(this::calculateItemCost)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Calculate cost for prescription item
     */
    public BigDecimal calculateItemCost(PrescriptionItem item) {
        if (item.getQuantity() == null) {
            return BigDecimal.ZERO;
        }

        // Assuming price is stored in medicine or calculated elsewhere
        // For now, return quantity as cost placeholder
        return BigDecimal.valueOf(item.getQuantity());
    }

    /**
     * Calculate average daily dose
     */
    public BigDecimal calculateAverageDailyDose(Prescription prescription) {
        if (prescription.getTotalDoses() == null || prescription.getStartDate() == null || prescription.getEndDate() == null) {
            return BigDecimal.ZERO;
        }

        long totalDays = ChronoUnit.DAYS.between(prescription.getStartDate(), prescription.getEndDate());
        if (totalDays <= 0) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(prescription.getTotalDoses())
                .divide(BigDecimal.valueOf(totalDays), 2, RoundingMode.HALF_UP);
    }

    /**
     * Calculate missed doses
     */
    public Integer calculateMissedDoses(Prescription prescription) {
        if (prescription.getTotalDoses() == null) {
            return 0;
        }

        int takenDoses = prescription.getTakenDoses() != null ? prescription.getTakenDoses() : 0;
        return Math.max(0, prescription.getTotalDoses() - takenDoses);
    }

    /**
     * Update prescription calculations (only compliance rate for now)
     */
    public void updatePrescriptionCalculations(Prescription prescription) {
        prescription.setComplianceRate(calculateComplianceRate(prescription));
        // Note: Other calculations would need to be added to Prescription entity
        // or returned as separate values
    }
} 