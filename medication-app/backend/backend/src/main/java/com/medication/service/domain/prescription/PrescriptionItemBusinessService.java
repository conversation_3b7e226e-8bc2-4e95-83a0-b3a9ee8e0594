package com.medication.service.domain.prescription;

import com.medication.dto.request.CreatePrescriptionItemRequest;
import com.medication.dto.request.UpdatePrescriptionItemRequest;
import com.medication.entity.PrescriptionItem;
import com.medication.entity.Prescription;
import com.medication.entity.Medicine;
import com.medication.enums.DoseUnit;
import com.medication.enums.DoseStatus;
import com.medication.exception.domain.prescription.PrescriptionNotFoundException;
import com.medication.exception.domain.prescription.InvalidPrescriptionDataException;
import com.medication.exception.domain.medicine.MedicineNotFoundException;
import com.medication.repository.domain.prescription.PrescriptionItemRepository;
import com.medication.repository.domain.prescription.PrescriptionRepository;
import com.medication.repository.domain.medicine.MedicineRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Business service cho PrescriptionItem entity
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class PrescriptionItemBusinessService {

    private final PrescriptionItemRepository prescriptionItemRepository;
    private final PrescriptionRepository prescriptionRepository;
    private final MedicineRepository medicineRepository;

    // ==================== REGISTERED USER METHODS ====================

    /**
     * Tạo prescription item cho registered user
     */
    public PrescriptionItem createPrescriptionItemForUser(CreatePrescriptionItemRequest request, Long userId) {
        log.info("Creating prescription item for user ID: {}", userId);

        validateCreateRequest(request);

        // Validate prescription belongs to user
        Prescription prescription = prescriptionRepository.findByIdAndPatient_Id(request.getPrescriptionId(), userId)
                .orElseThrow(() -> new PrescriptionNotFoundException("Prescription not found with ID: " + request.getPrescriptionId() + " for user: " + userId));

        // Validate medicine exists
        Medicine medicine = medicineRepository.findById(request.getMedicineId())
                .orElseThrow(() -> new MedicineNotFoundException("Medicine not found with ID: " + request.getMedicineId()));

        PrescriptionItem prescriptionItem = mapToPrescriptionItem(request, prescription, medicine);

        PrescriptionItem savedItem = prescriptionItemRepository.save(prescriptionItem);
        log.info("Created prescription item with ID: {} for user ID: {}", savedItem.getId(), userId);

        return savedItem;
    }

    /**
     * Tìm prescription item theo ID cho registered user
     */
    public PrescriptionItem findPrescriptionItemByIdForUser(Long id, Long userId) {
        log.debug("Finding prescription item ID: {} for user ID: {}", id, userId);

        return prescriptionItemRepository.findByIdAndPatientId(id, userId)
                .orElseThrow(() -> new PrescriptionNotFoundException("Prescription item not found with ID: " + id + " for user: " + userId));
    }

    /**
     * Lấy danh sách prescription items cho registered user
     */
    public List<PrescriptionItem> getPrescriptionItemsForUser(Long userId) {
        log.debug("Getting prescription items for user ID: {}", userId);

        return prescriptionItemRepository.findByPatientId(userId);
    }

    /**
     * Lấy danh sách prescription items với pagination cho registered user
     */
    public Page<PrescriptionItem> getPrescriptionItemsForUser(Long userId, Pageable pageable) {
        log.debug("Getting prescription items for user ID: {} with pagination", userId);

        return prescriptionItemRepository.findByPatientId(userId, pageable);
    }

    /**
     * Lấy prescription items theo prescription ID cho registered user
     */
    public List<PrescriptionItem> getPrescriptionItemsByPrescriptionIdForUser(Long prescriptionId, Long userId) {
        log.debug("Getting prescription items for prescription ID: {} and user ID: {}", prescriptionId, userId);

        // Validate prescription belongs to user
        prescriptionRepository.findByIdAndPatient_Id(prescriptionId, userId)
                .orElseThrow(() -> new PrescriptionNotFoundException("Prescription not found with ID: " + prescriptionId + " for user: " + userId));

        return prescriptionItemRepository.findByPrescriptionIdAndPatientId(prescriptionId, userId);
    }

    /**
     * Lấy prescription items theo prescription ID với pagination cho registered user
     */
    public Page<PrescriptionItem> getPrescriptionItemsByPrescriptionIdForUser(Long prescriptionId, Long userId, Pageable pageable) {
        log.debug("Getting prescription items for prescription ID: {} and user ID: {} with pagination", prescriptionId, userId);

        // Validate prescription belongs to user
        prescriptionRepository.findByIdAndPatient_Id(prescriptionId, userId)
                .orElseThrow(() -> new PrescriptionNotFoundException("Prescription not found with ID: " + prescriptionId + " for user: " + userId));

        return prescriptionItemRepository.findByPrescriptionIdAndPatientId(prescriptionId, userId, pageable);
    }

    /**
     * Cập nhật prescription item cho registered user
     */
    public PrescriptionItem updatePrescriptionItemForUser(Long id, UpdatePrescriptionItemRequest request, Long userId) {
        log.info("Updating prescription item ID: {} for user ID: {}", id, userId);

        validateUpdateRequest(request);

        PrescriptionItem existingItem = findPrescriptionItemByIdForUser(id, userId);

        updatePrescriptionItemFromRequest(existingItem, request);

        PrescriptionItem updatedItem = prescriptionItemRepository.save(existingItem);
        log.info("Updated prescription item with ID: {} for user ID: {}", updatedItem.getId(), userId);

        return updatedItem;
    }

    /**
     * Xóa prescription item cho registered user
     */
    public void deletePrescriptionItemForUser(Long id, Long userId) {
        log.info("Deleting prescription item ID: {} for user ID: {}", id, userId);

        PrescriptionItem existingItem = findPrescriptionItemByIdForUser(id, userId);

        prescriptionItemRepository.delete(existingItem);
        log.info("Deleted prescription item with ID: {} for user ID: {}", id, userId);
    }

    // ==================== ANONYMOUS USER METHODS ====================

    /**
     * Tạo prescription item cho anonymous user
     */
    public PrescriptionItem createPrescriptionItemForAnonymousUser(CreatePrescriptionItemRequest request, UUID anonymousUserId) {
        log.info("Creating prescription item for anonymous user ID: {}", anonymousUserId);

        validateCreateRequest(request);

        // Validate prescription belongs to anonymous user
        Prescription prescription = prescriptionRepository.findByIdAndAnonymousUserId(request.getPrescriptionId(), anonymousUserId)
                .orElseThrow(() -> new PrescriptionNotFoundException("Prescription not found with ID: " + request.getPrescriptionId() + " for anonymous user: " + anonymousUserId));

        // Validate medicine exists
        Medicine medicine = medicineRepository.findById(request.getMedicineId())
                .orElseThrow(() -> new MedicineNotFoundException("Medicine not found with ID: " + request.getMedicineId()));

        PrescriptionItem prescriptionItem = mapToPrescriptionItem(request, prescription, medicine);

        PrescriptionItem savedItem = prescriptionItemRepository.save(prescriptionItem);
        log.info("Created prescription item with ID: {} for anonymous user ID: {}", savedItem.getId(), anonymousUserId);

        return savedItem;
    }

    /**
     * Tìm prescription item theo ID cho anonymous user
     */
    public PrescriptionItem findPrescriptionItemByIdForAnonymousUser(Long id, UUID anonymousUserId) {
        log.debug("Finding prescription item ID: {} for anonymous user ID: {}", id, anonymousUserId);

        return prescriptionItemRepository.findByIdAndAnonymousUserId(id, anonymousUserId)
                .orElseThrow(() -> new PrescriptionNotFoundException("Prescription item not found with ID: " + id + " for anonymous user: " + anonymousUserId));
    }

    /**
     * Lấy danh sách prescription items cho anonymous user
     */
    public List<PrescriptionItem> getPrescriptionItemsForAnonymousUser(UUID anonymousUserId) {
        log.debug("Getting prescription items for anonymous user ID: {}", anonymousUserId);

        return prescriptionItemRepository.findByAnonymousUserId(anonymousUserId);
    }

    /**
     * Lấy danh sách prescription items với pagination cho anonymous user
     */
    public Page<PrescriptionItem> getPrescriptionItemsForAnonymousUser(UUID anonymousUserId, Pageable pageable) {
        log.debug("Getting prescription items for anonymous user ID: {} with pagination", anonymousUserId);

        return prescriptionItemRepository.findByAnonymousUserId(anonymousUserId, pageable);
    }

    /**
     * Lấy prescription items theo prescription ID cho anonymous user
     */
    public List<PrescriptionItem> getPrescriptionItemsByPrescriptionIdForAnonymousUser(Long prescriptionId, UUID anonymousUserId) {
        log.debug("Getting prescription items for prescription ID: {} and anonymous user ID: {}", prescriptionId, anonymousUserId);

        // Validate prescription belongs to anonymous user
        prescriptionRepository.findByIdAndAnonymousUserId(prescriptionId, anonymousUserId)
                .orElseThrow(() -> new PrescriptionNotFoundException("Prescription not found with ID: " + prescriptionId + " for anonymous user: " + anonymousUserId));

        return prescriptionItemRepository.findByPrescriptionIdAndAnonymousUserId(prescriptionId, anonymousUserId);
    }

    /**
     * Lấy prescription items theo prescription ID với pagination cho anonymous user
     */
    public Page<PrescriptionItem> getPrescriptionItemsByPrescriptionIdForAnonymousUser(Long prescriptionId, UUID anonymousUserId, Pageable pageable) {
        log.debug("Getting prescription items for prescription ID: {} and anonymous user ID: {} with pagination", prescriptionId, anonymousUserId);

        // Validate prescription belongs to anonymous user
        prescriptionRepository.findByIdAndAnonymousUserId(prescriptionId, anonymousUserId)
                .orElseThrow(() -> new PrescriptionNotFoundException("Prescription not found with ID: " + prescriptionId + " for anonymous user: " + anonymousUserId));

        return prescriptionItemRepository.findByPrescriptionIdAndAnonymousUserId(prescriptionId, anonymousUserId, pageable);
    }

    /**
     * Cập nhật prescription item cho anonymous user
     */
    public PrescriptionItem updatePrescriptionItemForAnonymousUser(Long id, UpdatePrescriptionItemRequest request, UUID anonymousUserId) {
        log.info("Updating prescription item ID: {} for anonymous user ID: {}", id, anonymousUserId);

        validateUpdateRequest(request);

        PrescriptionItem existingItem = findPrescriptionItemByIdForAnonymousUser(id, anonymousUserId);

        updatePrescriptionItemFromRequest(existingItem, request);

        PrescriptionItem updatedItem = prescriptionItemRepository.save(existingItem);
        log.info("Updated prescription item with ID: {} for anonymous user ID: {}", updatedItem.getId(), anonymousUserId);

        return updatedItem;
    }

    /**
     * Xóa prescription item cho anonymous user
     */
    public void deletePrescriptionItemForAnonymousUser(Long id, UUID anonymousUserId) {
        log.info("Deleting prescription item ID: {} for anonymous user ID: {}", id, anonymousUserId);

        PrescriptionItem existingItem = findPrescriptionItemByIdForAnonymousUser(id, anonymousUserId);

        prescriptionItemRepository.delete(existingItem);
        log.info("Deleted prescription item with ID: {} for anonymous user ID: {}", id, anonymousUserId);
    }

    // ==================== HELPER METHODS ====================

    /**
     * Validate create request
     */
    private void validateCreateRequest(CreatePrescriptionItemRequest request) {
        if (request == null) {
            throw new InvalidPrescriptionDataException("Create prescription item request cannot be null");
        }

        if (request.getPrescriptionId() == null) {
            throw new InvalidPrescriptionDataException("Prescription ID is required");
        }

        if (request.getMedicineId() == null) {
            throw new InvalidPrescriptionDataException("Medicine ID is required");
        }

        if (request.getDoseAmount() == null || request.getDoseAmount() <= 0) {
            throw new InvalidPrescriptionDataException("Dose amount must be positive");
        }

        if (request.getDoseUnit() == null) {
            throw new InvalidPrescriptionDataException("Dose unit is required");
        }

        if (request.getRoute() == null) {
            throw new InvalidPrescriptionDataException("Route is required");
        }

        if (request.getDurationDays() == null || request.getDurationDays() < 1) {
            throw new InvalidPrescriptionDataException("Duration days must be at least 1");
        }

        if (request.getQuantity() != null && request.getQuantity() < 1) {
            throw new InvalidPrescriptionDataException("Quantity must be at least 1");
        }

        if (request.getInstructions() != null && request.getInstructions().length() > 2000) {
            throw new InvalidPrescriptionDataException("Instructions must not exceed 2000 characters");
        }
    }

    /**
     * Validate update request
     */
    private void validateUpdateRequest(UpdatePrescriptionItemRequest request) {
        if (request == null) {
            throw new InvalidPrescriptionDataException("Update prescription item request cannot be null");
        }

        if (request.getDoseAmount() != null && request.getDoseAmount() <= 0) {
            throw new InvalidPrescriptionDataException("Dose amount must be positive");
        }

        if (request.getDurationDays() != null && request.getDurationDays() < 1) {
            throw new InvalidPrescriptionDataException("Duration days must be at least 1");
        }

        if (request.getQuantity() != null && request.getQuantity() < 1) {
            throw new InvalidPrescriptionDataException("Quantity must be at least 1");
        }

        if (request.getInstructions() != null && request.getInstructions().length() > 2000) {
            throw new InvalidPrescriptionDataException("Instructions must not exceed 2000 characters");
        }
    }

    /**
     * Map request to prescription item entity
     */
    private PrescriptionItem mapToPrescriptionItem(CreatePrescriptionItemRequest request, Prescription prescription, Medicine medicine) {
        PrescriptionItem prescriptionItem = new PrescriptionItem();
        prescriptionItem.setPrescription(prescription);
        prescriptionItem.setMedicine(medicine);
        prescriptionItem.setDoseAmount(request.getDoseAmount());
        prescriptionItem.setDoseUnit(request.getDoseUnit());
        prescriptionItem.setQuantity(request.getQuantity() != null ? request.getQuantity() : 1);
        prescriptionItem.setInstructions(request.getInstructions());
        prescriptionItem.setRoute(request.getRoute());
        prescriptionItem.setDurationDays(request.getDurationDays());

        return prescriptionItem;
    }

    /**
     * Update prescription item from request
     */
    private void updatePrescriptionItemFromRequest(PrescriptionItem prescriptionItem, UpdatePrescriptionItemRequest request) {
        if (request.getMedicineId() != null) {
            Medicine medicine = medicineRepository.findById(request.getMedicineId())
                    .orElseThrow(() -> new MedicineNotFoundException("Medicine not found with ID: " + request.getMedicineId()));
            prescriptionItem.setMedicine(medicine);
        }

        if (request.getDoseAmount() != null) {
            prescriptionItem.setDoseAmount(request.getDoseAmount());
        }

        if (request.getDoseUnit() != null) {
            prescriptionItem.setDoseUnit(request.getDoseUnit());
        }

        if (request.getQuantity() != null) {
            prescriptionItem.setQuantity(request.getQuantity());
        }

        if (StringUtils.hasText(request.getInstructions())) {
            prescriptionItem.setInstructions(request.getInstructions());
        }

        if (request.getRoute() != null) {
            prescriptionItem.setRoute(request.getRoute());
        }

        if (request.getDurationDays() != null) {
            prescriptionItem.setDurationDays(request.getDurationDays());
        }
    }

    // Dose calculation methods
    public Integer getExpectedTotalDoses(PrescriptionItem prescriptionItem) {
        if (prescriptionItem.getMedicationSchedules() == null || prescriptionItem.getMedicationSchedules().isEmpty()) {
            // Fallback to simple calculation if no schedules
            return prescriptionItem.getQuantity() != null && prescriptionItem.getDurationDays() != null 
                   ? prescriptionItem.getQuantity() * prescriptionItem.getDurationDays() 
                   : null;
        }
        
        // Calculate from active medication schedules
        return prescriptionItem.getMedicationSchedules().stream()
                .filter(schedule -> schedule.getIsActive())
                .mapToInt(schedule -> {
                    // Count how many times this schedule occurs in duration
                    if (prescriptionItem.getDurationDays() == null) return 0;
                    
                    int scheduleCount = 0;
                    for (int day = 1; day <= prescriptionItem.getDurationDays(); day++) {
                        // Calculate day of week (1=Monday, 7=Sunday)
                        int dayOfWeek = ((day - 1) % 7) + 1;
                        if (dayOfWeek == schedule.getDayOfWeek()) {
                            scheduleCount++;
                        }
                    }
                    return scheduleCount;
                })
                .sum();
    }
    
    public Integer getActualTotalDoses(PrescriptionItem prescriptionItem) {
        if (prescriptionItem.getMedicationSchedules() == null) return 0;
        
        return prescriptionItem.getMedicationSchedules().stream()
                .flatMap(schedule -> schedule.getMedicationDoses().stream())
                .filter(dose -> dose.getStatus() == DoseStatus.TAKEN)
                .mapToInt(dose -> 1)
                .sum();
    }
    
    public Double getComplianceRate(PrescriptionItem prescriptionItem) {
        Integer expected = getExpectedTotalDoses(prescriptionItem);
        Integer actual = getActualTotalDoses(prescriptionItem);
        
        if (expected == null || expected == 0) return null;
        if (actual == null) return 0.0;
        
        return Math.min(100.0, (double) actual / expected * 100);
    }
    
    public boolean isCompleted(PrescriptionItem prescriptionItem) {
        Integer expected = getExpectedTotalDoses(prescriptionItem);
        Integer actual = getActualTotalDoses(prescriptionItem);
        
        return expected != null && actual != null && actual >= expected;
    }
    
    public boolean hasActiveSchedules(PrescriptionItem prescriptionItem) {
        return prescriptionItem.getMedicationSchedules() != null && 
               prescriptionItem.getMedicationSchedules().stream().anyMatch(schedule -> schedule.getIsActive());
    }
    
    // Dose type validation methods
    public boolean isWeightBasedDose(PrescriptionItem prescriptionItem) {
        return prescriptionItem.getDoseUnit() != null &&
               (prescriptionItem.getDoseUnit() == DoseUnit.MILLIGRAM || prescriptionItem.getDoseUnit() == DoseUnit.MICROGRAM);
    }
    
    public boolean isLiquidDose(PrescriptionItem prescriptionItem) {
        return prescriptionItem.getDoseUnit() != null && prescriptionItem.getDoseUnit() == DoseUnit.MILLILITER;
    }
    
    public boolean isSolidDose(PrescriptionItem prescriptionItem) {
        return prescriptionItem.getDoseUnit() != null && 
               (prescriptionItem.getDoseUnit() == DoseUnit.TABLET || 
                prescriptionItem.getDoseUnit() == DoseUnit.CAPSULE || 
                prescriptionItem.getDoseUnit() == DoseUnit.GRAM);
    }
    
    public boolean isInjectionDose(PrescriptionItem prescriptionItem) {
        return false; // Not supported in new DoseUnit enum
    }
    
    public boolean isTopicalDose(PrescriptionItem prescriptionItem) {
        return prescriptionItem.getDoseUnit() != null && 
               (prescriptionItem.getDoseUnit() == DoseUnit.DROP || prescriptionItem.getDoseUnit() == DoseUnit.PUFF);
    }
    
    // Dose description methods
    public String getDoseDescription(PrescriptionItem prescriptionItem) {
        if (prescriptionItem.getDoseAmount() == null || prescriptionItem.getDoseUnit() == null) {
            return "Không xác định";
        }
        
        return String.format("%.2f %s", prescriptionItem.getDoseAmount(), prescriptionItem.getDoseUnit().getAbbreviation());
    }
    
    public String getDoseDescriptionWithQuantity(PrescriptionItem prescriptionItem) {
        if (prescriptionItem.getDoseAmount() == null || prescriptionItem.getDoseUnit() == null) {
            return "Không xác định";
        }
        
        String doseDesc = getDoseDescription(prescriptionItem);
        if (prescriptionItem.getQuantity() != null && prescriptionItem.getQuantity() > 1) {
            return String.format("%s x %d", doseDesc, prescriptionItem.getQuantity());
        }
        
        return doseDesc;
    }
    
    // Duration and time calculation methods
    public LocalDateTime getExpectedEndDate(PrescriptionItem prescriptionItem) {
        if (prescriptionItem.getPrescription() == null || 
            prescriptionItem.getPrescription().getStartDate() == null || 
            prescriptionItem.getDurationDays() == null) {
            return null;
        }
        return prescriptionItem.getPrescription().getStartDate().atStartOfDay().plusDays(prescriptionItem.getDurationDays() - 1); // -1 because start date counts as day 1
    }
    
    public Integer getRemainingDays(PrescriptionItem prescriptionItem) {
        if (prescriptionItem.getDurationDays() == null) return null;
        
        LocalDateTime startDate = prescriptionItem.getPrescription() != null ? 
                                 prescriptionItem.getPrescription().getStartDate().atStartOfDay() : null;
        if (startDate == null) return prescriptionItem.getDurationDays();
        
        long daysElapsed = java.time.Duration.between(startDate, LocalDateTime.now()).toDays();
        return Math.max(0, prescriptionItem.getDurationDays() - (int) daysElapsed);
    }
    
    public Double getProgressPercentage(PrescriptionItem prescriptionItem) {
        if (prescriptionItem.getDurationDays() == null) return null;
        
        Integer remaining = getRemainingDays(prescriptionItem);
        if (remaining == null) return null;
        
        return Math.min(100.0, (double) (prescriptionItem.getDurationDays() - remaining) / prescriptionItem.getDurationDays() * 100);
    }
    
    public boolean isOverdue(PrescriptionItem prescriptionItem) {
        LocalDateTime endDate = getExpectedEndDate(prescriptionItem);
        return endDate != null && LocalDateTime.now().isAfter(endDate);
    }
    
    public String getDurationDescription(PrescriptionItem prescriptionItem) {
        if (prescriptionItem.getDurationDays() == null) return "Không xác định";
        
        if (prescriptionItem.getDurationDays() == 1) {
            return "1 ngày";
        } else if (prescriptionItem.getDurationDays() < 7) {
            return String.format("%d ngày", prescriptionItem.getDurationDays());
        } else if (prescriptionItem.getDurationDays() < 30) {
            int weeks = prescriptionItem.getDurationDays() / 7;
            int remainingDays = prescriptionItem.getDurationDays() % 7;
            if (remainingDays == 0) {
                return String.format("%d tuần", weeks);
            } else {
                return String.format("%d tuần %d ngày", weeks, remainingDays);
            }
        } else {
            int months = prescriptionItem.getDurationDays() / 30;
            int remainingDays = prescriptionItem.getDurationDays() % 30;
            if (remainingDays == 0) {
                return String.format("%d tháng", months);
            } else {
                return String.format("%d tháng %d ngày", months, remainingDays);
            }
        }
    }
    
    // Validation methods
    public boolean isDoseUnitCompatibleWithRoute(PrescriptionItem prescriptionItem) {
        if (prescriptionItem.getDoseUnit() == null || prescriptionItem.getRoute() == null) return true;
        
        switch (prescriptionItem.getRoute()) {
            case ORAL:
                return isSolidDose(prescriptionItem) || isLiquidDose(prescriptionItem);
            case INJECTION:
                return isLiquidDose(prescriptionItem); // Injection not supported in new enum
            case TOPICAL:
                return isTopicalDose(prescriptionItem) || isLiquidDose(prescriptionItem);
            case INHALATION:
                return prescriptionItem.getDoseUnit() == DoseUnit.PUFF;
            case RECTAL:
                return isLiquidDose(prescriptionItem); // Suppository not supported in new enum
            case VAGINAL:
                return isLiquidDose(prescriptionItem); // Suppository not supported in new enum
            case OPHTHALMIC:
                return prescriptionItem.getDoseUnit() == DoseUnit.DROP;
            case OTIC:
                return prescriptionItem.getDoseUnit() == DoseUnit.DROP;
            case NASAL:
                return prescriptionItem.getDoseUnit() == DoseUnit.DROP || prescriptionItem.getDoseUnit() == DoseUnit.PUFF;
            default:
                return true;
        }
    }
    
    public boolean isWeightBasedDoseValid(PrescriptionItem prescriptionItem) {
        if (!isWeightBasedDose(prescriptionItem)) return true;
        
        // For weight-based doses, we need patient weight for proper calculation
        // This is a placeholder for future implementation
        return true;
    }
} 