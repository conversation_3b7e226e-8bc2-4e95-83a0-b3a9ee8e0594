package com.medication.service.domain.prescription;

import com.medication.entity.Prescription;
import com.medication.enums.PrescriptionStatus;
import com.medication.util.ValidationUtils;
import org.springframework.stereotype.Service;

/**
 * Service for Prescription validation logic
 */
@Service
public class PrescriptionValidationService {

    /**
     * Validate prescription date range
     */
    public boolean isDateRangeValid(Prescription prescription) {
        return ValidationUtils.isDateRangeValid(prescription.getStartDate(), prescription.getEndDate());
    }

    /**
     * Validate prescription date
     */
    public boolean isPrescriptionDateValid(Prescription prescription) {
        return ValidationUtils.isPrescriptionDateValid(prescription.getPrescriptionDate(), prescription.getStartDate());
    }

    /**
     * Validate doses
     */
    public boolean isDosesValid(Prescription prescription) {
        return ValidationUtils.isDosesValid(prescription.getTakenDoses(), prescription.getTotalDoses());
    }

    /**
     * Validate compliance rate
     */
    public boolean isComplianceRateValid(Prescription prescription) {
        if (prescription.getComplianceRate() == null) return true;
        return ValidationUtils.isComplianceRateValid(prescription.getComplianceRate().doubleValue());
    }

    /**
     * Validate prescription data
     */
    public void validatePrescription(Prescription prescription) {
        if (!isDateRangeValid(prescription)) {
            throw new IllegalArgumentException("Start date must be before or equal to end date");
        }

        if (!isDosesValid(prescription)) {
            throw new IllegalArgumentException("Taken doses cannot exceed total doses");
        }

        if (!isPrescriptionDateValid(prescription)) {
            throw new IllegalArgumentException("Prescription date must be before or equal to start date");
        }

        if (!isComplianceRateValid(prescription)) {
            throw new IllegalArgumentException("Compliance rate must be between 0 and 100");
        }
    }

    /**
     * Validate prescription for creation
     */
    public void validateForCreation(Prescription prescription) {
        validatePrescription(prescription);

        if (prescription.getDoctorName() == null || prescription.getDoctorName().trim().isEmpty()) {
            throw new IllegalArgumentException("Doctor name is required");
        }

        if (prescription.getDiagnosis() == null || prescription.getDiagnosis().trim().isEmpty()) {
            throw new IllegalArgumentException("Diagnosis is required");
        }

        if (prescription.getPatient() == null) {
            throw new IllegalArgumentException("Patient is required");
        }

        if (prescription.getPrescribedBy() == null) {
            throw new IllegalArgumentException("Prescribed by is required");
        }
    }

    /**
     * Validate prescription for update
     */
    public void validateForUpdate(Prescription prescription) {
        validatePrescription(prescription);

        if (prescription.getId() == null) {
            throw new IllegalArgumentException("Prescription ID is required for update");
        }
    }

    /**
     * Check if prescription can be updated
     */
    public boolean canBeUpdated(Prescription prescription) {
        return prescription.getIsActive() && 
               prescription.getDeletedAt() == null && 
               prescription.getStatus() != PrescriptionStatus.CANCELLED;
    }

    /**
     * Check if prescription can be cancelled
     */
    public boolean canBeCancelled(Prescription prescription) {
        return prescription.getIsActive() && 
               prescription.getDeletedAt() == null && 
               prescription.getStatus() != PrescriptionStatus.CANCELLED;
    }

    /**
     * Check if prescription can be restored
     */
    public boolean canBeRestored(Prescription prescription) {
        return !prescription.getIsActive() && 
               prescription.getDeletedAt() != null;
    }
}
