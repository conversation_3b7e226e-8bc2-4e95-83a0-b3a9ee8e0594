package com.medication.service.domain.schedule;

import com.medication.entity.MedicationDose;
import com.medication.enums.DoseUnit;
import com.medication.enums.DoseStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class MedicationDoseBusinessService {

    // Status check methods
    public boolean isTaken(MedicationDose medicationDose) {
        return medicationDose.getStatus() != null && medicationDose.getStatus().isCompleted();
    }
    
    public boolean isMissed(MedicationDose medicationDose) {
        return medicationDose.getStatus() != null && medicationDose.getStatus().isMissed();
    }
    
    public boolean isOverdue(MedicationDose medicationDose) {
        return medicationDose.getStatus() != null && 
               medicationDose.getStatus().isPending() && 
               medicationDose.getScheduledDate().isBefore(LocalDateTime.now());
    }
    
    // Adherence calculation methods
    public Double getAdherenceRate(MedicationDose medicationDose) {
        if (!isTaken(medicationDose) || 
            medicationDose.getExpectedDoseAmount() == null || 
            medicationDose.getActualDoseAmount() == null) {
            return null;
        }
        
        if (!isDoseUnitCompatible(medicationDose)) {
            return null; // Cannot calculate if units are not compatible
        }
        
        // Convert to same unit if needed
        Double actualAmount = medicationDose.getActualDoseAmount();
        if (!medicationDose.getExpectedDoseUnit().equals(medicationDose.getActualDoseUnit())) {
            Double conversionFactor = medicationDose.getExpectedDoseUnit().getConversionFactor(medicationDose.getActualDoseUnit());
            if (conversionFactor != null) {
                actualAmount = medicationDose.getActualDoseAmount() * conversionFactor;
            } else {
                return null; // Cannot convert between incompatible units
            }
        }
        
        return Math.min(100.0, (actualAmount / medicationDose.getExpectedDoseAmount()) * 100);
    }
    
    public boolean isFullyAdherent(MedicationDose medicationDose) {
        Double adherence = getAdherenceRate(medicationDose);
        return adherence != null && adherence >= 100.0;
    }
    
    public boolean isPartiallyAdherent(MedicationDose medicationDose) {
        Double adherence = getAdherenceRate(medicationDose);
        return adherence != null && adherence > 0 && adherence < 100.0;
    }
    
    public boolean isNonAdherent(MedicationDose medicationDose) {
        Double adherence = getAdherenceRate(medicationDose);
        return adherence != null && adherence == 0.0;
    }
    
    public String getAdherenceStatus(MedicationDose medicationDose) {
        if (!isTaken(medicationDose)) return "Chưa uống";
        if (isFullyAdherent(medicationDose)) return "Tuân thủ hoàn toàn";
        if (isPartiallyAdherent(medicationDose)) return "Tuân thủ một phần";
        if (isNonAdherent(medicationDose)) return "Không tuân thủ";
        return "Không thể tính toán";
    }
    
    // Status update methods
    public void markAsTaken(MedicationDose medicationDose, Double actualAmount, DoseUnit actualUnit) {
        medicationDose.setStatus(DoseStatus.TAKEN);
        medicationDose.setActualDoseAmount(actualAmount);
        medicationDose.setActualDoseUnit(actualUnit);
        medicationDose.setTakenDate(LocalDateTime.now());
    }
    
    public void markAsPartial(MedicationDose medicationDose, Double actualAmount, DoseUnit actualUnit) {
        medicationDose.setStatus(DoseStatus.PARTIAL);
        medicationDose.setActualDoseAmount(actualAmount);
        medicationDose.setActualDoseUnit(actualUnit);
        medicationDose.setTakenDate(LocalDateTime.now());
    }
    
    public void markAsMissed(MedicationDose medicationDose, String reason) {
        medicationDose.setStatus(DoseStatus.MISSED);
        medicationDose.setMissedReason(reason);
    }
    
    public void markAsSkipped(MedicationDose medicationDose, String reason) {
        medicationDose.setStatus(DoseStatus.SKIPPED);
        medicationDose.setMissedReason(reason);
    }
    
    // Validation methods
    public boolean isActualDoseValid(MedicationDose medicationDose) {
        if (medicationDose.getStatus() != null && medicationDose.getStatus().requiresActualDose()) {
            return medicationDose.getActualDoseAmount() != null && 
                   medicationDose.getActualDoseAmount() > 0 && 
                   medicationDose.getActualDoseUnit() != null;
        }
        return true;
    }
    
    public boolean isDoseUnitCompatible(MedicationDose medicationDose) {
        if (medicationDose.getExpectedDoseUnit() != null && medicationDose.getActualDoseUnit() != null) {
            return medicationDose.getExpectedDoseUnit().isCompatibleWith(medicationDose.getActualDoseUnit());
        }
        return true;
    }
} 