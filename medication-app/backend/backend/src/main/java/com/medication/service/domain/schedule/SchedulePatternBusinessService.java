package com.medication.service.domain.schedule;

import com.medication.entity.SchedulePattern;
import com.medication.enums.RepeatType;
import com.medication.enums.DayOfWeekCode;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class SchedulePatternBusinessService {

    // Pattern logic methods
    public boolean isActiveOnDate(SchedulePattern schedulePattern, LocalDateTime date) {
        if (!schedulePattern.getIsActive() || date == null) return false;
        if (date.isBefore(schedulePattern.getStartDate())) return false;
        if (schedulePattern.getEndDate() != null && date.isAfter(schedulePattern.getEndDate())) return false;

        switch (schedulePattern.getRepeatType()) {
            case ONE_TIME:
                return date.toLocalDate().equals(schedulePattern.getStartDate().toLocalDate());

            case DAILY:
                return true;

            case WEEKLY:
            case CUSTOM_DAYS:
                return schedulePattern.getWeekDays().contains(DayOfWeekCode.fromDayOfWeek(date.getDayOfWeek()));

            case EVERY_N_DAYS:
                long daysDiff = java.time.Duration.between(
                    schedulePattern.getStartDate().toLocalDate().atStartOfDay(), 
                    date.toLocalDate().atStartOfDay()
                ).toDays();
                return daysDiff % schedulePattern.getIntervalDays() == 0;

            case MONTHLY:
                return schedulePattern.getDaysOfMonth().contains(date.getDayOfMonth());

            case WEEKDAYS:
                int dow = date.getDayOfWeek().getValue();
                return dow >= 1 && dow <= 5;

            case WEEKENDS:
                int dow2 = date.getDayOfWeek().getValue();
                return dow2 == 6 || dow2 == 7;

            default:
                return false;
        }
    }
    
    public boolean isActiveToday(SchedulePattern schedulePattern) {
        return isActiveOnDate(schedulePattern, LocalDateTime.now());
    }
    
    public String getPatternDescription(SchedulePattern schedulePattern) {
        switch (schedulePattern.getRepeatType()) {
            case ONE_TIME:
                return "Một lần vào ngày " + schedulePattern.getStartDate().toLocalDate();
            case DAILY:
                return "Hàng ngày";
            case WEEKLY:
                return "Hàng tuần vào " + schedulePattern.getWeekDays();
            case EVERY_N_DAYS:
                return "Mỗi " + schedulePattern.getIntervalDays() + " ngày";
            case CUSTOM_DAYS:
                return "Các ngày: " + schedulePattern.getWeekDays();
            case MONTHLY:
                return "Hàng tháng vào ngày " + schedulePattern.getDaysOfMonth();
            case WEEKDAYS:
                return "Các ngày trong tuần (Thứ 2 - Thứ 6)";
            case WEEKENDS:
                return "Cuối tuần (Thứ 7, Chủ nhật)";
            default:
                return "Không xác định";
        }
    }

    public DayOfWeekCode getDayOfWeekCode(LocalDateTime date) {
        return DayOfWeekCode.fromDayOfWeek(date.getDayOfWeek());
    }
    
    // Validation methods
    public boolean isWeekDaysValid(SchedulePattern schedulePattern) {
        if (schedulePattern.getRepeatType() == RepeatType.WEEKLY || 
            schedulePattern.getRepeatType() == RepeatType.CUSTOM_DAYS ||
            schedulePattern.getRepeatType() == RepeatType.WEEKDAYS || 
            schedulePattern.getRepeatType() == RepeatType.WEEKENDS) {
            return schedulePattern.getWeekDays() != null && !schedulePattern.getWeekDays().isEmpty();
        }
        return true;
    }

    public boolean isIntervalDaysValid(SchedulePattern schedulePattern) {
        return schedulePattern.getRepeatType() != RepeatType.EVERY_N_DAYS || 
               (schedulePattern.getIntervalDays() != null && schedulePattern.getIntervalDays() > 0);
    }

    public boolean isDaysOfMonthValid(SchedulePattern schedulePattern) {
        return schedulePattern.getRepeatType() != RepeatType.MONTHLY || 
               (schedulePattern.getDaysOfMonth() != null && !schedulePattern.getDaysOfMonth().isEmpty());
    }

    public boolean isDateRangeValid(SchedulePattern schedulePattern) {
        return schedulePattern.getStartDate() == null || 
               schedulePattern.getEndDate() == null || 
               schedulePattern.getEndDate().isAfter(schedulePattern.getStartDate());
    }

    public boolean isOneTimeValid(SchedulePattern schedulePattern) {
        return schedulePattern.getRepeatType() != RepeatType.ONE_TIME || 
               schedulePattern.getStartDate() != null;
    }
} 