package com.medication.service.domain.session;

import com.medication.dto.request.CreateAnonymousSessionRequest;
import com.medication.dto.request.UpdateAnonymousSessionRequest;
import com.medication.dto.response.AnonymousSessionStatsResponse;
import com.medication.entity.AnonymousSession;
import com.medication.entity.User;
import com.medication.enums.AnonymousSessionStatus;
import com.medication.exception.domain.session.AnonymousSessionNotFoundException;
import com.medication.exception.domain.session.AnonymousSessionAlreadyMigratedException;
import com.medication.exception.domain.user.UserNotFoundException;
import com.medication.repository.domain.session.AnonymousSessionRepository;
import com.medication.repository.domain.user.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Business service cho AnonymousSession
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class AnonymousSessionBusinessService {

    private final AnonymousSessionRepository anonymousSessionRepository;
    private final UserRepository userRepository;

    // Helper methods
    private AnonymousSession updateStatus(AnonymousSession session, AnonymousSessionStatus status) {
        session.setStatus(status);
        return anonymousSessionRepository.save(session);
    }

    private void validateNotBlank(String value, String fieldName) {
        if (value == null || value.isBlank()) {
            throw new IllegalArgumentException(fieldName + " must not be blank");
        }
    }

    /**
     * Create new anonymous session
     */
    public AnonymousSession createAnonymousSession(CreateAnonymousSessionRequest request) {
        log.info("Creating anonymous session for device: {}", request.getDeviceId());

        validateNotBlank(request.getDeviceId(), "Device ID");

        AnonymousSession session = AnonymousSession.builder()
            .anonymousUserId(UUID.randomUUID())
            .deviceId(request.getDeviceId())
            .appVersion(request.getAppVersion())
            .status(AnonymousSessionStatus.ACTIVE)
            .dataSynced(false)
            .build();

        AnonymousSession savedSession = anonymousSessionRepository.save(session);
        log.info("Created anonymous session with ID: {} and UUID: {}",
                savedSession.getId(), savedSession.getAnonymousUserId());

        return savedSession;
    }

    /**
     * Find session by anonymous user ID
     */
    @Transactional(readOnly = true)
    public AnonymousSession findSessionByAnonymousUserId(UUID anonymousUserId) {
        log.debug("Finding session by anonymous user ID: {}", anonymousUserId);
        return anonymousSessionRepository.findByAnonymousUserId(anonymousUserId)
                .orElseThrow(() -> new AnonymousSessionNotFoundException(anonymousUserId));
    }

    /**
     * Update session
     */
    public AnonymousSession updateSession(UUID anonymousUserId, UpdateAnonymousSessionRequest request) {
        log.info("Updating session for anonymous user ID: {}", anonymousUserId);

        AnonymousSession session = findSessionByAnonymousUserId(anonymousUserId);

        if (request.getAppVersion() != null) {
            session.setAppVersion(request.getAppVersion());
        }
        if (request.getDataSynced() != null) {
            session.setDataSynced(request.getDataSynced());
        }

        updateLastActivity(anonymousUserId); // Thống nhất update last activity qua repository

        AnonymousSession updatedSession = anonymousSessionRepository.save(session);
        log.info("Updated session for anonymous user ID: {}", anonymousUserId);

        return updatedSession;
    }

    /**
     * Update last activity time
     */
    public void updateLastActivity(UUID anonymousUserId) {
        log.debug("Updating last activity for anonymous user ID: {}", anonymousUserId);
        int updatedRows = anonymousSessionRepository.updateLastActivity(anonymousUserId, LocalDateTime.now());
        if (updatedRows == 0) {
            log.warn("No session found to update last activity for anonymous user ID: {}", anonymousUserId);
        }
    }

    @Transactional(readOnly = true)
    public boolean isSessionExists(UUID anonymousUserId) {
        return anonymousSessionRepository.existsByAnonymousUserId(anonymousUserId);
    }

    @Transactional(readOnly = true)
    public List<AnonymousSession> findSessionsByDeviceId(String deviceId) {
        validateNotBlank(deviceId, "Device ID");
        log.debug("Finding sessions by device ID: {}", deviceId);
        return anonymousSessionRepository.findByDeviceId(deviceId);
    }

    @Transactional(readOnly = true)
    public List<AnonymousSession> findActiveSessions() {
        log.debug("Finding all active sessions");
        return anonymousSessionRepository.findByStatus(AnonymousSessionStatus.ACTIVE);
    }

    @Transactional(readOnly = true)
    public List<AnonymousSession> findUnmigratedSessions() {
        log.debug("Finding unmigrated sessions");
        return anonymousSessionRepository.findByMigratedToUserIdIsNull();
    }

    /**
     * Migrate session to user
     */
    public AnonymousSession migrateSessionToUser(UUID anonymousUserId, Long userId) {
        log.info("Migrating session {} to user ID: {}", anonymousUserId, userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + userId));

        AnonymousSession session = findSessionByAnonymousUserId(anonymousUserId);

        if (session.getMigratedToUserId() != null) {
            throw new AnonymousSessionAlreadyMigratedException(anonymousUserId, session.getMigratedToUserId());
        }

        session.setMigratedToUserId(userId);
        session.setMigratedAt(LocalDateTime.now());

        return updateStatus(session, AnonymousSessionStatus.MIGRATED);
    }

    /**
     * Expire session
     */
    public AnonymousSession expireSession(UUID anonymousUserId) {
        log.info("Expiring session: {}", anonymousUserId);
        AnonymousSession session = findSessionByAnonymousUserId(anonymousUserId);
        return updateStatus(session, AnonymousSessionStatus.EXPIRED);
    }

    /**
     * Cleanup expired sessions
     */
    public void cleanupExpiredSessions(LocalDateTime cutoffTime) {
        log.info("Cleaning up expired sessions older than: {}", cutoffTime);
        List<AnonymousSession> expiredSessions =
                anonymousSessionRepository.findInactiveSessions(cutoffTime, AnonymousSessionStatus.EXPIRED);

        if (!expiredSessions.isEmpty()) {
            anonymousSessionRepository.deleteAll(expiredSessions);
            log.info("Cleaned up {} expired sessions", expiredSessions.size());
        } else {
            log.debug("No expired sessions to clean up");
        }
    }

    @Transactional(readOnly = true)
    public long countSessionsByStatus(AnonymousSessionStatus status) {
        return anonymousSessionRepository.countByStatus(status);
    }

    @Transactional(readOnly = true)
    public long countSessionsByDeviceId(String deviceId) {
        return anonymousSessionRepository.countByDeviceId(deviceId);
    }

    /**
     * Get session statistics
     */
    @Transactional(readOnly = true)
    public AnonymousSessionStatsResponse getSessionStats() {
        log.debug("Getting session statistics");

        // Count by status
        long totalSessions = anonymousSessionRepository.count();
        long activeSessions = countSessionsByStatus(AnonymousSessionStatus.ACTIVE);
        long expiredSessions = countSessionsByStatus(AnonymousSessionStatus.EXPIRED);
        long migratedSessions = countSessionsByStatus(AnonymousSessionStatus.MIGRATED);

        // Count sessions created in last 24h
        LocalDateTime last24h = LocalDateTime.now().minusHours(24);
        long sessionsCreatedLast24h = anonymousSessionRepository.countByCreatedAtAfter(last24h);

        // Count sessions active in last 24h
        long sessionsActiveLast24h = anonymousSessionRepository.countByLastActivityAtAfter(last24h);

        return AnonymousSessionStatsResponse.of(
            totalSessions,
            activeSessions,
            expiredSessions,
            migratedSessions,
            sessionsCreatedLast24h,
            sessionsActiveLast24h
        );
    }
} 