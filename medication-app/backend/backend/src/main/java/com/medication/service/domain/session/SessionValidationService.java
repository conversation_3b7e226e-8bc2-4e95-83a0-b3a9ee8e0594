package com.medication.service.domain.session;

import com.medication.dto.request.CreateAnonymousSessionRequest;
import com.medication.dto.request.UpdateAnonymousSessionRequest;
import com.medication.entity.AnonymousSession;
import com.medication.exception.common.ValidationException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Service for Session validation logic
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class SessionValidationService {

    /**
     * Validate create session request
     */
    public void validateCreateRequest(CreateAnonymousSessionRequest request) {
        Map<String, String> errors = new HashMap<>();

        if (request == null) {
            throw new ValidationException("Request cannot be null");
        }

        if (request.getDeviceId() == null || request.getDeviceId().trim().isEmpty()) {
            errors.put("deviceId", "Device ID is required");
        } else if (request.getDeviceId().length() > 255) {
            errors.put("deviceId", "Device ID cannot exceed 255 characters");
        }

        if (request.getAppVersion() != null && request.getAppVersion().length() > 50) {
            errors.put("appVersion", "App version cannot exceed 50 characters");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("Session creation validation failed", errors);
        }
    }

    /**
     * Validate update session request
     */
    public void validateUpdateRequest(UpdateAnonymousSessionRequest request) {
        Map<String, String> errors = new HashMap<>();

        if (request == null) {
            throw new ValidationException("Request cannot be null");
        }

        if (request.getAppVersion() != null && request.getAppVersion().length() > 50) {
            errors.put("appVersion", "App version cannot exceed 50 characters");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("Session update validation failed", errors);
        }
    }

    /**
     * Validate anonymous user ID
     */
    public void validateAnonymousUserId(UUID anonymousUserId) {
        if (anonymousUserId == null) {
            throw new ValidationException("Anonymous user ID cannot be null");
        }
    }

    /**
     * Validate session for migration
     */
    public void validateForMigration(AnonymousSession session, Long userId) {
        Map<String, String> errors = new HashMap<>();

        if (session == null) {
            throw new ValidationException("Session cannot be null");
        }

        if (userId == null) {
            errors.put("userId", "User ID is required for migration");
        }

        if (session.getMigratedToUserId() != null) {
            errors.put("session", "Session has already been migrated");
        }

        if (session.getStatus() != com.medication.enums.AnonymousSessionStatus.ACTIVE) {
            errors.put("session", "Only active sessions can be migrated");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("Session migration validation failed", errors);
        }
    }

    /**
     * Validate session for expiration
     */
    public void validateForExpiration(AnonymousSession session) {
        if (session == null) {
            throw new ValidationException("Session cannot be null");
        }

        if (session.getStatus() != com.medication.enums.AnonymousSessionStatus.ACTIVE) {
            throw new ValidationException("Only active sessions can be expired");
        }
    }

    /**
     * Validate device ID
     */
    public void validateDeviceId(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            throw new ValidationException("Device ID cannot be null or empty");
        }

        if (deviceId.length() > 255) {
            throw new ValidationException("Device ID cannot exceed 255 characters");
        }
    }
} 