package com.medication.service.domain.user;

import com.medication.entity.User;
import com.medication.enums.UserRole;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service for User business logic
 */
@Service
@Transactional
public class UserBusinessService {

    /**
     * Get authorities for user
     */
    public List<GrantedAuthority> getAuthorities(User user) {
        return List.of(new SimpleGrantedAuthority("ROLE_" + user.getRole().name()));
    }

    /**
     * Check if user account is non-expired
     */
    public boolean isAccountNonExpired(User user) {
        return user.getIsActive();
    }

    /**
     * Check if user account is non-locked
     */
    public boolean isAccountNonLocked(User user) {
        return user.getIsActive();
    }

    /**
     * Check if user credentials are non-expired
     */
    public boolean isCredentialsNonExpired(User user) {
        return user.getIsActive();
    }

    /**
     * Check if user is enabled
     */
    public boolean isEnabled(User user) {
        return user.getIsActive();
    }

    /**
     * Check if user has specific role
     */
    public boolean hasRole(User user, UserRole role) {
        return user.getRole() == role;
    }

    /**
     * Check if user is admin
     */
    public boolean isAdmin(User user) {
        return hasRole(user, UserRole.ADMIN) || hasRole(user, UserRole.SUPER_ADMIN);
    }

    /**
     * Check if user is doctor
     */
    public boolean isDoctor(User user) {
        return hasRole(user, UserRole.DOCTOR);
    }

    /**
     * Check if user is pharmacist
     */
    public boolean isPharmacist(User user) {
        return hasRole(user, UserRole.PHARMACIST);
    }

    /**
     * Check if user is regular user
     */
    public boolean isRegularUser(User user) {
        return hasRole(user, UserRole.USER);
    }

    /**
     * Check if user can prescribe medications
     */
    public boolean canPrescribe(User user) {
        return isDoctor(user) || isPharmacist(user) || isAdmin(user);
    }

    /**
     * Check if user can manage other users
     */
    public boolean canManageUsers(User user) {
        return isAdmin(user);
    }

    /**
     * Check if user can view audit logs
     */
    public boolean canViewAuditLogs(User user) {
        return isAdmin(user);
    }

    /**
     * Get role from string
     */
    public UserRole getRoleFromString(String role) {
        try {
            return UserRole.valueOf(role.toUpperCase());
        } catch (IllegalArgumentException e) {
            return UserRole.USER; // Default role
        }
    }

    /**
     * Validate user
     */
    public void validateUser(User user) {
        if (user == null) {
            throw new IllegalArgumentException("User cannot be null");
        }

        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("User email cannot be null or empty");
        }

        if (user.getFullName() == null || user.getFullName().trim().isEmpty()) {
            throw new IllegalArgumentException("User full name cannot be null or empty");
        }

        if (user.getRole() == null) {
            throw new IllegalArgumentException("User role cannot be null");
        }
    }

    /**
     * Check if user is active and verified
     */
    public boolean isActiveAndVerified(User user) {
        return user.getIsActive() && user.getEmailVerified();
    }

    /**
     * Check if user needs email verification
     */
    public boolean needsEmailVerification(User user) {
        return !user.getEmailVerified();
    }
} 