package com.medication.service.domain.user;

import com.medication.entity.User;
import com.medication.enums.UserRole;
import com.medication.exception.common.ValidationException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Service for User validation logic
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class UserValidationService {

    /**
     * Validate user for creation
     */
    public void validateForCreation(User user) {
        Map<String, String> errors = new HashMap<>();

        if (user == null) {
            throw new ValidationException("User cannot be null");
        }

        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            errors.put("email", "Email is required");
        } else if (!isValidEmail(user.getEmail())) {
            errors.put("email", "Invalid email format");
        }

        if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
            errors.put("password", "Password is required");
        } else if (user.getPassword().length() < 6) {
            errors.put("password", "Password must be at least 6 characters");
        }

        if (user.getFullName() == null || user.getFullName().trim().isEmpty()) {
            errors.put("fullName", "Full name is required");
        }

        if (user.getRole() == null) {
            errors.put("role", "Role is required");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("User validation failed", errors);
        }
    }

    /**
     * Validate user for update
     */
    public void validateForUpdate(User user) {
        Map<String, String> errors = new HashMap<>();

        if (user == null) {
            throw new ValidationException("User cannot be null");
        }

        if (user.getId() == null) {
            errors.put("id", "User ID is required for update");
        }

        if (user.getEmail() != null && !user.getEmail().trim().isEmpty() && !isValidEmail(user.getEmail())) {
            errors.put("email", "Invalid email format");
        }

        if (user.getPassword() != null && user.getPassword().length() < 6) {
            errors.put("password", "Password must be at least 6 characters");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("User validation failed", errors);
        }
    }

    /**
     * Validate user role
     */
    public void validateRole(UserRole role) {
        if (role == null) {
            throw new ValidationException("Role cannot be null");
        }

        try {
            UserRole.valueOf(role.name());
        } catch (IllegalArgumentException e) {
            throw new ValidationException("Invalid role: " + role);
        }
    }

    /**
     * Check if email is valid
     */
    private boolean isValidEmail(String email) {
        return email != null && email.matches("^[A-Za-z0-9+_.-]+@(.+)$");
    }

    /**
     * Validate user permissions
     */
    public void validateUserPermissions(User user, String operation) {
        if (user == null) {
            throw new ValidationException("User cannot be null");
        }

        if (!user.getIsActive()) {
            throw new ValidationException("User account is not active");
        }

        if (!user.getEmailVerified()) {
            throw new ValidationException("User email is not verified");
        }
    }
} 