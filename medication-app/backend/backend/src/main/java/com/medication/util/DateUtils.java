package com.medication.util;

import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Utility class for date and time calculations
 */
@Component
public class DateUtils {

    /**
     * Calculate remaining days until end date
     */
    public static Integer getRemainingDays(LocalDate endDate) {
        if (endDate == null) return null;
        long days = Duration.between(LocalDate.now().atStartOfDay(),
                                   endDate.atStartOfDay()).toDays();
        return Math.max(0, (int) days);
    }

    /**
     * Calculate progress percentage between start and end date
     */
    public static BigDecimal getProgressPercentage(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return BigDecimal.ZERO;
        }

        long totalDays = Duration.between(startDate.atStartOfDay(), endDate.atStartOfDay()).toDays();
        if (totalDays <= 0) return BigDecimal.valueOf(100);

        long elapsedDays = Duration.between(startDate.atStartOfDay(),
                                          LocalDate.now().atStartOfDay()).toDays();
        double percentage = Math.min(100.0, Math.max(0.0, (double) elapsedDays / totalDays * 100));
        return BigDecimal.valueOf(percentage).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * Check if a date is in the past
     */
    public static boolean isDateInPast(LocalDate date) {
        return date != null && date.isBefore(LocalDate.now());
    }

    /**
     * Check if a date is in the future
     */
    public static boolean isDateInFuture(LocalDate date) {
        return date != null && date.isAfter(LocalDate.now());
    }

    /**
     * Check if a date is today
     */
    public static boolean isDateToday(LocalDate date) {
        return date != null && date.equals(LocalDate.now());
    }

    /**
     * Calculate minutes until a specific time
     */
    public static long getMinutesUntilTime(LocalTime targetTime) {
        if (targetTime == null) return -1;
        
        LocalTime now = LocalTime.now();
        if (now.isBefore(targetTime)) {
            return Duration.between(now, targetTime).toMinutes();
        }
        return 0; // Time has passed
    }

    /**
     * Check if current time is approaching target time within specified minutes
     */
    public static boolean isTimeApproaching(LocalTime targetTime, int minutesBefore) {
        if (targetTime == null) return false;
        
        LocalTime now = LocalTime.now();
        LocalTime notificationTime = targetTime.minusMinutes(minutesBefore);
        
        return now.isAfter(notificationTime) && now.isBefore(targetTime);
    }

    /**
     * Check if a time has passed
     */
    public static boolean isTimePassed(LocalTime time) {
        return time != null && LocalTime.now().isAfter(time);
    }

    /**
     * Format time until next dose
     */
    public static String formatTimeUntilNextDose(LocalTime targetTime) {
        if (targetTime == null) return "Không xác định";
        
        LocalTime now = LocalTime.now();
        if (now.isBefore(targetTime)) {
            long minutes = getMinutesUntilTime(targetTime);
            if (minutes < 60) {
                return String.format("%d phút nữa", minutes);
            } else {
                long hours = minutes / 60;
                long remainingMinutes = minutes % 60;
                return String.format("%d giờ %d phút nữa", hours, remainingMinutes);
            }
        }
        return "Đã đến giờ";
    }

    /**
     * Calculate duration between two LocalDateTime
     */
    public static Duration getDurationBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) return Duration.ZERO;
        return Duration.between(start, end);
    }

    /**
     * Get time until scheduled time
     */
    public static Duration getTimeUntilScheduled(LocalDateTime scheduledAt) {
        if (scheduledAt == null) return Duration.ZERO;
        return Duration.between(LocalDateTime.now(), scheduledAt);
    }

    /**
     * Get time since a specific datetime
     */
    public static Duration getTimeSince(LocalDateTime since) {
        if (since == null) return Duration.ZERO;
        return Duration.between(since, LocalDateTime.now());
    }
}
