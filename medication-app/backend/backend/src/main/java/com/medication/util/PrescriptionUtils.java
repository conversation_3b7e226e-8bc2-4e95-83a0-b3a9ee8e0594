package com.medication.util;

import com.medication.entity.PrescriptionItem;
import com.medication.entity.MedicationSchedule;
import com.medication.enums.DoseStatus;

/**
 * Utility class for prescription-related calculations
 */
public class PrescriptionUtils {
    
    /**
     * Calculate expected total doses for a prescription item
     */
    public static Integer getExpectedTotalDoses(PrescriptionItem item) {
        if (item == null || item.getMedicationSchedules() == null) {
            return 0;
        }
        
        return item.getMedicationSchedules().stream()
                .filter(schedule -> schedule.getIsActive())
                .mapToInt(schedule -> calculateExpectedDosesForSchedule(schedule, item.getDurationDays()))
                .sum();
    }
    
    /**
     * Calculate actual total doses taken for a prescription item
     */
    public static Integer getActualTotalDoses(PrescriptionItem item) {
        if (item == null || item.getMedicationSchedules() == null) {
            return 0;
        }
        
        return item.getMedicationSchedules().stream()
                .filter(schedule -> schedule.getIsActive())
                .flatMap(schedule -> schedule.getMedicationDoses().stream())
                .filter(dose -> DoseStatus.TAKEN.equals(dose.getStatus()) || DoseStatus.PARTIAL.equals(dose.getStatus()))
                .mapToInt(dose -> dose.getActualDoseAmount() != null ? dose.getActualDoseAmount().intValue() : 0)
                .sum();
    }
    
    /**
     * Calculate expected doses for a specific schedule over duration
     */
    private static int calculateExpectedDosesForSchedule(MedicationSchedule schedule, Integer durationDays) {
        if (durationDays == null || durationDays <= 0) {
            return 0;
        }
        
        // For now, assume daily dosing (1 dose per day)
        // This can be enhanced based on schedule patterns
        return durationDays;
    }
    
    /**
     * Calculate compliance rate for a prescription item
     */
    public static double getComplianceRate(PrescriptionItem item) {
        Integer expected = getExpectedTotalDoses(item);
        Integer actual = getActualTotalDoses(item);
        
        if (expected == null || expected == 0) {
            return 0.0;
        }
        
        return (double) actual / expected * 100.0;
    }
    
    /**
     * Check if prescription item is completed
     */
    public static boolean isCompleted(PrescriptionItem item) {
        Integer expected = getExpectedTotalDoses(item);
        Integer actual = getActualTotalDoses(item);
        
        return expected != null && actual != null && actual >= expected;
    }
    
    /**
     * Get remaining doses for a prescription item
     */
    public static Integer getRemainingDoses(PrescriptionItem item) {
        Integer expected = getExpectedTotalDoses(item);
        Integer actual = getActualTotalDoses(item);
        
        if (expected == null || actual == null) {
            return 0;
        }
        
        return Math.max(0, expected - actual);
    }
} 