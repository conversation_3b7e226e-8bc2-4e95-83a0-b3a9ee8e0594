package com.medication.util;

import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.LocalDate;
import com.medication.entity.SchedulePattern;
import com.medication.enums.RepeatType;
import com.medication.enums.DayOfWeekCode;

/**
 * Utility class for schedule-related calculations and operations
 */
@Component
public class ScheduleUtils {

    /**
     * Get day of week name in Vietnamese
     */
    public static String getDayOfWeekName(Integer dayOfWeek) {
        if (dayOfWeek == null) return "Không xác định";
        
        switch (dayOfWeek) {
            case 1: return "Thứ 2";
            case 2: return "Thứ 3";
            case 3: return "Th<PERSON> 4";
            case 4: return "Thứ 5";
            case 5: return "Th<PERSON> 6";
            case 6: return "Thứ 7";
            case 7: return "Chủ nhật";
            default: return "Không xác định";
        }
    }

    /**
     * Check if a day of week is scheduled for today
     */
    public static boolean isScheduledForToday(Integer dayOfWeek) {
        if (dayOfWeek == null) return false;
        int currentDayOfWeek = LocalDateTime.now().getDayOfWeek().getValue();
        return dayOfWeek.equals(currentDayOfWeek);
    }

    /**
     * Check if a day of week is scheduled for a specific day
     */
    public static boolean isScheduledForDay(Integer dayOfWeek, int targetDayOfWeek) {
        return dayOfWeek != null && dayOfWeek.equals(targetDayOfWeek);
    }

    /**
     * Check if a schedule is active for today
     */
    public static boolean isActiveForToday(Integer dayOfWeek, Boolean isActive) {
        return isActive != null && isActive && isScheduledForToday(dayOfWeek);
    }

    /**
     * Format schedule description
     */
    public static String formatScheduleDescription(String dayName, String time) {
        return String.format("%s lúc %s", dayName, time);
    }

    /**
     * Check if schedule should send notification
     */
    public static boolean shouldSendNotification(Integer dayOfWeek, Boolean isActive, boolean isTimePassed) {
        return isActive != null && isActive && isScheduledForToday(dayOfWeek) && !isTimePassed;
    }

    /**
     * Check if schedule is overdue
     */
    public static boolean isOverdue(Integer dayOfWeek, Boolean isActive, boolean isTimePassed) {
        return isActive != null && isActive && isScheduledForToday(dayOfWeek) && isTimePassed;
    }
    
    /**
     * Check if pattern is active today
     */
    public static boolean isPatternActiveToday(SchedulePattern pattern) {
        if (pattern == null || !Boolean.TRUE.equals(pattern.getIsActive())) {
            return false;
        }
        
        LocalDate today = LocalDate.now();
        
        // Check date range
        if (pattern.getStartDate() != null && today.isBefore(pattern.getStartDate().toLocalDate())) {
            return false;
        }
        
        if (pattern.getEndDate() != null && today.isAfter(pattern.getEndDate().toLocalDate())) {
            return false;
        }
        
        // Check repeat pattern
        RepeatType repeatType = pattern.getRepeatType();
        if (repeatType == null) {
            return false;
        }
        
        switch (repeatType) {
            case ONE_TIME:
                return today.equals(pattern.getStartDate().toLocalDate());
                
            case DAILY:
                return true;
                
            case WEEKLY:
            case CUSTOM_DAYS:
                if (pattern.getWeekDays() == null || pattern.getWeekDays().isEmpty()) {
                    return false;
                }
                DayOfWeekCode todayCode = getDayOfWeekCode(today);
                return pattern.getWeekDays().contains(todayCode);
                
            case EVERY_N_DAYS:
                if (pattern.getIntervalDays() == null || pattern.getStartDate() == null) {
                    return false;
                }
                long daysSinceStart = java.time.temporal.ChronoUnit.DAYS.between(
                    pattern.getStartDate().toLocalDate(), today);
                return daysSinceStart % pattern.getIntervalDays() == 0;
                
            case MONTHLY:
                if (pattern.getDaysOfMonth() == null || pattern.getDaysOfMonth().isEmpty()) {
                    return false;
                }
                return pattern.getDaysOfMonth().contains(today.getDayOfMonth());
                
            case WEEKDAYS:
                int dayOfWeek = today.getDayOfWeek().getValue();
                return dayOfWeek >= 1 && dayOfWeek <= 5; // Monday to Friday
                
            case WEEKENDS:
                int dayOfWeek2 = today.getDayOfWeek().getValue();
                return dayOfWeek2 == 6 || dayOfWeek2 == 7; // Saturday or Sunday
                
            default:
                return false;
        }
    }
    
    /**
     * Get pattern description
     */
    public static String getPatternDescription(SchedulePattern pattern) {
        if (pattern == null || pattern.getRepeatType() == null) {
            return "Không xác định";
        }
        
        RepeatType repeatType = pattern.getRepeatType();
        
        switch (repeatType) {
            case ONE_TIME:
                return "Một lần";
                
            case DAILY:
                return "Hàng ngày";
                
            case WEEKLY:
                if (pattern.getWeekDays() != null && !pattern.getWeekDays().isEmpty()) {
                    return "Hàng tuần vào " + formatWeekDays(pattern.getWeekDays());
                }
                return "Hàng tuần";
                
            case CUSTOM_DAYS:
                if (pattern.getWeekDays() != null && !pattern.getWeekDays().isEmpty()) {
                    return "Tùy chỉnh: " + formatWeekDays(pattern.getWeekDays());
                }
                return "Tùy chỉnh";
                
            case EVERY_N_DAYS:
                if (pattern.getIntervalDays() != null) {
                    return String.format("Cứ %d ngày một lần", pattern.getIntervalDays());
                }
                return "Định kỳ";
                
            case MONTHLY:
                if (pattern.getDaysOfMonth() != null && !pattern.getDaysOfMonth().isEmpty()) {
                    return "Hàng tháng vào ngày " + formatDaysOfMonth(pattern.getDaysOfMonth());
                }
                return "Hàng tháng";
                
            case WEEKDAYS:
                return "Các ngày trong tuần (Thứ 2 - Thứ 6)";
                
            case WEEKENDS:
                return "Cuối tuần (Thứ 7 - Chủ nhật)";
                
            default:
                return "Không xác định";
        }
    }
    
    /**
     * Get day of week code from date
     */
    private static DayOfWeekCode getDayOfWeekCode(LocalDate date) {
        int dayOfWeek = date.getDayOfWeek().getValue();
        switch (dayOfWeek) {
            case 1: return DayOfWeekCode.MON;
            case 2: return DayOfWeekCode.TUE;
            case 3: return DayOfWeekCode.WED;
            case 4: return DayOfWeekCode.THU;
            case 5: return DayOfWeekCode.FRI;
            case 6: return DayOfWeekCode.SAT;
            case 7: return DayOfWeekCode.SUN;
            default: return DayOfWeekCode.MON;
        }
    }
    
    /**
     * Format week days for display
     */
    private static String formatWeekDays(java.util.List<DayOfWeekCode> weekDays) {
        if (weekDays == null || weekDays.isEmpty()) {
            return "";
        }
        
        return weekDays.stream()
                .map(day -> {
                    switch (day) {
                        case MON: return "Thứ 2";
                        case TUE: return "Thứ 3";
                        case WED: return "Thứ 4";
                        case THU: return "Thứ 5";
                        case FRI: return "Thứ 6";
                        case SAT: return "Thứ 7";
                        case SUN: return "Chủ nhật";
                        default: return day.name();
                    }
                })
                .reduce((a, b) -> a + ", " + b)
                .orElse("");
    }
    
    /**
     * Format days of month for display
     */
    private static String formatDaysOfMonth(java.util.List<Integer> daysOfMonth) {
        if (daysOfMonth == null || daysOfMonth.isEmpty()) {
            return "";
        }
        
        return daysOfMonth.stream()
                .sorted()
                .map(String::valueOf)
                .reduce((a, b) -> a + ", " + b)
                .orElse("");
    }
} 