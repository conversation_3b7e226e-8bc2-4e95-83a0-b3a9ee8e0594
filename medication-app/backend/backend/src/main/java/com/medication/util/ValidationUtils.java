package com.medication.util;

import org.springframework.stereotype.Component;
import java.time.LocalDate;
import java.util.List;
import java.time.LocalDateTime;
import com.medication.enums.DoseUnit;
import com.medication.enums.Route;
import com.medication.enums.DoseStatus;

/**
 * Utility class for validation logic
 */
@Component
public class ValidationUtils {

    /**
     * Check if date range is valid (start date before or equal to end date)
     */
    public static boolean isDateRangeValid(LocalDate startDate, LocalDate endDate) {
        return startDate == null || endDate == null || !startDate.isAfter(endDate);
    }

    /**
     * Check if prescription date is valid (before or equal to start date)
     */
    public static boolean isPrescriptionDateValid(LocalDate prescriptionDate, LocalDate startDate) {
        return prescriptionDate == null || startDate == null || !prescriptionDate.isAfter(startDate);
    }

    /**
     * Check if doses are valid (taken doses cannot exceed total doses)
     */
    public static boolean isDosesValid(Integer takenDoses, Integer totalDoses) {
        return takenDoses == null || totalDoses == null || takenDoses <= totalDoses;
    }

    /**
     * Check if compliance rate is valid (0-100)
     */
    public static boolean isComplianceRateValid(Double complianceRate) {
        return complianceRate == null || (complianceRate >= 0.0 && complianceRate <= 100.0);
    }

    /**
     * Check if dose amount is valid (positive)
     */
    public static boolean isDoseAmountValid(Double doseAmount) {
        return doseAmount != null && doseAmount > 0.0;
    }

    /**
     * Check if day of week is valid (1-7)
     */
    public static boolean isDayOfWeekValid(Integer dayOfWeek) {
        return dayOfWeek != null && dayOfWeek >= 1 && dayOfWeek <= 7;
    }

    /**
     * Check if retry count is valid (non-negative)
     */
    public static boolean isRetryCountValid(Integer retryCount) {
        return retryCount == null || retryCount >= 0;
    }

    /**
     * Check if max retries is valid (positive)
     */
    public static boolean isMaxRetriesValid(Integer maxRetries) {
        return maxRetries != null && maxRetries > 0;
    }
    
    /**
     * Check if dose unit is compatible with route
     */
    public static boolean isDoseUnitCompatibleWithRoute(DoseUnit doseUnit, Route route) {
        if (doseUnit == null || route == null) return true;
        
        switch (route) {
            case ORAL:
                return isSolidDose(doseUnit) || isLiquidDose(doseUnit);
            case INJECTION:
                return isLiquidDose(doseUnit); // Injection not supported in new enum
            case TOPICAL:
                return isTopicalDose(doseUnit) || isLiquidDose(doseUnit);
            case INHALATION:
                return doseUnit == DoseUnit.PUFF;
            case RECTAL:
                return isLiquidDose(doseUnit); // Suppository not supported in new enum
            case VAGINAL:
                return isLiquidDose(doseUnit); // Suppository not supported in new enum
            case OPHTHALMIC:
                return doseUnit == DoseUnit.DROP;
            case OTIC:
                return doseUnit == DoseUnit.DROP;
            case NASAL:
                return doseUnit == DoseUnit.DROP || doseUnit == DoseUnit.PUFF;
            default:
                return true;
        }
    }
    
    /**
     * Check if weight-based dose is valid
     */
    public static boolean isWeightBasedDoseValid(DoseUnit doseUnit) {
        if (!isWeightBasedDose(doseUnit)) return true;
        
        // For weight-based doses, we need patient weight for proper calculation
        // This is a placeholder for future implementation
        return true;
    }
    
    /**
     * Check if dose unit is weight-based
     */
    public static boolean isWeightBasedDose(DoseUnit doseUnit) {
        return doseUnit != null && (doseUnit == DoseUnit.MILLIGRAM || doseUnit == DoseUnit.MICROGRAM);
    }
    
    /**
     * Check if dose unit is liquid
     */
    public static boolean isLiquidDose(DoseUnit doseUnit) {
        return doseUnit != null && doseUnit == DoseUnit.MILLILITER;
    }
    
    /**
     * Check if dose unit is solid
     */
    public static boolean isSolidDose(DoseUnit doseUnit) {
        return doseUnit != null && (doseUnit == DoseUnit.TABLET || doseUnit == DoseUnit.CAPSULE || doseUnit == DoseUnit.GRAM);
    }
    
    /**
     * Check if dose unit is topical
     */
    public static boolean isTopicalDose(DoseUnit doseUnit) {
        return doseUnit != null && (doseUnit == DoseUnit.DROP || doseUnit == DoseUnit.PUFF);
    }
    
    /**
     * Check if actual dose is valid for given status
     */
    public static boolean isActualDoseValid(DoseStatus status, Double actualDoseAmount, DoseUnit actualDoseUnit) {
        if (status != null && status.requiresActualDose()) {
            return actualDoseAmount != null && actualDoseAmount > 0 && actualDoseUnit != null;
        }
        return true;
    }
    
    /**
     * Check if dose units are compatible
     */
    public static boolean isDoseUnitCompatible(DoseUnit expectedDoseUnit, DoseUnit actualDoseUnit) {
        if (expectedDoseUnit != null && actualDoseUnit != null) {
            return expectedDoseUnit.isCompatibleWith(actualDoseUnit);
        }
        return true;
    }

    public static boolean isIntervalDaysValid(Enum<?> repeatType, Integer intervalDays) {
        return !("EVERY_N_DAYS".equals(repeatType.name())) || (intervalDays != null && intervalDays > 0);
    }

    /**
     * Check if weekDays is valid for repeat type
     */
    public static boolean isWeekDaysValid(Enum<?> repeatType, List<?> weekDays) {
        String type = repeatType.name();
        if (type.equals("WEEKLY") || type.equals("CUSTOM_DAYS") || type.equals("WEEKDAYS") || type.equals("WEEKENDS")) {
            return weekDays != null && !weekDays.isEmpty();
        }
        return true;
    }

    /**
     * Check if daysOfMonth is valid for repeat type
     */
    public static boolean isDaysOfMonthValid(Enum<?> repeatType, List<Integer> daysOfMonth) {
        return !"MONTHLY".equals(repeatType.name()) || (daysOfMonth != null && !daysOfMonth.isEmpty());
    }

    /**
     * Check if date range is valid (LocalDateTime version)
     */
    public static boolean isDateRangeValid(LocalDateTime startDate, LocalDateTime endDate) {
        return startDate == null || endDate == null || endDate.isAfter(startDate);
    }

    /**
     * Check if startDate is valid for ONE_TIME repeat type
     */
    public static boolean isOneTimeValid(Enum<?> repeatType, LocalDateTime startDate) {
        return !"ONE_TIME".equals(repeatType.name()) || startDate != null;
    }
} 