spring.application.name=medication-backend

# Database Configuration
spring.datasource.url=**********************************************
spring.datasource.username=dongtran
spring.datasource.password=
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.type.preferred_instant_jdbc_type=TIMESTAMP

# Flyway Migration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.out-of-order=true
spring.flyway.init-sqls=SET search_path TO public;

# Server Configuration
server.port=8081
server.servlet.context-path=/

# JWT Configuration
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# Email Configuration (Gmail SMTP)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your-app-password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

# Application Configuration
app.name=Medication App

# Logging Configuration
logging.level.org.springframework.security=DEBUG
logging.level.com.medication=DEBUG
logging.level.org.flywaydb=INFO

# Validation
spring.jpa.defer-datasource-initialization=true
