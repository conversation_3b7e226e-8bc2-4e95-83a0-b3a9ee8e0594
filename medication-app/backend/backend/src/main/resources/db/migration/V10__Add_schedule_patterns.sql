-- Migration V10: Add schedule patterns for flexible medication scheduling
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Add schedule patterns table and related tables for flexible scheduling

-- Create schedule_patterns table
CREATE TABLE schedule_patterns (
    id BIGSERIAL PRIMARY KEY,
    medication_schedule_id BIGINT NOT NULL,
    repeat_type VARCHAR(50) NOT NULL,
    interval_days INTEGER CHECK (interval_days > 0),
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    notes VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (medication_schedule_id) REFERENCES medication_schedules(id) ON DELETE CASCADE
);

-- Create schedule_pattern_weekdays table
CREATE TABLE schedule_pattern_weekdays (
    pattern_id BIGINT NOT NULL,
    weekday VARCHAR(10) NOT NULL,
    PRIMARY KEY (pattern_id, weekday),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (pattern_id) REFERENCES schedule_patterns(id) ON DELETE CASCADE
);

-- Create schedule_pattern_monthdays table
CREATE TABLE schedule_pattern_monthdays (
    pattern_id BIGINT NOT NULL,
    day_of_month INTEGER NOT NULL CHECK (day_of_month >= 1 AND day_of_month <= 31),
    PRIMARY KEY (pattern_id, day_of_month),
    FOREIGN KEY (pattern_id) REFERENCES schedule_patterns(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_schedule_patterns_medication_schedule_id ON schedule_patterns(medication_schedule_id);
CREATE INDEX idx_schedule_patterns_repeat_type ON schedule_patterns(repeat_type);
CREATE INDEX idx_schedule_patterns_start_date ON schedule_patterns(start_date);
CREATE INDEX idx_schedule_patterns_end_date ON schedule_patterns(end_date);
CREATE INDEX idx_schedule_patterns_is_active ON schedule_patterns(is_active);
CREATE INDEX idx_schedule_patterns_date_range ON schedule_patterns(start_date, end_date, is_active);

CREATE INDEX idx_pattern_weekdays_pattern_id ON schedule_pattern_weekdays(pattern_id);
CREATE INDEX idx_pattern_weekdays_weekday ON schedule_pattern_weekdays(weekday);

CREATE INDEX idx_pattern_monthdays_pattern_id ON schedule_pattern_monthdays(pattern_id);
CREATE INDEX idx_pattern_monthdays_day ON schedule_pattern_monthdays(day_of_month);

-- Add constraints
ALTER TABLE schedule_patterns ADD CONSTRAINT chk_schedule_patterns_repeat_type
    CHECK (repeat_type IN ('DAILY', 'WEEKLY', 'EVERY_N_DAYS', 'CUSTOM_DAYS', 'MONTHLY', 'WEEKDAYS', 'WEEKENDS'));

ALTER TABLE schedule_patterns ADD CONSTRAINT chk_schedule_patterns_end_date_after_start
    CHECK (end_date IS NULL OR end_date > start_date);

ALTER TABLE schedule_pattern_weekdays ADD CONSTRAINT chk_weekday_format
    CHECK (weekday IN ('MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'));

-- Add trigger for updated_at
CREATE TRIGGER trg_schedule_patterns_updated_at
    BEFORE UPDATE ON schedule_patterns
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments
COMMENT ON TABLE schedule_patterns IS 'Flexible scheduling patterns for medication schedules';
COMMENT ON COLUMN schedule_patterns.repeat_type IS 'Type of repetition: DAILY, WEEKLY, EVERY_N_DAYS, CUSTOM_DAYS, MONTHLY, WEEKDAYS, WEEKENDS';
COMMENT ON COLUMN schedule_patterns.interval_days IS 'Interval in days for EVERY_N_DAYS pattern';
COMMENT ON COLUMN schedule_patterns.start_date IS 'Start date for the pattern';
COMMENT ON COLUMN schedule_patterns.end_date IS 'End date for the pattern (optional)';

COMMENT ON TABLE schedule_pattern_weekdays IS 'Weekdays for WEEKLY and CUSTOM_DAYS patterns';
COMMENT ON TABLE schedule_pattern_monthdays IS 'Days of month for MONTHLY pattern'; 