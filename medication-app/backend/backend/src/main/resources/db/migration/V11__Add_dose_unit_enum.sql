-- Migration V11: Add DoseUnit enum and update related tables
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Convert dose_unit fields from VARCHAR to ENUM for consistency

-- Create DoseUnit enum type
CREATE TYPE dose_unit_enum AS ENUM (
    -- Solid forms
    'TABLET', 'CAPSULE', 'PILL', 'GRAM', 'MILLIGRAM', 'MICROGRAM',
    -- Liquid forms
    'MILLILITER', 'LITER', 'DROP', 'TEASPOON', 'TABLESPOON', 'CUP',
    -- Injection forms
    'UNIT', 'INTERNATIONAL_UNIT',
    -- Topical forms
    'APPLICATION', 'PUFF', 'SPRAY',
    -- Special forms
    'PATCH', 'SUPPOSITORY', 'INHALATION',
    -- Weight-based
    'MG_PER_KG', 'MCG_PER_KG', 'UNITS_PER_KG'
);

-- Update prescription_items table
ALTER TABLE prescription_items ALTER COLUMN dose_unit TYPE dose_unit_enum USING dose_unit::dose_unit_enum;

-- Update medication_schedules table
ALTER TABLE medication_schedules ALTER COLUMN dose_unit TYPE dose_unit_enum USING dose_unit::dose_unit_enum;

-- Update medication_doses table
ALTER TABLE medication_doses ALTER COLUMN expected_dose_unit TYPE dose_unit_enum USING expected_dose_unit::dose_unit_enum;
ALTER TABLE medication_doses ALTER COLUMN actual_dose_unit TYPE dose_unit_enum USING actual_dose_unit::dose_unit_enum;

-- Add comments
COMMENT ON TYPE dose_unit_enum IS 'Enumeration of dose units for medication prescriptions';
COMMENT ON COLUMN prescription_items.dose_unit IS 'Dose unit for the medication (enum)';
COMMENT ON COLUMN medication_schedules.dose_unit IS 'Dose unit for this schedule (enum)';
COMMENT ON COLUMN medication_doses.expected_dose_unit IS 'Expected dose unit (enum)';
COMMENT ON COLUMN medication_doses.actual_dose_unit IS 'Actual dose unit taken (enum)';

-- Add check constraints for dose unit compatibility
-- Note: These constraints can be enhanced based on business rules
ALTER TABLE prescription_items ADD CONSTRAINT chk_prescription_items_dose_unit_not_null
    CHECK (dose_unit IS NOT NULL);

ALTER TABLE medication_schedules ADD CONSTRAINT chk_medication_schedules_dose_unit_not_null
    CHECK (dose_unit IS NOT NULL);

-- Add function to get dose unit label
CREATE OR REPLACE FUNCTION get_dose_unit_label(unit dose_unit_enum)
RETURNS TEXT AS $$
BEGIN
    CASE unit
        WHEN 'TABLET' THEN RETURN 'Viên';
        WHEN 'CAPSULE' THEN RETURN 'Viên nang';
        WHEN 'PILL' THEN RETURN 'Thuốc viên';
        WHEN 'GRAM' THEN RETURN 'Gam';
        WHEN 'MILLIGRAM' THEN RETURN 'Miligam';
        WHEN 'MICROGRAM' THEN RETURN 'Microgam';
        WHEN 'MILLILITER' THEN RETURN 'Mililit';
        WHEN 'LITER' THEN RETURN 'Lít';
        WHEN 'DROP' THEN RETURN 'Giọt';
        WHEN 'TEASPOON' THEN RETURN 'Thìa cà phê';
        WHEN 'TABLESPOON' THEN RETURN 'Thìa canh';
        WHEN 'CUP' THEN RETURN 'Cốc';
        WHEN 'UNIT' THEN RETURN 'Đơn vị';
        WHEN 'INTERNATIONAL_UNIT' THEN RETURN 'Đơn vị quốc tế';
        WHEN 'APPLICATION' THEN RETURN 'Lần bôi';
        WHEN 'PUFF' THEN RETURN 'Nhát xịt';
        WHEN 'SPRAY' THEN RETURN 'Lần xịt';
        WHEN 'PATCH' THEN RETURN 'Miếng dán';
        WHEN 'SUPPOSITORY' THEN RETURN 'Viên đặt';
        WHEN 'INHALATION' THEN RETURN 'Lần hít';
        WHEN 'MG_PER_KG' THEN RETURN 'mg/kg';
        WHEN 'MCG_PER_KG' THEN RETURN 'mcg/kg';
        WHEN 'UNITS_PER_KG' THEN RETURN 'đơn vị/kg';
        ELSE RETURN unit::TEXT;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Add function to get dose unit abbreviation
CREATE OR REPLACE FUNCTION get_dose_unit_abbreviation(unit dose_unit_enum)
RETURNS TEXT AS $$
BEGIN
    CASE unit
        WHEN 'TABLET' THEN RETURN 'tab';
        WHEN 'CAPSULE' THEN RETURN 'cap';
        WHEN 'PILL' THEN RETURN 'pill';
        WHEN 'GRAM' THEN RETURN 'g';
        WHEN 'MILLIGRAM' THEN RETURN 'mg';
        WHEN 'MICROGRAM' THEN RETURN 'mcg';
        WHEN 'MILLILITER' THEN RETURN 'ml';
        WHEN 'LITER' THEN RETURN 'L';
        WHEN 'DROP' THEN RETURN 'drop';
        WHEN 'TEASPOON' THEN RETURN 'tsp';
        WHEN 'TABLESPOON' THEN RETURN 'tbsp';
        WHEN 'CUP' THEN RETURN 'cup';
        WHEN 'UNIT' THEN RETURN 'U';
        WHEN 'INTERNATIONAL_UNIT' THEN RETURN 'IU';
        WHEN 'APPLICATION' THEN RETURN 'app';
        WHEN 'PUFF' THEN RETURN 'puff';
        WHEN 'SPRAY' THEN RETURN 'spray';
        WHEN 'PATCH' THEN RETURN 'patch';
        WHEN 'SUPPOSITORY' THEN RETURN 'supp';
        WHEN 'INHALATION' THEN RETURN 'inh';
        WHEN 'MG_PER_KG' THEN RETURN 'mg/kg';
        WHEN 'MCG_PER_KG' THEN RETURN 'mcg/kg';
        WHEN 'UNITS_PER_KG' THEN RETURN 'U/kg';
        ELSE RETURN unit::TEXT;
    END CASE;
END;
$$ LANGUAGE plpgsql; 