-- Migration V12: Add ONE_TIME pattern and DayOfWeekCode enum
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Add ONE_TIME repeat type and convert weekdays to enum

-- Create DayOfWeekCode enum type (if not exists)
DO $$ BEGIN
    CREATE TYPE day_of_week_code_enum AS ENUM ('MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create RepeatType enum type (if not exists)
DO $$ BEGIN
    CREATE TYPE repeat_type_enum AS ENUM (
        'ONE_TIME',
        'DAILY',
        'WEEKLY',
        'EVERY_N_DAYS',
        'CUSTOM_DAYS',
        'MONTHLY',
        'WEEKDAYS',
        'WEEKENDS'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update schedule_patterns table to use enum
ALTER TABLE schedule_patterns ALTER COLUMN repeat_type TYPE repeat_type_enum USING repeat_type::repeat_type_enum;

-- Update schedule_pattern_weekdays table to use enum
ALTER TABLE schedule_pattern_weekdays ALTER COLUMN weekday TYPE day_of_week_code_enum USING weekday::day_of_week_code_enum;

-- Add comments
COMMENT ON TYPE day_of_week_code_enum IS 'Enumeration of day of week codes for schedule patterns';
COMMENT ON COLUMN schedule_pattern_weekdays.weekday IS 'Day of week code (enum)';

-- Add function to get day of week label
CREATE OR REPLACE FUNCTION get_day_of_week_label(day_code day_of_week_code_enum)
RETURNS TEXT AS $$
BEGIN
    CASE day_code
        WHEN 'MON' THEN RETURN 'Thứ 2';
        WHEN 'TUE' THEN RETURN 'Thứ 3';
        WHEN 'WED' THEN RETURN 'Thứ 4';
        WHEN 'THU' THEN RETURN 'Thứ 5';
        WHEN 'FRI' THEN RETURN 'Thứ 6';
        WHEN 'SAT' THEN RETURN 'Thứ 7';
        WHEN 'SUN' THEN RETURN 'Chủ nhật';
        ELSE RETURN day_code::TEXT;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Add function to get day of week code from date
CREATE OR REPLACE FUNCTION get_day_of_week_code(input_date DATE)
RETURNS day_of_week_code_enum AS $$
BEGIN
    CASE EXTRACT(DOW FROM input_date)
        WHEN 1 THEN RETURN 'MON';
        WHEN 2 THEN RETURN 'TUE';
        WHEN 3 THEN RETURN 'WED';
        WHEN 4 THEN RETURN 'THU';
        WHEN 5 THEN RETURN 'FRI';
        WHEN 6 THEN RETURN 'SAT';
        WHEN 0 THEN RETURN 'SUN';
        ELSE RETURN 'MON';
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Add function to check if date matches pattern
CREATE OR REPLACE FUNCTION is_pattern_active_on_date(
    pattern_repeat_type repeat_type_enum,
    pattern_start_date TIMESTAMP,
    pattern_end_date TIMESTAMP,
    pattern_interval_days INTEGER,
    pattern_weekdays day_of_week_code_enum[],
    pattern_monthdays INTEGER[],
    check_date DATE
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check date range
    IF check_date < pattern_start_date::DATE THEN
        RETURN FALSE;
    END IF;
    
    IF pattern_end_date IS NOT NULL AND check_date > pattern_end_date::DATE THEN
        RETURN FALSE;
    END IF;

    -- Check repeat pattern
    CASE pattern_repeat_type
        WHEN 'ONE_TIME' THEN
            RETURN check_date = pattern_start_date::DATE;
            
        WHEN 'DAILY' THEN
            RETURN TRUE;
            
        WHEN 'WEEKLY', 'CUSTOM_DAYS' THEN
            RETURN get_day_of_week_code(check_date) = ANY(pattern_weekdays);
            
        WHEN 'EVERY_N_DAYS' THEN
            IF pattern_interval_days IS NULL THEN
                RETURN FALSE;
            END IF;
            RETURN (check_date - pattern_start_date::DATE) % pattern_interval_days = 0;
            
        WHEN 'MONTHLY' THEN
            RETURN EXTRACT(DAY FROM check_date) = ANY(pattern_monthdays);
            
        WHEN 'WEEKDAYS' THEN
            RETURN EXTRACT(DOW FROM check_date) BETWEEN 1 AND 5;
            
        WHEN 'WEEKENDS' THEN
            RETURN EXTRACT(DOW FROM check_date) IN (0, 6);
            
        ELSE
            RETURN FALSE;
    END CASE;
END;
$$ LANGUAGE plpgsql; 