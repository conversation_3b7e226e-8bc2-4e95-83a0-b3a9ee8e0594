-- Migration V13: Add schedule item active index
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Add index for filtering by prescription item and active status

-- Add index for filtering schedules by prescription item and active status
-- This optimizes queries like: SELECT * FROM medication_schedules WHERE prescription_item_id = ? AND is_active = true
CREATE INDEX idx_schedule_item_active ON medication_schedules (prescription_item_id, is_active);

-- Add comment
COMMENT ON INDEX idx_schedule_item_active IS 'Index for filtering medication schedules by prescription item and active status'; 