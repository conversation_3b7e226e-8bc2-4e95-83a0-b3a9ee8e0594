-- Migration: Add soft delete functionality to prescriptions table
-- Version: V15
-- Description: Add deleted_at and deleted_by columns for soft delete functionality
-- Author: quydong.tran
-- Date: December 2024

-- Add soft delete columns to prescriptions table
ALTER TABLE prescriptions 
ADD COLUMN deleted_at TIMESTAMP,
ADD COLUMN deleted_by VARCHAR(100);

-- Add index for soft delete queries (if not exists)
DO $$ BEGIN
    CREATE INDEX idx_prescriptions_deleted_at ON prescriptions(deleted_at);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add index for compliance rate queries (if not exists)
DO $$ BEGIN
    CREATE INDEX idx_prescriptions_compliance_rate ON prescriptions(compliance_rate);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Index idx_prescriptions_status already exists from V2 migration

-- Add index for date range queries (if not exists)
DO $$ BEGIN
    CREATE INDEX idx_prescriptions_date_range ON prescriptions(start_date, end_date);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update compliance_rate column to use DECIMAL for precision
ALTER TABLE prescriptions 
ALTER COLUMN compliance_rate TYPE DECIMAL(5,2);

-- Set default value for compliance_rate
UPDATE prescriptions 
SET compliance_rate = 0.00 
WHERE compliance_rate IS NULL;

-- Add constraint to ensure compliance_rate is not null
ALTER TABLE prescriptions 
ALTER COLUMN compliance_rate SET NOT NULL;

-- Add constraint to ensure compliance_rate is between 0 and 100 (if not exists)
DO $$ BEGIN
    ALTER TABLE prescriptions 
    ADD CONSTRAINT chk_prescriptions_compliance_rate 
    CHECK (compliance_rate >= 0.00 AND compliance_rate <= 100.00);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add constraint to ensure prescription_date is before or equal to start_date (if not exists)
DO $$ BEGIN
    ALTER TABLE prescriptions 
    ADD CONSTRAINT chk_prescriptions_date_order 
    CHECK (prescription_date <= start_date);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add comment for documentation
COMMENT ON COLUMN prescriptions.deleted_at IS 'Timestamp when prescription was soft deleted';
COMMENT ON COLUMN prescriptions.deleted_by IS 'User who soft deleted the prescription';
COMMENT ON COLUMN prescriptions.compliance_rate IS 'Compliance rate as percentage (0.00-100.00)'; 