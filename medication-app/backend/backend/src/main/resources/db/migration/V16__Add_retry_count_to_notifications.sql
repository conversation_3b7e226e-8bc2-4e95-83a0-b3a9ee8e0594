-- Migration: Add retry count and indexes to notifications table
-- Version: V16
-- Description: Add retry_count column and performance indexes for notifications
-- Author: quydong.tran
-- Date: December 2024

-- Add retry_count column to notifications table
ALTER TABLE notifications 
ADD COLUMN retry_count INTEGER NOT NULL DEFAULT 0;

-- Add indexes for performance optimization
CREATE INDEX idx_notification_user ON notifications(user_id);
CREATE INDEX idx_notification_scheduled ON notifications(scheduled_at);
CREATE INDEX idx_notification_status ON notifications(status);
CREATE INDEX idx_notification_read ON notifications(is_read);
CREATE INDEX idx_notification_created ON notifications(created_at);

-- Add composite index for common queries
CREATE INDEX idx_notification_user_status ON notifications(user_id, status);
CREATE INDEX idx_notification_user_read ON notifications(user_id, is_read);
CREATE INDEX idx_notification_scheduled_status ON notifications(scheduled_at, status);

-- Add constraint to ensure retry_count is non-negative
ALTER TABLE notifications 
ADD CONSTRAINT chk_notification_retry_count 
CHECK (retry_count >= 0);

-- Add comment for documentation
COMMENT ON COLUMN notifications.retry_count IS 'Number of retry attempts for failed notifications'; 