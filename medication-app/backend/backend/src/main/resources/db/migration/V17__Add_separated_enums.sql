-- Migration V17: Add separated enum types for refactored entities
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Create enum types for UserRole, PrescriptionStatus, DoseStatus, FamilyRole

-- Create UserRole enum type
DO $$ BEGIN
    CREATE TYPE user_role_enum AS ENUM (
        'USER',
        'ADMIN', 
        'DOCTOR',
        'PHARMACIST',
        'SUPER_ADMIN'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create PrescriptionStatus enum type
DO $$ BEGIN
    CREATE TYPE prescription_status_enum AS ENUM (
        'ACTIVE',
        'COMPLETED',
        'CANCELLED',
        'EXPIRED',
        'PAUSED'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create DoseStatus enum type
DO $$ BEGIN
    CREATE TYPE dose_status_enum AS ENUM (
        'PENDING',
        'TAKEN',
        'MISSED',
        'SKIPPED',
        'PARTIAL'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create FamilyRole enum type
DO $$ BEGIN
    CREATE TYPE family_role_enum AS ENUM (
        'OWNER',
        'ADMIN',
        'MEMBER'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update users table to use enum
-- First drop default value if exists
ALTER TABLE users ALTER COLUMN role DROP DEFAULT;
-- Then change type
ALTER TABLE users ALTER COLUMN role TYPE user_role_enum USING role::user_role_enum;

-- Update prescriptions table to use enum
-- First drop default value if exists
ALTER TABLE prescriptions ALTER COLUMN status DROP DEFAULT;
-- Then change type
ALTER TABLE prescriptions ALTER COLUMN status TYPE prescription_status_enum USING status::prescription_status_enum;

-- Update medication_doses table to use enum
-- First drop default value if exists
ALTER TABLE medication_doses ALTER COLUMN status DROP DEFAULT;
-- Then change type
ALTER TABLE medication_doses ALTER COLUMN status TYPE dose_status_enum USING status::dose_status_enum;

-- Update family_members table to use enum
-- First drop default value if exists
ALTER TABLE family_members ALTER COLUMN role DROP DEFAULT;
-- Then change type
ALTER TABLE family_members ALTER COLUMN role TYPE family_role_enum USING role::family_role_enum;

-- Add comments
COMMENT ON TYPE user_role_enum IS 'Enumeration of user roles in the system';
COMMENT ON TYPE prescription_status_enum IS 'Enumeration of prescription statuses';
COMMENT ON TYPE dose_status_enum IS 'Enumeration of medication dose statuses';
COMMENT ON TYPE family_role_enum IS 'Enumeration of family member roles';

-- Add check constraints
ALTER TABLE users ADD CONSTRAINT chk_users_role_not_null CHECK (role IS NOT NULL);
ALTER TABLE prescriptions ADD CONSTRAINT chk_prescriptions_status_not_null CHECK (status IS NOT NULL);
ALTER TABLE medication_doses ADD CONSTRAINT chk_medication_doses_status_not_null CHECK (status IS NOT NULL);
ALTER TABLE family_members ADD CONSTRAINT chk_family_members_role_not_null CHECK (role IS NOT NULL); 