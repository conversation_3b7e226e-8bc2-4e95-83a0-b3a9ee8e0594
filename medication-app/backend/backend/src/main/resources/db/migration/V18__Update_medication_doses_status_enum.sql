-- Migration V18: Update medication_doses status to use enum
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Convert medication_doses status from VARCHAR to dose_status_enum

-- Remove old check constraint
ALTER TABLE medication_doses DROP CONSTRAINT IF EXISTS chk_medication_doses_status;

-- Update medication_doses table to use enum
ALTER TABLE medication_doses ALTER COLUMN status TYPE dose_status_enum USING status::dose_status_enum;

-- Add new check constraint using enum
ALTER TABLE medication_doses ADD CONSTRAINT chk_medication_doses_status_enum 
    CHECK (status IS NOT NULL);

-- Add comment
COMMENT ON COLUMN medication_doses.status IS 'Dose status using dose_status_enum'; 