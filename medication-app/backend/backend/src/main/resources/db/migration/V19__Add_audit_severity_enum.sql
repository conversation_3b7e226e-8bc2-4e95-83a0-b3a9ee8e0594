-- Migration V19: Add AuditSeverity enum
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Create AuditSeverity enum type and update audit_logs table

-- Create AuditSeverity enum type
DO $$ BEGIN
    CREATE TYPE audit_severity_enum AS ENUM (
        'INFO',
        'WARNING',
        'ERROR',
        'CRITICAL'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update audit_logs table to use enum
-- First drop default value if exists
ALTER TABLE audit_logs ALTER COLUMN severity DROP DEFAULT;
-- Then change type
ALTER TABLE audit_logs ALTER COLUMN severity TYPE audit_severity_enum USING severity::audit_severity_enum;

-- Add comment
COMMENT ON TYPE audit_severity_enum IS 'Enumeration of audit log severity levels';
COMMENT ON COLUMN audit_logs.severity IS 'Severity level using audit_severity_enum';

-- Add check constraint
ALTER TABLE audit_logs ADD CONSTRAINT chk_audit_logs_severity_not_null CHECK (severity IS NOT NULL); 