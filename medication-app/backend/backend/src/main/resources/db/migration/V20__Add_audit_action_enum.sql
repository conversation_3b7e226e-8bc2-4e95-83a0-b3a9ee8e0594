-- Migration V20: Add AuditAction enum
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Create AuditAction enum type and update audit_logs table

-- Create AuditAction enum type
DO $$ BEGIN
    CREATE TYPE audit_action_enum AS ENUM (
        'CREATE',
        'READ',
        'UPDATE',
        'DELETE',
        'LOGIN',
        'LOGOUT',
        'PASSWORD_CHANGE',
        'PROFILE_UPDATE',
        'MEDICINE_ADD',
        'MEDICINE_UPDATE',
        'MEDICINE_DELETE',
        'PRESCRIPTION_CREATE',
        'PRESCRIPTION_UPDATE',
        'PRESCRIPTION_DELETE',
        'DOSE_TAKEN',
        'DOSE_MISSED',
        'DOSE_SKIPPED',
        'FAMILY_CREATE',
        'FAMILY_UPDATE',
        'FAMILY_DELETE',
        'MEMBER_ADD',
        'MEMBER_REMOVE',
        'MEMBER_ROLE_CHANGE',
        'NOTIFICATION_SENT',
        'NOTIFICATION_READ',
        'SYSTEM_BACKUP',
        'SYSTEM_RESTORE',
        'DATA_EXPORT',
        'DATA_IMPORT'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update audit_logs table to use enum
ALTER TABLE audit_logs ALTER COLUMN action TYPE audit_action_enum USING action::audit_action_enum;

-- Add comment
COMMENT ON TYPE audit_action_enum IS 'Enumeration of audit log actions';
COMMENT ON COLUMN audit_logs.action IS 'Action performed using audit_action_enum';

-- Add check constraint
ALTER TABLE audit_logs ADD CONSTRAINT chk_audit_logs_action_not_null CHECK (action IS NOT NULL); 