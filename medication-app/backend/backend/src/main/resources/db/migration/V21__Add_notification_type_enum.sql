-- Migration V21: Add NotificationType enum
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Create NotificationType enum and update notifications table

-- Create NotificationType enum
DO $$ BEGIN
    CREATE TYPE notification_type_enum AS ENUM (
        'INFO',
        'WARNING',
        'ERROR',
        'SUCCESS',
        'MEDICATION_REMINDER',
        'DOSE_OVERDUE',
        'PRESCRIPTION_EXPIRING',
        'MEDICINE_LOW_STOCK',
        'MEDICINE_EXPIRED',
        'FAMILY_INVITATION',
        'SYSTEM_MAINTENANCE',
        'SECURITY_ALERT'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update notifications table to use enum
-- First drop default value if exists
ALTER TABLE notifications ALTER COLUMN type DROP DEFAULT;
-- Then change type
ALTER TABLE notifications ALTER COLUMN type TYPE notification_type_enum USING type::notification_type_enum;

-- Add comment
COMMENT ON TYPE notification_type_enum IS 'Enumeration of notification types';
COMMENT ON COLUMN notifications.type IS 'Notification type using notification_type_enum';

-- Add check constraint
ALTER TABLE notifications ADD CONSTRAINT chk_notifications_type_not_null CHECK (type IS NOT NULL); 