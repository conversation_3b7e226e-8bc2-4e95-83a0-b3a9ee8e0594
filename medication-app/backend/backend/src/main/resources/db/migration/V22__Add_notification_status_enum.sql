-- Migration V22: Add NotificationStatus enum
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Create NotificationStatus enum and update notifications.status column

-- Create NotificationStatus enum
DO $$ BEGIN
    CREATE TYPE notification_status_enum AS ENUM (
        'PENDING',
        'SENT',
        'FAILED',
        'CANCELLED',
        'READ',
        'ARCHIVED'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update notifications table to use enum
-- First drop default value if exists
ALTER TABLE notifications ALTER COLUMN status DROP DEFAULT;
-- Then change type
ALTER TABLE notifications ALTER COLUMN status TYPE notification_status_enum USING status::notification_status_enum;

-- Add comment
COMMENT ON TYPE notification_status_enum IS 'Enumeration of notification statuses';
COMMENT ON COLUMN notifications.status IS 'Notification status using notification_status_enum';

-- Add check constraint
ALTER TABLE notifications ADD CONSTRAINT chk_notifications_status_not_null CHECK (status IS NOT NULL); 