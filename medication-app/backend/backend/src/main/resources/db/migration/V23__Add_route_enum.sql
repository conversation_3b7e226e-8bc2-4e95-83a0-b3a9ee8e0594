-- Migration V23: Add Route enum
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Create Route enum and update prescription_items.route column

-- Create Route enum
DO $$ BEGIN
    CREATE TYPE route_enum AS ENUM (
        'OR<PERSON>',
        'INJECTION',
        'TOPICAL',
        'INHALATION',
        'SUBLINGUAL',
        'BUCCAL',
        'RECTAL',
        'VAGINAL',
        'TRANSDERMAL',
        'INTRAVENOUS',
        'INTRAMUSCULAR',
        'SUBCUTANEOUS',
        'INTRADERMAL',
        'NASAL',
        'OPHTHALMIC',
        'OTIC',
        'OTHER'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update prescription_items table to use enum
ALTER TABLE prescription_items ALTER COLUMN route TYPE route_enum USING route::route_enum;

-- Add comment
COMMENT ON TYPE route_enum IS 'Enumeration of medication administration routes';
COMMENT ON COLUMN prescription_items.route IS 'Route of administration using route_enum';

-- Add check constraint
ALTER TABLE prescription_items ADD CONSTRAINT chk_prescription_items_route_not_null CHECK (route IS NOT NULL); 