-- V24__Add_anonymous_user_support.sql (improved v2)
-- Add Authentication Optional Pattern support for anonymous users (idempotent, safe, UUID, FK, cascade)

-- 0. Enable pgcrypto extension for UUID generation
DO $$ BEGIN
    CREATE EXTENSION IF NOT EXISTS "pgcrypto";
EXCEPTION WHEN OTHERS THEN
    -- ignore if not superuser
END $$;

-- 1. Create user type enum (if not exists)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_type_enum') THEN
        CREATE TYPE user_type_enum AS ENUM ('ANONYMOUS', 'REGISTERED', 'PREMIUM');
    END IF;
END $$;

-- 2. Create anonymous session status enum (if not exists)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'anonymous_session_status_enum') THEN
        CREATE TYPE anonymous_session_status_enum AS ENUM ('ACTIVE', 'EXPIRED', 'MIGRATED', 'CLEANED');
    END IF;
END $$;

-- 3. Update users table to support anonymous users (anonymous_user_id UUID)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'user_type') THEN
        ALTER TABLE users ADD COLUMN user_type user_type_enum DEFAULT 'REGISTERED';
    END IF;
END $$;
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'anonymous_user_id') THEN
        ALTER TABLE users ADD COLUMN anonymous_user_id UUID;
    END IF;
END $$;
DO $$ BEGIN
    -- Only drop NOT NULL if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'email' AND is_nullable = 'NO') THEN
        ALTER TABLE users ALTER COLUMN email DROP NOT NULL;
    END IF;
END $$;
DO $$ BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'password' AND is_nullable = 'NO') THEN
        ALTER TABLE users ALTER COLUMN password DROP NOT NULL;
    END IF;
END $$;

-- 3.1. Add unique constraint for users.anonymous_user_id (for foreign key reference)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE table_name = 'users' AND constraint_name = 'uk_users_anonymous_user_id') THEN
        ALTER TABLE users ADD CONSTRAINT uk_users_anonymous_user_id UNIQUE (anonymous_user_id);
    END IF;
END $$;

-- 4. Create anonymous_sessions table (if not exists, id BIGSERIAL, anonymous_user_id UUID, FK mềm)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'anonymous_sessions') THEN
        CREATE TABLE anonymous_sessions (
            id BIGSERIAL PRIMARY KEY,
            anonymous_user_id UUID NOT NULL UNIQUE,
            device_id VARCHAR(100),
            app_version VARCHAR(20),
            created_at TIMESTAMP NOT NULL DEFAULT NOW(),
            last_activity_at TIMESTAMP NOT NULL DEFAULT NOW(),
            data_synced BOOLEAN DEFAULT FALSE,
            migrated_to_user_id BIGINT,
            migrated_at TIMESTAMP,
            status anonymous_session_status_enum DEFAULT 'ACTIVE',
            CONSTRAINT fk_anonymous_user_id_soft FOREIGN KEY (anonymous_user_id) REFERENCES users(anonymous_user_id) ON DELETE SET NULL,
            CONSTRAINT fk_migrated_to_user_id FOREIGN KEY (migrated_to_user_id) REFERENCES users(id) ON DELETE SET NULL
        );
    END IF;
END $$;

-- 5. Update medicines table to support anonymous users (anonymous_user_id UUID)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medicines' AND column_name = 'user_type') THEN
        ALTER TABLE medicines ADD COLUMN user_type user_type_enum DEFAULT 'REGISTERED';
    END IF;
END $$;
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medicines' AND column_name = 'anonymous_user_id') THEN
        ALTER TABLE medicines ADD COLUMN anonymous_user_id UUID;
    END IF;
END $$;
DO $$ BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medicines' AND column_name = 'user_id' AND is_nullable = 'NO') THEN
        ALTER TABLE medicines ALTER COLUMN user_id DROP NOT NULL;
    END IF;
END $$;

-- 6. Update prescriptions table to support anonymous users (anonymous_user_id UUID)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prescriptions' AND column_name = 'user_type') THEN
        ALTER TABLE prescriptions ADD COLUMN user_type user_type_enum DEFAULT 'REGISTERED';
    END IF;
END $$;
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prescriptions' AND column_name = 'anonymous_user_id') THEN
        ALTER TABLE prescriptions ADD COLUMN anonymous_user_id UUID;
    END IF;
END $$;
DO $$ BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'prescriptions' AND column_name = 'patient_id' AND is_nullable = 'NO') THEN
        ALTER TABLE prescriptions ALTER COLUMN patient_id DROP NOT NULL;
    END IF;
END $$;

-- 7. Update audit_logs table to support anonymous users (anonymous_user_id UUID)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'audit_logs' AND column_name = 'anonymous_user_id') THEN
        ALTER TABLE audit_logs ADD COLUMN anonymous_user_id UUID;
    END IF;
END $$;

-- 8. Add indexes for performance optimization (if not exists, update type)
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_users_user_type') THEN CREATE INDEX idx_users_user_type ON users(user_type); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_users_anonymous_id') THEN CREATE INDEX idx_users_anonymous_id ON users(anonymous_user_id); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_anonymous_sessions_user_id') THEN CREATE INDEX idx_anonymous_sessions_user_id ON anonymous_sessions(anonymous_user_id); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_anonymous_sessions_last_activity') THEN CREATE INDEX idx_anonymous_sessions_last_activity ON anonymous_sessions(last_activity_at); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_anonymous_sessions_migrated') THEN CREATE INDEX idx_anonymous_sessions_migrated ON anonymous_sessions(migrated_to_user_id); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_anonymous_sessions_status') THEN CREATE INDEX idx_anonymous_sessions_status ON anonymous_sessions(status); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_medicines_anonymous_user_id') THEN CREATE INDEX idx_medicines_anonymous_user_id ON medicines(anonymous_user_id); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_medicines_user_type') THEN CREATE INDEX idx_medicines_user_type ON medicines(user_type); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_prescriptions_anonymous_user_id') THEN CREATE INDEX idx_prescriptions_anonymous_user_id ON prescriptions(anonymous_user_id); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_prescriptions_user_type') THEN CREATE INDEX idx_prescriptions_user_type ON prescriptions(user_type); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_audit_logs_anonymous_user_id') THEN CREATE INDEX idx_audit_logs_anonymous_user_id ON audit_logs(anonymous_user_id); END IF; END $$;

-- 9. Add composite indexes for better query performance (if not exists)
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_medicines_user_type_anonymous_id') THEN CREATE INDEX idx_medicines_user_type_anonymous_id ON medicines(user_type, anonymous_user_id) WHERE anonymous_user_id IS NOT NULL; END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_medicines_user_type_user_id') THEN CREATE INDEX idx_medicines_user_type_user_id ON medicines(user_type, user_id) WHERE user_id IS NOT NULL; END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_prescriptions_user_type_anonymous_id') THEN CREATE INDEX idx_prescriptions_user_type_anonymous_id ON prescriptions(user_type, anonymous_user_id) WHERE anonymous_user_id IS NOT NULL; END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_prescriptions_user_type_patient_id') THEN CREATE INDEX idx_prescriptions_user_type_patient_id ON prescriptions(user_type, patient_id) WHERE patient_id IS NOT NULL; END IF; END $$;

-- 10. Update existing data to set user_type as REGISTERED
UPDATE users SET user_type = 'REGISTERED' WHERE user_type IS NULL;
UPDATE medicines SET user_type = 'REGISTERED' WHERE user_type IS NULL;
UPDATE prescriptions SET user_type = 'REGISTERED' WHERE user_type IS NULL;

-- 11. Add constraints to ensure data integrity (if not exists)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE table_name = 'medicines' AND constraint_name = 'chk_medicines_user_type_consistency') THEN
        ALTER TABLE medicines ADD CONSTRAINT chk_medicines_user_type_consistency 
            CHECK (
                (user_type = 'ANONYMOUS' AND anonymous_user_id IS NOT NULL AND user_id IS NULL) OR
                (user_type = 'REGISTERED' AND user_id IS NOT NULL AND anonymous_user_id IS NULL) OR
                (user_type = 'PREMIUM' AND user_id IS NOT NULL AND anonymous_user_id IS NULL)
            );
    END IF;
END $$;
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE table_name = 'prescriptions' AND constraint_name = 'chk_prescriptions_user_type_consistency') THEN
        ALTER TABLE prescriptions ADD CONSTRAINT chk_prescriptions_user_type_consistency 
            CHECK (
                (user_type = 'ANONYMOUS' AND anonymous_user_id IS NOT NULL AND patient_id IS NULL) OR
                (user_type = 'REGISTERED' AND patient_id IS NOT NULL AND anonymous_user_id IS NULL) OR
                (user_type = 'PREMIUM' AND patient_id IS NOT NULL AND anonymous_user_id IS NULL)
            );
    END IF;
END $$;

-- 12. Add comments for documentation (idempotent)
DO $$ BEGIN
    COMMENT ON TABLE anonymous_sessions IS 'Stores anonymous user sessions for users who use the app without registration. id is BIGSERIAL, anonymous_user_id is UUID and soft-FK to users.anonymous_user_id.';
    COMMENT ON COLUMN anonymous_sessions.id IS 'Primary key, BIGSERIAL for consistency with other tables';
    COMMENT ON COLUMN users.user_type IS 'Type of user: ANONYMOUS, REGISTERED, or PREMIUM';
    COMMENT ON COLUMN users.anonymous_user_id IS 'UUID for anonymous users, NULL for registered users';
    COMMENT ON COLUMN medicines.user_type IS 'Type of user who owns this medicine';
    COMMENT ON COLUMN medicines.anonymous_user_id IS 'Anonymous user ID (UUID), NULL for registered users';
    COMMENT ON COLUMN prescriptions.user_type IS 'Type of user who owns this prescription';
    COMMENT ON COLUMN prescriptions.anonymous_user_id IS 'Anonymous user ID (UUID), NULL for registered users';
    COMMENT ON COLUMN audit_logs.anonymous_user_id IS 'Anonymous user ID (UUID) for audit tracking';
    COMMENT ON COLUMN anonymous_sessions.anonymous_user_id IS 'UUID, soft-FK to users.anonymous_user_id';
    COMMENT ON COLUMN anonymous_sessions.migrated_to_user_id IS 'FK to users.id, ON DELETE SET NULL';
END $$;

-- 13. Create function to clean up expired anonymous sessions (replace if exists)
CREATE OR REPLACE FUNCTION cleanup_expired_anonymous_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM anonymous_sessions 
    WHERE last_activity_at < NOW() - INTERVAL '30 days'
    AND status = 'ACTIVE';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 14. Create function to migrate anonymous data to registered user (replace if exists, update UUID)
CREATE OR REPLACE FUNCTION migrate_anonymous_data(
    p_anonymous_user_id UUID,
    p_registered_user_id BIGINT
)
RETURNS JSON AS $$
DECLARE
    result JSON;
    medicines_migrated INTEGER;
    prescriptions_migrated INTEGER;
BEGIN
    -- Migrate medicines
    UPDATE medicines 
    SET user_type = 'REGISTERED',
        user_id = p_registered_user_id,
        anonymous_user_id = NULL
    WHERE anonymous_user_id = p_anonymous_user_id 
    AND user_type = 'ANONYMOUS';
    
    GET DIAGNOSTICS medicines_migrated = ROW_COUNT;
    
    -- Migrate prescriptions
    UPDATE prescriptions 
    SET user_type = 'REGISTERED',
        patient_id = p_registered_user_id,
        anonymous_user_id = NULL
    WHERE anonymous_user_id = p_anonymous_user_id 
    AND user_type = 'ANONYMOUS';
    
    GET DIAGNOSTICS prescriptions_migrated = ROW_COUNT;
    
    -- Update anonymous session status
    UPDATE anonymous_sessions 
    SET migrated_to_user_id = p_registered_user_id,
        migrated_at = NOW(),
        status = 'MIGRATED'
    WHERE anonymous_user_id = p_anonymous_user_id;
    
    -- Return migration statistics
    result = json_build_object(
        'success', true,
        'medicines_migrated', medicines_migrated,
        'prescriptions_migrated', prescriptions_migrated,
        'migrated_at', NOW()
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 15. Create view for anonymous user statistics (replace if exists)
DO $$ BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'anonymous_user_stats') THEN
        DROP VIEW anonymous_user_stats;
    END IF;
END $$;
CREATE VIEW anonymous_user_stats AS
SELECT 
    COUNT(*) as total_anonymous_sessions,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_sessions,
    COUNT(CASE WHEN status = 'MIGRATED' THEN 1 END) as migrated_sessions,
    COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) as expired_sessions,
    COUNT(CASE WHEN last_activity_at < NOW() - INTERVAL '7 days' THEN 1 END) as inactive_7_days,
    COUNT(CASE WHEN last_activity_at < NOW() - INTERVAL '30 days' THEN 1 END) as inactive_30_days
FROM anonymous_sessions;

-- 16. Add comments for the new functions and view (idempotent)
DO $$ BEGIN
    COMMENT ON FUNCTION cleanup_expired_anonymous_sessions() IS 'Cleans up anonymous sessions that have been inactive for 30 days';
    COMMENT ON FUNCTION migrate_anonymous_data(UUID, BIGINT) IS 'Migrates anonymous user data to a registered user account';
    COMMENT ON VIEW anonymous_user_stats IS 'Provides statistics about anonymous user sessions';
END $$; 