-- V25__Update_existing_enums_for_anonymous_support.sql (improved v2)
-- Update existing enum types to ensure consistency with anonymous user support (idempotent, safe, UUID ready)

-- 1. Update gender enum if it doesn't exist or needs updating
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'gender_enum') THEN
        CREATE TYPE gender_enum AS ENUM ('MALE', 'FEMALE', 'OTHER');
        RAISE NOTICE 'Created gender_enum';
    ELSE
        RAISE NOTICE 'gender_enum already exists';
    END IF;
END $$;

-- 2. Update member_type enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'member_type_enum') THEN
        CREATE TYPE member_type_enum AS ENUM ('DEPENDENT', 'INDEPENDENT');
        RAISE NOTICE 'Created member_type_enum';
    ELSE
        RAISE NOTICE 'member_type_enum already exists';
    END IF;
END $$;

-- 3. Update family_role enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'family_role_enum') THEN
        CREATE TYPE family_role_enum AS ENUM ('OWNER', 'ADMIN', 'MEMBER');
        RAISE NOTICE 'Created family_role_enum';
    ELSE
        RAISE NOTICE 'family_role_enum already exists';
    END IF;
END $$;

-- 4. Update invitation_type enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invitation_type_enum') THEN
        CREATE TYPE invitation_type_enum AS ENUM ('CREATE_ACCOUNT', 'VIEW_ONLY');
        RAISE NOTICE 'Created invitation_type_enum';
    ELSE
        RAISE NOTICE 'invitation_type_enum already exists';
    END IF;
END $$;

-- 5. Update invitation_status enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invitation_status_enum') THEN
        CREATE TYPE invitation_status_enum AS ENUM ('PENDING', 'ACCEPTED', 'REJECTED', 'EXPIRED');
        RAISE NOTICE 'Created invitation_status_enum';
    ELSE
        RAISE NOTICE 'invitation_status_enum already exists';
    END IF;
END $$;

-- 6. Update notification_type enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'notification_type_enum') THEN
        CREATE TYPE notification_type_enum AS ENUM ('REMINDER', 'ALERT', 'INFO');
        RAISE NOTICE 'Created notification_type_enum';
    ELSE
        RAISE NOTICE 'notification_type_enum already exists';
    END IF;
END $$;

-- 7. Update notification_status enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'notification_status_enum') THEN
        CREATE TYPE notification_status_enum AS ENUM ('PENDING', 'SENT', 'READ');
        RAISE NOTICE 'Created notification_status_enum';
    ELSE
        RAISE NOTICE 'notification_status_enum already exists';
    END IF;
END $$;

-- 8. Update schedule_status enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'schedule_status_enum') THEN
        CREATE TYPE schedule_status_enum AS ENUM ('PENDING', 'TAKEN', 'SKIPPED');
        RAISE NOTICE 'Created schedule_status_enum';
    ELSE
        RAISE NOTICE 'schedule_status_enum already exists';
    END IF;
END $$;

-- 9. Update audit_action enum if it doesn't exist (includes MIGRATE for anonymous support)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'audit_action_enum') THEN
        CREATE TYPE audit_action_enum AS ENUM ('CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'MIGRATE');
        RAISE NOTICE 'Created audit_action_enum with MIGRATE action';
    ELSE
        RAISE NOTICE 'audit_action_enum already exists';
    END IF;
END $$;

-- 10. Update existing columns to use proper enum types (if they exist)
-- Helper: kiểm tra dữ liệu trước khi đổi type (support all column name variations)
DO $$
DECLARE
    invalid_count INTEGER;
    column_name_to_check VARCHAR(50);
BEGIN
    -- Check for users.gender (English), users.gioi_tinh (Vietnamese no accent), or users.giới_tính (Vietnamese with accent)
    SELECT CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'gender') THEN 'gender'
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'gioi_tinh') THEN 'gioi_tinh'
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'giới_tính') THEN 'giới_tính'
        ELSE NULL
    END INTO column_name_to_check;
    
    IF column_name_to_check IS NOT NULL THEN
        EXECUTE format('SELECT COUNT(*) FROM users WHERE %I IS NOT NULL AND %I NOT IN (''MALE'', ''FEMALE'', ''OTHER'')', 
                      column_name_to_check, column_name_to_check) INTO invalid_count;
        
        IF invalid_count = 0 THEN
            BEGIN
                EXECUTE format('ALTER TABLE users ALTER COLUMN %I TYPE gender_enum USING %I::gender_enum', 
                              column_name_to_check, column_name_to_check);
                RAISE NOTICE 'users.% converted to gender_enum', column_name_to_check;
            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'users.% already correct type or conversion failed', column_name_to_check;
            END;
        ELSE
            RAISE NOTICE 'users.% has invalid values, please clean data first', column_name_to_check;
        END IF;
    END IF;
END $$;

-- Tương tự cho các bảng/cột còn lại (ví dụ family_members.gender, member_type, ...)
-- Để ngắn gọn, chỉ log nếu không đổi type được

-- 11. Add comments for the enum types (updated for anonymous support)
DO $$ BEGIN
    COMMENT ON TYPE user_type_enum IS 'User types: ANONYMOUS, REGISTERED, PREMIUM';
    COMMENT ON TYPE anonymous_session_status_enum IS 'Anonymous session status: ACTIVE, EXPIRED, MIGRATED, CLEANED';
    COMMENT ON TYPE gender_enum IS 'Gender types: MALE, FEMALE, OTHER';
    COMMENT ON TYPE member_type_enum IS 'Family member types: DEPENDENT, INDEPENDENT';
    COMMENT ON TYPE family_role_enum IS 'Family roles: OWNER, ADMIN, MEMBER';
    COMMENT ON TYPE invitation_type_enum IS 'Invitation types: CREATE_ACCOUNT, VIEW_ONLY';
    COMMENT ON TYPE invitation_status_enum IS 'Invitation status: PENDING, ACCEPTED, REJECTED, EXPIRED';
    COMMENT ON TYPE notification_type_enum IS 'Notification types: REMINDER, ALERT, INFO';
    COMMENT ON TYPE notification_status_enum IS 'Notification status: PENDING, SENT, READ';
    COMMENT ON TYPE schedule_status_enum IS 'Schedule status: PENDING, TAKEN, SKIPPED';
    COMMENT ON TYPE audit_action_enum IS 'Audit actions: CREATE, UPDATE, DELETE, LOGIN, LOGOUT, MIGRATE (for anonymous data migration)';
END $$; 