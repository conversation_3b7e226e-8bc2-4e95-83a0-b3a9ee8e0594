-- V26__Rename_vietnamese_columns_to_english.sql
-- Rename Vietnamese column names to English for better portability and JPA compatibility

-- 1. Rename users.giới_tính to users.gioi_tinh
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'giới_tính') THEN
        ALTER TABLE users RENAME COLUMN giới_tính TO gioi_tinh;
        RAISE NOTICE 'Renamed users.giới_tính to users.gioi_tinh';
    ELSE
        RAISE NOTICE 'users.giới_tính column not found or already renamed';
    END IF;
END $$;

-- 2. Rename family_members.giới_tính to family_members.gioi_tinh
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'family_members' AND column_name = 'giới_tính') THEN
        ALTER TABLE family_members RENAME COLUMN giới_tính TO gioi_tinh;
        RAISE NOTICE 'Renamed family_members.giới_tính to family_members.gioi_tinh';
    ELSE
        RAISE NOTICE 'family_members.giới_tính column not found or already renamed';
    END IF;
END $$;

-- 3. Rename family_invitations.trạng_thái to family_invitations.trang_thai
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'family_invitations' AND column_name = 'trạng_thái') THEN
        ALTER TABLE family_invitations RENAME COLUMN trạng_thái TO trang_thai;
        RAISE NOTICE 'Renamed family_invitations.trạng_thái to family_invitations.trang_thai';
    ELSE
        RAISE NOTICE 'family_invitations.trạng_thái column not found or already renamed';
    END IF;
END $$;

-- 4. Rename notifications.loại_thông_báo to notifications.loai_thong_bao
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'loại_thông_báo') THEN
        ALTER TABLE notifications RENAME COLUMN loại_thông_báo TO loai_thong_bao;
        RAISE NOTICE 'Renamed notifications.loại_thông_báo to notifications.loai_thong_bao';
    ELSE
        RAISE NOTICE 'notifications.loại_thông_báo column not found or already renamed';
    END IF;
END $$;

-- 5. Rename notifications.trạng_thái to notifications.trang_thai
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'trạng_thái') THEN
        ALTER TABLE notifications RENAME COLUMN trạng_thái TO trang_thai;
        RAISE NOTICE 'Renamed notifications.trạng_thái to notifications.trang_thai';
    ELSE
        RAISE NOTICE 'notifications.trạng_thái column not found or already renamed';
    END IF;
END $$;

-- 6. Rename medication_schedules.trạng_thái to medication_schedules.trang_thai
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medication_schedules' AND column_name = 'trạng_thái') THEN
        ALTER TABLE medication_schedules RENAME COLUMN trạng_thái TO trang_thai;
        RAISE NOTICE 'Renamed medication_schedules.trạng_thái to medication_schedules.trang_thai';
    ELSE
        RAISE NOTICE 'medication_schedules.trạng_thái column not found or already renamed';
    END IF;
END $$;

-- 7. Update any existing indexes that reference old column names
-- Note: PostgreSQL automatically updates indexes when columns are renamed
-- But we should verify and recreate if needed

-- 8. Add comments for the renamed columns (only if they exist)
DO $$ 
BEGIN
    -- Check and comment users.gioi_tinh
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'gioi_tinh') THEN
        COMMENT ON COLUMN users.gioi_tinh IS 'Gender: MALE, FEMALE, OTHER (renamed from giới_tính)';
    END IF;
    
    -- Check and comment family_members.gioi_tinh
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'family_members' AND column_name = 'gioi_tinh') THEN
        COMMENT ON COLUMN family_members.gioi_tinh IS 'Gender: MALE, FEMALE, OTHER (renamed from giới_tính)';
    END IF;
    
    -- Check and comment family_invitations.trang_thai
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'family_invitations' AND column_name = 'trang_thai') THEN
        COMMENT ON COLUMN family_invitations.trang_thai IS 'Invitation status: PENDING, ACCEPTED, REJECTED, EXPIRED (renamed from trạng_thái)';
    END IF;
    
    -- Check and comment notifications.loai_thong_bao
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'loai_thong_bao') THEN
        COMMENT ON COLUMN notifications.loai_thong_bao IS 'Notification type: REMINDER, ALERT, INFO (renamed from loại_thông_báo)';
    END IF;
    
    -- Check and comment notifications.trang_thai
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'trang_thai') THEN
        COMMENT ON COLUMN notifications.trang_thai IS 'Notification status: PENDING, SENT, READ (renamed from trạng_thái)';
    END IF;
    
    -- Check and comment medication_schedules.trang_thai
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medication_schedules' AND column_name = 'trang_thai') THEN
        COMMENT ON COLUMN medication_schedules.trang_thai IS 'Schedule status: PENDING, TAKEN, SKIPPED (renamed from trạng_thái)';
    END IF;
END $$;

-- 9. Update V25 migration script reference (if it still exists)
-- The V25 script checks for 'giới_tính' but now it should be 'gioi_tinh'
-- This is handled by the idempotent nature of V25

-- 10. Verify the changes
DO $$ 
DECLARE
    column_count INTEGER;
BEGIN
    -- Check if any Vietnamese columns still exist
    SELECT COUNT(*) INTO column_count 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND column_name LIKE '%_%' 
    AND column_name ~ '[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]';
    
    IF column_count = 0 THEN
        RAISE NOTICE 'All Vietnamese column names have been successfully renamed to English';
    ELSE
        RAISE NOTICE 'Found % columns with Vietnamese characters that may need attention', column_count;
    END IF;
END $$; 