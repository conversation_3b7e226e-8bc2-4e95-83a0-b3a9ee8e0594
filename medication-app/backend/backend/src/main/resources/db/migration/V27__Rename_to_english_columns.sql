-- V27__Rename_to_english_columns.sql
-- Rename Vietnamese columns to proper English names for international standards

-- 1. Rename users.gioi_tinh to users.gender
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'gioi_tinh') THEN
        ALTER TABLE users RENAME COLUMN gioi_tinh TO gender;
        RAISE NOTICE 'Renamed users.gioi_tinh to users.gender';
    ELSE
        RAISE NOTICE 'users.gioi_tinh column not found or already renamed';
    END IF;
END $$;

-- 2. Rename family_members.gioi_tinh to family_members.gender
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'family_members' AND column_name = 'gioi_tinh') THEN
        ALTER TABLE family_members RENAME COLUMN gioi_tinh TO gender;
        RAISE NOTICE 'Renamed family_members.gioi_tinh to family_members.gender';
    ELSE
        RAISE NOTICE 'family_members.gioi_tinh column not found or already renamed';
    END IF;
END $$;

-- 3. Rename family_invitations.trang_thai to family_invitations.status
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'family_invitations' AND column_name = 'trang_thai') THEN
        ALTER TABLE family_invitations RENAME COLUMN trang_thai TO status;
        RAISE NOTICE 'Renamed family_invitations.trang_thai to family_invitations.status';
    ELSE
        RAISE NOTICE 'family_invitations.trang_thai column not found or already renamed';
    END IF;
END $$;

-- 4. Rename notifications.loai_thong_bao to notifications.notification_type
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'loai_thong_bao') THEN
        ALTER TABLE notifications RENAME COLUMN loai_thong_bao TO notification_type;
        RAISE NOTICE 'Renamed notifications.loai_thong_bao to notifications.notification_type';
    ELSE
        RAISE NOTICE 'notifications.loai_thong_bao column not found or already renamed';
    END IF;
END $$;

-- 5. Rename notifications.trang_thai to notifications.status
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'trang_thai') THEN
        ALTER TABLE notifications RENAME COLUMN trang_thai TO status;
        RAISE NOTICE 'Renamed notifications.trang_thai to notifications.status';
    ELSE
        RAISE NOTICE 'notifications.trang_thai column not found or already renamed';
    END IF;
END $$;

-- 6. Rename medication_schedules.trang_thai to medication_schedules.status
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medication_schedules' AND column_name = 'trang_thai') THEN
        ALTER TABLE medication_schedules RENAME COLUMN trang_thai TO status;
        RAISE NOTICE 'Renamed medication_schedules.trang_thai to medication_schedules.status';
    ELSE
        RAISE NOTICE 'medication_schedules.trang_thai column not found or already renamed';
    END IF;
END $$;

-- 7. Add comments for the renamed columns (only if they exist)
DO $$ 
BEGIN
    -- Check and comment users.gender
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'gender') THEN
        COMMENT ON COLUMN users.gender IS 'Gender: MALE, FEMALE, OTHER (renamed from gioi_tinh)';
    END IF;
    
    -- Check and comment family_members.gender
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'family_members' AND column_name = 'gender') THEN
        COMMENT ON COLUMN family_members.gender IS 'Gender: MALE, FEMALE, OTHER (renamed from gioi_tinh)';
    END IF;
    
    -- Check and comment family_invitations.status
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'family_invitations' AND column_name = 'status') THEN
        COMMENT ON COLUMN family_invitations.status IS 'Invitation status: PENDING, ACCEPTED, REJECTED, EXPIRED (renamed from trang_thai)';
    END IF;
    
    -- Check and comment notifications.notification_type
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'notification_type') THEN
        COMMENT ON COLUMN notifications.notification_type IS 'Notification type: REMINDER, ALERT, INFO (renamed from loai_thong_bao)';
    END IF;
    
    -- Check and comment notifications.status
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'status') THEN
        COMMENT ON COLUMN notifications.status IS 'Notification status: PENDING, SENT, READ (renamed from trang_thai)';
    END IF;
    
    -- Check and comment medication_schedules.status
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medication_schedules' AND column_name = 'status') THEN
        COMMENT ON COLUMN medication_schedules.status IS 'Schedule status: PENDING, TAKEN, SKIPPED (renamed from trang_thai)';
    END IF;
END $$;

-- 8. Update V25 migration script to check for new English column names
-- This ensures V25 can handle both old Vietnamese and new English column names

-- 9. Verify the changes
DO $$ 
DECLARE
    column_count INTEGER;
BEGIN
    -- Check if any Vietnamese-style columns still exist
    SELECT COUNT(*) INTO column_count 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND column_name IN ('gioi_tinh', 'trang_thai', 'loai_thong_bao');
    
    IF column_count = 0 THEN
        RAISE NOTICE 'All columns have been successfully renamed to proper English names';
    ELSE
        RAISE NOTICE 'Found % columns that may still need attention', column_count;
    END IF;
END $$; 