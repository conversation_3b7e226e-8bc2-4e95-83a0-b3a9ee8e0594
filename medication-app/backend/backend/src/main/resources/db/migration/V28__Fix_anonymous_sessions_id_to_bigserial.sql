-- V28__Fix_anonymous_sessions_id_to_bigserial.sql
-- Fix anonymous_sessions.id from UUID to BIGSERIAL for consistency with other tables
-- This migration handles cases where V24 was already applied with UUID

-- 1. Check if anonymous_sessions exists and id column is UUID type
DO $$ 
DECLARE
    id_data_type TEXT;
    record_count INTEGER;
BEGIN
    -- Get current data type of id column
    SELECT data_type INTO id_data_type
    FROM information_schema.columns 
    WHERE table_name = 'anonymous_sessions' 
    AND column_name = 'id' 
    AND table_schema = 'public';
    
    IF id_data_type IS NOT NULL THEN
        RAISE NOTICE 'Found anonymous_sessions.id with type: %', id_data_type;
        
        -- Check if it's UUID and needs conversion
        IF id_data_type = 'uuid' THEN
            RAISE NOTICE 'Converting anonymous_sessions.id from UUID to BIGSERIAL...';
            
            -- Check if table has data
            SELECT COUNT(*) INTO record_count FROM anonymous_sessions;
            RAISE NOTICE 'Table has % records', record_count;
            
            -- If table has data, we need to handle it carefully
            IF record_count > 0 THEN
                RAISE NOTICE 'Table has data, performing safe migration...';
                
                -- Step 0: Drop dependent view first
                DROP VIEW IF EXISTS anonymous_user_stats;
                
                -- Step 1: Create temporary table with new structure
                CREATE TABLE anonymous_sessions_temp (
                    id BIGSERIAL PRIMARY KEY,
                    anonymous_user_id UUID NOT NULL UNIQUE,
                    device_id VARCHAR(100),
                    app_version VARCHAR(20),
                    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    last_activity_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    data_synced BOOLEAN DEFAULT FALSE,
                    migrated_to_user_id BIGINT,
                    migrated_at TIMESTAMP,
                    status anonymous_session_status_enum DEFAULT 'ACTIVE',
                    old_uuid_id UUID  -- Store old UUID for reference
                );
                
                -- Step 2: Copy data (excluding id, let BIGSERIAL generate new ones)
                INSERT INTO anonymous_sessions_temp (
                    anonymous_user_id, device_id, app_version, created_at, 
                    last_activity_at, data_synced, migrated_to_user_id, 
                    migrated_at, status, old_uuid_id
                )
                SELECT 
                    anonymous_user_id, device_id, app_version, created_at,
                    last_activity_at, data_synced, migrated_to_user_id,
                    migrated_at, status, id
                FROM anonymous_sessions;
                
                -- Step 3: Drop old table
                DROP TABLE anonymous_sessions;
                
                -- Step 4: Rename temp table
                ALTER TABLE anonymous_sessions_temp RENAME TO anonymous_sessions;
                
                -- Step 5: Drop the temporary old_uuid_id column
                ALTER TABLE anonymous_sessions DROP COLUMN old_uuid_id;
                
                -- Step 6: Recreate foreign key constraints
                ALTER TABLE anonymous_sessions 
                ADD CONSTRAINT fk_anonymous_user_id_soft 
                FOREIGN KEY (anonymous_user_id) REFERENCES users(anonymous_user_id) ON DELETE SET NULL;
                
                ALTER TABLE anonymous_sessions 
                ADD CONSTRAINT fk_migrated_to_user_id 
                FOREIGN KEY (migrated_to_user_id) REFERENCES users(id) ON DELETE SET NULL;
                
                -- Step 7: Recreate the view
                CREATE VIEW anonymous_user_stats AS
                SELECT 
                    COUNT(*) as total_anonymous_sessions,
                    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_sessions,
                    COUNT(CASE WHEN status = 'MIGRATED' THEN 1 END) as migrated_sessions,
                    COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) as expired_sessions,
                    COUNT(CASE WHEN last_activity_at < NOW() - INTERVAL '7 days' THEN 1 END) as inactive_7_days,
                    COUNT(CASE WHEN last_activity_at < NOW() - INTERVAL '30 days' THEN 1 END) as inactive_30_days
                FROM anonymous_sessions;
                
                RAISE NOTICE 'Successfully migrated % records to new structure', record_count;
                
            ELSE
                RAISE NOTICE 'Table is empty, performing simple structure change...';
                
                -- Drop dependent view first
                DROP VIEW IF EXISTS anonymous_user_stats;
                
                -- Drop and recreate empty table with correct structure
                DROP TABLE anonymous_sessions;
                
                CREATE TABLE anonymous_sessions (
                    id BIGSERIAL PRIMARY KEY,
                    anonymous_user_id UUID NOT NULL UNIQUE,
                    device_id VARCHAR(100),
                    app_version VARCHAR(20),
                    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    last_activity_at TIMESTAMP NOT NULL DEFAULT NOW(),
                    data_synced BOOLEAN DEFAULT FALSE,
                    migrated_to_user_id BIGINT,
                    migrated_at TIMESTAMP,
                    status anonymous_session_status_enum DEFAULT 'ACTIVE',
                    CONSTRAINT fk_anonymous_user_id_soft FOREIGN KEY (anonymous_user_id) REFERENCES users(anonymous_user_id) ON DELETE SET NULL,
                    CONSTRAINT fk_migrated_to_user_id FOREIGN KEY (migrated_to_user_id) REFERENCES users(id) ON DELETE SET NULL
                );
                
                -- Recreate the view
                CREATE VIEW anonymous_user_stats AS
                SELECT 
                    COUNT(*) as total_anonymous_sessions,
                    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_sessions,
                    COUNT(CASE WHEN status = 'MIGRATED' THEN 1 END) as migrated_sessions,
                    COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) as expired_sessions,
                    COUNT(CASE WHEN last_activity_at < NOW() - INTERVAL '7 days' THEN 1 END) as inactive_7_days,
                    COUNT(CASE WHEN last_activity_at < NOW() - INTERVAL '30 days' THEN 1 END) as inactive_30_days
                FROM anonymous_sessions;
                
                RAISE NOTICE 'Successfully recreated empty table with BIGSERIAL id';
            END IF;
            
        ELSIF id_data_type = 'bigint' THEN
            RAISE NOTICE 'anonymous_sessions.id is already BIGINT, no changes needed';
            
        ELSE
            RAISE NOTICE 'Unexpected data type for anonymous_sessions.id: %, manual intervention may be needed', id_data_type;
        END IF;
        
    ELSE
        RAISE NOTICE 'anonymous_sessions table or id column not found, no changes needed';
    END IF;
END $$;

-- 2. Recreate indexes if they were dropped during migration
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_anonymous_sessions_user_id') THEN CREATE INDEX idx_anonymous_sessions_user_id ON anonymous_sessions(anonymous_user_id); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_anonymous_sessions_last_activity') THEN CREATE INDEX idx_anonymous_sessions_last_activity ON anonymous_sessions(last_activity_at); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_anonymous_sessions_migrated') THEN CREATE INDEX idx_anonymous_sessions_migrated ON anonymous_sessions(migrated_to_user_id); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'idx_anonymous_sessions_status') THEN CREATE INDEX idx_anonymous_sessions_status ON anonymous_sessions(status); END IF; END $$;

-- 3. Update comments to reflect BIGSERIAL
DO $$ BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'anonymous_sessions') THEN
        COMMENT ON TABLE anonymous_sessions IS 'Stores anonymous user sessions for users who use the app without registration. id is BIGSERIAL, anonymous_user_id is UUID and soft-FK to users.anonymous_user_id.';
        COMMENT ON COLUMN anonymous_sessions.id IS 'Primary key, BIGSERIAL for consistency with other tables';
        COMMENT ON COLUMN anonymous_sessions.anonymous_user_id IS 'UUID, soft-FK to users.anonymous_user_id';
        COMMENT ON COLUMN anonymous_sessions.migrated_to_user_id IS 'FK to users.id, ON DELETE SET NULL';
    END IF;
END $$;

-- 4. Verify the final structure
DO $$ 
DECLARE
    id_data_type TEXT;
    constraint_count INTEGER;
BEGIN
    SELECT data_type INTO id_data_type
    FROM information_schema.columns 
    WHERE table_name = 'anonymous_sessions' 
    AND column_name = 'id' 
    AND table_schema = 'public';
    
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.table_constraints 
    WHERE table_name = 'anonymous_sessions' 
    AND constraint_type = 'FOREIGN KEY';
    
    RAISE NOTICE 'Final verification: id type = %, foreign key constraints = %', id_data_type, constraint_count;
    
    IF id_data_type = 'bigint' AND constraint_count >= 2 THEN
        RAISE NOTICE '✅ Migration V28 completed successfully';
    ELSE
        RAISE NOTICE '⚠️ Migration may need manual verification';
    END IF;
END $$; 