-- V29__Update_anonymous_user_id_to_uuid_type.sql
-- Update anonymous_user_id columns to use proper UUID type instead of VARCHAR

-- 1. Update anonymous_sessions.anonymous_user_id to UUID type
DO $$ BEGIN
    -- Check if column exists and is not already UUID type
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'anonymous_sessions' 
        AND column_name = 'anonymous_user_id'
        AND data_type != 'uuid'
    ) THEN
        -- Convert VARCHAR to UUID
        ALTER TABLE anonymous_sessions 
        ALTER COLUMN anonymous_user_id TYPE UUID USING anonymous_user_id::UUID;
    END IF;
END $$;

-- 2. Update users.anonymous_user_id to UUID type
DO $$ BEGIN
    -- Check if column exists and is not already UUID type
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'anonymous_user_id'
        AND data_type != 'uuid'
    ) THEN
        -- Convert VARCHAR to UUID
        ALTER TABLE users 
        ALTER COLUMN anonymous_user_id TYPE UUID USING anonymous_user_id::UUID;
    END IF;
END $$;

-- 3. Update medicines.anonymous_user_id to UUID type
DO $$ BEGIN
    -- Check if column exists and is not already UUID type
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'medicines' 
        AND column_name = 'anonymous_user_id'
        AND data_type != 'uuid'
    ) THEN
        -- Convert VARCHAR to UUID
        ALTER TABLE medicines 
        ALTER COLUMN anonymous_user_id TYPE UUID USING anonymous_user_id::UUID;
    END IF;
END $$;

-- 4. Update prescriptions.anonymous_user_id to UUID type
DO $$ BEGIN
    -- Check if column exists and is not already UUID type
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'prescriptions' 
        AND column_name = 'anonymous_user_id'
        AND data_type != 'uuid'
    ) THEN
        -- Convert VARCHAR to UUID
        ALTER TABLE prescriptions 
        ALTER COLUMN anonymous_user_id TYPE UUID USING anonymous_user_id::UUID;
    END IF;
END $$;

-- 5. Update audit_logs.anonymous_user_id to UUID type
DO $$ BEGIN
    -- Check if column exists and is not already UUID type
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'audit_logs' 
        AND column_name = 'anonymous_user_id'
        AND data_type != 'uuid'
    ) THEN
        -- Convert VARCHAR to UUID
        ALTER TABLE audit_logs 
        ALTER COLUMN anonymous_user_id TYPE UUID USING anonymous_user_id::UUID;
    END IF;
END $$;

-- 6. Add comments to document the UUID type
COMMENT ON COLUMN anonymous_sessions.anonymous_user_id IS 'UUID type for anonymous user identification';
COMMENT ON COLUMN users.anonymous_user_id IS 'UUID type for anonymous user identification';
COMMENT ON COLUMN medicines.anonymous_user_id IS 'UUID type for anonymous user identification';
COMMENT ON COLUMN prescriptions.anonymous_user_id IS 'UUID type for anonymous user identification';
COMMENT ON COLUMN audit_logs.anonymous_user_id IS 'UUID type for anonymous user identification'; 