-- Migration V2: Add new entities (Family, FamilyMember, Notification, AuditLog)
-- Created: December 2024

-- Create families table
CREATE TABLE families (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create family_members table
CREATE TABLE family_members (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    family_id BIGINT NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'MEMBER',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (family_id) REFERENCES families(id) ON DELETE CASCADE,
    UNIQUE(user_id, family_id)
);

-- Create notifications table
CREATE TABLE notifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message VARCHAR(1000) NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'INFO',
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    scheduled_at TIMESTAMP,
    sent_at TIMESTAMP,
    read_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create audit_logs table
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(100) NOT NULL,
    entity_id BIGINT,
    description VARCHAR(1000),
    old_values TEXT,
    new_values TEXT,
    severity VARCHAR(50) NOT NULL DEFAULT 'INFO',
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Add new columns to users table
ALTER TABLE users ADD COLUMN phone_number VARCHAR(20);
ALTER TABLE users ADD COLUMN role VARCHAR(50) NOT NULL DEFAULT 'USER';
ALTER TABLE users ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE users ADD COLUMN email_verified BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP;

-- Add new columns to prescriptions table
ALTER TABLE prescriptions ADD COLUMN start_date DATE NOT NULL DEFAULT CURRENT_DATE;
ALTER TABLE prescriptions ADD COLUMN end_date DATE NOT NULL DEFAULT CURRENT_DATE + INTERVAL '30 days';
ALTER TABLE prescriptions ADD COLUMN diagnosis VARCHAR(500) NOT NULL DEFAULT 'General diagnosis';
ALTER TABLE prescriptions ADD COLUMN patient_id BIGINT NOT NULL DEFAULT 1;
ALTER TABLE prescriptions ADD COLUMN family_id BIGINT NULL;
ALTER TABLE prescriptions ADD COLUMN status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE';
ALTER TABLE prescriptions ADD COLUMN compliance_rate DECIMAL(5,2) DEFAULT 0.0;
ALTER TABLE prescriptions ADD COLUMN total_doses INTEGER DEFAULT 0;
ALTER TABLE prescriptions ADD COLUMN taken_doses INTEGER DEFAULT 0;
ALTER TABLE prescriptions ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT TRUE;

-- Add foreign key constraints for prescriptions
ALTER TABLE prescriptions ADD CONSTRAINT fk_prescriptions_patient 
    FOREIGN KEY (patient_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE prescriptions ADD CONSTRAINT fk_prescriptions_family 
    FOREIGN KEY (family_id) REFERENCES families(id) ON DELETE SET NULL;

-- Add new columns to medicines table
ALTER TABLE medicines ADD COLUMN family_id BIGINT NULL;
ALTER TABLE medicines ADD COLUMN price DECIMAL(10,2);
ALTER TABLE medicines ADD COLUMN manufacturer VARCHAR(200);
ALTER TABLE medicines ADD COLUMN active_ingredient VARCHAR(500);
ALTER TABLE medicines ADD COLUMN expiry_date DATE;
ALTER TABLE medicines ADD COLUMN batch_number VARCHAR(100);
ALTER TABLE medicines ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE medicines ADD CONSTRAINT fk_medicines_family 
    FOREIGN KEY (family_id) REFERENCES families(id) ON DELETE SET NULL;

-- Create prescription_medicines junction table
CREATE TABLE prescription_medicines (
    prescription_id BIGINT NOT NULL,
    medicine_id BIGINT NOT NULL,
    PRIMARY KEY (prescription_id, medicine_id),
    FOREIGN KEY (prescription_id) REFERENCES prescriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (medicine_id) REFERENCES medicines(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_families_name ON families(name);
CREATE INDEX idx_families_is_active ON families(is_active);
CREATE INDEX idx_family_members_user_id ON family_members(user_id);
CREATE INDEX idx_family_members_family_id ON family_members(family_id);
CREATE INDEX idx_family_members_role ON family_members(role);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_scheduled_at ON notifications(scheduled_at);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_entity_type ON audit_logs(entity_type);
CREATE INDEX idx_audit_logs_entity_id ON audit_logs(entity_id);
CREATE INDEX idx_audit_logs_severity ON audit_logs(severity);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_users_phone_number ON users(phone_number);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_prescriptions_patient_id ON prescriptions(patient_id);
CREATE INDEX idx_prescriptions_family_id ON prescriptions(family_id);
CREATE INDEX idx_prescriptions_status ON prescriptions(status);
CREATE INDEX idx_prescriptions_start_date ON prescriptions(start_date);
CREATE INDEX idx_prescriptions_end_date ON prescriptions(end_date);
CREATE INDEX idx_medicines_family_id ON medicines(family_id);
CREATE INDEX idx_medicines_expiry_date ON medicines(expiry_date);
CREATE INDEX idx_medicines_is_active ON medicines(is_active);
CREATE INDEX idx_prescription_medicines_prescription_id ON prescription_medicines(prescription_id);
CREATE INDEX idx_prescription_medicines_medicine_id ON prescription_medicines(medicine_id); 