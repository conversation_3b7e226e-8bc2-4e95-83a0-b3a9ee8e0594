-- Migration V3: Improve database integrity and add missing constraints
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Normalize medicine units, improve foreign key constraints, add data integrity constraints, create triggers for updated_at

-- 1. Tạo bảng medicine_units để chuẩn hóa đơn vị thuốc
CREATE TABLE medicine_units (
    id BIGSERIAL PRIMARY KEY,
    unit_name VARCHAR(50) UNIQUE NOT NULL,
    description VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Thêm cột unit_id vào medicines và cập nhật foreign key
ALTER TABLE medicines ADD COLUMN unit_id BIGINT REFERENCES medicine_units(id) ON DELETE SET NULL;

-- 3. <PERSON><PERSON>i thiện foreign key constraints cho V1 tables (nếu chưa có ON DELETE)
-- <PERSON><PERSON><PERSON> tra và cập nhật medicine_type_id constraint
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'medicines_medicine_type_id_fkey'
    ) THEN
        ALTER TABLE medicines 
        DROP CONSTRAINT medicines_medicine_type_id_fkey;
    END IF;
END $$;

ALTER TABLE medicines 
ADD CONSTRAINT medicines_medicine_type_id_fkey 
FOREIGN KEY (medicine_type_id) REFERENCES medicine_types(id) ON DELETE SET NULL;

-- 4. Cải thiện foreign key constraints cho V2 tables
-- Cập nhật user_id constraints nếu chưa có ON DELETE CASCADE
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'medicines_user_id_fkey'
    ) THEN
        ALTER TABLE medicines 
        DROP CONSTRAINT medicines_user_id_fkey;
    END IF;
END $$;

ALTER TABLE medicines 
ADD CONSTRAINT medicines_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 5. Tạo trigger function cho updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ language 'plpgsql';

-- 6. Tạo triggers cho tất cả bảng có updated_at bằng loop (cải tiến)
DO $$
DECLARE
  tbl TEXT;
BEGIN
  FOREACH tbl IN ARRAY ARRAY['users', 'medicine_types', 'medicines', 'prescriptions', 'families', 'family_members', 'notifications']
  LOOP
    EXECUTE format('CREATE TRIGGER trg_%s_updated_at BEFORE UPDATE ON %I FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();', tbl, tbl);
  END LOOP;
END;
$$;

-- 7. Thêm bảng prescription_items để thay thế prescription_medicines (thiết kế tốt hơn)
CREATE TABLE prescription_items (
    id BIGSERIAL PRIMARY KEY,
    prescription_id BIGINT NOT NULL,
    medicine_id BIGINT,
    dosage VARCHAR(100),
    quantity INTEGER NOT NULL DEFAULT 1,
    instructions TEXT,
    frequency VARCHAR(100), -- 'daily', 'twice_daily', 'as_needed'
    duration_days INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (prescription_id) REFERENCES prescriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (medicine_id) REFERENCES medicines(id) ON DELETE SET NULL
);

-- 8. Thêm index cho prescription_items
CREATE INDEX idx_prescription_items_prescription_id ON prescription_items(prescription_id);
CREATE INDEX idx_prescription_items_medicine_id ON prescription_items(medicine_id);

-- 9. Thêm trigger cho prescription_items
CREATE TRIGGER trg_prescription_items_updated_at
    BEFORE UPDATE ON prescription_items
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 10. Insert default medicine units
INSERT INTO medicine_units (unit_name, description) VALUES 
    ('viên', 'Viên thuốc'),
    ('ml', 'Mililit'),
    ('ống', 'Ống tiêm'),
    ('gói', 'Gói thuốc'),
    ('chai', 'Chai thuốc'),
    ('lọ', 'Lọ thuốc'),
    ('mg', 'Miligam'),
    ('g', 'Gam'),
    ('mcg', 'Microgam'),
    ('IU', 'Đơn vị quốc tế');

-- 11. Thêm constraints để đảm bảo data integrity (tên constraint cải tiến)
-- Đảm bảo quantity không âm
ALTER TABLE medicines ADD CONSTRAINT medicines_quantity_positive_check 
    CHECK (quantity >= 0);

ALTER TABLE prescription_items ADD CONSTRAINT prescription_items_quantity_positive_check 
    CHECK (quantity > 0);

-- Đảm bảo compliance_rate trong khoảng 0-100
ALTER TABLE prescriptions ADD CONSTRAINT prescriptions_compliance_rate_check 
    CHECK (compliance_rate >= 0 AND compliance_rate <= 100);

-- Đảm bảo taken_doses không vượt quá total_doses
ALTER TABLE prescriptions ADD CONSTRAINT prescriptions_doses_check 
    CHECK (taken_doses <= total_doses);

-- 12. Thêm unique constraints
-- Đảm bảo mỗi user chỉ có một role trong một family
-- (Đã có trong V2: UNIQUE(user_id, family_id))

-- 13. Cải thiện tên cột (optional - có thể comment lại nếu muốn giữ tên cũ)
-- ALTER TABLE prescriptions RENAME COLUMN prescription_date TO issued_at;
-- ALTER TABLE prescriptions RENAME COLUMN doctor_name TO prescribed_by;

-- 14. Thêm index cho medicine_units
CREATE INDEX idx_medicine_units_unit_name ON medicine_units(unit_name);

-- 15. Thêm index cho medicines với unit_id
CREATE INDEX idx_medicines_unit_id ON medicines(unit_id);

-- 16. Thêm constraints cho email format (tên constraint cải tiến)
ALTER TABLE users ADD CONSTRAINT users_email_format_check 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- 17. Thêm constraints cho phone_number format (nếu có)
ALTER TABLE users ADD CONSTRAINT users_phone_format_check 
    CHECK (phone_number IS NULL OR phone_number ~* '^[0-9+\-\s()]+$');

-- 18. Thêm constraints cho expiry_date
ALTER TABLE medicines ADD CONSTRAINT medicines_expiry_date_check 
    CHECK (expiry_date IS NULL OR expiry_date >= CURRENT_DATE);

-- 19. Thêm constraints cho start_date và end_date
ALTER TABLE prescriptions ADD CONSTRAINT prescriptions_date_range_check 
    CHECK (start_date <= end_date);

-- 20. Thêm constraints cho frequency trong prescription_items
ALTER TABLE prescription_items ADD CONSTRAINT prescription_items_frequency_check 
    CHECK (frequency IS NULL OR frequency IN ('daily', 'twice_daily', 'three_times_daily', 'as_needed', 'weekly', 'monthly'));

-- 21. Thêm constraints cho severity trong audit_logs
ALTER TABLE audit_logs ADD CONSTRAINT audit_logs_severity_check 
    CHECK (severity IN ('INFO', 'WARNING', 'ERROR', 'CRITICAL'));

-- 22. Thêm constraints cho type trong notifications
ALTER TABLE notifications ADD CONSTRAINT notifications_type_check 
    CHECK (type IN ('INFO', 'WARNING', 'ERROR', 'REMINDER', 'ALERT'));

-- 23. Thêm constraints cho status trong notifications
ALTER TABLE notifications ADD CONSTRAINT notifications_status_check 
    CHECK (status IN ('PENDING', 'SENT', 'READ', 'FAILED'));

-- 24. Thêm constraints cho role trong family_members
ALTER TABLE family_members ADD CONSTRAINT family_members_role_check 
    CHECK (role IN ('ADMIN', 'MEMBER', 'VIEWER'));

-- 25. Thêm constraints cho role trong users
ALTER TABLE users ADD CONSTRAINT users_role_check 
    CHECK (role IN ('ADMIN', 'USER', 'DOCTOR', 'PHARMACIST')); 