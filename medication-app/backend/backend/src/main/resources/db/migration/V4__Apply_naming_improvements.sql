-- Migration V4: Apply naming improvements to existing constraints
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Rename existing constraints to follow improved naming convention

-- Rename existing constraints to follow the improved naming convention
-- This makes constraints easier to identify and debug

-- Rename constraints for medicines table
DO $$
BEGIN
    -- medicines_quantity_positive_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_medicines_quantity_positive') THEN
        ALTER TABLE medicines RENAME CONSTRAINT chk_medicines_quantity_positive TO medicines_quantity_positive_check;
    END IF;
    
    -- medicines_expiry_date_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_medicines_expiry_date') THEN
        ALTER TABLE medicines RENAME CONSTRAINT chk_medicines_expiry_date TO medicines_expiry_date_check;
    END IF;
END $$;

-- Rename constraints for prescription_items table
DO $$
BEGIN
    -- prescription_items_quantity_positive_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_prescription_items_quantity_positive') THEN
        ALTER TABLE prescription_items RENAME CONSTRAINT chk_prescription_items_quantity_positive TO prescription_items_quantity_positive_check;
    END IF;
    
    -- prescription_items_frequency_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_prescription_items_frequency') THEN
        ALTER TABLE prescription_items RENAME CONSTRAINT chk_prescription_items_frequency TO prescription_items_frequency_check;
    END IF;
END $$;

-- Rename constraints for prescriptions table
DO $$
BEGIN
    -- prescriptions_compliance_rate_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_prescriptions_compliance_rate') THEN
        ALTER TABLE prescriptions RENAME CONSTRAINT chk_prescriptions_compliance_rate TO prescriptions_compliance_rate_check;
    END IF;
    
    -- prescriptions_date_range_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_prescriptions_date_range') THEN
        ALTER TABLE prescriptions RENAME CONSTRAINT chk_prescriptions_date_range TO prescriptions_date_range_check;
    END IF;
    
    -- prescriptions_doses_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_prescriptions_doses') THEN
        ALTER TABLE prescriptions RENAME CONSTRAINT chk_prescriptions_doses TO prescriptions_doses_check;
    END IF;
END $$;

-- Rename constraints for users table
DO $$
BEGIN
    -- users_email_format_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_users_email_format') THEN
        ALTER TABLE users RENAME CONSTRAINT chk_users_email_format TO users_email_format_check;
    END IF;
    
    -- users_phone_format_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_users_phone_format') THEN
        ALTER TABLE users RENAME CONSTRAINT chk_users_phone_format TO users_phone_format_check;
    END IF;
    
    -- users_role_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_users_role') THEN
        ALTER TABLE users RENAME CONSTRAINT chk_users_role TO users_role_check;
    END IF;
END $$;

-- Rename constraints for audit_logs table
DO $$
BEGIN
    -- audit_logs_severity_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_audit_logs_severity') THEN
        ALTER TABLE audit_logs RENAME CONSTRAINT chk_audit_logs_severity TO audit_logs_severity_check;
    END IF;
END $$;

-- Rename constraints for notifications table
DO $$
BEGIN
    -- notifications_type_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_notifications_type') THEN
        ALTER TABLE notifications RENAME CONSTRAINT chk_notifications_type TO notifications_type_check;
    END IF;
    
    -- notifications_status_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_notifications_status') THEN
        ALTER TABLE notifications RENAME CONSTRAINT chk_notifications_status TO notifications_status_check;
    END IF;
END $$;

-- Rename constraints for family_members table
DO $$
BEGIN
    -- family_members_role_check
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_family_members_role') THEN
        ALTER TABLE family_members RENAME CONSTRAINT chk_family_members_role TO family_members_role_check;
    END IF;
END $$;

-- Add additional constraints with improved naming
-- Add constraint for medicine_units name length (only if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'medicine_units_name_length_check') THEN
        ALTER TABLE medicine_units ADD CONSTRAINT medicine_units_name_length_check 
            CHECK (LENGTH(unit_name) >= 1 AND LENGTH(unit_name) <= 50);
    END IF;
END $$;

-- Add constraint for medicine_units description length (only if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'medicine_units_description_length_check') THEN
        ALTER TABLE medicine_units ADD CONSTRAINT medicine_units_description_length_check 
            CHECK (description IS NULL OR LENGTH(description) <= 200);
    END IF;
END $$;

-- Add constraint for medicine name length (only if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'medicines_name_length_check') THEN
        ALTER TABLE medicines ADD CONSTRAINT medicines_name_length_check 
            CHECK (LENGTH(name) >= 1 AND LENGTH(name) <= 200);
    END IF;
END $$;

-- Add constraint for user full_name length (only if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'users_full_name_length_check') THEN
        ALTER TABLE users ADD CONSTRAINT users_full_name_length_check 
            CHECK (LENGTH(full_name) >= 1 AND LENGTH(full_name) <= 255);
    END IF;
END $$;

-- Add constraint for family name length (only if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'families_name_length_check') THEN
        ALTER TABLE families ADD CONSTRAINT families_name_length_check 
            CHECK (LENGTH(name) >= 1 AND LENGTH(name) <= 100);
    END IF;
END $$;

-- Add constraint for notification title length (only if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'notifications_title_length_check') THEN
        ALTER TABLE notifications ADD CONSTRAINT notifications_title_length_check 
            CHECK (LENGTH(title) >= 1 AND LENGTH(title) <= 200);
    END IF;
END $$;

-- Add constraint for notification message length (only if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'notifications_message_length_check') THEN
        ALTER TABLE notifications ADD CONSTRAINT notifications_message_length_check 
            CHECK (LENGTH(message) >= 1 AND LENGTH(message) <= 1000);
    END IF;
END $$; 