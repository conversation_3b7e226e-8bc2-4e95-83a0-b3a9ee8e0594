-- Migration V5: Update prescription column names for clarity
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Rename user_id to prescribed_by for better clarity

-- Rename user_id column to prescribed_by for better clarity
ALTER TABLE prescriptions RENAME COLUMN user_id TO prescribed_by;

-- Update the foreign key constraint name
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'prescriptions_user_id_fkey'
    ) THEN
        ALTER TABLE prescriptions 
        DROP CONSTRAINT prescriptions_user_id_fkey;
    END IF;
END $$;

-- Add new foreign key constraint with updated name
ALTER TABLE prescriptions 
ADD CONSTRAINT prescriptions_prescribed_by_fkey 
FOREIGN KEY (prescribed_by) REFERENCES users(id) ON DELETE CASCADE;

-- Update index name if exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_prescriptions_user_id'
    ) THEN
        DROP INDEX idx_prescriptions_user_id;
    END IF;
END $$;

-- Create new index with updated name
CREATE INDEX idx_prescriptions_prescribed_by ON prescriptions(prescribed_by); 