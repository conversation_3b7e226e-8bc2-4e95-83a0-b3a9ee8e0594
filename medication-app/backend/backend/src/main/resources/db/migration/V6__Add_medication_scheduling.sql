-- Migration V6: Add medication scheduling system
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Add medication scheduling and dose tracking tables

-- Update prescription_items table
-- Add new columns for improved dose tracking
ALTER TABLE prescription_items ADD COLUMN dose_amount DECIMAL(7,2);
ALTER TABLE prescription_items ADD COLUMN dose_unit VARCHAR(20);
ALTER TABLE prescription_items ADD COLUMN route VARCHAR(50);

-- Remove old columns
ALTER TABLE prescription_items DROP COLUMN IF EXISTS dosage;
ALTER TABLE prescription_items DROP COLUMN IF EXISTS frequency;
ALTER TABLE prescription_items DROP COLUMN IF EXISTS time_of_day;

-- Create medication_schedules table
CREATE TABLE medication_schedules (
    id BIGSERIAL PRIMARY KEY,
    prescription_item_id BIGINT NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 1 AND day_of_week <= 7),
    time TIME NOT NULL,
    dose_amount DECIMAL(7,2) NOT NULL CHECK (dose_amount > 0),
    dose_unit VARCHAR(20) NOT NULL,
    notes VARCHAR(500),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (prescription_item_id) REFERENCES prescription_items(id) ON DELETE CASCADE
);

-- Create medication_doses table
CREATE TABLE medication_doses (
    id BIGSERIAL PRIMARY KEY,
    medication_schedule_id BIGINT NOT NULL,
    scheduled_date TIMESTAMP NOT NULL,
    taken_date TIMESTAMP,
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    actual_dose_amount DECIMAL(7,2) CHECK (actual_dose_amount > 0),
    actual_dose_unit VARCHAR(20),
    notes VARCHAR(500),
    missed_reason VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (medication_schedule_id) REFERENCES medication_schedules(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_medication_schedules_prescription_item_id ON medication_schedules(prescription_item_id);
CREATE INDEX idx_medication_schedules_day_of_week ON medication_schedules(day_of_week);
CREATE INDEX idx_medication_schedules_time ON medication_schedules(time);
CREATE INDEX idx_medication_schedules_is_active ON medication_schedules(is_active);

CREATE INDEX idx_medication_doses_schedule_id ON medication_doses(medication_schedule_id);
CREATE INDEX idx_medication_doses_scheduled_date ON medication_doses(scheduled_date);
CREATE INDEX idx_medication_doses_taken_date ON medication_doses(taken_date);
CREATE INDEX idx_medication_doses_status ON medication_doses(status);

-- Add constraints
ALTER TABLE medication_doses ADD CONSTRAINT chk_medication_doses_status 
    CHECK (status IN ('PENDING', 'TAKEN', 'MISSED', 'SKIPPED', 'PARTIAL'));

-- Add trigger for updated_at
CREATE TRIGGER trg_medication_schedules_updated_at
    BEFORE UPDATE ON medication_schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_medication_doses_updated_at
    BEFORE UPDATE ON medication_doses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column(); 