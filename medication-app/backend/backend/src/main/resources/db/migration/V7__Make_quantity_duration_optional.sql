-- Migration V7: Make quantity and duration_days optional in prescription_items
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Make quantity and duration_days optional since MedicationSchedule handles scheduling

-- Make quantity optional (remove NOT NULL constraint)
ALTER TABLE prescription_items ALTER COLUMN quantity DROP NOT NULL;

-- Add comment to explain the change
COMMENT ON COLUMN prescription_items.quantity IS 'Optional: Suggested quantity per dose. Actual dosing is handled by MedicationSchedule.';
COMMENT ON COLUMN prescription_items.duration_days IS 'Optional: Suggested duration in days. Actual scheduling is handled by MedicationSchedule.'; 