-- Migration V8: Add expected dose fields to medication_doses for adherence calculation
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Add expected dose fields and validation constraints for adherence tracking

-- Add expected dose fields
ALTER TABLE medication_doses ADD COLUMN expected_dose_amount DECIMAL(7,2);
ALTER TABLE medication_doses ADD COLUMN expected_dose_unit VARCHAR(20);

-- Add comments
COMMENT ON COLUMN medication_doses.expected_dose_amount IS 'Expected dose amount from MedicationSchedule for adherence calculation';
COMMENT ON COLUMN medication_doses.expected_dose_unit IS 'Expected dose unit from MedicationSchedule for adherence calculation';
COMMENT ON COLUMN medication_doses.actual_dose_amount IS 'Actual dose amount taken (required for TAKEN/PARTIAL status)';
COMMENT ON COLUMN medication_doses.actual_dose_unit IS 'Actual dose unit taken (required for TAKEN/PARTIAL status)';

-- Add check constraint for actual dose when status is TAKEN or PARTIAL
ALTER TABLE medication_doses ADD CONSTRAINT chk_medication_doses_actual_dose_required
    CHECK (
        (status NOT IN ('TAKEN', 'PARTIAL')) OR 
        (actual_dose_amount IS NOT NULL AND actual_dose_amount > 0 AND actual_dose_unit IS NOT NULL)
    );

-- Add check constraint for dose unit consistency
ALTER TABLE medication_doses ADD CONSTRAINT chk_medication_doses_unit_consistency
    CHECK (
        (expected_dose_unit IS NULL OR actual_dose_unit IS NULL) OR 
        (expected_dose_unit = actual_dose_unit)
    );

-- Create function to auto-populate expected dose from MedicationSchedule
CREATE OR REPLACE FUNCTION populate_expected_dose()
RETURNS TRIGGER AS $$
BEGIN
    -- Only populate if expected dose is not set
    IF NEW.expected_dose_amount IS NULL OR NEW.expected_dose_unit IS NULL THEN
        SELECT ms.dose_amount, ms.dose_unit 
        INTO NEW.expected_dose_amount, NEW.expected_dose_unit
        FROM medication_schedules ms 
        WHERE ms.id = NEW.medication_schedule_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-populate expected dose
CREATE TRIGGER trg_populate_expected_dose
    BEFORE INSERT ON medication_doses
    FOR EACH ROW
    EXECUTE FUNCTION populate_expected_dose(); 