-- Migration V9: Optimize medication_schedules indexes for notification system
-- Author: quydong.tran
-- Created: December 2024
-- Applies to: PostgreSQL >= 12
-- Description: Add composite indexes for efficient notification queries

-- Drop existing single column indexes (if they exist) to avoid duplication
DROP INDEX IF EXISTS idx_medication_schedules_prescription_item_id;
DROP INDEX IF EXISTS idx_medication_schedules_day_of_week;
DROP INDEX IF EXISTS idx_medication_schedules_time;
DROP INDEX IF EXISTS idx_medication_schedules_is_active;

-- Create optimized composite indexes for notification system
-- Index for finding schedules by prescription item and day/time
CREATE INDEX idx_medication_schedules_prescription_time 
ON medication_schedules(prescription_item_id, day_of_week, time);

-- Index for notification queries (most common use case)
CREATE INDEX idx_medication_schedules_notification 
ON medication_schedules(day_of_week, time, is_active);

-- Index for finding active schedules for a specific prescription item
CREATE INDEX idx_medication_schedules_active_prescription 
ON medication_schedules(prescription_item_id, is_active);

-- Index for time-based queries (useful for overdue detection)
CREATE INDEX idx_medication_schedules_time_active 
ON medication_schedules(time, is_active);

-- Add comments explaining the indexes
COMMENT ON INDEX idx_medication_schedules_prescription_time IS 'Optimized for finding schedules by prescription item and day/time';
COMMENT ON INDEX idx_medication_schedules_notification IS 'Optimized for notification queries (day_of_week, time, is_active)';
COMMENT ON INDEX idx_medication_schedules_active_prescription IS 'Optimized for finding active schedules for a prescription item';
COMMENT ON INDEX idx_medication_schedules_time_active IS 'Optimized for time-based queries and overdue detection'; 