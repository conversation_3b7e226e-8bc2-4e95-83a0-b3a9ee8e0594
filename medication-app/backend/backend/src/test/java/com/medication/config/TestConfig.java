package com.medication.config;

import com.medication.security.JwtUtil;
import com.medication.service.EmailService;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.security.core.userdetails.UserDetailsService;

/**
 * Test configuration để mock các dependencies cần thiết cho testing
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@TestConfiguration
public class TestConfig {



    /**
     * Mock JavaMailSender cho testing
     */
    @Bean
    @Primary
    public JavaMailSender javaMailSender() {
        return Mockito.mock(JavaMailSender.class);
    }

    /**
     * Mock JwtUtil cho testing
     */
    @Bean
    @Primary
    public JwtUtil jwtUtil() {
        return Mockito.mock(JwtUtil.class);
    }

    /**
     * Mock UserDetailsService cho testing
     */
    @Bean
    @Primary
    public UserDetailsService userDetailsService() {
        return Mockito.mock(UserDetailsService.class);
    }

    /**
     * Mock EmailService cho testing
     */
    @Bean
    @Primary
    public EmailService emailService() {
        return Mockito.mock(EmailService.class);
    }
}