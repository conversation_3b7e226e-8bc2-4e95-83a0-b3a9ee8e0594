package com.medication.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.medication.dto.request.CreatePrescriptionItemRequest;
import com.medication.dto.request.UpdatePrescriptionItemRequest;
import com.medication.entity.PrescriptionItem;
import com.medication.enums.DoseUnit;
import com.medication.enums.Route;
import com.medication.service.domain.prescription.PrescriptionItemBusinessService;
import com.medication.config.TestConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(PrescriptionItemController.class)
@Import(TestConfig.class)
class PrescriptionItemControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PrescriptionItemBusinessService prescriptionItemBusinessService;

    @Autowired
    private ObjectMapper objectMapper;

    private Long userId;
    private CreatePrescriptionItemRequest createRequest;
    private UpdatePrescriptionItemRequest updateRequest;
    private PrescriptionItem prescriptionItem;

    @BeforeEach
    void setUp() {
        userId = 1L;

        createRequest = CreatePrescriptionItemRequest.builder()
                .prescriptionId(1L)
                .medicineId(1L)
                .doseAmount(10.0)
                .doseUnit(DoseUnit.MILLIGRAM)
                .quantity(1)
                .instructions("Take with food")
                .route(Route.ORAL)
                .durationDays(7)
                .build();

        updateRequest = UpdatePrescriptionItemRequest.builder()
                .doseAmount(20.0)
                .quantity(2)
                .instructions("Take after meal")
                .build();

        prescriptionItem = new PrescriptionItem();
        prescriptionItem.setId(1L);
        prescriptionItem.setDoseAmount(10.0);
        prescriptionItem.setDoseUnit(DoseUnit.MILLIGRAM);
        prescriptionItem.setQuantity(1);
        prescriptionItem.setInstructions("Take with food");
        prescriptionItem.setRoute(Route.ORAL);
        prescriptionItem.setDurationDays(7);
    }

    @Test
    void createPrescriptionItem_Success() throws Exception {
        // Given
        when(prescriptionItemBusinessService.createPrescriptionItemForUser(any(CreatePrescriptionItemRequest.class), eq(userId)))
                .thenReturn(prescriptionItem);

        // When & Then
        mockMvc.perform(post("/api/prescription-items")
                        .param("userId", userId.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.statusCode").value(201))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEM_CREATED"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.doseAmount").value(10.0))
                .andExpect(jsonPath("$.data.doseUnit").value("MG"))
                .andExpect(jsonPath("$.data.quantity").value(1))
                .andExpect(jsonPath("$.data.instructions").value("Take with food"))
                .andExpect(jsonPath("$.data.route").value("ORAL"))
                .andExpect(jsonPath("$.data.durationDays").value(7));
    }

    @Test
    void createPrescriptionItem_InvalidRequest() throws Exception {
        // Given - request with null required fields
        CreatePrescriptionItemRequest invalidRequest = CreatePrescriptionItemRequest.builder()
                .prescriptionId(null) // Required field is null
                .medicineId(1L)
                .doseAmount(10.0)
                .doseUnit(DoseUnit.MILLIGRAM)
                .route(Route.ORAL)
                .durationDays(7)
                .build();

        // When & Then
        mockMvc.perform(post("/api/prescription-items")
                        .param("userId", userId.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.statusCode").value(400));
    }

    @Test
    void createPrescriptionItem_MissingUserId() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/prescription-items")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().is5xxServerError()); // Expect 500 instead of 400 due to missing parameter handling
    }

    @Test
    void getPrescriptionItem_Success() throws Exception {
        // Given
        when(prescriptionItemBusinessService.findPrescriptionItemByIdForUser(1L, userId))
                .thenReturn(prescriptionItem);

        // When & Then
        mockMvc.perform(get("/api/prescription-items/1")
                        .param("userId", userId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.statusCode").value(200))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEM_FOUND"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.doseAmount").value(10.0));
    }

    @Test
    void getPrescriptionItemsByPrescriptionId_Success() throws Exception {
        // Given
        List<PrescriptionItem> prescriptionItems = Arrays.asList(prescriptionItem);
        when(prescriptionItemBusinessService.getPrescriptionItemsByPrescriptionIdForUser(1L, userId))
                .thenReturn(prescriptionItems);

        // When & Then
        mockMvc.perform(get("/api/prescription-items/by-prescription/1")
                        .param("userId", userId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.statusCode").value(200))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEMS_FOUND"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].doseAmount").value(10.0));
    }

    @Test
    void getPrescriptionItems_WithPagination() throws Exception {
        // Given
        List<PrescriptionItem> prescriptionItems = Arrays.asList(prescriptionItem);
        Page<PrescriptionItem> page = new PageImpl<>(prescriptionItems, PageRequest.of(0, 10), 1);
        when(prescriptionItemBusinessService.getPrescriptionItemsForUser(eq(userId), any()))
                .thenReturn(page);

        // When & Then
        mockMvc.perform(get("/api/prescription-items")
                        .param("userId", userId.toString())
                        .param("page", "0")
                        .param("size", "10")
                        .param("sortBy", "createdAt")
                        .param("sortDir", "desc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.statusCode").value(200))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEMS_FOUND"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content[0].id").value(1))
                .andExpect(jsonPath("$.data.totalElements").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));
    }

    @Test
    void updatePrescriptionItem_Success() throws Exception {
        // Given
        PrescriptionItem updatedItem = new PrescriptionItem();
        updatedItem.setId(1L);
        updatedItem.setDoseAmount(20.0);
        updatedItem.setQuantity(2);
        updatedItem.setInstructions("Take after meal");
        
        when(prescriptionItemBusinessService.updatePrescriptionItemForUser(eq(1L), any(UpdatePrescriptionItemRequest.class), eq(userId)))
                .thenReturn(updatedItem);

        // When & Then
        mockMvc.perform(put("/api/prescription-items/1")
                        .param("userId", userId.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.statusCode").value(200))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEM_UPDATED"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.doseAmount").value(20.0))
                .andExpect(jsonPath("$.data.quantity").value(2))
                .andExpect(jsonPath("$.data.instructions").value("Take after meal"));
    }

    @Test
    void deletePrescriptionItem_Success() throws Exception {
        // When & Then
        mockMvc.perform(delete("/api/prescription-items/1")
                        .param("userId", userId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.statusCode").value(200))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEM_DELETED"))
                .andExpect(jsonPath("$.data").doesNotExist());
    }
}
