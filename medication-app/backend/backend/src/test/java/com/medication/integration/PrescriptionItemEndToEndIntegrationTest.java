package com.medication.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.medication.dto.request.*;
import com.medication.dto.response.*;
import com.medication.enums.*;
import com.medication.config.TestConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * End-to-end integration tests for PrescriptionItem API
 * Tests the complete flow from Anonymous Session -> Medicine -> Prescription -> PrescriptionItem
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@Import(TestConfig.class)
class PrescriptionItemEndToEndIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private UUID anonymousUserId;
    private Long medicineId;
    private Long prescriptionId;

    @BeforeEach
    void setUp() throws Exception {
        anonymousUserId = UUID.randomUUID();
        
        // Step 1: Create Anonymous Session
        createAnonymousSession();
        
        // Step 2: Create Medicine
        createMedicine();
        
        // Step 3: Create Prescription
        createPrescription();
    }

    private void createAnonymousSession() throws Exception {
        CreateAnonymousSessionRequest sessionRequest = new CreateAnonymousSessionRequest();
        // Set required fields based on actual DTO structure

        mockMvc.perform(post("/api/anonymous/sessions")
                        .header("X-Anonymous-User-Id", anonymousUserId.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(sessionRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.anonymousUserId").value(anonymousUserId.toString()));
    }

    private void createMedicine() throws Exception {
        CreateMedicineRequest medicineRequest = new CreateMedicineRequest();
        medicineRequest.setName("Test Medicine for Integration");
        medicineRequest.setActiveIngredient("Test Active Ingredient");
        medicineRequest.setManufacturer("Test Manufacturer");
        medicineRequest.setDosage("10mg");

        MvcResult result = mockMvc.perform(post("/api/anonymous/medicines")
                        .header("X-Anonymous-User-Id", anonymousUserId.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(medicineRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.name").value("Test Medicine for Integration"))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<MedicineResponse> response = objectMapper.readValue(responseContent, 
                objectMapper.getTypeFactory().constructParametricType(ApiResponse.class, MedicineResponse.class));
        medicineId = response.getData().getId();
    }

    private void createPrescription() throws Exception {
        CreatePrescriptionRequest prescriptionRequest = CreatePrescriptionRequest.builder()
                .doctorName("Dr. Integration Test")
                .prescriptionDate(LocalDate.now())
                .startDate(LocalDate.now())
                .endDate(LocalDate.now().plusDays(7))
                .diagnosis("Integration test diagnosis")
                .complianceRate(BigDecimal.ZERO)
                .totalDoses(0)
                .takenDoses(0)
                .isActive(true)
                .build();

        MvcResult result = mockMvc.perform(post("/api/anonymous/prescriptions")
                        .header("X-Anonymous-User-Id", anonymousUserId.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(prescriptionRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.doctorName").value("Dr. Integration Test"))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        ApiResponse<PrescriptionResponse> response = objectMapper.readValue(responseContent, 
                objectMapper.getTypeFactory().constructParametricType(ApiResponse.class, PrescriptionResponse.class));
        prescriptionId = response.getData().getId();
    }

    @Test
    void completeWorkflow_CreateReadUpdateDelete_ShouldWorkEndToEnd() throws Exception {
        // Step 4: Create PrescriptionItem
        CreatePrescriptionItemRequest createRequest = CreatePrescriptionItemRequest.builder()
                .prescriptionId(prescriptionId)
                .medicineId(medicineId)
                .doseAmount(10.0)
                .doseUnit(DoseUnit.MILLIGRAM)
                .quantity(1)
                .instructions("Take with food during integration test")
                .route(Route.ORAL)
                .durationDays(7)
                .build();

        MvcResult createResult = mockMvc.perform(post("/api/anonymous/prescription-items")
                        .header("X-Anonymous-User-Id", anonymousUserId.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEM_CREATED"))
                .andExpect(jsonPath("$.data.prescriptionId").value(prescriptionId))
                .andExpect(jsonPath("$.data.medicineId").value(medicineId))
                .andExpect(jsonPath("$.data.medicineName").value("Test Medicine for Integration"))
                .andExpect(jsonPath("$.data.doseAmount").value(10.0))
                .andExpect(jsonPath("$.data.doseUnit").value("MILLIGRAM"))
                .andExpect(jsonPath("$.data.quantity").value(1))
                .andExpect(jsonPath("$.data.instructions").value("Take with food during integration test"))
                .andExpect(jsonPath("$.data.route").value("ORAL"))
                .andExpect(jsonPath("$.data.durationDays").value(7))
                .andReturn();

        String createResponseContent = createResult.getResponse().getContentAsString();
        ApiResponse<PrescriptionItemResponse> createResponse = objectMapper.readValue(createResponseContent, 
                objectMapper.getTypeFactory().constructParametricType(ApiResponse.class, PrescriptionItemResponse.class));
        Long prescriptionItemId = createResponse.getData().getId();

        // Step 5: Read PrescriptionItem by ID
        mockMvc.perform(get("/api/anonymous/prescription-items/{id}", prescriptionItemId)
                        .header("X-Anonymous-User-Id", anonymousUserId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEM_FOUND"))
                .andExpect(jsonPath("$.data.id").value(prescriptionItemId))
                .andExpect(jsonPath("$.data.prescriptionId").value(prescriptionId))
                .andExpect(jsonPath("$.data.medicineId").value(medicineId))
                .andExpect(jsonPath("$.data.doseAmount").value(10.0));

        // Step 6: Get PrescriptionItems by Prescription ID
        mockMvc.perform(get("/api/anonymous/prescription-items/by-prescription/{prescriptionId}", prescriptionId)
                        .header("X-Anonymous-User-Id", anonymousUserId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEMS_FOUND"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value(prescriptionItemId))
                .andExpect(jsonPath("$.data[0].prescriptionId").value(prescriptionId));

        // Step 7: Get PrescriptionItems with pagination
        mockMvc.perform(get("/api/anonymous/prescription-items")
                        .header("X-Anonymous-User-Id", anonymousUserId.toString())
                        .param("page", "0")
                        .param("size", "10")
                        .param("sortBy", "createdAt")
                        .param("sortDir", "desc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEMS_FOUND"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content[0].id").value(prescriptionItemId))
                .andExpect(jsonPath("$.data.totalElements").value(1))
                .andExpect(jsonPath("$.data.size").value(10))
                .andExpect(jsonPath("$.data.number").value(0));

        // Step 8: Update PrescriptionItem
        UpdatePrescriptionItemRequest updateRequest = UpdatePrescriptionItemRequest.builder()
                .doseAmount(20.0)
                .quantity(2)
                .instructions("Updated instructions - take after meal")
                .durationDays(14)
                .build();

        mockMvc.perform(put("/api/anonymous/prescription-items/{id}", prescriptionItemId)
                        .header("X-Anonymous-User-Id", anonymousUserId.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEM_UPDATED"))
                .andExpect(jsonPath("$.data.id").value(prescriptionItemId))
                .andExpect(jsonPath("$.data.doseAmount").value(20.0))
                .andExpect(jsonPath("$.data.quantity").value(2))
                .andExpect(jsonPath("$.data.instructions").value("Updated instructions - take after meal"))
                .andExpect(jsonPath("$.data.durationDays").value(14));

        // Step 9: Verify update by reading again
        mockMvc.perform(get("/api/anonymous/prescription-items/{id}", prescriptionItemId)
                        .header("X-Anonymous-User-Id", anonymousUserId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.doseAmount").value(20.0))
                .andExpect(jsonPath("$.data.quantity").value(2))
                .andExpect(jsonPath("$.data.instructions").value("Updated instructions - take after meal"))
                .andExpect(jsonPath("$.data.durationDays").value(14));

        // Step 10: Delete PrescriptionItem
        mockMvc.perform(delete("/api/anonymous/prescription-items/{id}", prescriptionItemId)
                        .header("X-Anonymous-User-Id", anonymousUserId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value("PRESCRIPTION_ITEM_DELETED"))
                .andExpect(jsonPath("$.data").doesNotExist());

        // Step 11: Verify deletion - should return 404
        mockMvc.perform(get("/api/anonymous/prescription-items/{id}", prescriptionItemId)
                        .header("X-Anonymous-User-Id", anonymousUserId.toString()))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.success").value(false));

        // Step 12: Verify empty list after deletion
        mockMvc.perform(get("/api/anonymous/prescription-items/by-prescription/{prescriptionId}", prescriptionId)
                        .header("X-Anonymous-User-Id", anonymousUserId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data").isEmpty());
    }
}
