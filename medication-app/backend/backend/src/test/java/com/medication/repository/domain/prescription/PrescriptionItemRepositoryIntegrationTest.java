package com.medication.repository.domain.prescription;

import com.medication.entity.*;
import com.medication.enums.*;
import com.medication.repository.domain.medicine.MedicineRepository;
import com.medication.repository.domain.user.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@DataJpaTest
@ActiveProfiles("test")
class PrescriptionItemRepositoryIntegrationTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private PrescriptionItemRepository prescriptionItemRepository;

    @Autowired
    private PrescriptionRepository prescriptionRepository;

    @Autowired
    private MedicineRepository medicineRepository;

    @Autowired
    private UserRepository userRepository;

    private User testUser;
    private Medicine testMedicine1;
    private Medicine testMedicine2;
    private Prescription testPrescription;
    private Prescription anonymousPrescription;
    private PrescriptionItem testPrescriptionItem1;
    private PrescriptionItem testPrescriptionItem2;
    private UUID anonymousUserId;

    @BeforeEach
    void setUp() {
        anonymousUserId = UUID.randomUUID();

        // Create test user
        testUser = new User();
        testUser.setFullName("Test User");
        testUser.setEmail("<EMAIL>");
        testUser.setPhoneNumber("1234567890");
        testUser.setUserType(UserType.REGISTERED);
        testUser.setIsActive(true);
        testUser = userRepository.save(testUser);

        // Create test medicines
        testMedicine1 = new Medicine();
        testMedicine1.setName("Test Medicine 1");
        testMedicine1.setActiveIngredient("Active Ingredient 1");
        testMedicine1.setManufacturer("Test Manufacturer 1");
        testMedicine1.setDosage("10mg");
        testMedicine1.setUserType(UserType.REGISTERED);
        testMedicine1.setIsActive(true);
        testMedicine1 = medicineRepository.save(testMedicine1);

        testMedicine2 = new Medicine();
        testMedicine2.setName("Test Medicine 2");
        testMedicine2.setActiveIngredient("Active Ingredient 2");
        testMedicine2.setManufacturer("Test Manufacturer 2");
        testMedicine2.setDosage("20mg");
        testMedicine2.setUserType(UserType.REGISTERED);
        testMedicine2.setIsActive(true);
        testMedicine2 = medicineRepository.save(testMedicine2);

        // Create test prescription for registered user
        testPrescription = Prescription.builder()
                .doctorName("Dr. Test")
                .prescriptionDate(LocalDate.now())
                .startDate(LocalDate.now())
                .endDate(LocalDate.now().plusDays(7))
                .diagnosis("Test diagnosis")
                .patient(testUser)
                .userType(UserType.REGISTERED)
                .status(PrescriptionStatus.ACTIVE)
                .complianceRate(BigDecimal.ZERO)
                .totalDoses(0)
                .takenDoses(0)
                .isActive(true)
                .build();
        testPrescription = prescriptionRepository.save(testPrescription);

        // Create test prescription for anonymous user
        anonymousPrescription = Prescription.builder()
                .doctorName("Dr. Anonymous")
                .prescriptionDate(LocalDate.now())
                .startDate(LocalDate.now())
                .endDate(LocalDate.now().plusDays(14))
                .diagnosis("Anonymous diagnosis")
                .anonymousUserId(anonymousUserId)
                .userType(UserType.ANONYMOUS)
                .status(PrescriptionStatus.ACTIVE)
                .complianceRate(BigDecimal.ZERO)
                .totalDoses(0)
                .takenDoses(0)
                .isActive(true)
                .build();
        anonymousPrescription = prescriptionRepository.save(anonymousPrescription);

        // Create test prescription items
        testPrescriptionItem1 = new PrescriptionItem();
        testPrescriptionItem1.setPrescription(testPrescription);
        testPrescriptionItem1.setMedicine(testMedicine1);
        testPrescriptionItem1.setDoseAmount(10.0);
        testPrescriptionItem1.setDoseUnit(DoseUnit.MILLIGRAM);
        testPrescriptionItem1.setQuantity(1);
        testPrescriptionItem1.setInstructions("Take with food");
        testPrescriptionItem1.setRoute(Route.ORAL);
        testPrescriptionItem1.setDurationDays(7);
        testPrescriptionItem1 = prescriptionItemRepository.save(testPrescriptionItem1);

        testPrescriptionItem2 = new PrescriptionItem();
        testPrescriptionItem2.setPrescription(anonymousPrescription);
        testPrescriptionItem2.setMedicine(testMedicine2);
        testPrescriptionItem2.setDoseAmount(20.0);
        testPrescriptionItem2.setDoseUnit(DoseUnit.MILLIGRAM);
        testPrescriptionItem2.setQuantity(2);
        testPrescriptionItem2.setInstructions("Take after meal");
        testPrescriptionItem2.setRoute(Route.ORAL);
        testPrescriptionItem2.setDurationDays(14);
        testPrescriptionItem2 = prescriptionItemRepository.save(testPrescriptionItem2);

        entityManager.flush();
        entityManager.clear();
    }

    // ==================== BASIC QUERIES TESTS ====================

    @Test
    void findByPrescription_Id_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByPrescription_Id(testPrescription.getId());

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem1.getId());
        assertThat(items.get(0).getMedicine().getName()).isEqualTo("Test Medicine 1");
    }

    @Test
    void findByPrescription_IdWithPagination_ShouldReturnPagedResults() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<PrescriptionItem> page = prescriptionItemRepository.findByPrescription_Id(testPrescription.getId(), pageable);

        // Then
        assertThat(page.getContent()).hasSize(1);
        assertThat(page.getTotalElements()).isEqualTo(1);
        assertThat(page.getContent().get(0).getId()).isEqualTo(testPrescriptionItem1.getId());
    }

    @Test
    void findByIdAndPrescription_Id_ShouldReturnCorrectItem() {
        // When
        Optional<PrescriptionItem> item = prescriptionItemRepository.findByIdAndPrescription_Id(
                testPrescriptionItem1.getId(), testPrescription.getId());

        // Then
        assertThat(item).isPresent();
        assertThat(item.get().getId()).isEqualTo(testPrescriptionItem1.getId());
    }

    @Test
    void findByMedicine_Id_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByMedicine_Id(testMedicine1.getId());

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem1.getId());
    }

    // ==================== REGISTERED USER QUERIES TESTS ====================

    @Test
    void findByPatientId_ShouldReturnUserItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByPatientId(testUser.getId());

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem1.getId());
        assertThat(items.get(0).getPrescription().getPatient().getId()).isEqualTo(testUser.getId());
    }

    @Test
    void findByPatientIdWithPagination_ShouldReturnPagedResults() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<PrescriptionItem> page = prescriptionItemRepository.findByPatientId(testUser.getId(), pageable);

        // Then
        assertThat(page.getContent()).hasSize(1);
        assertThat(page.getTotalElements()).isEqualTo(1);
        assertThat(page.getContent().get(0).getId()).isEqualTo(testPrescriptionItem1.getId());
    }

    @Test
    void findByIdAndPatientId_ShouldReturnCorrectItem() {
        // When
        Optional<PrescriptionItem> item = prescriptionItemRepository.findByIdAndPatientId(
                testPrescriptionItem1.getId(), testUser.getId());

        // Then
        assertThat(item).isPresent();
        assertThat(item.get().getId()).isEqualTo(testPrescriptionItem1.getId());
    }

    @Test
    void findByIdAndPatientId_WithWrongUser_ShouldReturnEmpty() {
        // When
        Optional<PrescriptionItem> item = prescriptionItemRepository.findByIdAndPatientId(
                testPrescriptionItem1.getId(), 999L);

        // Then
        assertThat(item).isEmpty();
    }

    @Test
    void findByPrescriptionIdAndPatientId_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByPrescriptionIdAndPatientId(
                testPrescription.getId(), testUser.getId());

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem1.getId());
    }

    // ==================== ANONYMOUS USER QUERIES TESTS ====================

    @Test
    void findByAnonymousUserId_ShouldReturnAnonymousItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByAnonymousUserId(anonymousUserId);

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem2.getId());
        assertThat(items.get(0).getPrescription().getAnonymousUserId()).isEqualTo(anonymousUserId);
    }

    @Test
    void findByAnonymousUserIdWithPagination_ShouldReturnPagedResults() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<PrescriptionItem> page = prescriptionItemRepository.findByAnonymousUserId(anonymousUserId, pageable);

        // Then
        assertThat(page.getContent()).hasSize(1);
        assertThat(page.getTotalElements()).isEqualTo(1);
        assertThat(page.getContent().get(0).getId()).isEqualTo(testPrescriptionItem2.getId());
    }

    @Test
    void findByIdAndAnonymousUserId_ShouldReturnCorrectItem() {
        // When
        Optional<PrescriptionItem> item = prescriptionItemRepository.findByIdAndAnonymousUserId(
                testPrescriptionItem2.getId(), anonymousUserId);

        // Then
        assertThat(item).isPresent();
        assertThat(item.get().getId()).isEqualTo(testPrescriptionItem2.getId());
    }

    @Test
    void findByIdAndAnonymousUserId_WithWrongUser_ShouldReturnEmpty() {
        // When
        Optional<PrescriptionItem> item = prescriptionItemRepository.findByIdAndAnonymousUserId(
                testPrescriptionItem2.getId(), UUID.randomUUID());

        // Then
        assertThat(item).isEmpty();
    }

    @Test
    void findByPrescriptionIdAndAnonymousUserId_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByPrescriptionIdAndAnonymousUserId(
                anonymousPrescription.getId(), anonymousUserId);

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem2.getId());
    }

    // ==================== SEARCH AND FILTER QUERIES TESTS ====================

    @Test
    void findByRoute_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByRoute(Route.ORAL);

        // Then
        assertThat(items).hasSize(2); // Both test items have ORAL route
        assertThat(items).extracting(PrescriptionItem::getRoute).containsOnly(Route.ORAL);
    }

    @Test
    void findByDoseUnit_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByDoseUnit(DoseUnit.MILLIGRAM);

        // Then
        assertThat(items).hasSize(2); // Both test items have MILLIGRAM dose unit
        assertThat(items).extracting(PrescriptionItem::getDoseUnit).containsOnly(DoseUnit.MILLIGRAM);
    }

    @Test
    void findByDurationDays_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByDurationDays(7);

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem1.getId());
        assertThat(items.get(0).getDurationDays()).isEqualTo(7);
    }

    @Test
    void findByDurationDaysBetween_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByDurationDaysBetween(5, 10);

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem1.getId());
        assertThat(items.get(0).getDurationDays()).isBetween(5, 10);
    }

    @Test
    void findByDoseAmountBetween_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByDoseAmountBetween(15.0, 25.0);

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem2.getId());
        assertThat(items.get(0).getDoseAmount()).isBetween(15.0, 25.0);
    }

    @Test
    void findByMedicineNameContainingIgnoreCase_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByMedicineNameContainingIgnoreCase("medicine 1");

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem1.getId());
        assertThat(items.get(0).getMedicine().getName()).containsIgnoringCase("medicine 1");
    }

    @Test
    void findByInstructionsContainingIgnoreCase_ShouldReturnCorrectItems() {
        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByInstructionsContainingIgnoreCase("FOOD");

        // Then
        assertThat(items).hasSize(1);
        assertThat(items.get(0).getId()).isEqualTo(testPrescriptionItem1.getId());
        assertThat(items.get(0).getInstructions()).containsIgnoringCase("food");
    }

    // ==================== COUNT QUERIES TESTS ====================

    @Test
    void countByPrescription_Id_ShouldReturnCorrectCount() {
        // When
        long count = prescriptionItemRepository.countByPrescription_Id(testPrescription.getId());

        // Then
        assertThat(count).isEqualTo(1);
    }

    @Test
    void countByPatientId_ShouldReturnCorrectCount() {
        // When
        long count = prescriptionItemRepository.countByPatientId(testUser.getId());

        // Then
        assertThat(count).isEqualTo(1);
    }

    @Test
    void countByAnonymousUserId_ShouldReturnCorrectCount() {
        // When
        long count = prescriptionItemRepository.countByAnonymousUserId(anonymousUserId);

        // Then
        assertThat(count).isEqualTo(1);
    }

    @Test
    void countByMedicine_Id_ShouldReturnCorrectCount() {
        // When
        long count = prescriptionItemRepository.countByMedicine_Id(testMedicine1.getId());

        // Then
        assertThat(count).isEqualTo(1);
    }

    @Test
    void countByRoute_ShouldReturnCorrectCount() {
        // When
        long count = prescriptionItemRepository.countByRoute(Route.ORAL);

        // Then
        assertThat(count).isEqualTo(2); // Both test items have ORAL route
    }

    @Test
    void countByDoseUnit_ShouldReturnCorrectCount() {
        // When
        long count = prescriptionItemRepository.countByDoseUnit(DoseUnit.MILLIGRAM);

        // Then
        assertThat(count).isEqualTo(2); // Both test items have MILLIGRAM dose unit
    }

    // ==================== EXISTENCE QUERIES TESTS ====================

    @Test
    void existsByPrescription_Id_ShouldReturnTrue() {
        // When
        boolean exists = prescriptionItemRepository.existsByPrescription_Id(testPrescription.getId());

        // Then
        assertThat(exists).isTrue();
    }

    @Test
    void existsByPrescription_Id_WithNonExistentId_ShouldReturnFalse() {
        // When
        boolean exists = prescriptionItemRepository.existsByPrescription_Id(999L);

        // Then
        assertThat(exists).isFalse();
    }

    @Test
    void existsByPatientId_ShouldReturnTrue() {
        // When
        boolean exists = prescriptionItemRepository.existsByPatientId(testUser.getId());

        // Then
        assertThat(exists).isTrue();
    }

    @Test
    void existsByAnonymousUserId_ShouldReturnTrue() {
        // When
        boolean exists = prescriptionItemRepository.existsByAnonymousUserId(anonymousUserId);

        // Then
        assertThat(exists).isTrue();
    }

    @Test
    void existsByMedicine_Id_ShouldReturnTrue() {
        // When
        boolean exists = prescriptionItemRepository.existsByMedicine_Id(testMedicine1.getId());

        // Then
        assertThat(exists).isTrue();
    }

    // ==================== DATE RANGE QUERIES TESTS ====================

    @Test
    void findByCreatedAtBetween_ShouldReturnCorrectItems() {
        // Given
        LocalDateTime start = LocalDateTime.now().minusHours(1);
        LocalDateTime end = LocalDateTime.now().plusHours(1);

        // When
        List<PrescriptionItem> items = prescriptionItemRepository.findByCreatedAtBetween(start, end);

        // Then
        assertThat(items).hasSize(2); // Both items should be created within this range
    }

    @Test
    void countByCreatedAtBetween_ShouldReturnCorrectCount() {
        // Given
        LocalDateTime start = LocalDateTime.now().minusHours(1);
        LocalDateTime end = LocalDateTime.now().plusHours(1);

        // When
        long count = prescriptionItemRepository.countByCreatedAtBetween(start, end);

        // Then
        assertThat(count).isEqualTo(2); // Both items should be created within this range
    }
}
