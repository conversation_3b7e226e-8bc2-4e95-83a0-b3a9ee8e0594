package com.medication.service.domain.prescription;

import com.medication.dto.request.CreatePrescriptionItemRequest;
import com.medication.dto.request.UpdatePrescriptionItemRequest;
import com.medication.entity.Medicine;
import com.medication.entity.Prescription;
import com.medication.entity.PrescriptionItem;
import com.medication.entity.User;
import com.medication.enums.DoseUnit;
import com.medication.enums.Route;
import com.medication.enums.UserType;
import com.medication.exception.domain.medicine.MedicineNotFoundException;
import com.medication.exception.domain.prescription.InvalidPrescriptionDataException;
import com.medication.exception.domain.prescription.PrescriptionNotFoundException;
import com.medication.repository.domain.medicine.MedicineRepository;
import com.medication.repository.domain.prescription.PrescriptionItemRepository;
import com.medication.repository.domain.prescription.PrescriptionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PrescriptionItemBusinessServiceTest {

    @Mock
    private PrescriptionItemRepository prescriptionItemRepository;

    @Mock
    private PrescriptionRepository prescriptionRepository;

    @Mock
    private MedicineRepository medicineRepository;

    @InjectMocks
    private PrescriptionItemBusinessService prescriptionItemBusinessService;

    private CreatePrescriptionItemRequest createRequest;
    private UpdatePrescriptionItemRequest updateRequest;
    private Prescription prescription;
    private Medicine medicine;
    private PrescriptionItem prescriptionItem;
    private User user;
    private UUID anonymousUserId;

    @BeforeEach
    void setUp() {
        // Setup test data
        anonymousUserId = UUID.randomUUID();
        
        user = new User();
        user.setId(1L);
        user.setFullName("Test User");

        prescription = new Prescription();
        prescription.setId(1L);
        prescription.setDoctorName("Dr. Test");
        prescription.setPrescriptionDate(LocalDate.now());
        prescription.setStartDate(LocalDate.now());
        prescription.setEndDate(LocalDate.now().plusDays(7));
        prescription.setDiagnosis("Test diagnosis");
        prescription.setPatient(user);
        prescription.setUserType(UserType.REGISTERED);

        medicine = new Medicine();
        medicine.setId(1L);
        medicine.setName("Test Medicine");
        medicine.setActiveIngredient("Test Active Ingredient");

        createRequest = CreatePrescriptionItemRequest.builder()
                .prescriptionId(1L)
                .medicineId(1L)
                .doseAmount(10.0)
                .doseUnit(DoseUnit.MILLIGRAM)
                .quantity(1)
                .instructions("Take with food")
                .route(Route.ORAL)
                .durationDays(7)
                .build();

        updateRequest = UpdatePrescriptionItemRequest.builder()
                .doseAmount(20.0)
                .quantity(2)
                .instructions("Take after meal")
                .build();

        prescriptionItem = new PrescriptionItem();
        prescriptionItem.setId(1L);
        prescriptionItem.setPrescription(prescription);
        prescriptionItem.setMedicine(medicine);
        prescriptionItem.setDoseAmount(10.0);
        prescriptionItem.setDoseUnit(DoseUnit.MILLIGRAM);
        prescriptionItem.setQuantity(1);
        prescriptionItem.setInstructions("Take with food");
        prescriptionItem.setRoute(Route.ORAL);
        prescriptionItem.setDurationDays(7);
    }

    @Test
    void createPrescriptionItemForUser_Success() {
        // Given
        when(prescriptionRepository.findByIdAndPatient_Id(1L, 1L)).thenReturn(Optional.of(prescription));
        when(medicineRepository.findById(1L)).thenReturn(Optional.of(medicine));
        when(prescriptionItemRepository.save(any(PrescriptionItem.class))).thenReturn(prescriptionItem);

        // When
        PrescriptionItem result = prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L);

        // Then
        assertNotNull(result);
        assertEquals(prescriptionItem.getId(), result.getId());
        verify(prescriptionRepository).findByIdAndPatient_Id(1L, 1L);
        verify(medicineRepository).findById(1L);
        verify(prescriptionItemRepository).save(any(PrescriptionItem.class));
    }

    @Test
    void createPrescriptionItemForUser_PrescriptionNotFound() {
        // Given
        when(prescriptionRepository.findByIdAndPatient_Id(1L, 1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(PrescriptionNotFoundException.class, () -> 
            prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L));
        
        verify(prescriptionRepository).findByIdAndPatient_Id(1L, 1L);
        verify(medicineRepository, never()).findById(any());
        verify(prescriptionItemRepository, never()).save(any());
    }

    @Test
    void createPrescriptionItemForUser_MedicineNotFound() {
        // Given
        when(prescriptionRepository.findByIdAndPatient_Id(1L, 1L)).thenReturn(Optional.of(prescription));
        when(medicineRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(MedicineNotFoundException.class, () -> 
            prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L));
        
        verify(prescriptionRepository).findByIdAndPatient_Id(1L, 1L);
        verify(medicineRepository).findById(1L);
        verify(prescriptionItemRepository, never()).save(any());
    }

    @Test
    void createPrescriptionItemForAnonymousUser_Success() {
        // Given
        prescription.setAnonymousUserId(anonymousUserId);
        prescription.setUserType(UserType.ANONYMOUS);
        prescription.setPatient(null);
        
        when(prescriptionRepository.findByIdAndAnonymousUserId(1L, anonymousUserId)).thenReturn(Optional.of(prescription));
        when(medicineRepository.findById(1L)).thenReturn(Optional.of(medicine));
        when(prescriptionItemRepository.save(any(PrescriptionItem.class))).thenReturn(prescriptionItem);

        // When
        PrescriptionItem result = prescriptionItemBusinessService.createPrescriptionItemForAnonymousUser(createRequest, anonymousUserId);

        // Then
        assertNotNull(result);
        assertEquals(prescriptionItem.getId(), result.getId());
        verify(prescriptionRepository).findByIdAndAnonymousUserId(1L, anonymousUserId);
        verify(medicineRepository).findById(1L);
        verify(prescriptionItemRepository).save(any(PrescriptionItem.class));
    }

    @Test
    void validateCreateRequest_NullRequest() {
        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () -> 
            prescriptionItemBusinessService.createPrescriptionItemForUser(null, 1L));
    }

    @Test
    void validateCreateRequest_NullPrescriptionId() {
        // Given
        createRequest.setPrescriptionId(null);

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () -> 
            prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L));
    }

    @Test
    void validateCreateRequest_NullMedicineId() {
        // Given
        createRequest.setMedicineId(null);

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () -> 
            prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L));
    }

    @Test
    void validateCreateRequest_InvalidDoseAmount() {
        // Given
        createRequest.setDoseAmount(-1.0);

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () -> 
            prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L));
    }

    @Test
    void validateCreateRequest_NullDoseUnit() {
        // Given
        createRequest.setDoseUnit(null);

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () -> 
            prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L));
    }

    @Test
    void validateCreateRequest_NullRoute() {
        // Given
        createRequest.setRoute(null);

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () -> 
            prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L));
    }

    @Test
    void validateCreateRequest_InvalidDurationDays() {
        // Given
        createRequest.setDurationDays(0);

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () -> 
            prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L));
    }

    @Test
    void updatePrescriptionItemForUser_Success() {
        // Given
        when(prescriptionItemRepository.findByIdAndPatientId(1L, 1L)).thenReturn(Optional.of(prescriptionItem));
        when(prescriptionItemRepository.save(any(PrescriptionItem.class))).thenReturn(prescriptionItem);

        // When
        PrescriptionItem result = prescriptionItemBusinessService.updatePrescriptionItemForUser(1L, updateRequest, 1L);

        // Then
        assertNotNull(result);
        verify(prescriptionItemRepository).findByIdAndPatientId(1L, 1L);
        verify(prescriptionItemRepository).save(any(PrescriptionItem.class));
    }

    @Test
    void findPrescriptionItemByIdForUser_Success() {
        // Given
        when(prescriptionItemRepository.findByIdAndPatientId(1L, 1L)).thenReturn(Optional.of(prescriptionItem));

        // When
        PrescriptionItem result = prescriptionItemBusinessService.findPrescriptionItemByIdForUser(1L, 1L);

        // Then
        assertNotNull(result);
        assertEquals(prescriptionItem.getId(), result.getId());
        verify(prescriptionItemRepository).findByIdAndPatientId(1L, 1L);
    }

    @Test
    void findPrescriptionItemByIdForUser_NotFound() {
        // Given
        when(prescriptionItemRepository.findByIdAndPatientId(1L, 1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(PrescriptionNotFoundException.class, () -> 
            prescriptionItemBusinessService.findPrescriptionItemByIdForUser(1L, 1L));
        
        verify(prescriptionItemRepository).findByIdAndPatientId(1L, 1L);
    }

    @Test
    void deletePrescriptionItemForUser_Success() {
        // Given
        when(prescriptionItemRepository.findByIdAndPatientId(1L, 1L)).thenReturn(Optional.of(prescriptionItem));

        // When
        prescriptionItemBusinessService.deletePrescriptionItemForUser(1L, 1L);

        // Then
        verify(prescriptionItemRepository).findByIdAndPatientId(1L, 1L);
        verify(prescriptionItemRepository).delete(prescriptionItem);
    }

    // ==================== ANONYMOUS USER TESTS ====================

    @Test
    void createPrescriptionItemForAnonymousUser_PrescriptionNotFound() {
        // Given
        when(prescriptionRepository.findByIdAndAnonymousUserId(1L, anonymousUserId)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(PrescriptionNotFoundException.class, () ->
            prescriptionItemBusinessService.createPrescriptionItemForAnonymousUser(createRequest, anonymousUserId));

        verify(prescriptionRepository).findByIdAndAnonymousUserId(1L, anonymousUserId);
        verify(medicineRepository, never()).findById(any());
        verify(prescriptionItemRepository, never()).save(any());
    }

    @Test
    void findPrescriptionItemByIdForAnonymousUser_Success() {
        // Given
        when(prescriptionItemRepository.findByIdAndAnonymousUserId(1L, anonymousUserId)).thenReturn(Optional.of(prescriptionItem));

        // When
        PrescriptionItem result = prescriptionItemBusinessService.findPrescriptionItemByIdForAnonymousUser(1L, anonymousUserId);

        // Then
        assertNotNull(result);
        assertEquals(prescriptionItem.getId(), result.getId());
        verify(prescriptionItemRepository).findByIdAndAnonymousUserId(1L, anonymousUserId);
    }

    @Test
    void findPrescriptionItemByIdForAnonymousUser_NotFound() {
        // Given
        when(prescriptionItemRepository.findByIdAndAnonymousUserId(1L, anonymousUserId)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(PrescriptionNotFoundException.class, () ->
            prescriptionItemBusinessService.findPrescriptionItemByIdForAnonymousUser(1L, anonymousUserId));

        verify(prescriptionItemRepository).findByIdAndAnonymousUserId(1L, anonymousUserId);
    }

    @Test
    void updatePrescriptionItemForAnonymousUser_Success() {
        // Given
        when(prescriptionItemRepository.findByIdAndAnonymousUserId(1L, anonymousUserId)).thenReturn(Optional.of(prescriptionItem));
        when(prescriptionItemRepository.save(any(PrescriptionItem.class))).thenReturn(prescriptionItem);

        // When
        PrescriptionItem result = prescriptionItemBusinessService.updatePrescriptionItemForAnonymousUser(1L, updateRequest, anonymousUserId);

        // Then
        assertNotNull(result);
        verify(prescriptionItemRepository).findByIdAndAnonymousUserId(1L, anonymousUserId);
        verify(prescriptionItemRepository).save(any(PrescriptionItem.class));
    }

    @Test
    void deletePrescriptionItemForAnonymousUser_Success() {
        // Given
        when(prescriptionItemRepository.findByIdAndAnonymousUserId(1L, anonymousUserId)).thenReturn(Optional.of(prescriptionItem));

        // When
        prescriptionItemBusinessService.deletePrescriptionItemForAnonymousUser(1L, anonymousUserId);

        // Then
        verify(prescriptionItemRepository).findByIdAndAnonymousUserId(1L, anonymousUserId);
        verify(prescriptionItemRepository).delete(prescriptionItem);
    }

    // ==================== VALIDATION TESTS ====================

    @Test
    void validateUpdateRequest_NullRequest() {
        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () ->
            prescriptionItemBusinessService.updatePrescriptionItemForUser(1L, null, 1L));
    }

    @Test
    void validateUpdateRequest_InvalidDoseAmount() {
        // Given
        updateRequest.setDoseAmount(-1.0);

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () ->
            prescriptionItemBusinessService.updatePrescriptionItemForUser(1L, updateRequest, 1L));
    }

    @Test
    void validateUpdateRequest_InvalidDurationDays() {
        // Given
        updateRequest.setDurationDays(0);

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () ->
            prescriptionItemBusinessService.updatePrescriptionItemForUser(1L, updateRequest, 1L));
    }

    @Test
    void validateUpdateRequest_InvalidQuantity() {
        // Given
        updateRequest.setQuantity(0);

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () ->
            prescriptionItemBusinessService.updatePrescriptionItemForUser(1L, updateRequest, 1L));
    }

    @Test
    void validateUpdateRequest_TooLongInstructions() {
        // Given
        updateRequest.setInstructions("a".repeat(2001)); // Exceed 2000 characters

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () ->
            prescriptionItemBusinessService.updatePrescriptionItemForUser(1L, updateRequest, 1L));
    }

    @Test
    void validateCreateRequest_TooLongInstructions() {
        // Given
        createRequest.setInstructions("a".repeat(2001)); // Exceed 2000 characters

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () ->
            prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L));
    }

    @Test
    void validateCreateRequest_InvalidQuantity() {
        // Given
        createRequest.setQuantity(0);

        // When & Then
        assertThrows(InvalidPrescriptionDataException.class, () ->
            prescriptionItemBusinessService.createPrescriptionItemForUser(createRequest, 1L));
    }

    // ==================== EDGE CASES ====================

    @Test
    void updatePrescriptionItemForUser_WithMedicineChange() {
        // Given
        Medicine newMedicine = new Medicine();
        newMedicine.setId(2L);
        newMedicine.setName("New Medicine");

        updateRequest.setMedicineId(2L);

        when(prescriptionItemRepository.findByIdAndPatientId(1L, 1L)).thenReturn(Optional.of(prescriptionItem));
        when(medicineRepository.findById(2L)).thenReturn(Optional.of(newMedicine));
        when(prescriptionItemRepository.save(any(PrescriptionItem.class))).thenReturn(prescriptionItem);

        // When
        PrescriptionItem result = prescriptionItemBusinessService.updatePrescriptionItemForUser(1L, updateRequest, 1L);

        // Then
        assertNotNull(result);
        verify(medicineRepository).findById(2L);
        verify(prescriptionItemRepository).save(any(PrescriptionItem.class));
    }

    @Test
    void updatePrescriptionItemForUser_MedicineNotFound() {
        // Given
        updateRequest.setMedicineId(999L);

        when(prescriptionItemRepository.findByIdAndPatientId(1L, 1L)).thenReturn(Optional.of(prescriptionItem));
        when(medicineRepository.findById(999L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(MedicineNotFoundException.class, () ->
            prescriptionItemBusinessService.updatePrescriptionItemForUser(1L, updateRequest, 1L));

        verify(medicineRepository).findById(999L);
        verify(prescriptionItemRepository, never()).save(any());
    }

    @Test
    void updatePrescriptionItemForUser_PartialUpdate() {
        // Given - Only update dose amount, leave other fields null
        UpdatePrescriptionItemRequest partialRequest = UpdatePrescriptionItemRequest.builder()
                .doseAmount(25.0)
                .build();

        when(prescriptionItemRepository.findByIdAndPatientId(1L, 1L)).thenReturn(Optional.of(prescriptionItem));
        when(prescriptionItemRepository.save(any(PrescriptionItem.class))).thenReturn(prescriptionItem);

        // When
        PrescriptionItem result = prescriptionItemBusinessService.updatePrescriptionItemForUser(1L, partialRequest, 1L);

        // Then
        assertNotNull(result);
        verify(prescriptionItemRepository).save(any(PrescriptionItem.class));
        // Medicine should not be changed since medicineId is null
        verify(medicineRepository, never()).findById(any());
    }
}
