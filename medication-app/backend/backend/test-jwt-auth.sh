#!/bin/bash

# Test JWT Authentication APIs
# Đảm bảo server đang chạy trên port 8081

BASE_URL="http://localhost:8081"

echo "🧪 Testing JWT Authentication APIs"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test 1: Register user (Step 1 - Send OTP)
echo -e "\n${YELLOW}1. Testing User Registration (Send OTP)${NC}"
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123",
    "fullName": "Test User",
    "phoneNumber": "0123456789"
  }')

echo "Response: $REGISTER_RESPONSE"

if echo "$REGISTER_RESPONSE" | grep -q "OTP_SENT"; then
    echo -e "${GREEN}✅ Registration OTP sent successfully${NC}"
else
    echo -e "${RED}❌ Registration failed${NC}"
fi

# Test 2: Verify OTP (sẽ fail vì không có OTP thật)
echo -e "\n${YELLOW}2. Testing OTP Verification (will fail - no real OTP)${NC}"
OTP_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/verify-otp" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "otpCode": "123456"
  }')

echo "Response: $OTP_RESPONSE"

# Test 3: Try to access protected endpoint without token
echo -e "\n${YELLOW}3. Testing Protected Endpoint Without Token${NC}"
PROTECTED_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/medicines" \
  -H "Content-Type: application/json")

echo "Response: $PROTECTED_RESPONSE"

if echo "$PROTECTED_RESPONSE" | grep -q "401\|403\|Unauthorized\|Forbidden"; then
    echo -e "${GREEN}✅ Protected endpoint correctly requires authentication${NC}"
else
    echo -e "${RED}❌ Protected endpoint should require authentication${NC}"
fi

# Test 4: Test login with non-existent user
echo -e "\n${YELLOW}4. Testing Login with Non-existent User${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "wrongpassword"
  }')

echo "Response: $LOGIN_RESPONSE"

if echo "$LOGIN_RESPONSE" | grep -q "401\|400\|không đúng\|not found"; then
    echo -e "${GREEN}✅ Login correctly rejects invalid credentials${NC}"
else
    echo -e "${RED}❌ Login should reject invalid credentials${NC}"
fi

# Test 5: Test refresh token without valid token
echo -e "\n${YELLOW}5. Testing Refresh Token Without Valid Token${NC}"
REFRESH_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/refresh-token" \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "invalid-token"
  }')

echo "Response: $REFRESH_RESPONSE"

if echo "$REFRESH_RESPONSE" | grep -q "401\|400\|không hợp lệ\|invalid"; then
    echo -e "${GREEN}✅ Refresh token correctly rejects invalid token${NC}"
else
    echo -e "${RED}❌ Refresh token should reject invalid token${NC}"
fi

# Test 6: Test /me endpoint without token
echo -e "\n${YELLOW}6. Testing /me Endpoint Without Token${NC}"
ME_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/auth/me" \
  -H "Content-Type: application/json")

echo "Response: $ME_RESPONSE"

if echo "$ME_RESPONSE" | grep -q "401\|403\|Unauthorized"; then
    echo -e "${GREEN}✅ /me endpoint correctly requires authentication${NC}"
else
    echo -e "${RED}❌ /me endpoint should require authentication${NC}"
fi

# Test 7: Test logout
echo -e "\n${YELLOW}7. Testing Logout${NC}"
LOGOUT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/logout" \
  -H "Content-Type: application/json")

echo "Response: $LOGOUT_RESPONSE"

if echo "$LOGOUT_RESPONSE" | grep -q "LOGOUT_SUCCESS\|thành công"; then
    echo -e "${GREEN}✅ Logout works (even without authentication)${NC}"
else
    echo -e "${RED}❌ Logout failed${NC}"
fi

echo -e "\n${YELLOW}=================================="
echo -e "🏁 JWT Authentication Test Complete"
echo -e "====================================${NC}"

echo -e "\n${YELLOW}📝 Notes:${NC}"
echo "- OTP verification will fail because we don't have real OTP codes"
echo "- To fully test, you need to:"
echo "  1. Configure email settings in application.properties"
echo "  2. Use real email to receive OTP"
echo "  3. Complete registration flow"
echo "  4. Test login with registered user"
echo "  5. Use JWT token to access protected endpoints"
