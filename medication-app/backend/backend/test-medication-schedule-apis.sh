#!/bin/bash

# Comprehensive test script for Medication Schedule APIs
# Tests both Anonymous and Registered user endpoints

set -e

BASE_URL="http://localhost:8081"
ANONYMOUS_USER_ID="550e8400-e29b-41d4-a716-446655440000"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to print test results
print_test_result() {
    local test_name="$1"
    local status_code="$2"
    local expected_code="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$status_code" -eq "$expected_code" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $test_name (HTTP $status_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAIL${NC}: $test_name (Expected HTTP $expected_code, got $status_code)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Function to make HTTP request and return status code
make_request() {
    local method="$1"
    local url="$2"
    local headers="$3"
    local data="$4"

    if [ -n "$data" ]; then
        curl -s -o /dev/null -w "%{http_code}" -X "$method" "$url" \
             -H "Content-Type: application/json" \
             $headers \
             -d "$data" \
             --max-time 10
    else
        curl -s -o /dev/null -w "%{http_code}" -X "$method" "$url" \
             $headers \
             --max-time 10
    fi
}

echo -e "${BLUE}🧪 Starting Medication Schedule API Tests${NC}"
echo "=================================================="

# Check if server is running
echo -e "${YELLOW}📡 Checking server status...${NC}"
SERVER_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/actuator/health" || echo "000")
if [ "$SERVER_STATUS" != "200" ]; then
    echo -e "${RED}❌ Server is not running on $BASE_URL${NC}"
    echo "Please start the server with: ./gradlew bootRun"
    exit 1
fi
echo -e "${GREEN}✅ Server is running${NC}"

echo ""
echo -e "${BLUE}🔍 Testing Anonymous User APIs${NC}"
echo "=================================="

# Test 1: Get all schedules (pagination)
echo -e "${YELLOW}Testing GET /api/anonymous/medication-schedules${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules?page=0&size=10" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Get all schedules with pagination" "$STATUS" "200"

# Test 2: Get schedules by day of week
echo -e "${YELLOW}Testing GET /api/anonymous/medication-schedules/by-day/{dayOfWeek}${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/by-day/1" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Get schedules by day of week (Monday)" "$STATUS" "200"

# Test 3: Get schedules by time range
echo -e "${YELLOW}Testing GET /api/anonymous/medication-schedules/by-time-range${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/by-time-range?startTime=08:00&endTime=18:00" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Get schedules by time range" "$STATUS" "200"

# Test 4: Get schedules by dose unit
echo -e "${YELLOW}Testing GET /api/anonymous/medication-schedules/by-dose-unit/{doseUnit}${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/by-dose-unit/mg" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Get schedules by dose unit" "$STATUS" "200"

# Test 5: Get schedules by dose range
echo -e "${YELLOW}Testing GET /api/anonymous/medication-schedules/by-dose-range${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/by-dose-range?minDose=1.0&maxDose=100.0" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Get schedules by dose range" "$STATUS" "200"

# Test 6: Search schedules by notes
echo -e "${YELLOW}Testing GET /api/anonymous/medication-schedules/search${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/search?searchText=morning" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Search schedules by notes" "$STATUS" "200"

# Test 7: Count schedules
echo -e "${YELLOW}Testing GET /api/anonymous/medication-schedules/count${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/count" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Count schedules" "$STATUS" "200"

# Test 8: Get active schedules
echo -e "${YELLOW}Testing GET /api/anonymous/medication-schedules/active${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/active?page=0&size=10" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Get active schedules" "$STATUS" "200"

# Test 9: Invalid day of week (should return 400)
echo -e "${YELLOW}Testing invalid day of week${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/by-day/8" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Invalid day of week (should return 400)" "$STATUS" "400"

# Test 10: Invalid time range (should return 400)
echo -e "${YELLOW}Testing invalid time range${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/by-time-range?startTime=18:00&endTime=08:00" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Invalid time range (should return 400)" "$STATUS" "400"

# Test 11: Empty search text (should return 400)
echo -e "${YELLOW}Testing empty search text${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/search?searchText=" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Empty search text (should return 400)" "$STATUS" "400"

# Test 12: Invalid dose range (should return 400)
echo -e "${YELLOW}Testing invalid dose range${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/anonymous/medication-schedules/by-dose-range?minDose=100.0&maxDose=1.0" "-H 'X-Anonymous-User-Id: $ANONYMOUS_USER_ID'")
print_test_result "Invalid dose range (should return 400)" "$STATUS" "400"

echo ""
echo -e "${BLUE}🔐 Testing Registered User APIs (without JWT - should fail)${NC}"
echo "=========================================================="

# Test 13: Get all schedules without JWT (should return 401/403)
echo -e "${YELLOW}Testing GET /api/v1/medication-schedules without JWT${NC}"
STATUS=$(make_request "GET" "$BASE_URL/api/v1/medication-schedules")
print_test_result "Get schedules without JWT (should return 401/403)" "$STATUS" "401"

echo ""
echo -e "${BLUE}📊 Test Summary${NC}"
echo "================"
echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed!${NC}"
    exit 0
else
    echo -e "${RED}❌ Some tests failed. Please check the API implementation.${NC}"
    exit 1
fi
