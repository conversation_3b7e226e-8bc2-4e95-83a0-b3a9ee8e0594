#!/bin/bash

# Test script để kiểm tra Swagger UI và JWT authentication

BASE_URL="http://localhost:8081"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Swagger UI and JWT Authentication${NC}"
echo "=================================================="

# Test 1: Check if server is running
echo -e "\n${YELLOW}1. Checking if server is running${NC}"
if curl -s "$BASE_URL/actuator/health" > /dev/null; then
    echo -e "${GREEN}✅ Server is running${NC}"
else
    echo -e "${RED}❌ Server is not running. Please start the server first.${NC}"
    echo "Run: ./gradlew bootRun"
    exit 1
fi

# Test 2: Check Swagger UI accessibility
echo -e "\n${YELLOW}2. Checking Swagger UI accessibility${NC}"
SWAGGER_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/swagger-ui/index.html")
if [ "$SWAGGER_RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Swagger UI is accessible at $BASE_URL/swagger-ui/index.html${NC}"
else
    echo -e "${RED}❌ Swagger UI is not accessible (HTTP $SWAGGER_RESPONSE)${NC}"
fi

# Test 3: Check OpenAPI docs
echo -e "\n${YELLOW}3. Checking OpenAPI documentation${NC}"
API_DOCS_RESPONSE=$(curl -s "$BASE_URL/v3/api-docs")
if echo "$API_DOCS_RESPONSE" | grep -q "bearerAuth"; then
    echo -e "${GREEN}✅ OpenAPI docs contain JWT bearerAuth configuration${NC}"
else
    echo -e "${RED}❌ OpenAPI docs missing JWT bearerAuth configuration${NC}"
fi

# Test 4: Check if protected endpoints require authentication
echo -e "\n${YELLOW}4. Testing protected endpoint without token${NC}"
PROTECTED_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/api/medicines")
if [ "$PROTECTED_RESPONSE" = "401" ] || [ "$PROTECTED_RESPONSE" = "403" ]; then
    echo -e "${GREEN}✅ Protected endpoint correctly requires authentication (HTTP $PROTECTED_RESPONSE)${NC}"
else
    echo -e "${RED}❌ Protected endpoint should require authentication (HTTP $PROTECTED_RESPONSE)${NC}"
fi

# Test 5: Register a test user
echo -e "\n${YELLOW}5. Registering test user${NC}"
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "fullName": "Test User"
  }')

if echo "$REGISTER_RESPONSE" | grep -q "success\|created\|registered"; then
    echo -e "${GREEN}✅ Test user registered successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Test user might already exist or registration failed${NC}"
fi

# Test 6: Login and get JWT token
echo -e "\n${YELLOW}6. Testing login to get JWT token${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }')

if echo "$LOGIN_RESPONSE" | grep -q "accessToken"; then
    echo -e "${GREEN}✅ Login successful, JWT token received${NC}"
    
    # Extract token
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$TOKEN" ]; then
        echo -e "${GREEN}✅ JWT Token extracted: ${TOKEN:0:50}...${NC}"
        
        # Test 7: Use JWT token to access protected endpoint
        echo -e "\n${YELLOW}7. Testing protected endpoint with JWT token${NC}"
        PROTECTED_WITH_TOKEN_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" \
          -H "Authorization: Bearer $TOKEN" \
          "$BASE_URL/api/medicines")
        
        if [ "$PROTECTED_WITH_TOKEN_RESPONSE" = "200" ]; then
            echo -e "${GREEN}✅ Protected endpoint accessible with JWT token (HTTP $PROTECTED_WITH_TOKEN_RESPONSE)${NC}"
        else
            echo -e "${RED}❌ Protected endpoint not accessible with JWT token (HTTP $PROTECTED_WITH_TOKEN_RESPONSE)${NC}"
        fi
    else
        echo -e "${RED}❌ Could not extract JWT token from login response${NC}"
    fi
else
    echo -e "${RED}❌ Login failed or no JWT token in response${NC}"
    echo "Response: $LOGIN_RESPONSE"
fi

echo -e "\n${BLUE}📋 Summary${NC}"
echo "=================================================="
echo -e "• Swagger UI: $BASE_URL/swagger-ui/index.html"
echo -e "• OpenAPI Docs: $BASE_URL/v3/api-docs"
echo -e "• Test with JWT token in Swagger UI using the 'Authorize' button"
echo -e "• Use format: Bearer <your-jwt-token>"

echo -e "\n${GREEN}🎉 Test completed!${NC}"
