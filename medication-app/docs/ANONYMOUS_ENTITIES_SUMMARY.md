# 🔐 Anonymous Support - Entities & Enums Summary

## 📋 Overview

Tài liệu này tóm tắt các enum và entities đã được tạo để hỗ trợ **Authentication Optional Pattern** với anonymous users.

## ✅ **Đã hoàn thành:**

### **1. Enums**

#### **UserType.java** ✅

```java
public enum UserType {
    ANONYMOUS,    // User ẩn danh - không đăng ký tài khoản
    REGISTERED,   // User đã đăng ký - có tài khoản chính thức
    PREMIUM       // User premium - tính năng cao cấp (future)
}
```

**Key Features:**

- ✅ UUID-based anonymous identification
- ✅ Helper methods: `isAnonymous()`, `isRegistered()`, `isPremium()`
- ✅ Migration support: `canMigrateData()`
- ✅ Default value: `REGISTERED`

#### **AnonymousSessionStatus.java** ✅

```java
public enum AnonymousSessionStatus {
    ACTIVE,       // Session đang hoạt động
    EXPIRED,      // Session đã hết hạn (30 ngày)
    MIGRATED,     // Session đã được migrate sang registered user
    CLEANED       // Session đã được cleanup
}
```

**Key Features:**

- ✅ Session lifecycle management
- ✅ Helper methods: `isActive()`, `canUse()`, `canMigrate()`
- ✅ Default value: `ACTIVE`

### **2. Entities**

#### **AnonymousSession.java** ✅

```java
@Entity
@Table(name = "anonymous_sessions")
public class AnonymousSession {
    private UUID id;                           // Primary key
    private String anonymousUserId;            // UUID string cho client
    private String deviceId;                   // Device tracking
    private String appVersion;                 // App version tracking
    private LocalDateTime createdAt;           // Creation time
    private LocalDateTime lastActivityAt;      // Last activity
    private Boolean dataSynced;                // Sync status
    private Long migratedToUserId;             // Migration tracking
    private LocalDateTime migratedAt;          // Migration time
    private AnonymousSessionStatus status;     // Session status
    private LocalDateTime updatedAt;           // Update time
}
```

**Key Features:**

- ✅ UUID-based identification
- ✅ Device và app version tracking
- ✅ Session expiration (30 days)
- ✅ Migration tracking
- ✅ Helper methods: `isActive()`, `canMigrate()`, `isExpired()`
- ✅ Performance indexes

#### **User.java** ✅ **Updated**

```java
@Entity
@Table(name = "users")
public class User {
    // Existing fields...
    private UserType userType = UserType.REGISTERED;    // 🆕 User type
    private String anonymousUserId;                     // 🆕 Anonymous ID
}
```

**Key Updates:**

- ✅ Removed `@NotBlank` constraints cho email và password (anonymous users)
- ✅ Added `userType` field với default `REGISTERED`
- ✅ Added `anonymousUserId` field
- ✅ Added performance indexes
- ✅ Added helper methods: `isAnonymous()`, `isRegistered()`, `isPremium()`

#### **Medicine.java** ✅ **Updated**

```java
@Entity
@Table(name = "medicines")
public class Medicine {
    // Existing fields...
    private UserType userType = UserType.REGISTERED;    // 🆕 User type
    private String anonymousUserId;                     // 🆕 Anonymous ID
}
```

**Key Updates:**

- ✅ Removed `@NotNull` constraint cho `user` relationship
- ✅ Added `userType` field với default `REGISTERED`
- ✅ Added `anonymousUserId` field
- ✅ Added performance indexes
- ✅ Added helper methods: `isAnonymous()`, `isRegistered()`

#### **Prescription.java** ✅ **Updated**

```java
@Entity
@Table(name = "prescriptions")
public class Prescription {
    // Existing fields...
    private UserType userType = UserType.REGISTERED;    // 🆕 User type
    private String anonymousUserId;                     // 🆕 Anonymous ID
}
```

**Key Updates:**

- ✅ Removed `@NotNull` constraints cho `patient` và `prescribedBy` relationships
- ✅ Added `userType` field với default `REGISTERED`
- ✅ Added `anonymousUserId` field
- ✅ Added performance indexes
- ✅ Added helper methods: `isAnonymous()`, `isRegistered()`

#### **AuditLog.java** ✅ **Updated**

```java
@Entity
@Table(name = "audit_logs")
public class AuditLog {
    // Existing fields...
    private String anonymousUserId;                     // 🆕 Anonymous ID
}
```

**Key Updates:**

- ✅ Removed `@NotNull` constraint cho `user` relationship
- ✅ Added `anonymousUserId` field
- ✅ Added performance index

## 🏗️ **Database Schema Support**

### **Migration Script** ✅

- ✅ `V24__Add_anonymous_user_support.sql` đã được tạo
- ✅ Enum types: `user_type_enum`, `anonymous_session_status_enum`
- ✅ New table: `anonymous_sessions`
- ✅ Updated tables: `users`, `medicines`, `prescriptions`, `audit_logs`
- ✅ Performance indexes cho tất cả anonymous queries
- ✅ Data integrity constraints

### **Indexes Created** ✅

```sql
-- Users table
CREATE INDEX idx_user_type ON users(user_type);
CREATE INDEX idx_user_anonymous_id ON users(anonymous_user_id);

-- Medicines table
CREATE INDEX idx_medicine_user_type ON medicines(user_type);
CREATE INDEX idx_medicine_anonymous_id ON medicines(anonymous_user_id);

-- Prescriptions table
CREATE INDEX idx_prescription_user_type ON prescriptions(user_type);
CREATE INDEX idx_prescription_anonymous_id ON prescriptions(anonymous_user_id);

-- Anonymous sessions table
CREATE INDEX idx_anonymous_sessions_user_id ON anonymous_sessions(anonymous_user_id);
CREATE INDEX idx_anonymous_sessions_last_activity ON anonymous_sessions(last_activity_at);
CREATE INDEX idx_anonymous_sessions_migrated ON anonymous_sessions(migrated_to_user_id);
CREATE INDEX idx_anonymous_sessions_status ON anonymous_sessions(status);
CREATE INDEX idx_anonymous_sessions_device ON anonymous_sessions(device_id);

-- Audit logs table
CREATE INDEX idx_audit_anonymous_user_id ON audit_logs(anonymous_user_id);
```

## 🔍 **Key Design Decisions**

### **1. UUID-based Anonymous Identification**

- ✅ Anonymous users được identify bằng UUID string
- ✅ Client có thể generate UUID locally
- ✅ No collision risk với UUID v4
- ✅ Easy migration sang registered user

### **2. Dual Mode Support**

- ✅ Tất cả entities hỗ trợ cả anonymous và registered users
- ✅ `userType` field để phân biệt loại user
- ✅ `anonymousUserId` field cho anonymous data
- ✅ `user` relationship cho registered data

### **3. Migration Strategy**

- ✅ Anonymous data có thể migrate sang registered user
- ✅ Atomic migration operations
- ✅ Migration tracking trong `AnonymousSession`
- ✅ Data consistency guarantees

### **4. Performance Optimization**

- ✅ Indexes cho tất cả anonymous queries
- ✅ Composite indexes cho common query patterns
- ✅ Efficient data retrieval cho cả anonymous và registered users

## 🚀 **Next Steps**

### **Phase 2: Repository Layer** (1-2 tuần)

- [ ] Create `AnonymousSessionRepository`
- [ ] Update `UserRepository` với anonymous support
- [ ] Update `MedicineRepository` với anonymous queries
- [ ] Update `PrescriptionRepository` với anonymous queries
- [ ] Create migration query methods

### **Phase 3: Service Layer** (2-3 tuần)

- [ ] Create `AnonymousUserService`
- [ ] Create `AnonymousSessionService`
- [ ] Create `MigrationService`
- [ ] Update existing services với anonymous support

### **Phase 4: Controller Layer** (1-2 tuần)

- [ ] Create `AnonymousController`
- [ ] Update `AuthController` với migration support
- [ ] Create anonymous API endpoints
- [ ] Implement anonymous session validation

### **Phase 5: Security Configuration** (1 tuần)

- [ ] Update `SecurityConfig` cho anonymous access
- [ ] Create anonymous session validation filter
- [ ] Implement rate limiting cho anonymous endpoints
- [ ] Add anonymous request interceptor

## 📊 **Benefits Achieved**

### **User Experience**

- ✅ **Immediate Access**: Users can start using app instantly
- ✅ **No Friction**: No registration barrier to entry
- ✅ **Seamless Upgrade**: Easy migration to registered account
- ✅ **Data Preservation**: All anonymous data preserved during migration

### **Technical Benefits**

- ✅ **Scalable Architecture**: Supports both anonymous and authenticated users
- ✅ **Data Isolation**: Secure separation of anonymous and registered data
- ✅ **Performance Optimized**: Efficient queries for both user types
- ✅ **Future Proof**: Easy to extend with premium features

---

**Status**: ✅ **Phase 1 Complete** - Enums & Entities  
**Last Updated**: December 2024  
**Version**: 1.0  
**Next Phase**: Repository Layer Implementation
