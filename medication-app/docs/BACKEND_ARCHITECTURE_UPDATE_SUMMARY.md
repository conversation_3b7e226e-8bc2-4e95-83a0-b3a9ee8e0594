# 🏗️ Backend Architecture Update Summary

## 📋 Overview

Tài liệu này tóm tắt việc cập nhật `BACKEND_ARCHITECTURE.md` đ<PERSON> bổ sung các phần còn thiếu về database migrations, entities, và enums cho Authentication Optional Pattern.

## ✅ **C<PERSON>c phần đã được bổ sung:**

### **1. 🆕 Enums & Constants**

```java
// User Types
public enum UserType {
    ANONYMOUS,    // Không đăng nhập, dùng local storage
    REGISTERED,   // Đã đăng nhập, có family
    PREMIUM       // Premium features (future)
}

// Anonymous Session Management
public enum AnonymousSessionStatus {
    ACTIVE,       // Session đang hoạt động
    EXPIRED,      // Session đã hết hạn (30 ngày)
    MIGRATED,     // Session đã được migrate sang registered user
    CLEANED       // Session đã được cleanup
}

// Family Member Types
public enum MemberType {
    DEPENDENT,    // Không có tài khoản (trẻ em, người già)
    INDEPENDENT   // Có tài khoản riêng
}

// Family Roles
public enum FamilyRole {
    OWNER,        // Chủ gia đình
    ADMIN,        // Quản trị viên
    MEMBER        // Thành viên thường
}

// Gender
public enum Gender {
    MALE, FEMALE, OTHER
}

// Notification Types
public enum NotificationType {
    REMINDER,     // Nhắc nhở uống thuốc
    ALERT,        // Cảnh báo
    INFO          // Thông tin
}

// Notification Status
public enum NotificationStatus {
    PENDING, SENT, READ
}

// Medication Schedule Status
public enum ScheduleStatus {
    PENDING, TAKEN, SKIPPED
}

// Audit Action Types
public enum AuditAction {
    CREATE, UPDATE, DELETE, LOGIN, LOGOUT, MIGRATE
}
```

### **2. 🆕 Database Migration Scripts**

#### **V1\_\_init.sql - Initial Schema**

- `users` table với basic fields
- `families` table với owner relationship

#### **V2\_\_add_audit_logs.sql**

- `audit_logs` table cho activity tracking

#### **V3\_\_add_notifications.sql**

- `notifications` table cho user notifications

#### **V20\_\_add_anonymous_user_support.sql**

- Tạo `user_type_enum` và `anonymous_session_status_enum`
- Cập nhật `users` table với `user_type` và `anonymous_user_id`
- Tạo `anonymous_sessions` table
- Cập nhật `audit_logs` với `anonymous_user_id`
- Thêm indexes cho performance optimization

### **3. 🆕 JPA Entities Structure**

#### **User Entity**

```java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "email", unique = true)
    private String email;

    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false)
    private UserType userType = UserType.REGISTERED;

    @Column(name = "anonymous_user_id")
    private String anonymousUserId;

    // ... other fields
}
```

#### **AnonymousSession Entity**

```java
@Entity
@Table(name = "anonymous_sessions")
public class AnonymousSession {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "anonymous_user_id", nullable = false, unique = true)
    private String anonymousUserId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AnonymousSessionStatus status = AnonymousSessionStatus.ACTIVE;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    // ... other fields
}
```

#### **Medicine Entity với Anonymous Support**

```java
@Entity
@Table(name = "medicines")
public class Medicine {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false)
    private UserType userType = UserType.REGISTERED;

    @Column(name = "anonymous_user_id")
    private String anonymousUserId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    // ... other fields
}
```

#### **Prescription Entity với Anonymous Support**

```java
@Entity
@Table(name = "prescriptions")
public class Prescription {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false)
    private UserType userType = UserType.REGISTERED;

    @Column(name = "anonymous_user_id")
    private String anonymousUserId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id")
    private User patient;

    // ... other fields
}
```

#### **AuditLog Entity với Anonymous Support**

```java
@Entity
@Table(name = "audit_logs")
public class AuditLog {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Column(name = "anonymous_user_id")
    private String anonymousUserId;

    @Enumerated(EnumType.STRING)
    @Column(name = "action", nullable = false)
    private AuditAction action;

    // ... other fields
}
```

### **4. 🆕 Repository Methods**

#### **AnonymousSessionRepository**

```java
@Repository
public interface AnonymousSessionRepository extends JpaRepository<AnonymousSession, UUID> {
    Optional<AnonymousSession> findByAnonymousUserId(String anonymousUserId);

    @Modifying
    @Query("UPDATE AnonymousSession a SET a.migratedToUserId = :userId, " +
           "a.migratedAt = :migratedAt, a.status = 'MIGRATED' " +
           "WHERE a.anonymousUserId = :anonymousUserId")
    void updateMigrationStatus(String anonymousUserId, Long userId, LocalDateTime migratedAt);

    @Query("SELECT a FROM AnonymousSession a WHERE a.lastActivityAt < :expiryDate")
    List<AnonymousSession> findExpiredSessions(LocalDateTime expiryDate);
}
```

#### **MedicineRepository với Anonymous Support**

```java
@Repository
public interface MedicineRepository extends JpaRepository<Medicine, UUID> {
    // For registered users
    List<Medicine> findByUserAndFamily(User user, Family family);
    List<Medicine> findByUser(User user);

    // For anonymous users
    List<Medicine> findByAnonymousUserIdAndUserType(String anonymousUserId, UserType userType);

    // Migration methods
    @Modifying
    @Query("UPDATE Medicine m SET m.userType = :userType, m.user = :user, " +
           "m.anonymousUserId = NULL WHERE m.anonymousUserId = :anonymousUserId")
    int updateUserTypeAndUserId(String anonymousUserId, User user, UserType userType);
}
```

#### **PrescriptionRepository với Anonymous Support**

```java
@Repository
public interface PrescriptionRepository extends JpaRepository<Prescription, UUID> {
    // For registered users
    List<Prescription> findByPatient(User patient);

    // For anonymous users
    List<Prescription> findByAnonymousUserIdAndUserType(String anonymousUserId, UserType userType);

    // Migration methods
    @Modifying
    @Query("UPDATE Prescription p SET p.userType = :userType, p.patient = :patient, " +
           "p.anonymousUserId = NULL WHERE p.anonymousUserId = :anonymousUserId")
    int updateUserTypeAndPatientId(String anonymousUserId, User patient, UserType userType);
}
```

### **5. 🆕 Service Layer Implementation**

#### **AnonymousUserService**

```java
@Service
@Transactional
public class AnonymousUserService {

    public AnonymousSession createAnonymousSession(String deviceId, String appVersion) {
        String anonymousUserId = UUID.randomUUID().toString();
        AnonymousSession session = AnonymousSession.builder()
            .anonymousUserId(anonymousUserId)
            .deviceId(deviceId)
            .appVersion(appVersion)
            .status(AnonymousSessionStatus.ACTIVE)
            .createdAt(LocalDateTime.now())
            .lastActivityAt(LocalDateTime.now())
            .dataSynced(false)
            .build();

        return anonymousSessionRepository.save(session);
    }

    public List<Medicine> getMedicinesForAnonymousUser(String anonymousUserId) {
        return medicineRepository.findByAnonymousUserIdAndUserType(
            anonymousUserId, UserType.ANONYMOUS);
    }

    // ... other methods
}
```

#### **MigrationService**

```java
@Service
@Transactional
public class MigrationService {

    public MigrationResult migrateAnonymousData(String anonymousUserId, User registeredUser) {
        MigrationResult result = new MigrationResult();

        try {
            // Migrate medicines
            int medicinesMigrated = medicineRepository
                .updateUserTypeAndUserId(anonymousUserId, registeredUser, UserType.REGISTERED);
            result.setMedicinesMigrated(medicinesMigrated);

            // Migrate prescriptions
            int prescriptionsMigrated = prescriptionRepository
                .updateUserTypeAndPatientId(anonymousUserId, registeredUser, UserType.REGISTERED);
            result.setPrescriptionsMigrated(prescriptionsMigrated);

            // Update anonymous session status
            anonymousSessionRepository.updateMigrationStatus(
                anonymousUserId, registeredUser.getId(), LocalDateTime.now());

            result.setSuccess(true);
            result.setMessage("Migration completed successfully");

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("Migration failed: " + e.getMessage());
            throw new BusinessException("MIGRATION_FAILED", "Data migration failed");
        }

        return result;
    }
}
```

## 🎯 **Key Features Implemented:**

### **Database Design**

- ✅ **Complete Migration Scripts**: V1, V2, V3, V20
- ✅ **Enum Definitions**: All business enums defined
- ✅ **Entity Relationships**: Proper JPA mappings
- ✅ **Anonymous Support**: All entities support anonymous users
- ✅ **Migration Support**: Fields for data migration tracking

### **Repository Layer**

- ✅ **Anonymous Queries**: Methods for anonymous user data
- ✅ **Migration Methods**: Atomic migration operations
- ✅ **Performance Indexes**: Optimized queries
- ✅ **Type Safety**: Proper enum usage

### **Service Layer**

- ✅ **Anonymous Services**: Dedicated anonymous user operations
- ✅ **Migration Services**: Secure data migration
- ✅ **Session Management**: Anonymous session lifecycle
- ✅ **Error Handling**: Comprehensive exception handling

## 🚀 **Implementation Ready:**

Tất cả các thành phần cần thiết đã được định nghĩa trong `BACKEND_ARCHITECTURE.md`:

1. **Enums**: Complete business enums
2. **Entities**: JPA entities với anonymous support
3. **Migrations**: Database migration scripts
4. **Repositories**: Data access methods
5. **Services**: Business logic implementation
6. **Security**: Anonymous session management

## 📞 **Next Steps:**

1. **Review Architecture**: Confirm all components meet requirements
2. **Implementation**: Begin coding based on architecture
3. **Testing**: Create tests for all components
4. **Documentation**: Update API documentation

---

**Status**: ✅ Complete  
**Last Updated**: December 2024  
**Version**: 2.0  
**Reviewer**: Tech Lead
