# 🗄️ Database Schema Complete Summary

## 📋 Overview

Tài liệu này tóm tắt toàn bộ database schema đã được thiết kế cho ứng dụng Medication Management với Authentication Optional Pattern.

## 🎯 **Database Design Summary**

### **🏗️ Core Architecture**

- **Database**: PostgreSQL
- **ORM**: Spring Boot JPA/Hibernate
- **Pattern**: Authentication Optional Pattern
- **Support**: Anonymous + Registered users
- **Migration**: Flyway version control

## 📊 **Complete Table List**

### **1. Core User Tables**

| Table                | Purpose                  | Anonymous Support |
| -------------------- | ------------------------ | ----------------- |
| `users`              | User accounts & profiles | ✅                |
| `anonymous_sessions` | Anonymous user sessions  | ✅                |

### **2. Family Management Tables**

| Table                | Purpose            | Anonymous Support    |
| -------------------- | ------------------ | -------------------- |
| `families`           | Family groups      | ❌ (Registered only) |
| `family_members`     | Family membership  | ❌ (Registered only) |
| `family_invitations` | Family invitations | ❌ (Registered only) |

### **3. Medicine Management Tables**

| Table                  | Purpose               | Anonymous Support    |
| ---------------------- | --------------------- | -------------------- |
| `medicine_types`       | Medicine categories   | ✅                   |
| `medicines`            | Medicine inventory    | ✅                   |
| `prescriptions`        | Medical prescriptions | ✅                   |
| `prescription_items`   | Prescription details  | ✅                   |
| `medication_schedules` | Medication scheduling | ❌ (Registered only) |

### **4. System Tables**

| Table           | Purpose            | Anonymous Support    |
| --------------- | ------------------ | -------------------- |
| `notifications` | User notifications | ❌ (Registered only) |
| `audit_logs`    | Activity tracking  | ✅                   |

## 🔧 **Complete Enum Definitions**

### **1. User Management Enums**

```sql
-- User types
CREATE TYPE user_type_enum AS ENUM ('ANONYMOUS', 'REGISTERED', 'PREMIUM');

-- Anonymous session status
CREATE TYPE anonymous_session_status_enum AS ENUM ('ACTIVE', 'EXPIRED', 'MIGRATED', 'CLEANED');

-- Gender
CREATE TYPE gender_enum AS ENUM ('MALE', 'FEMALE', 'OTHER');
```

### **2. Family Management Enums**

```sql
-- Member types
CREATE TYPE member_type_enum AS ENUM ('DEPENDENT', 'INDEPENDENT');

-- Family roles
CREATE TYPE family_role_enum AS ENUM ('OWNER', 'ADMIN', 'MEMBER');

-- Invitation types
CREATE TYPE invitation_type_enum AS ENUM ('CREATE_ACCOUNT', 'VIEW_ONLY');

-- Invitation status
CREATE TYPE invitation_status_enum AS ENUM ('PENDING', 'ACCEPTED', 'REJECTED', 'EXPIRED');
```

### **3. System Enums**

```sql
-- Notification types
CREATE TYPE notification_type_enum AS ENUM ('REMINDER', 'ALERT', 'INFO');

-- Notification status
CREATE TYPE notification_status_enum AS ENUM ('PENDING', 'SENT', 'READ');

-- Schedule status
CREATE TYPE schedule_status_enum AS ENUM ('PENDING', 'TAKEN', 'SKIPPED');

-- Audit actions
CREATE TYPE audit_action_enum AS ENUM ('CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'MIGRATE');
```

## 📋 **Detailed Table Schemas**

### **1. USERS Table**

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE,
    password VARCHAR(255),
    họ_tên VARCHAR(255) NOT NULL,
    ngày_sinh DATE,
    giới_tính gender_enum,
    ảnh_đại_diện VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    user_type user_type_enum DEFAULT 'REGISTERED',
    anonymous_user_id VARCHAR(36),
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### **2. ANONYMOUS_SESSIONS Table**

```sql
CREATE TABLE anonymous_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    anonymous_user_id VARCHAR(36) NOT NULL UNIQUE,
    device_id VARCHAR(100),
    app_version VARCHAR(20),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_activity_at TIMESTAMP NOT NULL DEFAULT NOW(),
    data_synced BOOLEAN DEFAULT FALSE,
    migrated_to_user_id BIGINT REFERENCES users(id),
    migrated_at TIMESTAMP,
    status anonymous_session_status_enum DEFAULT 'ACTIVE'
);
```

### **3. FAMILIES Table**

```sql
CREATE TABLE families (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tên_gia_đình VARCHAR(255) NOT NULL,
    owner_id UUID NOT NULL REFERENCES users(id),
    mã_invite VARCHAR(50) UNIQUE NOT NULL,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### **4. FAMILY_MEMBERS Table**

```sql
CREATE TABLE family_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    family_id UUID NOT NULL REFERENCES families(id),
    user_id UUID REFERENCES users(id),
    managed_by UUID REFERENCES users(id),
    tên VARCHAR(255) NOT NULL,
    tuổi INTEGER,
    giới_tính gender_enum,
    ảnh VARCHAR(500),
    member_type member_type_enum NOT NULL,
    can_login BOOLEAN NOT NULL,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE(family_id, tên, tuổi)
);
```

### **5. FAMILY_INVITATIONS Table**

```sql
CREATE TABLE family_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    family_id UUID NOT NULL REFERENCES families(id),
    invited_by UUID NOT NULL REFERENCES users(id),
    email VARCHAR(255) NOT NULL,
    member_name VARCHAR(255),
    invitation_type invitation_type_enum NOT NULL,
    trạng_thái invitation_status_enum NOT NULL,
    mã_invite VARCHAR(50) UNIQUE NOT NULL,
    hết_hạn TIMESTAMP NOT NULL,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### **6. MEDICINE_TYPES Table**

```sql
CREATE TABLE medicine_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tên_loại VARCHAR(100) NOT NULL,
    mô_tả TEXT,
    màu_sắc VARCHAR(20),
    icon VARCHAR(100),
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### **7. MEDICINES Table**

```sql
CREATE TABLE medicines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tên_thuốc VARCHAR(200) NOT NULL,
    loại_thuốc_id UUID NOT NULL REFERENCES medicine_types(id),
    liều_lượng VARCHAR(100),
    đơn_vị VARCHAR(50),
    hướng_dẫn TEXT,
    ảnh_thuốc VARCHAR(500),
    hạn_sử_dụng DATE,
    số_lượng INTEGER NOT NULL,
    user_type user_type_enum DEFAULT 'REGISTERED',
    anonymous_user_id VARCHAR(36),
    user_id UUID REFERENCES users(id),
    family_id UUID REFERENCES families(id),
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### **8. PRESCRIPTIONS Table**

```sql
CREATE TABLE prescriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_type user_type_enum DEFAULT 'REGISTERED',
    anonymous_user_id VARCHAR(36),
    patient_id UUID REFERENCES users(id),
    bác_sĩ VARCHAR(200),
    ngày_kê_đơn DATE NOT NULL,
    ngày_bắt_đầu DATE NOT NULL,
    ngày_kết_thúc DATE,
    ghi_chú TEXT,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### **9. PRESCRIPTION_ITEMS Table**

```sql
CREATE TABLE prescription_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    prescription_id UUID NOT NULL REFERENCES prescriptions(id),
    medicine_id UUID NOT NULL REFERENCES medicines(id),
    liều_lượng VARCHAR(100) NOT NULL,
    tần_suất VARCHAR(100) NOT NULL,
    thời_gian_uống VARCHAR(100),
    ghi_chú TEXT,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### **10. MEDICATION_SCHEDULES Table**

```sql
CREATE TABLE medication_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    prescription_id UUID NOT NULL REFERENCES prescriptions(id),
    member_id UUID NOT NULL REFERENCES family_members(id),
    medicine_id UUID NOT NULL REFERENCES medicines(id),
    thời_gian_uống TIME NOT NULL,
    liều_lượng VARCHAR(100) NOT NULL,
    trạng_thái schedule_status_enum NOT NULL,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### **11. NOTIFICATIONS Table**

```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    loại_thông_báo notification_type_enum NOT NULL,
    tiêu_đề VARCHAR(200) NOT NULL,
    nội_dung TEXT NOT NULL,
    trạng_thái notification_status_enum NOT NULL,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_gửi TIMESTAMP,
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### **12. AUDIT_LOGS Table**

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    anonymous_user_id VARCHAR(36),
    action audit_action_enum NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(50) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    ip_address VARCHAR(45),
    user_agent TEXT,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## 🔍 **Key Features**

### **Anonymous User Support**

- ✅ **UUID-based identification**: Anonymous users get unique UUID
- ✅ **Local storage**: Data stored locally for anonymous users
- ✅ **Session management**: 30-day session expiration
- ✅ **Data migration**: Seamless migration to registered accounts
- ✅ **Feature gating**: Limited features for anonymous users

### **Registered User Support**

- ✅ **Full features**: All application features available
- ✅ **Family management**: Multi-user family support
- ✅ **Cloud sync**: Multi-device synchronization
- ✅ **Data backup**: Cloud backup and restore
- ✅ **Advanced analytics**: Usage analytics and reporting

### **Security Features**

- ✅ **Data isolation**: Anonymous data isolated from registered users
- ✅ **Session validation**: UUID validation and rate limiting
- ✅ **Migration security**: Secure data migration process
- ✅ **Audit trail**: Complete activity tracking
- ✅ **Privacy protection**: No PII in anonymous sessions

## 📈 **Performance Optimizations**

### **Indexes**

- **User queries**: `idx_users_email`, `idx_users_user_type`, `idx_users_anonymous_id`
- **Anonymous queries**: `idx_anonymous_sessions_user_id`, `idx_anonymous_sessions_last_activity`
- **Medicine queries**: `idx_medicines_anonymous_user_id`, `idx_medicines_user_type`
- **Prescription queries**: `idx_prescriptions_anonymous_user_id`, `idx_prescriptions_user_type`
- **Audit queries**: `idx_audit_logs_anonymous_user_id`

### **Query Optimization**

- **Separate queries**: Anonymous vs registered data queries
- **Composite indexes**: User type + user ID combinations
- **Batch processing**: Large data migration operations
- **Cleanup scheduling**: Regular expired session cleanup

## 🚀 **Migration Strategy**

### **Database Migrations**

- **V1\_\_init.sql**: Initial schema setup
- **V2\_\_add_audit_logs.sql**: Audit logging system
- **V3\_\_add_notifications.sql**: Notification system
- **V20\_\_add_anonymous_user_support.sql**: Anonymous user support

### **Data Migration**

```sql
-- Anonymous to Registered migration
UPDATE medicines
SET user_type = 'REGISTERED',
    user_id = ?,
    anonymous_user_id = NULL
WHERE anonymous_user_id = ?;

UPDATE prescriptions
SET user_type = 'REGISTERED',
    patient_id = ?,
    anonymous_user_id = NULL
WHERE anonymous_user_id = ?;

UPDATE anonymous_sessions
SET migrated_to_user_id = ?,
    migrated_at = NOW(),
    status = 'MIGRATED'
WHERE anonymous_user_id = ?;
```

## 📞 **Implementation Status**

### **✅ Completed**

- [x] Complete table schemas
- [x] All enum definitions
- [x] Migration scripts
- [x] Index strategies
- [x] Security considerations
- [x] Performance optimizations

### **🔄 Next Steps**

- [ ] Implement JPA entities
- [ ] Create repository interfaces
- [ ] Implement service layer
- [ ] Add controller endpoints
- [ ] Write comprehensive tests
- [ ] Deploy and monitor

---

**Status**: ✅ Complete  
**Last Updated**: December 2024  
**Version**: 2.0  
**Reviewer**: Tech Lead
