# 🏗️ Backend Architecture Documentation

## 📋 Overview

Tài liệu này mô tả chi tiết kiến trúc backend của ứng dụng Medication Management, bao gồm công nghệ, c<PERSON>u trúc thư mục, dependencies và implementation guidelines.

**🆕 NEW: Authentication Optional Pattern** - Hỗ trợ cả Anonymous và Registered users

## 🎯 Technology Stack

### **Core Framework**

- **Spring Boot 3.3.0**: Main application framework
- **Java 17**: Programming language
- **Gradle**: Build tool và dependency management

### **Database & ORM**

- **PostgreSQL**: Primary database
- **Spring Data JPA**: Object-relational mapping
- **Hibernate**: JPA implementation

### **Security**

- **Spring Security**: Authentication & authorization
- **JWT (JSON Web Tokens)**: Stateless authentication
- **BCrypt**: Password hashing
- **🆕 Anonymous Session Management**: UUID-based anonymous identification

### **API & Communication**

- **Spring Web**: RESTful API framework
- **Jackson**: JSON serialization/deserialization
- **Validation API**: Request/response validation

### **Testing**

- **JUnit 5**: Unit testing framework
- **Spring Boot Test**: Integration testing
- **Spring Security Test**: Security testing
- **TestContainers**: Database testing (recommended)

### **Development Tools**

- **Spring Boot DevTools**: Development utilities
- **Spring Boot Actuator**: Monitoring & metrics
- **Lombok**: Code generation (recommended)

## 📁 Project Structure

```
medication-app/
├── backend/
│   └── backend/
│       ├── build.gradle                    # Build configuration
│       ├── settings.gradle                 # Project settings
│       ├── gradlew                         # Gradle wrapper
│       ├── gradlew.bat                     # Gradle wrapper (Windows)
│       └── src/
│           ├── main/
│           │   ├── java/
│           │   │   └── com/
│           │   │       └── medication/
│           │   │           ├── BackendApplication.java
│           │   │           ├── config/                    # Configuration classes
│           │   │           │   ├── SecurityConfig.java
│           │   │           │   ├── DatabaseConfig.java
│           │   │           │   ├── JwtConfig.java
│           │   │           │   └── CorsConfig.java
│           │   │           ├── controller/                # REST Controllers
│           │   │           │   ├── AuthController.java
│           │   │           │   ├── AnonymousController.java    # 🆕 Anonymous APIs
│           │   │           │   ├── FamilyController.java
│           │   │           │   ├── MedicineController.java
│           │   │           │   ├── PrescriptionController.java
│           │   │           │   ├── NotificationController.java
│           │   │           │   ├── DashboardController.java
│           │   │           │   └── UtilityController.java
│           │   │           ├── service/                   # Business Logic
│           │   │           │   ├── AuthService.java
│           │   │           │   ├── AnonymousUserService.java   # 🆕 Anonymous management
│           │   │           │   ├── AnonymousSessionService.java # 🆕 Session management
│           │   │           │   ├── FamilyService.java
│           │   │           │   ├── MedicineService.java
│           │   │           │   ├── PrescriptionService.java
│           │   │           │   ├── NotificationService.java
│           │   │           │   ├── DashboardService.java
│           │   │           │   └── UtilityService.java
│           │   │           ├── repository/                # Data Access Layer
│           │   │           │   ├── UserRepository.java
│           │   │           │   ├── AnonymousSessionRepository.java # 🆕 Anonymous sessions
│           │   │           │   ├── FamilyRepository.java
│           │   │           │   ├── MedicineRepository.java
│           │   │           │   ├── PrescriptionRepository.java
│           │   │           │   ├── NotificationRepository.java
│           │   │           │   └── AuditRepository.java
│           │   │           ├── entity/                    # JPA Entities
│           │   │           │   ├── User.java              # 🆕 Updated with UserType
│           │   │           │   ├── AnonymousSession.java  # 🆕 New entity
│           │   │           │   ├── Family.java
│           │   │           │   ├── FamilyMember.java
│           │   │           │   ├── MedicineType.java
│           │   │           │   ├── Medicine.java          # 🆕 Updated with anonymous support
│           │   │           │   ├── Prescription.java      # 🆕 Updated with anonymous support
│           │   │           │   ├── PrescriptionItem.java
│           │   │           │   ├── MedicationSchedule.java
│           │   │           │   ├── Notification.java
│           │   │           │   ├── AuditLog.java
│           │   │           │   └── SystemSettings.java
│           │   │           ├── dto/                       # Data Transfer Objects
│           │   │           │   ├── request/               # Request DTOs
│           │   │           │   │   ├── LoginRequest.java
│           │   │           │   │   ├── RegisterRequest.java
│           │   │           │   │   ├── AnonymousSessionRequest.java # 🆕 New DTO
│           │   │           │   │   ├── CreateFamilyRequest.java
│           │   │           │   │   ├── AddMedicineRequest.java
│           │   │           │   │   └── CreatePrescriptionRequest.java
│           │   │           │   ├── response/              # Response DTOs
│           │   │           │   │   ├── ApiResponse.java
│           │   │           │   │   ├── LoginResponse.java
│           │   │           │   │   ├── RegisterResponse.java # 🆕 Updated with migration info
│           │   │           │   │   ├── FamilyResponse.java
│           │   │           │   │   └── MedicineResponse.java
│           │   │           │   └── common/                # Common DTOs
│           │   │           │       ├── PaginationRequest.java
│           │   │           │       └── PaginationResponse.java
│           │   │           ├── exception/                 # Exception Handling
│           │   │           │   ├── GlobalExceptionHandler.java
│           │   │           │   ├── BusinessException.java
│           │   │           │   ├── ValidationException.java
│           │   │           │   └── SecurityException.java
│           │   │           ├── security/                  # Security Components
│           │   │           │   ├── JwtTokenProvider.java
│           │   │           │   ├── UserDetailsServiceImpl.java
│           │   │           │   ├── JwtAuthenticationFilter.java
│           │   │           │   ├── AnonymousRequestInterceptor.java # 🆕 New interceptor
│           │   │           │   └── SecurityUtils.java
│           │   │           ├── util/                      # Utility Classes
│           │   │           │   ├── DateUtils.java
│           │   │           │   ├── ValidationUtils.java
│           │   │           │   ├── FileUtils.java
│           │   │           │   └── EncryptionUtils.java
│           │   │           └── constant/                  # Constants
│           │   │               ├── ApiConstants.java
│           │   │               ├── SecurityConstants.java
│           │   │               └── ErrorConstants.java
│           │   └── resources/
│           │       ├── application.yml                    # Main configuration
│           │       ├── application-dev.yml                # Development config
│           │       ├── application-prod.yml               # Production config
│           │       ├── application-test.yml               # Test config
│           │       ├── db/
│           │       │   └── migration/                     # Database migrations
│           │       │       ├── V1__init.sql
│           │       │       ├── V2__add_audit_logs.sql
│           │       │       ├── V3__add_notifications.sql
│           │       │       └── V20__add_anonymous_user_support.sql # 🆕 New migration
│           │       └── static/                            # Static resources
│           └── test/
│               ├── java/
│               │   └── com/
│               │       └── medication/
│               │           ├── controller/                # Controller tests
│               │           ├── service/                   # Service tests
│               │           ├── repository/                # Repository tests
│               │           └── integration/               # Integration tests
│               └── resources/
│                   ├── application-test.yml               # Test configuration
│                   └── data/                              # Test data
│                       ├── test-users.sql
│                       └── test-families.sql
```

## 🔧 Dependencies Configuration

### **Current Dependencies (build.gradle)**

```gradle
dependencies {
    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'

    // Database
    runtimeOnly 'org.postgresql:postgresql'

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}
```

### **Recommended Additional Dependencies**

```gradle
dependencies {
    // Current dependencies...

    // JWT
    implementation 'io.jsonwebtoken:jjwt-api:0.12.3'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.3'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.3'

    // Validation
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    // Development Tools
    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    // Monitoring
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    // Code Generation
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // Database Migration
    implementation 'org.flywaydb:flyway-core'

    // Testing
    testImplementation 'org.testcontainers:postgresql:1.19.3'
    testImplementation 'org.testcontainers:junit-jupiter:1.19.3'

    // JSON Processing
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
}
```

## 🏛️ Architecture Patterns

### **🆕 Authentication Optional Pattern**

```
┌─────────────────────────────────────┐
│           Controller Layer          │  ← REST API Controllers
│     (Anonymous + Authenticated)     │
├─────────────────────────────────────┤
│            Service Layer            │  ← Business Logic
│     (Anonymous + Authenticated)     │
├─────────────────────────────────────┤
│          Repository Layer           │  ← Data Access
│     (Anonymous + Authenticated)     │
├─────────────────────────────────────┤
│           Database Layer            │  ← PostgreSQL
│     (Anonymous + Authenticated)     │
└─────────────────────────────────────┘
```

### **🆕 User Types & Access Control**

```java
public enum UserType {
    ANONYMOUS,    // Không đăng nhập, dùng local storage
    REGISTERED,   // Đã đăng nhập, có family
    PREMIUM       // Premium features (future)
}

// Anonymous session management
public enum AnonymousSessionStatus {
    ACTIVE,       // Session đang hoạt động
    EXPIRED,      // Session đã hết hạn (30 ngày)
    MIGRATED,     // Session đã được migrate sang registered user
    CLEANED       // Session đã được cleanup
}

// Family member types
public enum MemberType {
    DEPENDENT,    // Không có tài khoản (trẻ em, người già)
    INDEPENDENT   // Có tài khoản riêng
}

// Family member roles
public enum FamilyRole {
    OWNER,        // Chủ gia đình
    ADMIN,        // Quản trị viên
    MEMBER        // Thành viên thường
}

// Gender enum
public enum Gender {
    MALE, FEMALE, OTHER
}

// Notification types
public enum NotificationType {
    REMINDER,     // Nhắc nhở uống thuốc
    ALERT,        // Cảnh báo
    INFO          // Thông tin
}

// Notification status
public enum NotificationStatus {
    PENDING, SENT, READ
}

// Medication schedule status
public enum ScheduleStatus {
    PENDING, TAKEN, SKIPPED
}

// Audit action types
public enum AuditAction {
    CREATE, UPDATE, DELETE, LOGIN, LOGOUT, MIGRATE
}
```

### **🆕 Feature Matrix**

| Feature              | Anonymous | Registered | Premium |
| -------------------- | --------- | ---------- | ------- |
| View medicines       | ✅        | ✅         | ✅      |
| Add medicines        | ✅        | ✅         | ✅      |
| Create prescriptions | ✅        | ✅         | ✅      |
| View schedules       | ✅        | ✅         | ✅      |
| Family management    | ❌        | ✅         | ✅      |
| Multi-device sync    | ❌        | ✅         | ✅      |
| Cloud backup         | ❌        | ✅         | ✅      |
| Data export          | ❌        | ✅         | ✅      |
| Advanced analytics   | ❌        | ❌         | ✅      |

### **Package Structure Principles**

- **Separation of Concerns**: Each package has specific responsibility
- **Dependency Inversion**: High-level modules don't depend on low-level modules
- **Single Responsibility**: Each class has one reason to change
- **Open/Closed Principle**: Open for extension, closed for modification
- **🆕 Anonymous Support**: All layers support both anonymous and authenticated users

## 🔐 Security Architecture

### **🆕 Authentication Flow**

```
1. Anonymous User:
   App Start → Generate Anonymous User ID (UUID)
   API Call → Include X-Anonymous-User-Id header
   Backend → Validate anonymous session
   Response → Return data associated with anonymous user

2. Registered User:
   User Login → AuthController
   Validate Credentials → AuthService
   Generate JWT → JwtTokenProvider
   Return Token → Client
   Include Token in Requests → JwtAuthenticationFilter
   Validate Token → SecurityConfig
   Set Security Context → Controller Access
```

### **🆕 Anonymous Session Management**

```java
// Anonymous session validation
public boolean isValidAnonymousSession(String anonymousUserId) {
    AnonymousSession session = anonymousSessionRepository
        .findByAnonymousUserId(anonymousUserId)
        .orElse(null);

    if (session == null) return false;

    // Check if not migrated
    if (session.getMigratedToUserId() != null) return false;

    // Check if not expired (30 days)
    if (session.getLastActivityAt().isBefore(
        LocalDateTime.now().minusDays(30))) {
        return false;
    }

    // Update last activity
    session.setLastActivityAt(LocalDateTime.now());
    anonymousSessionRepository.save(session);

    return true;
}

// Anonymous session creation
public AnonymousSession createAnonymousSession(String deviceId, String appVersion) {
    String anonymousUserId = UUID.randomUUID().toString();
    return AnonymousSession.builder()
        .anonymousUserId(anonymousUserId)
        .deviceId(deviceId)
        .appVersion(appVersion)
        .status(AnonymousSessionStatus.ACTIVE)
        .createdAt(LocalDateTime.now())
        .lastActivityAt(LocalDateTime.now())
        .dataSynced(false)
        .build();
}

// Data migration from anonymous to registered
@Transactional
public MigrationResult migrateAnonymousData(String anonymousUserId, Long registeredUserId) {
    MigrationResult result = new MigrationResult();

    try {
        // Migrate medicines
        int medicinesMigrated = medicineRepository
            .updateUserTypeAndUserId(anonymousUserId, registeredUserId, UserType.REGISTERED);
        result.setMedicinesMigrated(medicinesMigrated);

        // Migrate prescriptions
        int prescriptionsMigrated = prescriptionRepository
            .updateUserTypeAndPatientId(anonymousUserId, registeredUserId, UserType.REGISTERED);
        result.setPrescriptionsMigrated(prescriptionsMigrated);

        // Update anonymous session status
        anonymousSessionRepository.updateMigrationStatus(
            anonymousUserId, registeredUserId, LocalDateTime.now());

        result.setSuccess(true);
        result.setMessage("Migration completed successfully");

    } catch (Exception e) {
        result.setSuccess(false);
        result.setMessage("Migration failed: " + e.getMessage());
        throw new BusinessException("MIGRATION_FAILED", "Data migration failed");
    }

    return result;
}
```

### **Authorization Strategy**

- **🆕 Anonymous Access**: Limited features, no family management
- **Role-Based Access Control (RBAC)**: ADMIN, OWNER, MEMBER roles
- **Family Context**: All operations scoped to family (registered users only)
- **Resource-Level Security**: Individual resource access control
- **Method-Level Security**: @PreAuthorize annotations

## 📊 Database Design

### **🆕 Core Tables with Anonymous Support**

- `users`: User accounts và profiles (🆕 + user_type, anonymous_user_id)
- `anonymous_sessions`: Anonymous user sessions (🆕 New table)
- `families`: Family groups (registered users only)
- `family_members`: Family membership và roles (registered users only)
- `medicine_types`: Medicine catalog
- `medicines`: Family medicine stock (🆕 + user_type, anonymous_user_id)
- `prescriptions`: Medical prescriptions (🆕 + user_type, anonymous_user_id)
- `prescription_items`: Medicine assignments
- `medication_schedules`: Medication scheduling
- `notifications`: User preferences
- `audit_logs`: Activity tracking (🆕 + anonymous_user_id)
- `system_settings`: Family configuration

### **🆕 JPA Entities Structure**

```java
// Core entities with anonymous support
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "email", unique = true)
    private String email;

    @Column(name = "password")
    private String password;

    @Column(name = "họ_tên", nullable = false)
    private String hoTen;

    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false)
    private UserType userType = UserType.REGISTERED;

    @Column(name = "anonymous_user_id")
    private String anonymousUserId;

    // ... other fields
}

@Entity
@Table(name = "anonymous_sessions")
public class AnonymousSession {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "anonymous_user_id", nullable = false, unique = true)
    private String anonymousUserId;

    @Column(name = "device_id")
    private String deviceId;

    @Column(name = "app_version")
    private String appVersion;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AnonymousSessionStatus status = AnonymousSessionStatus.ACTIVE;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "last_activity_at", nullable = false)
    private LocalDateTime lastActivityAt;

    @Column(name = "data_synced")
    private Boolean dataSynced = false;

    @Column(name = "migrated_to_user_id")
    private Long migratedToUserId;

    @Column(name = "migrated_at")
    private LocalDateTime migratedAt;
}

@Entity
@Table(name = "medicines")
public class Medicine {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "tên_thuốc", nullable = false)
    private String tenThuoc;

    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false)
    private UserType userType = UserType.REGISTERED;

    @Column(name = "anonymous_user_id")
    private String anonymousUserId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "family_id")
    private Family family;

    // ... other fields
}

@Entity
@Table(name = "prescriptions")
public class Prescription {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false)
    private UserType userType = UserType.REGISTERED;

    @Column(name = "anonymous_user_id")
    private String anonymousUserId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id")
    private User patient;

    // ... other fields
}

@Entity
@Table(name = "audit_logs")
public class AuditLog {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Column(name = "anonymous_user_id")
    private String anonymousUserId;

    @Enumerated(EnumType.STRING)
    @Column(name = "action", nullable = false)
    private AuditAction action;

    // ... other fields
}
```

### **🆕 Repository Methods for Anonymous Support**

````java
// AnonymousSessionRepository
@Repository
public interface AnonymousSessionRepository extends JpaRepository<AnonymousSession, UUID> {
    Optional<AnonymousSession> findByAnonymousUserId(String anonymousUserId);

    @Modifying
    @Query("UPDATE AnonymousSession a SET a.migratedToUserId = :userId, " +
           "a.migratedAt = :migratedAt, a.status = 'MIGRATED' " +
           "WHERE a.anonymousUserId = :anonymousUserId")
    void updateMigrationStatus(String anonymousUserId, Long userId, LocalDateTime migratedAt);

    @Query("SELECT a FROM AnonymousSession a WHERE a.lastActivityAt < :expiryDate")
    List<AnonymousSession> findExpiredSessions(LocalDateTime expiryDate);
}

// MedicineRepository with anonymous support
@Repository
public interface MedicineRepository extends JpaRepository<Medicine, UUID> {
    // For registered users
    List<Medicine> findByUserAndFamily(User user, Family family);
    List<Medicine> findByUser(User user);

    // For anonymous users
    List<Medicine> findByAnonymousUserIdAndUserType(String anonymousUserId, UserType userType);

    // Migration methods
    @Modifying
    @Query("UPDATE Medicine m SET m.userType = :userType, m.user = :user, " +
           "m.anonymousUserId = NULL WHERE m.anonymousUserId = :anonymousUserId")
    int updateUserTypeAndUserId(String anonymousUserId, User user, UserType userType);
}

// PrescriptionRepository with anonymous support
@Repository
public interface PrescriptionRepository extends JpaRepository<Prescription, UUID> {
    // For registered users
    List<Prescription> findByPatient(User patient);

    // For anonymous users
    List<Prescription> findByAnonymousUserIdAndUserType(String anonymousUserId, UserType userType);

    // Migration methods
    @Modifying
    @Query("UPDATE Prescription p SET p.userType = :userType, p.patient = :patient, " +
           "p.anonymousUserId = NULL WHERE p.anonymousUserId = :anonymousUserId")
    int updateUserTypeAndPatientId(String anonymousUserId, User patient, UserType userType);
}

### **🆕 Service Layer Implementation**

```java
// AnonymousUserService
@Service
@Transactional
public class AnonymousUserService {

    @Autowired
    private AnonymousSessionRepository anonymousSessionRepository;

    @Autowired
    private MedicineRepository medicineRepository;

    @Autowired
    private PrescriptionRepository prescriptionRepository;

    public AnonymousSession createAnonymousSession(String deviceId, String appVersion) {
        String anonymousUserId = UUID.randomUUID().toString();
        AnonymousSession session = AnonymousSession.builder()
            .anonymousUserId(anonymousUserId)
            .deviceId(deviceId)
            .appVersion(appVersion)
            .status(AnonymousSessionStatus.ACTIVE)
            .createdAt(LocalDateTime.now())
            .lastActivityAt(LocalDateTime.now())
            .dataSynced(false)
            .build();

        return anonymousSessionRepository.save(session);
    }

    public List<Medicine> getMedicinesForAnonymousUser(String anonymousUserId) {
        return medicineRepository.findByAnonymousUserIdAndUserType(
            anonymousUserId, UserType.ANONYMOUS);
    }

    public Medicine addMedicineForAnonymousUser(String anonymousUserId, AddMedicineRequest request) {
        Medicine medicine = Medicine.builder()
            .tenThuoc(request.getTenThuoc())
            .userType(UserType.ANONYMOUS)
            .anonymousUserId(anonymousUserId)
            .build();

        return medicineRepository.save(medicine);
    }
}

// MigrationService
@Service
@Transactional
public class MigrationService {

    @Autowired
    private AnonymousSessionRepository anonymousSessionRepository;

    @Autowired
    private MedicineRepository medicineRepository;

    @Autowired
    private PrescriptionRepository prescriptionRepository;

    public MigrationResult migrateAnonymousData(String anonymousUserId, User registeredUser) {
        MigrationResult result = new MigrationResult();

        try {
            // Migrate medicines
            int medicinesMigrated = medicineRepository
                .updateUserTypeAndUserId(anonymousUserId, registeredUser, UserType.REGISTERED);
            result.setMedicinesMigrated(medicinesMigrated);

            // Migrate prescriptions
            int prescriptionsMigrated = prescriptionRepository
                .updateUserTypeAndPatientId(anonymousUserId, registeredUser, UserType.REGISTERED);
            result.setPrescriptionsMigrated(prescriptionsMigrated);

            // Update anonymous session status
            anonymousSessionRepository.updateMigrationStatus(
                anonymousUserId, registeredUser.getId(), LocalDateTime.now());

            result.setSuccess(true);
            result.setMessage("Migration completed successfully");

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("Migration failed: " + e.getMessage());
            throw new BusinessException("MIGRATION_FAILED", "Data migration failed");
        }

        return result;
    }
}
```

### **🆕 Database Features**

- **Anonymous User Support**: UUID-based anonymous identification
- **Data Migration**: Seamless migration from anonymous to registered
- **Soft Delete**: Logical deletion with deleted_at
- **Audit Trail**: Automatic change tracking
- **Indexing Strategy**: Performance optimization
- **Constraints**: Data integrity enforcement
- **Migrations**: Version-controlled schema changes

### **🆕 Database Migration Scripts**

```sql
-- V1__init.sql - Initial schema
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE,
    password VARCHAR(255),
    họ_tên VARCHAR(255) NOT NULL,
    ngày_sinh DATE,
    giới_tính VARCHAR(10),
    ảnh_đại_diện VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE TABLE families (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tên_gia_đình VARCHAR(255) NOT NULL,
    owner_id UUID NOT NULL REFERENCES users(id),
    mã_invite VARCHAR(50) UNIQUE NOT NULL,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);

-- V2__add_audit_logs.sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(50) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    ip_address VARCHAR(45),
    user_agent TEXT,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW()
);

-- V3__add_notifications.sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    loại_thông_báo VARCHAR(50) NOT NULL,
    tiêu_đề VARCHAR(200) NOT NULL,
    nội_dung TEXT NOT NULL,
    trạng_thái VARCHAR(50) NOT NULL,
    ngày_tạo TIMESTAMP NOT NULL DEFAULT NOW(),
    ngày_gửi TIMESTAMP,
    ngày_cập_nhật TIMESTAMP NOT NULL DEFAULT NOW()
);

-- V20__add_anonymous_user_support.sql
-- Create enums
CREATE TYPE user_type_enum AS ENUM ('ANONYMOUS', 'REGISTERED', 'PREMIUM');
CREATE TYPE anonymous_session_status_enum AS ENUM ('ACTIVE', 'EXPIRED', 'MIGRATED', 'CLEANED');

-- Update users table
ALTER TABLE users ADD COLUMN user_type user_type_enum DEFAULT 'REGISTERED';
ALTER TABLE users ADD COLUMN anonymous_user_id VARCHAR(36);
ALTER TABLE users ALTER COLUMN email DROP NOT NULL;
ALTER TABLE users ALTER COLUMN password DROP NOT NULL;

-- Create anonymous_sessions table
CREATE TABLE anonymous_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    anonymous_user_id VARCHAR(36) NOT NULL,
    device_id VARCHAR(100),
    app_version VARCHAR(20),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_activity_at TIMESTAMP NOT NULL DEFAULT NOW(),
    data_synced BOOLEAN DEFAULT FALSE,
    migrated_to_user_id BIGINT REFERENCES users(id),
    migrated_at TIMESTAMP,
    status anonymous_session_status_enum DEFAULT 'ACTIVE'
);

-- Update audit_logs table
ALTER TABLE audit_logs ADD COLUMN anonymous_user_id VARCHAR(36);

-- Add indexes
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_anonymous_id ON users(anonymous_user_id);
CREATE INDEX idx_anonymous_sessions_user_id ON anonymous_sessions(anonymous_user_id);
CREATE INDEX idx_anonymous_sessions_last_activity ON anonymous_sessions(last_activity_at);
CREATE INDEX idx_anonymous_sessions_migrated ON anonymous_sessions(migrated_to_user_id);
CREATE INDEX idx_audit_logs_anonymous_user_id ON audit_logs(anonymous_user_id);
```

## 🚀 API Design

### **🆕 RESTful Principles with Anonymous Support**

- **Resource-Based URLs**: `/api/v1/anonymous/medicines`, `/api/v1/families/{id}/medicines`
- **HTTP Methods**: GET, POST, PUT, DELETE
- **Status Codes**: Standard HTTP status codes
- **Content Types**: JSON for all requests/responses
- **Versioning**: URL versioning (`/api/v1/`)

### **🆕 API Endpoints Structure**

```java
// Anonymous endpoints (no authentication required)
@RestController
@RequestMapping("/api/v1/anonymous")
public class AnonymousController {
    @PostMapping("/session")           // Register anonymous session
    @GetMapping("/medicines")          // Get medicines
    @PostMapping("/medicines")         // Add medicine
    @PutMapping("/medicines/{id}")     // Update medicine
    @DeleteMapping("/medicines/{id}")  // Delete medicine
    // ... other anonymous endpoints
}

// Authenticated endpoints (JWT required)
@RestController
@RequestMapping("/api/v1")
public class AuthenticatedController {
    @PostMapping("/auth/register")     // Register with migration
    @PostMapping("/auth/login")        // Login
    @PostMapping("/families")          // Create family
    // ... other authenticated endpoints
}
```

### **🆕 Response Format**

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  },
  "pagination": {
    "page": 1,
    "size": 20,
    "totalElements": 100,
    "totalPages": 5
  },
  "timestamp": "2024-12-01T10:00:00Z"
}
```

### **🆕 Error Handling**

```json
{
  "success": false,
  "error": {
    "code": "ANONYMOUS_SESSION_EXPIRED",
    "message": "Anonymous session has expired",
    "details": "Please register to continue using the app",
    "timestamp": "2024-12-01T10:00:00Z"
  }
}
```

## 🧪 Testing Strategy

### **🆕 Test Pyramid with Anonymous Support**

```
        E2E Tests (Few)
           /    \
          /      \
    Integration Tests (Some)
         /    \
        /      \
   Unit Tests (Many)
```

### **🆕 Testing Levels**

- **Unit Tests**: Individual components (Service, Repository)
- **Integration Tests**: Component interactions
- **API Tests**: End-to-end API testing (Anonymous + Authenticated)
- **Security Tests**: Authentication & authorization
- **Performance Tests**: Load và stress testing
- **🆕 Migration Tests**: Anonymous to registered user migration

### **🆕 Test Data Management**

- **TestContainers**: Isolated database instances
- **Test Data Builders**: Fluent API for test data
- **Database Cleanup**: Automatic cleanup between tests
- **Mocking**: External dependencies
- **🆕 Anonymous Session Testing**: Test anonymous user flows

## 📈 Performance & Monitoring

### **🆕 Performance Optimization**

- **Database Indexing**: Strategic index placement for anonymous queries
- **Caching**: Redis for frequently accessed data
- **Connection Pooling**: HikariCP configuration
- **Query Optimization**: N+1 problem prevention
- **Pagination**: Large dataset handling
- **🆕 Anonymous Session Cleanup**: Regular cleanup of expired sessions

### **🆕 Monitoring & Observability**

- **Spring Boot Actuator**: Health checks, metrics
- **Logging**: Structured logging với correlation IDs
- **Metrics**: Application performance metrics
- **Tracing**: Request tracing với Sleuth
- **Alerting**: Proactive issue detection
- **🆕 Anonymous Usage Analytics**: Track anonymous user behavior

## 🔄 Development Workflow

### **🆕 Development Environment**

- **IDE**: IntelliJ IDEA / VS Code
- **Database**: PostgreSQL (Docker)
- **API Testing**: Postman / Insomnia
- **Version Control**: Git với conventional commits
- **Code Quality**: SonarQube integration

### **🆕 Build & Deployment**

- **CI/CD**: GitHub Actions / Jenkins
- **Containerization**: Docker
- **Orchestration**: Kubernetes
- **Environment Management**: Config profiles
- **Database Migration**: Flyway

## 📋 Implementation Guidelines

### **🆕 Coding Standards**

- **Java Conventions**: Standard Java naming conventions
- **Spring Best Practices**: Spring framework guidelines
- **Security First**: Security considerations in every component
- **Documentation**: Comprehensive code documentation
- **Error Handling**: Consistent error handling patterns
- **🆕 Anonymous Support**: All components must support anonymous users

### **🆕 Code Organization**

- **Package Structure**: Logical package organization
- **Class Responsibilities**: Single responsibility principle
- **Dependency Injection**: Constructor injection preferred
- **Immutability**: Immutable objects where possible
- **Validation**: Input validation at all layers
- **🆕 User Type Handling**: Clear separation between anonymous and authenticated logic

### **🆕 Security Guidelines**

- **Input Validation**: Validate all inputs
- **SQL Injection Prevention**: Use parameterized queries
- **XSS Prevention**: Output encoding
- **CSRF Protection**: CSRF tokens
- **Secure Headers**: Security headers configuration
- **🆕 Anonymous Session Security**: UUID validation, rate limiting, session expiration

## 🎯 Next Steps

### **🆕 Implementation Priority**

1. **Setup Project Structure**: Create package structure
2. **Database Schema**: Implement JPA entities with anonymous support
3. **Security Configuration**: JWT authentication + anonymous session management
4. **Core APIs**: Anonymous + Authentication & family management
5. **Business Logic**: Medicine & prescription management (both modes)
6. **Advanced Features**: Notifications & analytics
7. **Testing**: Comprehensive test coverage
8. **Documentation**: API documentation

### **🆕 Estimated Timeline**

- **Phase 1**: Project setup & security (1-2 weeks)
- **Phase 2**: Core entities & basic APIs (2-3 weeks)
- **Phase 3**: Business logic implementation (3-4 weeks)
- **Phase 4**: Advanced features (2-3 weeks)
- **Phase 5**: Testing & optimization (2-3 weeks)
- **Total**: 10-15 weeks

---

## 📞 Contact & Support

**Architecture Lead**: Backend Team
**Reviewer**: Tech Lead
**Business Owner**: Product Manager

**Created**: December 2024
**Last Updated**: December 2024
**Version**: 2.0
**Status**: Updated with Authentication Optional Pattern
````
