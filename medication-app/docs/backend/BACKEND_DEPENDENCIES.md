# 🔧 Backend Dependencies & Technology Stack

## 📋 Overview

Tài liệu này mô tả chi tiết tất cả dependencies, công nghệ và tools được sử dụng trong backend của ứng dụng Medication Management.

## 🎯 Current Technology Stack

### **Core Framework**

| Technology      | Version | Purpose                             |
| --------------- | ------- | ----------------------------------- |
| **Spring Boot** | 3.3.0   | Main application framework          |
| **Java**        | 17      | Programming language                |
| **Gradle**      | 8.5+    | Build tool và dependency management |

### **Database & ORM**

| Technology          | Version | Purpose                   |
| ------------------- | ------- | ------------------------- |
| **PostgreSQL**      | 15+     | Primary database          |
| **Spring Data JPA** | 3.1+    | Object-relational mapping |
| **Hibernate**       | 6.2+    | JPA implementation        |

### **Security**

| Technology                | Version  | Purpose                        |
| ------------------------- | -------- | ------------------------------ |
| **Spring Security**       | 6.1+     | Authentication & authorization |
| **JWT (JSON Web Tokens)** | 0.12.3   | Stateless authentication       |
| **BCrypt**                | Built-in | Password hashing               |

## 📦 Current Dependencies

### **build.gradle (Current)**

```gradle
plugins {
    id 'java'
    id 'org.springframework.boot' version '3.3.0'
    id 'io.spring.dependency-management' version '1.1.5'
}

group = 'com.medication'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'

    // Database
    runtimeOnly 'org.postgresql:postgresql'

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
    useJUnitPlatform()
}
```

## 🚀 Recommended Enhanced Dependencies

### **Enhanced build.gradle**

```gradle
plugins {
    id 'java'
    id 'org.springframework.boot' version '3.3.0'
    id 'io.spring.dependency-management' version '1.1.5'
    id 'org.flywaydb.flyway' version '10.8.1'
    id 'org.sonarqube' version '4.4.1.3373'
}

group = 'com.medication'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // ===== SPRING BOOT STARTERS =====
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-mail'

    // ===== DATABASE & MIGRATION =====
    runtimeOnly 'org.postgresql:postgresql'
    implementation 'org.flywaydb:flyway-core'
    implementation 'org.flywaydb:flyway-database-postgresql'

    // ===== JWT & SECURITY =====
    implementation 'io.jsonwebtoken:jjwt-api:0.12.3'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.3'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.3'

    // ===== CACHING =====
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    // ===== MONITORING & METRICS =====
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'io.micrometer:micrometer-core'

    // ===== JSON PROCESSING =====
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr353'

    // ===== API DOCUMENTATION =====
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0'

    // ===== DEVELOPMENT TOOLS =====
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    compileOnly 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'

    // ===== TESTING =====
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.testcontainers:postgresql:1.19.3'
    testImplementation 'org.testcontainers:junit-jupiter:1.19.3'
    testImplementation 'org.testcontainers:testcontainers:1.19.3'
    testImplementation 'com.h2database:h2'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

    // ===== UTILITIES =====
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'commons-io:commons-io:2.15.1'
    implementation 'org.apache.commons:commons-collections4:4.4'

    // ===== VALIDATION =====
    implementation 'org.hibernate.validator:hibernate-validator:8.0.1.Final'

    // ===== LOGGING =====
    implementation 'net.logstash.logback:logstash-logback-encoder:7.4'

    // ===== FILE PROCESSING =====
    implementation 'org.apache.poi:poi:5.2.4'
    implementation 'org.apache.poi:poi-ooxml:5.2.4'
    implementation 'org.apache.poi:poi-ooxml-schemas:4.1.2'
    implementation 'org.apache.xmlbeans:xmlbeans:5.1.1'

    // ===== EMAIL TEMPLATES =====
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
}

// ===== GRADLE CONFIGURATION =====
tasks.named('test') {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}

// ===== SONARQUBE CONFIGURATION =====
sonarqube {
    properties {
        property "sonar.projectKey", "medication-backend"
        property "sonar.projectName", "Medication Management Backend"
        property "sonar.host.url", "http://localhost:9000"
    }
}

// ===== FLYWAY CONFIGURATION =====
flyway {
    url = '***********************************************'
    user = 'postgres'
    password = 'password'
    locations = ['classpath:db/migration']
}
```

## 📊 Dependency Categories

### **1. Core Spring Boot Dependencies**

#### **Spring Boot Web**

```gradle
implementation 'org.springframework.boot:spring-boot-starter-web'
```

- **Purpose**: RESTful web services
- **Features**: Embedded Tomcat, Spring MVC, Jackson
- **Usage**: All REST controllers

#### **Spring Boot Data JPA**

```gradle
implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
```

- **Purpose**: Database access layer
- **Features**: JPA, Hibernate, Repository pattern
- **Usage**: All entity và repository classes

#### **Spring Boot Security**

```gradle
implementation 'org.springframework.boot:spring-boot-starter-security'
```

- **Purpose**: Authentication & authorization
- **Features**: Security filters, password encoding
- **Usage**: Security configuration, JWT authentication

#### **Spring Boot Validation**

```gradle
implementation 'org.springframework.boot:spring-boot-starter-validation'
```

- **Purpose**: Input validation
- **Features**: Bean validation, custom validators
- **Usage**: Request DTOs, form validation

### **2. Database & Migration Dependencies**

#### **PostgreSQL Driver**

```gradle
runtimeOnly 'org.postgresql:postgresql'
```

- **Purpose**: PostgreSQL database connectivity
- **Features**: JDBC driver, connection pooling
- **Usage**: Database connections

#### **Flyway Migration**

```gradle
implementation 'org.flywaydb:flyway-core'
implementation 'org.flywaydb:flyway-database-postgresql'
```

- **Purpose**: Database schema migration
- **Features**: Version-controlled migrations
- **Usage**: Database schema management

### **3. Security Dependencies**

#### **JWT (JSON Web Tokens)**

```gradle
implementation 'io.jsonwebtoken:jjwt-api:0.12.3'
runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.3'
runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.3'
```

- **Purpose**: Stateless authentication
- **Features**: Token generation, validation, parsing
- **Usage**: JWT authentication system

### **4. Caching Dependencies**

#### **Redis Cache**

```gradle
implementation 'org.springframework.boot:spring-boot-starter-data-redis'
```

- **Purpose**: Distributed caching
- **Features**: Session storage, cache management
- **Usage**: User sessions, frequently accessed data

### **5. Monitoring & Metrics**

#### **Spring Boot Actuator**

```gradle
implementation 'org.springframework.boot:spring-boot-starter-actuator'
```

- **Purpose**: Application monitoring
- **Features**: Health checks, metrics, endpoints
- **Usage**: Production monitoring

#### **Micrometer Prometheus**

```gradle
implementation 'io.micrometer:micrometer-registry-prometheus'
```

- **Purpose**: Metrics collection
- **Features**: Prometheus metrics format
- **Usage**: Performance monitoring

### **6. API Documentation**

#### **SpringDoc OpenAPI**

```gradle
implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0'
```

- **Purpose**: API documentation
- **Features**: Swagger UI, OpenAPI 3.0
- **Usage**: API documentation generation

### **7. Development Tools**

#### **Lombok**

```gradle
compileOnly 'org.projectlombok:lombok:1.18.30'
annotationProcessor 'org.projectlombok:lombok:1.18.30'
```

- **Purpose**: Code generation
- **Features**: Boilerplate code reduction
- **Usage**: Entity classes, DTOs

#### **Spring Boot DevTools**

```gradle
developmentOnly 'org.springframework.boot:spring-boot-devtools'
```

- **Purpose**: Development utilities
- **Features**: Auto-restart, live reload
- **Usage**: Development environment

### **8. Testing Dependencies**

#### **Spring Boot Test**

```gradle
testImplementation 'org.springframework.boot:spring-boot-starter-test'
```

- **Purpose**: Integration testing
- **Features**: Test slices, embedded containers
- **Usage**: All test classes

#### **TestContainers**

```gradle
testImplementation 'org.testcontainers:postgresql:1.19.3'
testImplementation 'org.testcontainers:junit-jupiter:1.19.3'
```

- **Purpose**: Database testing
- **Features**: Isolated database instances
- **Usage**: Integration tests

#### **H2 Database**

```gradle
testImplementation 'com.h2database:h2'
```

- **Purpose**: In-memory database for tests
- **Features**: Fast, lightweight database
- **Usage**: Unit tests

### **9. Utility Dependencies**

#### **Apache Commons**

```gradle
implementation 'org.apache.commons:commons-lang3:3.14.0'
implementation 'commons-io:commons-io:2.15.1'
```

- **Purpose**: Utility functions
- **Features**: String utilities, file operations
- **Usage**: Utility classes

### **10. File Processing**

#### **Apache POI**

```gradle
implementation 'org.apache.poi:poi:5.2.4'
implementation 'org.apache.poi:poi-ooxml:5.2.4'
```

- **Purpose**: Excel file processing
- **Features**: Read/write Excel files
- **Usage**: Data export functionality

## 🔧 Configuration Properties

### **application.yml Structure**

```yaml
spring:
  application:
    name: medication-backend

  # Database Configuration
  datasource:
    url: ***********************************************
    username: postgres
    password: password
    driver-class-name: org.postgresql.Driver

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

  # Flyway Migration
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true

  # Redis Cache
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0

  # Security
  security:
    jwt:
      secret: your-secret-key-here
      expiration: 86400000 # 24 hours

  # Mail Configuration
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-app-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# Logging Configuration
logging:
  level:
    com.medication: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/medication-backend.log

# API Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /api
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
```

## 🚀 Version Compatibility Matrix

### **Spring Boot 3.3.0 Compatibility**

| Component  | Version | Status        |
| ---------- | ------- | ------------- |
| Java       | 17+     | ✅ Required   |
| PostgreSQL | 12+     | ✅ Compatible |
| Redis      | 6+      | ✅ Compatible |
| Gradle     | 8.5+    | ✅ Compatible |

### **Security Compatibility**

| Component       | Version  | Status        |
| --------------- | -------- | ------------- |
| Spring Security | 6.1+     | ✅ Compatible |
| JWT             | 0.12.3   | ✅ Latest     |
| BCrypt          | Built-in | ✅ Compatible |

## 📋 Implementation Checklist

### **Phase 1: Core Setup**

- [ ] Update build.gradle với enhanced dependencies
- [ ] Configure application.yml
- [ ] Setup database connection
- [ ] Configure Flyway migrations
- [ ] Setup basic security configuration

### **Phase 2: Security Implementation**

- [ ] Implement JWT authentication
- [ ] Configure Spring Security
- [ ] Setup password encoding
- [ ] Implement role-based access control

### **Phase 3: Database Layer**

- [ ] Create JPA entities
- [ ] Implement repositories
- [ ] Setup database indexes
- [ ] Configure audit logging

### **Phase 4: Business Logic**

- [ ] Implement service layer
- [ ] Add validation logic
- [ ] Implement business rules
- [ ] Add caching layer

### **Phase 5: API Layer**

- [ ] Create REST controllers
- [ ] Implement DTOs
- [ ] Add API documentation
- [ ] Setup exception handling

### **Phase 6: Testing**

- [ ] Setup test environment
- [ ] Implement unit tests
- [ ] Add integration tests
- [ ] Configure test containers

### **Phase 7: Monitoring**

- [ ] Configure Actuator
- [ ] Setup metrics collection
- [ ] Implement health checks
- [ ] Add logging configuration

---

## 📞 Contact & Support

**Architecture Lead**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Ready for Implementation
