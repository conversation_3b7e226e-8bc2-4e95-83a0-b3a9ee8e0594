# 📁 Backend Folder Structure Guide

## 📋 Overview

Tài liệu này mô tả chi tiết cấu trúc thư mục backend và hướng dẫn tổ chức code theo best practices của Spring Boot.

## 🏗️ Complete Directory Structure

```
medication-app/
├── backend/
│   └── backend/                                    # Main backend project
│       ├── build.gradle                           # Gradle build configuration
│       ├── settings.gradle                        # Project settings
│       ├── gradlew                                # Gradle wrapper (Unix)
│       ├── gradlew.bat                            # Gradle wrapper (Windows)
│       ├── gradle/                                # Gradle wrapper files
│       │   └── wrapper/
│       │       ├── gradle-wrapper.jar
│       │       └── gradle-wrapper.properties
│       ├── build/                                 # Build output (auto-generated)
│       ├── .gradle/                               # Gradle cache (auto-generated)
│       └── src/
│           ├── main/
│           │   ├── java/
│           │   │   └── com/
│           │   │       └── medication/
│           │   │           ├── BackendApplication.java
│           │   │           │
│           │   │           ├── config/            # Configuration classes
│           │   │           │   ├── SecurityConfig.java
│           │   │           │   ├── DatabaseConfig.java
│           │   │           │   ├── JwtConfig.java
│           │   │           │   ├── CorsConfig.java
│           │   │           │   ├── SwaggerConfig.java
│           │   │           │   └── AuditConfig.java
│           │   │           │
│           │   │           ├── controller/        # REST API Controllers
│           │   │           │   ├── AuthController.java
│           │   │           │   ├── FamilyController.java
│           │   │           │   ├── MedicineController.java
│           │   │           │   ├── PrescriptionController.java
│           │   │           │   ├── NotificationController.java
│           │   │           │   ├── DashboardController.java
│           │   │           │   ├── SearchController.java
│           │   │           │   ├── ExportController.java
│           │   │           │   ├── BackupController.java
│           │   │           │   └── SystemController.java
│           │   │           │
│           │   │           ├── service/           # Business Logic Layer
│           │   │           │   ├── AuthService.java
│           │   │           │   ├── FamilyService.java
│           │   │           │   ├── MedicineService.java
│           │   │           │   ├── PrescriptionService.java
│           │   │           │   ├── NotificationService.java
│           │   │           │   ├── DashboardService.java
│           │   │           │   ├── SearchService.java
│           │   │           │   ├── ExportService.java
│           │   │           │   ├── BackupService.java
│           │   │           │   ├── SystemService.java
│           │   │           │   └── impl/          # Service implementations
│           │   │           │       ├── AuthServiceImpl.java
│           │   │           │       ├── FamilyServiceImpl.java
│           │   │           │       └── MedicineServiceImpl.java
│           │   │           │
│           │   │           ├── repository/        # Data Access Layer
│           │   │           │   ├── UserRepository.java
│           │   │           │   ├── FamilyRepository.java
│           │   │           │   ├── FamilyMemberRepository.java
│           │   │           │   ├── MedicineTypeRepository.java
│           │   │           │   ├── MedicineInventoryRepository.java
│           │   │           │   ├── PrescriptionRepository.java
│           │   │           │   ├── PrescriptionMedicineRepository.java
│           │   │           │   ├── MedicationScheduleRepository.java
│           │   │           │   ├── MedicationScheduleInstanceRepository.java
│           │   │           │   ├── NotificationSettingsRepository.java
│           │   │           │   ├── NotificationQueueRepository.java
│           │   │           │   ├── AuditLogRepository.java
│           │   │           │   ├── DataExportRepository.java
│           │   │           │   ├── DataBackupRepository.java
│           │   │           │   └── SystemSettingsRepository.java
│           │   │           │
│           │   │           ├── entity/            # JPA Entities
│           │   │           │   ├── User.java
│           │   │           │   ├── Family.java
│           │   │           │   ├── FamilyMember.java
│           │   │           │   ├── MedicineType.java
│           │   │           │   ├── MedicineInventory.java
│           │   │           │   ├── Prescription.java
│           │   │           │   ├── PrescriptionMedicine.java
│           │   │           │   ├── MedicationSchedule.java
│           │   │           │   ├── MedicationScheduleInstance.java
│           │   │           │   ├── NotificationSettings.java
│           │   │           │   ├── NotificationQueue.java
│           │   │           │   ├── AuditLog.java
│           │   │           │   ├── DataExport.java
│           │   │           │   ├── DataBackup.java
│           │   │           │   └── SystemSettings.java
│           │   │           │
│           │   │           ├── dto/               # Data Transfer Objects
│           │   │           │   ├── request/       # Request DTOs
│           │   │           │   │   ├── auth/
│           │   │           │   │   │   ├── LoginRequest.java
│           │   │           │   │   │   ├── RegisterRequest.java
│           │   │           │   │   │   ├── PasswordResetRequest.java
│           │   │           │   │   │   └── TokenRefreshRequest.java
│           │   │           │   │   ├── family/
│           │   │           │   │   │   ├── CreateFamilyRequest.java
│           │   │           │   │   │   ├── InviteMemberRequest.java
│           │   │           │   │   │   ├── UpdateRoleRequest.java
│           │   │           │   │   │   └── AcceptInvitationRequest.java
│           │   │           │   │   ├── medicine/
│           │   │           │   │   │   ├── AddMedicineTypeRequest.java
│           │   │           │   │   │   ├── AddToInventoryRequest.java
│           │   │           │   │   │   ├── UpdateQuantityRequest.java
│           │   │           │   │   │   └── SearchMedicineRequest.java
│           │   │           │   │   ├── prescription/
│           │   │           │   │   │   ├── CreatePrescriptionRequest.java
│           │   │           │   │   │   ├── AssignMedicineRequest.java
│           │   │           │   │   │   └── ScheduleMedicationRequest.java
│           │   │           │   │   ├── notification/
│           │   │           │   │   │   ├── UpdateNotificationSettingsRequest.java
│           │   │           │   │   │   └── SetReminderRequest.java
│           │   │           │   │   ├── dashboard/
│           │   │           │   │   │   └── GenerateReportRequest.java
│           │   │           │   │   └── utility/
│           │   │           │   │       ├── ExportDataRequest.java
│           │   │           │   │       ├── BackupDataRequest.java
│           │   │           │   │       └── UpdateSystemSettingsRequest.java
│           │   │           │   ├── response/      # Response DTOs
│           │   │           │   │   ├── common/
│           │   │           │   │   │   ├── ApiResponse.java
│           │   │           │   │   │   ├── PaginationResponse.java
│           │   │           │   │   │   └── ErrorResponse.java
│           │   │           │   │   ├── auth/
│           │   │           │   │   │   ├── LoginResponse.java
│           │   │           │   │   │   ├── RegisterResponse.java
│           │   │           │   │   │   └── TokenRefreshResponse.java
│           │   │           │   │   ├── family/
│           │   │           │   │   │   ├── FamilyResponse.java
│           │   │           │   │   │   ├── FamilyMemberResponse.java
│           │   │           │   │   │   └── FamilyInvitationResponse.java
│           │   │           │   │   ├── medicine/
│           │   │           │   │   │   ├── MedicineTypeResponse.java
│           │   │           │   │   │   ├── MedicineInventoryResponse.java
│           │   │           │   │   │   └── SearchResultResponse.java
│           │   │           │   │   ├── prescription/
│           │   │           │   │   │   ├── PrescriptionResponse.java
│           │   │           │   │   │   ├── PrescriptionMedicineResponse.java
│           │   │           │   │   │   └── MedicationScheduleResponse.java
│           │   │           │   │   ├── notification/
│           │   │           │   │   │   ├── NotificationSettingsResponse.java
│           │   │           │   │   │   └── ReminderResponse.java
│           │   │           │   │   ├── dashboard/
│           │   │           │   │   │   └── ComplianceReportResponse.java
│           │   │           │   │   └── utility/
│           │   │           │   │       ├── ExportResponse.java
│           │   │           │   │       ├── BackupResponse.java
│           │   │           │   │       └── SystemSettingsResponse.java
│           │   │           │   └── common/        # Common DTOs
│           │   │           │       ├── PaginationRequest.java
│           │   │           │       ├── SearchRequest.java
│           │   │           │       ├── DateRangeRequest.java
│           │   │           │       └── FilterRequest.java
│           │   │           │
│           │   │           ├── exception/         # Exception Handling
│           │   │           │   ├── GlobalExceptionHandler.java
│           │   │           │   ├── BusinessException.java
│           │   │           │   ├── ValidationException.java
│           │   │           │   ├── SecurityException.java
│           │   │           │   ├── ResourceNotFoundException.java
│           │   │           │   ├── DuplicateResourceException.java
│           │   │           │   └── UnauthorizedException.java
│           │   │           │
│           │   │           ├── security/          # Security Components
│           │   │           │   ├── JwtTokenProvider.java
│           │   │           │   ├── UserDetailsServiceImpl.java
│           │   │           │   ├── JwtAuthenticationFilter.java
│           │   │           │   ├── SecurityUtils.java
│           │   │           │   ├── PasswordEncoder.java
│           │   │           │   └── SecurityConstants.java
│           │   │           │
│           │   │           ├── util/              # Utility Classes
│           │   │           │   ├── DateUtils.java
│           │   │           │   ├── ValidationUtils.java
│           │   │           │   ├── FileUtils.java
│           │   │           │   ├── EncryptionUtils.java
│           │   │           │   ├── PaginationUtils.java
│           │   │           │   ├── SearchUtils.java
│           │   │           │   └── NotificationUtils.java
│           │   │           │
│           │   │           ├── constant/          # Constants
│           │   │           │   ├── ApiConstants.java
│           │   │           │   ├── SecurityConstants.java
│           │   │           │   ├── ErrorConstants.java
│           │   │           │   ├── ValidationConstants.java
│           │   │           │   └── BusinessConstants.java
│           │   │           │
│           │   │           ├── enums/             # Enumerations
│           │   │           │   ├── UserRole.java
│           │   │           │   ├── FamilyRole.java
│           │   │           │   ├── MedicineCategory.java
│           │   │           │   ├── MedicineStatus.java
│           │   │           │   ├── PrescriptionStatus.java
│           │   │           │   ├── ScheduleType.java
│           │   │           │   ├── NotificationType.java
│           │   │           │   ├── NotificationChannel.java
│           │   │           │   ├── AuditAction.java
│           │   │           │   └── ExportFormat.java
│           │   │           │
│           │   │           └── mapper/            # Object Mappers
│           │   │               ├── UserMapper.java
│           │   │               ├── FamilyMapper.java
│           │   │               ├── MedicineMapper.java
│           │   │               ├── PrescriptionMapper.java
│           │   │               └── NotificationMapper.java
│           │   │
│           │   └── resources/                     # Resources
│           │       ├── application.yml            # Main configuration
│           │       ├── application-dev.yml        # Development environment
│           │       ├── application-prod.yml       # Production environment
│           │       ├── application-test.yml       # Test environment
│           │       ├── db/
│           │       │   └── migration/             # Database migrations
│           │       │       ├── V1__init_schema.sql
│           │       │       ├── V2__add_audit_logs.sql
│           │       │       ├── V3__add_notifications.sql
│           │       │       ├── V4__add_exports_backups.sql
│           │       │       └── V5__add_system_settings.sql
│           │       ├── static/                    # Static resources
│           │       │   ├── css/
│           │       │   ├── js/
│           │       │   └── images/
│           │       └── templates/                 # Template files
│           │           └── email/
│           │               ├── welcome.html
│           │               ├── invitation.html
│           │               └── reminder.html
│           │
│           └── test/                              # Test sources
│               ├── java/
│               │   └── com/
│               │       └── medication/
│               │           ├── controller/        # Controller tests
│               │           │   ├── AuthControllerTest.java
│               │           │   ├── FamilyControllerTest.java
│               │           │   ├── MedicineControllerTest.java
│               │           │   ├── PrescriptionControllerTest.java
│               │           │   ├── NotificationControllerTest.java
│               │           │   ├── DashboardControllerTest.java
│               │           │   └── UtilityControllerTest.java
│               │           ├── service/           # Service tests
│               │           │   ├── AuthServiceTest.java
│               │           │   ├── FamilyServiceTest.java
│               │           │   ├── MedicineServiceTest.java
│               │           │   ├── PrescriptionServiceTest.java
│               │           │   ├── NotificationServiceTest.java
│               │           │   ├── DashboardServiceTest.java
│               │           │   └── UtilityServiceTest.java
│               │           ├── repository/        # Repository tests
│               │           │   ├── UserRepositoryTest.java
│               │           │   ├── FamilyRepositoryTest.java
│               │           │   ├── MedicineRepositoryTest.java
│               │           │   ├── PrescriptionRepositoryTest.java
│               │           │   └── NotificationRepositoryTest.java
│               │           ├── integration/       # Integration tests
│               │           │   ├── AuthIntegrationTest.java
│               │           │   ├── FamilyIntegrationTest.java
│               │           │   ├── MedicineIntegrationTest.java
│               │           │   └── PrescriptionIntegrationTest.java
│               │           ├── security/          # Security tests
│               │           │   ├── SecurityConfigTest.java
│               │           │   ├── JwtTokenProviderTest.java
│               │           │   └── AuthenticationTest.java
│               │           └── util/              # Utility tests
│               │               ├── DateUtilsTest.java
│               │               ├── ValidationUtilsTest.java
│               │               └── FileUtilsTest.java
│               └── resources/
│                   ├── application-test.yml       # Test configuration
│                   ├── data/                      # Test data
│                   │   ├── test-users.sql
│                   │   ├── test-families.sql
│                   │   ├── test-medicines.sql
│                   │   └── test-prescriptions.sql
│                   └── test-files/                # Test files
│                       ├── test-export.json
│                       ├── test-backup.zip
│                       └── test-image.jpg
```

## 📋 Package Organization Principles

### **1. Layered Architecture**

```
Controller Layer (API) → Service Layer (Business Logic) → Repository Layer (Data Access) → Database
```

### **2. Package Naming Conventions**

- **Lowercase**: All package names in lowercase
- **Plural**: Use plural for collections (controllers, services, repositories)
- **Descriptive**: Clear and descriptive package names
- **Hierarchical**: Logical hierarchy in package structure

### **3. Class Organization**

- **One class per file**: Each Java class in its own file
- **Consistent naming**: Follow Java naming conventions
- **Logical grouping**: Related classes in same package
- **Dependency direction**: Dependencies flow downward

## 🔧 Configuration Files

### **Application Properties Structure**

```yaml
# application.yml
spring:
  profiles:
    active: dev
  application:
    name: medication-backend

# application-dev.yml
spring:
  datasource:
    url: ***********************************************
    username: postgres
    password: password
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true

# application-prod.yml
spring:
  datasource:
    url: **********************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

# application-test.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
```

## 🧪 Testing Structure

### **Test Organization**

- **Unit Tests**: Test individual components
- **Integration Tests**: Test component interactions
- **Security Tests**: Test authentication & authorization
- **Performance Tests**: Test performance characteristics

### **Test Data Management**

- **TestContainers**: Isolated database instances
- **Test Data Builders**: Fluent API for test data
- **Database Cleanup**: Automatic cleanup between tests
- **Mocking**: External dependencies

## 📁 File Naming Conventions

### **Java Files**

- **Controllers**: `*Controller.java`
- **Services**: `*Service.java` và `*ServiceImpl.java`
- **Repositories**: `*Repository.java`
- **Entities**: `*Entity.java` hoặc just `*.java`
- **DTOs**: `*Request.java`, `*Response.java`
- **Exceptions**: `*Exception.java`
- **Utils**: `*Utils.java`
- **Constants**: `*Constants.java`
- **Enums**: `*.java` (no suffix)

### **Resource Files**

- **Configuration**: `application-*.yml`
- **Migrations**: `V*__*.sql`
- **Test Data**: `test-*.sql`
- **Templates**: `*.html`, `*.txt`

## 🚀 Implementation Guidelines

### **1. Package Creation Order**

1. Create base package structure
2. Add configuration classes
3. Create entities
4. Create repositories
5. Create services
6. Create controllers
7. Add DTOs
8. Add utilities

### **2. File Creation Order**

1. Entity classes
2. Repository interfaces
3. Service interfaces
4. Service implementations
5. Controller classes
6. DTO classes
7. Exception handlers
8. Utility classes

### **3. Testing Strategy**

1. Unit tests for services
2. Integration tests for repositories
3. API tests for controllers
4. Security tests
5. Performance tests

## 📋 Best Practices

### **Code Organization**

- **Single Responsibility**: Each class has one reason to change
- **Dependency Injection**: Use constructor injection
- **Immutability**: Use immutable objects where possible
- **Validation**: Validate inputs at all layers
- **Documentation**: Comprehensive code documentation

### **Security**

- **Input Validation**: Validate all inputs
- **SQL Injection Prevention**: Use parameterized queries
- **XSS Prevention**: Output encoding
- **CSRF Protection**: CSRF tokens
- **Secure Headers**: Security headers configuration

### **Performance**

- **Database Indexing**: Strategic index placement
- **Caching**: Redis for frequently accessed data
- **Connection Pooling**: HikariCP configuration
- **Query Optimization**: N+1 problem prevention
- **Pagination**: Large dataset handling

---

## 📞 Contact & Support

**Architecture Lead**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Ready for Implementation
