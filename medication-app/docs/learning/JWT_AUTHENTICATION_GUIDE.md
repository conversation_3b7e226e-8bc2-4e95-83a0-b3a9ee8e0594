# 🔐 JWT Authentication Guide - Medication App

## 📋 Tổng quan

Tài liệu này giải thích chi tiết cơ chế JWT (JSON Web Token) authentication được implement trong Medication App, bao gồm cách hoạt động, implementation details, và best practices.

## 🎯 Mục tiêu học tập

Sau khi đọc tài liệu này, bạn sẽ hiểu:

- ✅ JWT là gì và cách hoạt động
- ✅ Cách implement JWT authentication trong Spring Boot
- ✅ Security configuration và filter chain
- ✅ Token generation, validation và refresh mechanism
- ✅ Best practices và security considerations
- ✅ Testing JWT authentication

## 🔍 JWT Là Gì?

### **Định nghĩa**

JWT (JSON Web Token) là một open standard (RFC 7519) để truyền thông tin an toàn giữa các bên dưới dạng JSON object. Token này được ký số để verify tính toàn vẹn.

### **Cấu trúc JWT**

```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
```

**Gồm 3 phần được ngăn cách bởi dấu chấm (.):**

1. **Header**: Chứa algorithm và token type
2. **Payload**: Chứa claims (thông tin user)
3. **Signature**: Verify tính toàn vẹn của token

### **Header Example**

```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

### **Payload Example**

```json
{
  "sub": "1234567890",
  "name": "John Doe",
  "email": "<EMAIL>",
  "iat": 1516239022,
  "exp": 1516242622
}
```

## 🏗️ Architecture Overview

### **JWT Flow trong Medication App**

```mermaid
sequenceDiagram
    participant Client
    participant AuthController
    participant JwtService
    participant SecurityFilter
    participant ProtectedEndpoint

    Client->>AuthController: POST /api/v1/auth/login
    AuthController->>JwtService: generateToken(user)
    JwtService-->>AuthController: JWT Token
    AuthController-->>Client: {accessToken, refreshToken}

    Client->>SecurityFilter: GET /api/v1/medicines (with Bearer token)
    SecurityFilter->>JwtService: validateToken(token)
    JwtService-->>SecurityFilter: User details
    SecurityFilter->>ProtectedEndpoint: Forward request
    ProtectedEndpoint-->>Client: Protected data
```

### **Components**

1. **JwtService**: Token generation và validation
2. **JwtAuthenticationFilter**: Intercept requests và validate tokens
3. **SecurityConfig**: Configure security rules
4. **AuthController**: Handle authentication endpoints
5. **UserDetailsService**: Load user information

## 🔧 Implementation Details

### **1. JWT Service**

```java
@Service
@Component
public class JwtService {

    @Value("${jwt.secret}")
    private String secretKey;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    @Value("${jwt.refresh-token.expiration}")
    private long refreshExpiration;

    // Generate access token
    public String generateToken(UserDetails userDetails) {
        return generateToken(new HashMap<>(), userDetails);
    }

    // Generate token with extra claims
    public String generateToken(Map<String, Object> extraClaims, UserDetails userDetails) {
        return buildToken(extraClaims, userDetails, jwtExpiration);
    }

    // Generate refresh token
    public String generateRefreshToken(UserDetails userDetails) {
        return buildToken(new HashMap<>(), userDetails, refreshExpiration);
    }

    // Build token
    private String buildToken(Map<String, Object> extraClaims, UserDetails userDetails, long expiration) {
        return Jwts.builder()
                .setClaims(extraClaims)
                .setSubject(userDetails.getUsername())
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(getSignInKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    // Validate token
    public boolean isTokenValid(String token, UserDetails userDetails) {
        final String username = extractUsername(token);
        return (username.equals(userDetails.getUsername())) && !isTokenExpired(token);
    }

    // Extract username from token
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    // Extract expiration date
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    // Extract specific claim
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    // Extract all claims
    private Claims extractAllClaims(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSignInKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    // Check if token is expired
    private boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    // Get signing key
    private Key getSignInKey() {
        byte[] keyBytes = Decoders.BASE64.decode(secretKey);
        return Keys.hmacShaKeyFor(keyBytes);
    }
}
```

### **2. JWT Authentication Filter**

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtService jwtService;
    private final UserDetailsService userDetailsService;

    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain
    ) throws ServletException, IOException {

        // Skip JWT validation for public endpoints
        if (isPublicEndpoint(request.getServletPath())) {
            filterChain.doFilter(request, response);
            return;
        }

        final String authHeader = request.getHeader("Authorization");
        final String jwt;
        final String userEmail;

        // Check if Authorization header exists and starts with "Bearer "
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            filterChain.doFilter(request, response);
            return;
        }

        // Extract JWT token
        jwt = authHeader.substring(7);
        userEmail = jwtService.extractUsername(jwt);

        // Validate token and set authentication
        if (userEmail != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            UserDetails userDetails = this.userDetailsService.loadUserByUsername(userEmail);

            if (jwtService.isTokenValid(jwt, userDetails)) {
                UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                        userDetails,
                        null,
                        userDetails.getAuthorities()
                );
                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authToken);

                log.debug("JWT authentication successful for user: {}", userEmail);
            } else {
                log.warn("Invalid JWT token for user: {}", userEmail);
            }
        }

        filterChain.doFilter(request, response);
    }

    // Check if endpoint is public (doesn't require authentication)
    private boolean isPublicEndpoint(String path) {
        return path.startsWith("/api/v1/auth/") ||
               path.startsWith("/api/v1/anonymous/") ||
               path.startsWith("/api/v1/medicine-types") ||
               path.startsWith("/swagger-ui/") ||
               path.startsWith("/v3/api-docs") ||
               path.startsWith("/actuator/health");
    }
}
```

### **3. Security Configuration**

```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthFilter;
    private final AuthenticationProvider authenticationProvider;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .authorizeHttpRequests(authz -> authz
                // Public endpoints - không cần authentication
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/api/v1/anonymous/**").permitAll()
                .requestMatchers("/api/v1/medicine-types/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                .requestMatchers("/error").permitAll()

                // Protected endpoints - cần JWT authentication
                .requestMatchers("/api/v1/medicines/**").authenticated()
                .requestMatchers("/api/v1/prescriptions/**").authenticated()
                .requestMatchers("/api/v1/prescription-items/**").authenticated()
                .requestMatchers("/api/v1/medication-schedules/**").authenticated()
                .requestMatchers("/api/v1/families/**").authenticated()
                .requestMatchers("/api/v1/users/**").authenticated()

                // Default: require authentication
                .anyRequest().authenticated()
            )
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authenticationProvider(authenticationProvider)
            .addFilterBefore(jwtAuthFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    @Bean
    public AuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService());
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public UserDetailsService userDetailsService() {
        return new CustomUserDetailsService();
    }
}
```
