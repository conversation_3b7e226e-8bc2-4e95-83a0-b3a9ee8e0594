# 🚀 JWT Implementation Advanced - Medication App

## 📋 Tổng quan

Tài liệu này tiếp tục từ JWT_AUTHENTICATION_GUIDE.md, tập trung vào các phần advanced như Authentication Service, Best Practices, Testing, và Troubleshooting.

## 🔧 Authentication Service

### **AuthenticationService Implementation**

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthenticationService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;
    private final OtpService otpService;
    
    /**
     * Đăng ký user mới
     */
    public AuthenticationResponse register(RegisterRequest request) {
        log.info("Registering new user with email: {}", request.getEmail());
        
        // Check if user already exists
        if (userRepository.findByEmail(request.getEmail()).isPresent()) {
            throw new UserAlreadyExistsException("Email đã được sử dụng: " + request.getEmail());
        }
        
        // Create new user
        User user = User.builder()
                .email(request.getEmail())
                .password(passwordEncoder.encode(request.getPassword()))
                .fullName(request.getFullName())
                .dateOfBirth(request.getDateOfBirth())
                .gender(request.getGender())
                .phoneNumber(request.getPhoneNumber())
                .status(UserStatus.PENDING_VERIFICATION)
                .role(Role.USER)
                .createdAt(LocalDateTime.now())
                .build();
        
        User savedUser = userRepository.save(user);
        
        // Send OTP for verification
        otpService.sendVerificationOtp(savedUser.getEmail());
        
        log.info("User registered successfully with ID: {}", savedUser.getId());
        
        return AuthenticationResponse.builder()
                .message("Đăng ký thành công. Vui lòng kiểm tra email để xác thực tài khoản.")
                .status("OTP_SENT")
                .build();
    }
    
    /**
     * Đăng nhập user
     */
    public AuthenticationResponse authenticate(AuthenticationRequest request) {
        log.info("Authenticating user with email: {}", request.getEmail());
        
        try {
            // Authenticate user credentials
            authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            request.getEmail(),
                            request.getPassword()
                    )
            );
            
            // Get user details
            User user = userRepository.findByEmail(request.getEmail())
                    .orElseThrow(() -> new UsernameNotFoundException("User not found"));
            
            // Check if user is verified
            if (user.getStatus() == UserStatus.PENDING_VERIFICATION) {
                throw new UserNotVerifiedException("Tài khoản chưa được xác thực");
            }
            
            if (user.getStatus() == UserStatus.INACTIVE) {
                throw new UserInactiveException("Tài khoản đã bị vô hiệu hóa");
            }
            
            // Generate tokens
            String accessToken = jwtService.generateToken(user);
            String refreshToken = jwtService.generateRefreshToken(user);
            
            // Update last login
            user.setLastLoginAt(LocalDateTime.now());
            userRepository.save(user);
            
            log.info("User authenticated successfully: {}", request.getEmail());
            
            return AuthenticationResponse.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .expiresIn(jwtService.getExpirationTime())
                    .tokenType("Bearer")
                    .user(UserResponse.fromEntity(user))
                    .build();
                    
        } catch (BadCredentialsException e) {
            log.warn("Authentication failed for email: {} - Invalid credentials", request.getEmail());
            throw new InvalidCredentialsException("Email hoặc mật khẩu không đúng");
        }
    }
    
    /**
     * Refresh JWT token
     */
    public AuthenticationResponse refreshToken(RefreshTokenRequest request) {
        log.info("Refreshing token");
        
        try {
            String userEmail = jwtService.extractUsername(request.getRefreshToken());
            
            if (userEmail != null) {
                User user = userRepository.findByEmail(userEmail)
                        .orElseThrow(() -> new UsernameNotFoundException("User not found"));
                
                if (jwtService.isTokenValid(request.getRefreshToken(), user)) {
                    String newAccessToken = jwtService.generateToken(user);
                    String newRefreshToken = jwtService.generateRefreshToken(user);
                    
                    log.info("Token refreshed successfully for user: {}", userEmail);
                    
                    return AuthenticationResponse.builder()
                            .accessToken(newAccessToken)
                            .refreshToken(newRefreshToken)
                            .expiresIn(jwtService.getExpirationTime())
                            .tokenType("Bearer")
                            .user(UserResponse.fromEntity(user))
                            .build();
                }
            }
            
            throw new InvalidTokenException("Invalid refresh token");
            
        } catch (Exception e) {
            log.error("Token refresh failed", e);
            throw new InvalidTokenException("Token refresh thất bại");
        }
    }
    
    /**
     * Verify OTP
     */
    public AuthenticationResponse verifyOtp(VerifyOtpRequest request) {
        log.info("Verifying OTP for email: {}", request.getEmail());
        
        // Verify OTP
        if (!otpService.verifyOtp(request.getEmail(), request.getOtpCode())) {
            throw new InvalidOtpException("Mã OTP không đúng hoặc đã hết hạn");
        }
        
        // Update user status
        User user = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new UsernameNotFoundException("User not found"));
        
        user.setStatus(UserStatus.ACTIVE);
        user.setEmailVerifiedAt(LocalDateTime.now());
        userRepository.save(user);
        
        // Generate tokens
        String accessToken = jwtService.generateToken(user);
        String refreshToken = jwtService.generateRefreshToken(user);
        
        log.info("OTP verified successfully for user: {}", request.getEmail());
        
        return AuthenticationResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .expiresIn(jwtService.getExpirationTime())
                .tokenType("Bearer")
                .user(UserResponse.fromEntity(user))
                .message("Xác thực thành công")
                .build();
    }
}
```

## 🔒 Security Utilities

### **SecurityUtils Class**

```java
@Component
@Slf4j
public class SecurityUtils {
    
    /**
     * Lấy user ID hiện tại từ SecurityContext
     */
    public static Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new UnauthorizedException("User chưa đăng nhập");
        }
        
        if (authentication.getPrincipal() instanceof UserDetails) {
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            
            if (userDetails instanceof User) {
                return ((User) userDetails).getId();
            }
            
            // If using custom UserDetails implementation
            if (userDetails instanceof CustomUserDetails) {
                return ((CustomUserDetails) userDetails).getUserId();
            }
        }
        
        throw new UnauthorizedException("Không thể xác định user hiện tại");
    }
    
    /**
     * Lấy email user hiện tại
     */
    public static String getCurrentUserEmail() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new UnauthorizedException("User chưa đăng nhập");
        }
        
        return authentication.getName();
    }
    
    /**
     * Lấy user details hiện tại
     */
    public static UserDetails getCurrentUserDetails() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new UnauthorizedException("User chưa đăng nhập");
        }
        
        if (authentication.getPrincipal() instanceof UserDetails) {
            return (UserDetails) authentication.getPrincipal();
        }
        
        throw new UnauthorizedException("Không thể lấy thông tin user");
    }
    
    /**
     * Kiểm tra user có role cụ thể không
     */
    public static boolean hasRole(String role) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        return authentication.getAuthorities().stream()
                .anyMatch(authority -> authority.getAuthority().equals("ROLE_" + role));
    }
    
    /**
     * Kiểm tra user có authority cụ thể không
     */
    public static boolean hasAuthority(String authority) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        return authentication.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals(authority));
    }
}
```

## 📝 Request/Response DTOs

### **Authentication DTOs**

```java
// Register Request
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegisterRequest {
    
    @NotBlank(message = "Email không được để trống")
    @Email(message = "Email không hợp lệ")
    private String email;
    
    @NotBlank(message = "Mật khẩu không được để trống")
    @Size(min = 8, message = "Mật khẩu phải có ít nhất 8 ký tự")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*$", 
             message = "Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường và 1 số")
    private String password;
    
    @NotBlank(message = "Xác nhận mật khẩu không được để trống")
    private String confirmPassword;
    
    @NotBlank(message = "Họ tên không được để trống")
    @Size(max = 100, message = "Họ tên không được vượt quá 100 ký tự")
    private String fullName;
    
    @NotNull(message = "Ngày sinh không được để trống")
    @Past(message = "Ngày sinh phải là ngày trong quá khứ")
    private LocalDate dateOfBirth;
    
    private Gender gender;
    
    @Pattern(regexp = "^[0-9+\\-\\s()]+$", message = "Số điện thoại không hợp lệ")
    private String phoneNumber;
    
    // Custom validation
    @AssertTrue(message = "Mật khẩu xác nhận không khớp")
    public boolean isPasswordMatching() {
        return password != null && password.equals(confirmPassword);
    }
}

// Authentication Request
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthenticationRequest {
    
    @NotBlank(message = "Email không được để trống")
    @Email(message = "Email không hợp lệ")
    private String email;
    
    @NotBlank(message = "Mật khẩu không được để trống")
    private String password;
    
    private boolean rememberMe = false;
}

// Authentication Response
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthenticationResponse {
    
    private String accessToken;
    private String refreshToken;
    private String tokenType;
    private Long expiresIn;
    private UserResponse user;
    private String message;
    private String status;
}

// Refresh Token Request
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefreshTokenRequest {
    
    @NotBlank(message = "Refresh token không được để trống")
    private String refreshToken;
}

// Verify OTP Request
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VerifyOtpRequest {
    
    @NotBlank(message = "Email không được để trống")
    @Email(message = "Email không hợp lệ")
    private String email;
    
    @NotBlank(message = "Mã OTP không được để trống")
    @Size(min = 6, max = 6, message = "Mã OTP phải có 6 ký tự")
    private String otpCode;
}
```
