# 🧪 JWT Testing & Best Practices - Medication App

## 📋 Tổng quan

Tài liệu này tập trung vào testing JWT authentication và các best practices để đảm bảo security và performance.

## 🧪 Testing JWT Authentication

### **1. Unit Tests cho JwtService**

```java
@ExtendWith(MockitoExtension.class)
class JwtServiceTest {

    @InjectMocks
    private JwtService jwtService;

    @Mock
    private UserDetails userDetails;

    @BeforeEach
    void setUp() {
        // Set test values using reflection
        ReflectionTestUtils.setField(jwtService, "secretKey", "404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970");
        ReflectionTestUtils.setField(jwtService, "jwtExpiration", 86400000L); // 24 hours
        ReflectionTestUtils.setField(jwtService, "refreshExpiration", 604800000L); // 7 days
    }

    @Test
    void shouldGenerateToken() {
        // Given
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        // When
        String token = jwtService.generateToken(userDetails);

        // Then
        assertThat(token).isNotNull();
        assertThat(token).isNotEmpty();
        assertThat(jwtService.extractUsername(token)).isEqualTo("<EMAIL>");
    }

    @Test
    void shouldValidateToken() {
        // Given
        when(userDetails.getUsername()).thenReturn("<EMAIL>");
        String token = jwtService.generateToken(userDetails);

        // When
        boolean isValid = jwtService.isTokenValid(token, userDetails);

        // Then
        assertThat(isValid).isTrue();
    }

    @Test
    void shouldExtractUsername() {
        // Given
        when(userDetails.getUsername()).thenReturn("<EMAIL>");
        String token = jwtService.generateToken(userDetails);

        // When
        String username = jwtService.extractUsername(token);

        // Then
        assertThat(username).isEqualTo("<EMAIL>");
    }

    @Test
    void shouldDetectExpiredToken() {
        // Given
        ReflectionTestUtils.setField(jwtService, "jwtExpiration", -1000L); // Expired
        when(userDetails.getUsername()).thenReturn("<EMAIL>");
        String token = jwtService.generateToken(userDetails);

        // When
        boolean isValid = jwtService.isTokenValid(token, userDetails);

        // Then
        assertThat(isValid).isFalse();
    }

    @Test
    void shouldGenerateRefreshToken() {
        // Given
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        // When
        String refreshToken = jwtService.generateRefreshToken(userDetails);

        // Then
        assertThat(refreshToken).isNotNull();
        assertThat(refreshToken).isNotEmpty();
        assertThat(jwtService.extractUsername(refreshToken)).isEqualTo("<EMAIL>");
    }
}
```

### **2. Integration Tests cho Authentication**

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.properties")
@Transactional
class AuthenticationIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @LocalServerPort
    private int port;

    private String baseUrl;

    @BeforeEach
    void setUp() {
        baseUrl = "http://localhost:" + port + "/api/v1/auth";
    }

    @Test
    void shouldRegisterUser() {
        // Given
        RegisterRequest request = RegisterRequest.builder()
                .email("<EMAIL>")
                .password("Password123")
                .confirmPassword("Password123")
                .fullName("Test User")
                .dateOfBirth(LocalDate.of(1990, 1, 1))
                .build();

        // When
        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
                baseUrl + "/register",
                request,
                ApiResponse.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        Optional<User> savedUser = userRepository.findByEmail("<EMAIL>");
        assertThat(savedUser).isPresent();
        assertThat(savedUser.get().getStatus()).isEqualTo(UserStatus.PENDING_VERIFICATION);
    }

    @Test
    void shouldLoginUser() {
        // Given - Create verified user
        User user = User.builder()
                .email("<EMAIL>")
                .password(passwordEncoder.encode("Password123"))
                .fullName("Test User")
                .status(UserStatus.ACTIVE)
                .role(Role.USER)
                .build();
        userRepository.save(user);

        AuthenticationRequest request = AuthenticationRequest.builder()
                .email("<EMAIL>")
                .password("Password123")
                .build();

        // When
        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
                baseUrl + "/login",
                request,
                ApiResponse.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();

        // Verify response contains tokens
        Map<String, Object> data = (Map<String, Object>) response.getBody().getData();
        assertThat(data.get("accessToken")).isNotNull();
        assertThat(data.get("refreshToken")).isNotNull();
    }

    @Test
    void shouldRejectInvalidCredentials() {
        // Given
        AuthenticationRequest request = AuthenticationRequest.builder()
                .email("<EMAIL>")
                .password("wrongpassword")
                .build();

        // When
        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
                baseUrl + "/login",
                request,
                ApiResponse.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
        assertThat(response.getBody().isSuccess()).isFalse();
    }

    @Test
    void shouldAccessProtectedEndpointWithValidToken() {
        // Given - Create user and get token
        User user = createActiveUser();
        String token = generateTokenForUser(user);

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        // When
        ResponseEntity<ApiResponse> response = restTemplate.exchange(
                "http://localhost:" + port + "/api/v1/auth/me",
                HttpMethod.GET,
                entity,
                ApiResponse.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
    }

    @Test
    void shouldRejectProtectedEndpointWithoutToken() {
        // When
        ResponseEntity<ApiResponse> response = restTemplate.getForEntity(
                "http://localhost:" + port + "/api/v1/medicines",
                ApiResponse.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }

    private User createActiveUser() {
        User user = User.builder()
                .email("<EMAIL>")
                .password(passwordEncoder.encode("Password123"))
                .fullName("Test User")
                .status(UserStatus.ACTIVE)
                .role(Role.USER)
                .build();
        return userRepository.save(user);
    }

    private String generateTokenForUser(User user) {
        // Implementation to generate token for testing
        return "test-jwt-token";
    }
}
```

### **3. Security Tests**

```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class SecurityConfigTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    void shouldAllowPublicEndpoints() throws Exception {
        // Test auth endpoints
        mockMvc.perform(post("/api/v1/auth/register"))
                .andExpect(status().isBadRequest()); // Bad request due to missing body, not unauthorized

        // Test anonymous endpoints
        mockMvc.perform(get("/api/v1/anonymous/medicines"))
                .andExpect(status().isOk());

        // Test public data endpoints
        mockMvc.perform(get("/api/v1/medicine-types"))
                .andExpect(status().isOk());

        // Test Swagger endpoints
        mockMvc.perform(get("/swagger-ui/index.html"))
                .andExpect(status().isOk());
    }

    @Test
    void shouldProtectPrivateEndpoints() throws Exception {
        // Test protected endpoints without token
        mockMvc.perform(get("/api/v1/medicines"))
                .andExpect(status().isUnauthorized());

        mockMvc.perform(get("/api/v1/prescriptions"))
                .andExpect(status().isUnauthorized());

        mockMvc.perform(get("/api/v1/auth/me"))
                .andExpected(status().isUnauthorized());
    }

    @Test
    void shouldAcceptValidJwtToken() throws Exception {
        // Given
        String validToken = generateValidTestToken();

        // When & Then
        mockMvc.perform(get("/api/v1/auth/me")
                .header("Authorization", "Bearer " + validToken))
                .andExpect(status().isOk());
    }

    @Test
    void shouldRejectInvalidJwtToken() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/auth/me")
                .header("Authorization", "Bearer invalid-token"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void shouldRejectExpiredJwtToken() throws Exception {
        // Given
        String expiredToken = generateExpiredTestToken();

        // When & Then
        mockMvc.perform(get("/api/v1/auth/me")
                .header("Authorization", "Bearer " + expiredToken))
                .andExpect(status().isUnauthorized());
    }

    private String generateValidTestToken() {
        // Implementation to generate valid test token
        return "valid-test-token";
    }

    private String generateExpiredTestToken() {
        // Implementation to generate expired test token
        return "expired-test-token";
    }
}
```

## 🔒 Security Best Practices

### **1. JWT Configuration**

```yaml
# application.yml
jwt:
  secret: ${JWT_SECRET:404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970}
  expiration: ${JWT_EXPIRATION:86400000} # 24 hours
  refresh-token:
    expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7 days

# application-prod.yml
jwt:
  secret: ${JWT_SECRET} # Must be set via environment variable
  expiration: 3600000   # 1 hour in production
  refresh-token:
    expiration: 86400000 # 1 day in production
```

### **2. Security Headers**

```java
@Configuration
public class SecurityHeadersConfig {

    @Bean
    public FilterRegistrationBean<SecurityHeadersFilter> securityHeadersFilter() {
        FilterRegistrationBean<SecurityHeadersFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new SecurityHeadersFilter());
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(1);
        return registrationBean;
    }
}

@Component
public class SecurityHeadersFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // Security headers
        httpResponse.setHeader("X-Content-Type-Options", "nosniff");
        httpResponse.setHeader("X-Frame-Options", "DENY");
        httpResponse.setHeader("X-XSS-Protection", "1; mode=block");
        httpResponse.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
        httpResponse.setHeader("Content-Security-Policy", "default-src 'self'");

        chain.doFilter(request, response);
    }
}
```

### **3. Rate Limiting**

```java
@Component
@Slf4j
public class RateLimitingFilter implements Filter {

    private final RedisTemplate<String, String> redisTemplate;
    private final ObjectMapper objectMapper;

    // Rate limits per endpoint
    private static final Map<String, Integer> RATE_LIMITS = Map.of(
        "/api/v1/auth/login", 5,      // 5 attempts per minute
        "/api/v1/auth/register", 3,   // 3 attempts per minute
        "/api/v1/auth/refresh-token", 10 // 10 attempts per minute
    );

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String endpoint = httpRequest.getRequestURI();
        String clientIp = getClientIpAddress(httpRequest);

        if (RATE_LIMITS.containsKey(endpoint)) {
            String key = "rate_limit:" + endpoint + ":" + clientIp;
            Integer limit = RATE_LIMITS.get(endpoint);

            String currentCount = redisTemplate.opsForValue().get(key);
            int count = currentCount != null ? Integer.parseInt(currentCount) : 0;

            if (count >= limit) {
                log.warn("Rate limit exceeded for IP: {} on endpoint: {}", clientIp, endpoint);
                sendRateLimitResponse(httpResponse);
                return;
            }

            // Increment counter
            redisTemplate.opsForValue().increment(key);
            redisTemplate.expire(key, Duration.ofMinutes(1));
        }

        chain.doFilter(request, response);
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }

    private void sendRateLimitResponse(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
        response.setContentType("application/json");

        ApiResponse<Object> apiResponse = ApiResponse.builder()
                .success(false)
                .message("Quá nhiều yêu cầu. Vui lòng thử lại sau.")
                .build();

        response.getWriter().write(objectMapper.writeValueAsString(apiResponse));
    }
}
```

### **4. Token Blacklisting**

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class TokenBlacklistService {

    private final RedisTemplate<String, String> redisTemplate;
    private final JwtService jwtService;

    /**
     * Blacklist token khi user logout
     */
    public void blacklistToken(String token) {
        try {
            Date expiration = jwtService.extractExpiration(token);
            long ttl = expiration.getTime() - System.currentTimeMillis();

            if (ttl > 0) {
                String key = "blacklisted_token:" + token;
                redisTemplate.opsForValue().set(key, "true", Duration.ofMillis(ttl));
                log.info("Token blacklisted successfully");
            }
        } catch (Exception e) {
            log.error("Failed to blacklist token", e);
        }
    }

    /**
     * Kiểm tra token có bị blacklist không
     */
    public boolean isTokenBlacklisted(String token) {
        try {
            String key = "blacklisted_token:" + token;
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("Failed to check token blacklist status", e);
            return false;
        }
    }

    /**
     * Blacklist tất cả tokens của user (khi change password)
     */
    public void blacklistAllUserTokens(String userEmail) {
        try {
            String pattern = "user_tokens:" + userEmail + ":*";
            Set<String> keys = redisTemplate.keys(pattern);

            if (keys != null && !keys.isEmpty()) {
                for (String key : keys) {
                    String token = redisTemplate.opsForValue().get(key);
                    if (token != null) {
                        blacklistToken(token);
                    }
                }
                redisTemplate.delete(keys);
            }

            log.info("All tokens blacklisted for user: {}", userEmail);
        } catch (Exception e) {
            log.error("Failed to blacklist all user tokens", e);
        }
    }
}
```

## 🚨 Common Issues & Troubleshooting

### **1. Token Validation Issues**

```java
// Problem: Token validation fails
// Solution: Check token format and signature

@Component
@Slf4j
public class JwtTroubleshooter {

    public void debugTokenIssues(String token) {
        try {
            // Check token format
            String[] parts = token.split("\\.");
            if (parts.length != 3) {
                log.error("Invalid JWT format. Expected 3 parts, got: {}", parts.length);
                return;
            }

            // Decode header
            String header = new String(Base64.getUrlDecoder().decode(parts[0]));
            log.info("JWT Header: {}", header);

            // Decode payload (without verification)
            String payload = new String(Base64.getUrlDecoder().decode(parts[1]));
            log.info("JWT Payload: {}", payload);

            // Check expiration
            ObjectMapper mapper = new ObjectMapper();
            JsonNode payloadNode = mapper.readTree(payload);
            long exp = payloadNode.get("exp").asLong();
            long now = System.currentTimeMillis() / 1000;

            if (exp < now) {
                log.error("Token expired. Exp: {}, Now: {}", exp, now);
            } else {
                log.info("Token is not expired. Exp: {}, Now: {}", exp, now);
            }

        } catch (Exception e) {
            log.error("Error debugging token", e);
        }
    }
}
```

### **2. Performance Optimization**

```java
// Cache user details to avoid database calls
@Service
@RequiredArgsConstructor
public class CachedUserDetailsService implements UserDetailsService {

    private final UserRepository userRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // Try cache first
        String cacheKey = "user_details:" + username;
        UserDetails cachedUser = (UserDetails) redisTemplate.opsForValue().get(cacheKey);

        if (cachedUser != null) {
            return cachedUser;
        }

        // Load from database
        User user = userRepository.findByEmail(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found: " + username));

        UserDetails userDetails = new CustomUserDetails(user);

        // Cache for 15 minutes
        redisTemplate.opsForValue().set(cacheKey, userDetails, Duration.ofMinutes(15));

        return userDetails;
    }
}
```

### **3. Monitoring & Logging**

```java
@Component
@Slf4j
public class JwtMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter tokenGenerationCounter;
    private final Counter tokenValidationCounter;
    private final Timer tokenValidationTimer;

    public JwtMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.tokenGenerationCounter = Counter.builder("jwt.token.generation")
                .description("Number of JWT tokens generated")
                .register(meterRegistry);
        this.tokenValidationCounter = Counter.builder("jwt.token.validation")
                .description("Number of JWT token validations")
                .tag("result", "success")
                .register(meterRegistry);
        this.tokenValidationTimer = Timer.builder("jwt.token.validation.time")
                .description("Time taken to validate JWT tokens")
                .register(meterRegistry);
    }

    public void recordTokenGeneration() {
        tokenGenerationCounter.increment();
    }

    public void recordTokenValidation(boolean success) {
        Counter.builder("jwt.token.validation")
                .tag("result", success ? "success" : "failure")
                .register(meterRegistry)
                .increment();
    }

    public Timer.Sample startValidationTimer() {
        return Timer.start(meterRegistry);
    }
}
```

## 📚 Learning Resources

### **Recommended Reading**

1. **JWT Specification**: [RFC 7519](https://tools.ietf.org/html/rfc7519)
2. **Spring Security Documentation**: [Spring Security Reference](https://docs.spring.io/spring-security/reference/)
3. **OWASP JWT Security**: [JWT Security Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/JSON_Web_Token_for_Java_Cheat_Sheet.html)

### **Tools for Testing**

1. **JWT.io**: Online JWT debugger
2. **Postman**: API testing with JWT
3. **JMeter**: Load testing with authentication

### **Security Checklist**

- ✅ Use strong secret keys (minimum 256 bits)
- ✅ Set appropriate token expiration times
- ✅ Implement token refresh mechanism
- ✅ Use HTTPS in production
- ✅ Implement rate limiting
- ✅ Log security events
- ✅ Validate all JWT claims
- ✅ Implement token blacklisting
- ✅ Use secure headers
- ✅ Regular security audits

## 🎯 Summary

JWT authentication trong Medication App được implement với:

- ✅ **Secure token generation** với HS256 algorithm
- ✅ **Comprehensive validation** với expiration và signature checks
- ✅ **Proper error handling** với custom exceptions
- ✅ **Performance optimization** với caching
- ✅ **Security best practices** với rate limiting và blacklisting
- ✅ **Comprehensive testing** với unit và integration tests
- ✅ **Monitoring và logging** cho security events

Hệ thống này đảm bảo security cao và performance tốt cho production environment.

```

```
