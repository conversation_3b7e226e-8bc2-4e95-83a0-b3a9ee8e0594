# 🏗️ Spring Boot Architecture & Project Structure

## 📋 **Tổng quan**

Spring Boot là một framework Java giúp tạo ứng dụng web một cách nhanh chóng và dễ dàng. Project này sử dụng **Layered Architecture** (Kiến trúc phân lớp) để tổ chức code một cách có hệ thống.

---

## 🏛️ **Layered Architecture (Kiến trúc phân lớp)**

### **Mục đích:**

- **Separation of Concerns:** Mỗi layer có trách nhiệm riêng biệt
- **Maintainability:** <PERSON><PERSON> bảo trì, sửa đổi
- **Testability:** Dễ viết unit tests
- **Scalability:** Dễ mở rộng, thêm tính năng

### **4 Layers chính:**

```
┌─────────────────────────────────────┐
│           Controller Layer          │ ← REST API Endpoints
├─────────────────────────────────────┤
│            Service Layer            │ ← Business Logic
├─────────────────────────────────────┤
│          Repository Layer           │ ← Data Access
├─────────────────────────────────────┤
│            Entity Layer             │ ← Database Mapping
└─────────────────────────────────────┘
```

---

## 📁 **Project Structure**

### **C<PERSON>u tr<PERSON><PERSON> thư mục chuẩn:**

```
src/main/java/com/medication/
├── BackendApplication.java              # Main class (Entry point)
├── controller/                          # REST Controllers
│   ├── UserController.java
│   ├── PrescriptionController.java
│   └── MedicineController.java
├── service/                             # Business Logic
│   ├── UserService.java
│   ├── PrescriptionService.java
│   └── MedicineService.java
├── repository/                          # Data Access
│   ├── UserRepository.java
│   ├── PrescriptionRepository.java
│   └── MedicineRepository.java
├── entity/                              # Database Entities
│   ├── User.java
│   ├── Prescription.java
│   └── Medicine.java
├── dto/                                 # Data Transfer Objects
│   ├── UserDto.java
│   ├── PrescriptionDto.java
│   └── MedicineDto.java
├── config/                              # Configuration
│   ├── SecurityConfig.java
│   └── CorsConfig.java
└── exception/                           # Custom Exceptions
    └── CustomException.java
```

---

## 🔄 **Request/Response Flow**

### **1. HTTP Request Flow:**

```
Flutter App (Client)
    ↓ HTTP Request (GET /api/users)
Controller (UserController)
    ↓ Call Service Method
Service (UserService)
    ↓ Call Repository Method
Repository (UserRepository)
    ↓ Execute SQL Query
Database (PostgreSQL)
```

### **2. HTTP Response Flow:**

```
Database (PostgreSQL)
    ↓ Raw Data
Repository (UserRepository)
    ↓ Entity Objects
Service (UserService)
    ↓ Business Logic + DTOs
Controller (UserController)
    ↓ JSON Response
Flutter App (Client)
```

---

## 🎯 **Chi tiết từng Layer**

### **1. Controller Layer (REST API)**

#### **Vai trò:**

- Nhận HTTP requests từ client
- Validate input data
- Gọi Service layer
- Trả về HTTP responses

#### **Annotations chính:**

```java
@RestController          // Đánh dấu class là REST controller
@RequestMapping("/api")  // Base path cho tất cả endpoints
@GetMapping("/users")    // HTTP GET endpoint
@PostMapping("/users")   // HTTP POST endpoint
@PutMapping("/users/{id}") // HTTP PUT endpoint
@DeleteMapping("/users/{id}") // HTTP DELETE endpoint
```

#### **Ví dụ:**

```java
@RestController
@RequestMapping("/api/users")
public class UserController {

    @GetMapping
    public List<UserDto> getAllUsers() {
        return userService.getAllUsers();
    }

    @PostMapping
    public UserDto createUser(@RequestBody UserDto userDto) {
        return userService.createUser(userDto);
    }
}
```

---

### **2. Service Layer (Business Logic)**

#### **Vai trò:**

- Xử lý business logic
- Validation business rules
- Transaction management
- Gọi Repository layer

#### **Annotations chính:**

```java
@Service              // Đánh dấu class là Service
@Transactional        // Quản lý database transactions
```

#### **Ví dụ:**

```java
@Service
@Transactional
public class UserService {

    public UserDto createUser(UserDto userDto) {
        // Business logic validation
        if (userRepository.existsByEmail(userDto.getEmail())) {
            throw new UserAlreadyExistsException("Email already exists");
        }

        // Convert DTO to Entity
        User user = convertToEntity(userDto);

        // Save to database
        User savedUser = userRepository.save(user);

        // Convert back to DTO
        return convertToDto(savedUser);
    }
}
```

---

### **3. Repository Layer (Data Access)**

#### **Vai trò:**

- Truy cập database
- Thực hiện CRUD operations
- Custom queries

#### **Annotations chính:**

```java
@Repository           // Đánh dấu interface là Repository
@Query("SELECT u FROM User u WHERE u.email = :email") // Custom query
```

#### **Ví dụ:**

```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    // Spring Data JPA tự động tạo method này
    Optional<User> findByEmail(String email);

    // Custom query
    @Query("SELECT u FROM User u WHERE u.age > :minAge")
    List<User> findUsersOlderThan(@Param("minAge") int minAge);
}
```

---

### **4. Entity Layer (Database Mapping)**

#### **Vai trò:**

- Mapping Java objects với database tables
- Định nghĩa database schema

#### **Annotations chính:**

```java
@Entity               // Đánh dấu class là JPA Entity
@Table(name = "users") // Tên table trong database
@Column(name = "email") // Tên column trong database
@Id                   // Primary key
@GeneratedValue       // Auto-generate ID
@OneToMany           // One-to-Many relationship
@ManyToOne           // Many-to-One relationship
```

#### **Ví dụ:**

```java
@Entity
@Table(name = "users")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "email", unique = true, nullable = false)
    private String email;

    @Column(name = "password", nullable = false)
    private String password;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL)
    private List<Prescription> prescriptions;
}
```

---

## 🔧 **Configuration Files**

### **1. application.properties**

```properties
# Database Configuration
spring.datasource.url=**********************************************
spring.datasource.username=dongtran
spring.datasource.password=
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Server Configuration
server.port=8080
```

### **2. build.gradle**

```gradle
dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.postgresql:postgresql'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}
```

---

## 🧪 **Testing Strategy**

### **1. Unit Tests**

- **Controller Tests:** Test API endpoints
- **Service Tests:** Test business logic
- **Repository Tests:** Test data access

### **2. Integration Tests**

- **End-to-End Tests:** Test toàn bộ flow
- **Database Tests:** Test với real database

---

## 📚 **Best Practices**

### **1. Naming Conventions**

- **Controllers:** `*Controller.java`
- **Services:** `*Service.java`
- **Repositories:** `*Repository.java`
- **Entities:** `*Entity.java` hoặc chỉ tên class
- **DTOs:** `*Dto.java`

### **2. Package Structure**

- **com.medication.controller**
- **com.medication.service**
- **com.medication.repository**
- **com.medication.entity**
- **com.medication.dto**

### **3. Error Handling**

- **Global Exception Handler:** `@ControllerAdvice`
- **Custom Exceptions:** Extend `RuntimeException`
- **Proper HTTP Status Codes:** 200, 201, 400, 404, 500

---

## 🚀 **Development Workflow**

### **1. Tạo Entity trước**

```java
@Entity
public class User {
    // Define fields and relationships
}
```

### **2. Tạo Repository**

```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    // Define custom queries
}
```

### **3. Tạo Service**

```java
@Service
public class UserService {
    // Implement business logic
}
```

### **4. Tạo Controller**

```java
@RestController
public class UserController {
    // Define API endpoints
}
```

### **5. Test và Debug**

- Run application
- Test API endpoints
- Check database
- Fix issues

---

## 📖 **Learning Resources**

### **1. Official Documentation**

- [Spring Boot Reference](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [Spring Data JPA](https://docs.spring.io/spring-data/jpa/docs/current/reference/html/)
- [Spring Security](https://docs.spring.io/spring-security/reference/)

### **2. Tutorials**

- [Spring Boot Tutorial](https://spring.io/guides)
- [Building REST services with Spring](https://spring.io/guides/gs/rest-service/)

### **3. Best Practices**

- [Spring Boot Best Practices](https://github.com/spring-projects/spring-boot/wiki/Best-Practices)
- [Spring Data JPA Best Practices](https://docs.spring.io/spring-data/jpa/docs/current/reference/html/#jpa.repositories)

---

**🎯 Mục tiêu:** Hiểu rõ kiến trúc này để có thể tổ chức code một cách có hệ thống và dễ maintain.
