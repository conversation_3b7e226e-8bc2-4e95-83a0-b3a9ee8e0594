# 📖 Quick Reference - Spring Boot

## 🎯 **Annotations thường dùng**

### **Core Spring Annotations**

#### **@Component & Stereotypes**

```java
@Component          // Generic Spring component
@Service            // Business logic layer
@Repository         // Data access layer
@Controller         // Web controller (MVC)
@RestController     // REST API controller
```

#### **Dependency Injection**

```java
@Autowired          // Inject dependency
@Qualifier          // Specify which bean to inject
@Value              // Inject property value
@ConfigurationProperties // Inject multiple properties
```

#### **Bean Configuration**

```java
@Configuration      // Configuration class
@Bean               // Define a bean
@Primary            // Primary bean when multiple candidates
@ConditionalOnProperty // Conditional bean creation
```

---

### **Web Layer Annotations**

#### **Controller Annotations**

```java
@RestController     // REST controller (no view resolution)
@Controller         // MVC controller
@RequestMapping("/api") // Base path for all endpoints
@CrossOrigin        // CORS configuration
```

#### **HTTP Method Annotations**

```java
@GetMapping("/users")           // GET /api/users
@PostMapping("/users")          // POST /api/users
@PutMapping("/users/{id}")      // PUT /api/users/{id}
@DeleteMapping("/users/{id}")   // DELETE /api/users/{id}
@PatchMapping("/users/{id}")    // PATCH /api/users/{id}
```

#### **Request/Response Annotations**

```java
@RequestBody        // Request body parameter
@ResponseBody       // Response body
@PathVariable       // Path variable
@RequestParam       // Query parameter
@RequestHeader      // Header parameter
@ResponseStatus     // HTTP status code
```

#### **Example Controller**

```java
@RestController
@RequestMapping("/api/users")
public class UserController {

    @GetMapping
    public List<UserDto> getAllUsers() {
        return userService.getAllUsers();
    }

    @GetMapping("/{id}")
    public UserDto getUser(@PathVariable Long id) {
        return userService.getUserById(id);
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public UserDto createUser(@RequestBody @Valid UserDto userDto) {
        return userService.createUser(userDto);
    }

    @PutMapping("/{id}")
    public UserDto updateUser(@PathVariable Long id, @RequestBody UserDto userDto) {
        return userService.updateUser(id, userDto);
    }

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
    }
}
```

---

### **Data Layer Annotations**

#### **JPA Entity Annotations**

```java
@Entity             // JPA entity
@Table(name = "users") // Table name
@Column(name = "email") // Column name
@Id                 // Primary key
@GeneratedValue     // Auto-generated value
@OneToMany          // One-to-Many relationship
@ManyToOne          // Many-to-One relationship
@ManyToMany         // Many-to-Many relationship
@JoinColumn         // Foreign key column
@JoinTable          // Join table for Many-to-Many
```

#### **Example Entity**

```java
@Entity
@Table(name = "users")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "email", unique = true, nullable = false)
    private String email;

    @Column(name = "password", nullable = false)
    private String password;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL)
    private List<Prescription> prescriptions;

    // Constructors, getters, setters
}
```

#### **Repository Annotations**

```java
@Repository         // Data access component
@Query              // Custom JPQL query
@Modifying          // Modifying query (UPDATE, DELETE)
@Transactional      // Transaction management
```

#### **Example Repository**

```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    // Spring Data JPA auto-generates these methods
    Optional<User> findByEmail(String email);
    List<User> findByAgeGreaterThan(int age);
    boolean existsByEmail(String email);

    // Custom query
    @Query("SELECT u FROM User u WHERE u.email LIKE %:domain")
    List<User> findByEmailDomain(@Param("domain") String domain);

    // Modifying query
    @Modifying
    @Query("UPDATE User u SET u.active = :active WHERE u.id = :id")
    void updateUserStatus(@Param("id") Long id, @Param("active") boolean active);
}
```

---

### **Service Layer Annotations**

#### **Service Annotations**

```java
@Service            // Business logic component
@Transactional      // Transaction management
@Async              // Asynchronous method
@Cacheable          // Method result caching
@CacheEvict         // Cache eviction
```

#### **Example Service**

```java
@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Transactional(readOnly = true)
    public List<UserDto> getAllUsers() {
        return userRepository.findAll()
            .stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    public UserDto createUser(UserDto userDto) {
        if (userRepository.existsByEmail(userDto.getEmail())) {
            throw new UserAlreadyExistsException("Email already exists");
        }

        User user = convertToEntity(userDto);
        User savedUser = userRepository.save(user);
        return convertToDto(savedUser);
    }

    @Async
    public CompletableFuture<Void> sendWelcomeEmail(User user) {
        // Async email sending
        return CompletableFuture.completedFuture(null);
    }
}
```

---

### **Validation Annotations**

#### **Bean Validation**

```java
@NotNull            // Field cannot be null
@NotEmpty           // String/Collection cannot be empty
@NotBlank           // String cannot be blank
@Size(min = 1, max = 100) // Size constraint
@Email              // Email format validation
@Min(18)            // Minimum value
@Max(100)           // Maximum value
@Pattern(regexp = "...") // Regex pattern
@Valid              // Validate nested objects
```

#### **Example DTO with Validation**

```java
public class UserDto {

    @NotBlank(message = "Email is required")
    @Email(message = "Invalid email format")
    private String email;

    @NotBlank(message = "Password is required")
    @Size(min = 6, message = "Password must be at least 6 characters")
    private String password;

    @NotBlank(message = "Name is required")
    @Size(max = 100, message = "Name cannot exceed 100 characters")
    private String name;

    @Min(value = 0, message = "Age cannot be negative")
    @Max(value = 150, message = "Age cannot exceed 150")
    private Integer age;

    // Getters and setters
}
```

---

### **Security Annotations**

#### **Spring Security**

```java
@EnableWebSecurity  // Enable Spring Security
@PreAuthorize       // Method-level security
@PostAuthorize      // Post-execution security
@Secured            // Role-based security
@RolesAllowed       // Role-based security
```

#### **Example Security Config**

```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .csrf(csrf -> csrf.disable())
            .cors(cors -> cors.configurationSource(corsConfigurationSource()));

        return http.build();
    }
}
```

---

### **Configuration Annotations**

#### **Application Properties**

```java
@ConfigurationProperties(prefix = "app")
@PropertySource("classpath:custom.properties")
@Value("${app.name}")
```

#### **Example Configuration**

```java
@Configuration
@ConfigurationProperties(prefix = "app")
public class AppConfig {

    private String name;
    private String version;
    private DatabaseConfig database;

    // Getters and setters
}

@Component
public class DatabaseService {

    @Value("${spring.datasource.url}")
    private String databaseUrl;

    @Value("${app.feature.enabled:false}")
    private boolean featureEnabled;
}
```

---

## 🔧 **Common Patterns**

### **1. DTO Pattern**

```java
// Entity
@Entity
public class User {
    private Long id;
    private String email;
    private String password; // Never expose in DTO
}

// DTO
public class UserDto {
    private Long id;
    private String email;
    // No password field
}
```

### **2. Repository Pattern**

```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    // Spring Data JPA provides basic CRUD operations
    // You can add custom methods
}
```

### **3. Service Pattern**

```java
@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    public UserDto createUser(UserDto userDto) {
        // Business logic here
        // Convert DTO to Entity
        // Save to database
        // Convert back to DTO
        // Return DTO
    }
}
```

### **4. Controller Pattern**

```java
@RestController
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping("/{id}")
    public ResponseEntity<UserDto> getUser(@PathVariable Long id) {
        UserDto user = userService.getUserById(id);
        return ResponseEntity.ok(user);
    }
}
```

---

## 📝 **Common HTTP Status Codes**

```java
// Success
HttpStatus.OK (200)           // GET successful
HttpStatus.CREATED (201)      // POST successful
HttpStatus.NO_CONTENT (204)   // DELETE successful

// Client Error
HttpStatus.BAD_REQUEST (400)  // Invalid request
HttpStatus.UNAUTHORIZED (401) // Authentication required
HttpStatus.FORBIDDEN (403)    // Authorization failed
HttpStatus.NOT_FOUND (404)    // Resource not found
HttpStatus.CONFLICT (409)     // Resource conflict

// Server Error
HttpStatus.INTERNAL_SERVER_ERROR (500) // Server error
```

---

## 🚀 **Quick Start Templates**

### **1. Basic REST Controller**

```java
@RestController
@RequestMapping("/api")
public class BasicController {

    @GetMapping("/hello")
    public String hello() {
        return "Hello, Spring Boot!";
    }

    @GetMapping("/users")
    public List<User> getUsers() {
        return userService.getAllUsers();
    }
}
```

### **2. Basic Entity**

```java
@Entity
@Table(name = "items")
public class Item {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    // Getters and setters
}
```

### **3. Basic Repository**

```java
@Repository
public interface ItemRepository extends JpaRepository<Item, Long> {
    List<Item> findByName(String name);
}
```

### **4. Basic Service**

```java
@Service
public class ItemService {

    @Autowired
    private ItemRepository itemRepository;

    public List<Item> getAllItems() {
        return itemRepository.findAll();
    }

    public Item createItem(Item item) {
        return itemRepository.save(item);
    }
}
```

---

**💡 Tip:** Bookmark this page for quick reference while coding!
