# 🗄️ Database Schema (ERD) - Ứng dụng Tủ Thuốc Gia Đình

## 📋 Tổng quan

Database schema được thiết kế cho ứng dụng quản lý tủ thuốc gia đình với PostgreSQL, sử dụng Spring Boot JPA/Hibernate và **thiết kế hybrid family/member model** để hỗ trợ cả dependent members (trẻ em, người già không có tài khoản) và independent members (có tài khoản riêng).

**🆕 NEW: Authentication Optional Pattern** - Hỗ trợ cả Anonymous và Registered users

## 🎯 Mục tiêu thiết kế

- **Tính linh hoạt**: Hỗ trợ quản lý thuốc cho cả cá nhân và gia đình
- **Hybrid Model**: Kết hợp 2 loại thành viên (dependent/independent)
- **🆕 Anonymous Support**: Hỗ trợ users không đăng nhập với limited features
- **Tính mở rộng**: Dễ dàng thêm tính năng mới
- **Hiệu suất**: T<PERSON>i ưu hóa cho các truy vấn thường xuyên
- **Bảo mật**: Đảm bảo dữ liệu cá nhân được bảo vệ
- **Tính toàn vẹn**: Ràng buộc dữ liệu chặt chẽ

## 🏗️ Thiết kế Hybrid Family/Member

### **Concept chính:**

- **USERS**: Tài khoản chính có thể đăng nhập
- **🆕 ANONYMOUS_SESSIONS**: Anonymous user sessions (UUID-based)
- **FAMILIES**: Nhóm gia đình độc lập (registered users only)
- **FAMILY_MEMBERS**: Thành viên có 2 loại:
  - **DEPENDENT**: Không có tài khoản (trẻ em, người già), được quản lý bởi user khác
  - **INDEPENDENT**: Có tài khoản riêng, có thể tự quản lý
- **FAMILY_INVITATIONS**: Hệ thống mời thành viên vào family

### **🆕 User Types:**

```sql
-- User types enum
CREATE TYPE user_type_enum AS ENUM ('ANONYMOUS', 'REGISTERED', 'PREMIUM');

-- Anonymous session status enum
CREATE TYPE anonymous_session_status_enum AS ENUM ('ACTIVE', 'EXPIRED', 'MIGRATED', 'CLEANED');

-- Gender enum
CREATE TYPE gender_enum AS ENUM ('MALE', 'FEMALE', 'OTHER');

-- Member type enum
CREATE TYPE member_type_enum AS ENUM ('DEPENDENT', 'INDEPENDENT');

-- Family role enum
CREATE TYPE family_role_enum AS ENUM ('OWNER', 'ADMIN', 'MEMBER');

-- Invitation type enum
CREATE TYPE invitation_type_enum AS ENUM ('CREATE_ACCOUNT', 'VIEW_ONLY');

-- Invitation status enum
CREATE TYPE invitation_status_enum AS ENUM ('PENDING', 'ACCEPTED', 'REJECTED', 'EXPIRED');

-- Notification type enum
CREATE TYPE notification_type_enum AS ENUM ('REMINDER', 'ALERT', 'INFO');

-- Notification status enum
CREATE TYPE notification_status_enum AS ENUM ('PENDING', 'SENT', 'READ');

-- Schedule status enum
CREATE TYPE schedule_status_enum AS ENUM ('PENDING', 'TAKEN', 'SKIPPED');

-- Audit action enum
CREATE TYPE audit_action_enum AS ENUM ('CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'MIGRATE');

-- Add status column to anonymous_sessions table
ALTER TABLE anonymous_sessions ADD COLUMN status anonymous_session_status_enum DEFAULT 'ACTIVE';
```

### **Ưu điểm:**

- ✅ Hỗ trợ trẻ em/người già không có khả năng tạo tài khoản
- ✅ Hỗ trợ người lớn có tài khoản riêng tham gia family
- ✅ Tránh trùng lặp dữ liệu trong cùng family
- ✅ Cho phép chuyển đổi từ dependent sang independent
- ✅ Hỗ trợ multi-family membership
- **🆕 Anonymous users có thể dùng app ngay lập tức**
- **🆕 Seamless migration từ anonymous sang registered**

---

## 🏗️ Entity Relationship Diagram (ERD)

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     USERS       │    │    FAMILIES     │    │ FAMILY_MEMBERS  │    │FAMILY_INVITATIONS│
├─────────────────┤    ├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (UUID) PK    │    │ id (UUID) PK    │    │ id (UUID) PK    │    │ id (UUID) PK    │
│ email UNIQUE    │    │ tên_gia_đình    │    │ family_id FK    │    │ family_id FK    │
│ password        │    │ owner_id FK     │    │ user_id FK (NULL)│   │ invited_by FK   │
│ họ_tên          │    │ mã_invite       │    │ managed_by FK   │    │ email           │
│ ngày_sinh       │    │ ngày_tạo        │    │ tên             │    │ invitation_type │
│ giới_tính       │    │ ngày_cập_nhật   │    │ tuổi            │    │ trạng_thái ENUM │
│ ảnh_đại_diện    │    └─────────────────┘    │ giới_tính       │    │ mã_invite       │
│ is_active       │             │             │ ảnh             │    │ hết_hạn         │
│ 🆕 user_type     │             │             │ member_type ENUM│    │ ngày_tạo        │
│ 🆕 anonymous_id  │             │             │ can_login BOOL  │    │ ngày_cập_nhật   │
│ ngày_tạo        │             │             │ ngày_tạo        │    └─────────────────┘
│ ngày_cập_nhật   │             │             │ ngày_cập_nhật   │             │
└─────────────────┘             │             └─────────────────┘             │
         │                      │                      │                      │
         │                      ▼                      ▼                      │
         │             ┌─────────────────┐    ┌─────────────────┐             │
         │             │  PRESCRIPTIONS  │    │ MEDICINE_TYPES  │             │
         │             ├─────────────────┤    ├─────────────────┤             │
         │             │ id (UUID) PK    │    │ id (UUID) PK    │             │
         │             │ member_id FK    │    │ tên_loại        │             │
         │             │ bác_sĩ          │    │ mô_tả           │             │
         │             │ ngày_kê_đơn     │    │ màu_sắc         │             │
         │             │ ngày_bắt_đầu    │    │ icon            │             │
         │             │ ngày_kết_thúc   │    │ ngày_tạo        │             │
         │             │ ghi_chú         │    │ ngày_cập_nhật   │             │
         │             │ 🆕 user_type     │    └─────────────────┘             │
         │             │ 🆕 anonymous_id  │             │                      │
         │             │ ngày_tạo        │             │                      │
         │             │ ngày_cập_nhật   │             │                      │
         │             └─────────────────┘             │                      │
         │                      │                      │                      │
         │                      │                      ▼                      │
         │                      │             ┌─────────────────┐             │
         │                      │             │    MEDICINES    │             │
         │                      │             ├─────────────────┤             │
         │                      │             │ id (UUID) PK    │             │
         │                      │             │ tên_thuốc       │             │
         │                      │             │ loại_thuốc_id FK│            │
         │                      │             │ liều_lượng      │             │
         │                      │             │ đơn_vị          │             │
         │                      │             │ hướng_dẫn       │             │
         │                      │             │ ảnh_thuốc       │             │
         │                      │             │ hạn_sử_dụng     │             │
         │                      │             │ số_lượng        │             │
         │                      │             │ 🆕 user_type     │             │
         │                      │             │ 🆕 anonymous_id  │             │
         │                      │             │ ngày_tạo        │             │
         │                      │             │ ngày_cập_nhật   │             │
         │                      │             └─────────────────┘             │
         │                      │                      │                      │
         │                      ▼                      ▼                      │
         │             ┌─────────────────┐    ┌─────────────────┐             │
         │             │PRESCRIPTION_MEDICINES│ │MEDICATION_SCHEDULE│           │
         │             ├─────────────────┤    ├─────────────────┤             │
         │             │ id (UUID) PK    │    │ id (UUID) PK    │             │
         │             │ prescription_id │    │ prescription_id │             │
         │             │ medicine_id FK  │    │ member_id FK    │             │
         │             │ liều_lượng      │    │ medicine_id FK  │             │
         │             │ tần_suất        │    │ thời_gian_uống  │             │
         │             │ thời_gian_uống  │    │ liều_lượng      │             │
         │             │ ghi_chú         │    │ trạng_thái ENUM │             │
         │             │ ngày_tạo        │    │ ngày_tạo        │             │
         │             │ ngày_cập_nhật   │    │ ngày_cập_nhật   │             │
         │             └─────────────────┘    └─────────────────┘             │
         │                                             │                      │
         ▼                                             ▼                      │
┌─────────────────┐                           ┌─────────────────┐             │
│  NOTIFICATIONS  │                           │                 │             │
├─────────────────┤                           │                 │             │
│ id (UUID) PK    │                           │                 │             │
│ user_id FK      │                           │                 │             │
│ loại_thông_báo  │                           │                 │             │
│ tiêu_đề         │                           │                 │             │
│ nội_dung        │                           │                 │             │
│ trạng_thái ENUM │                           │                 │             │
│ ngày_tạo        │                           │                 │             │
│ ngày_gửi        │                           │                 │             │
│ ngày_cập_nhật   │                           │                 │             │
└─────────────────┘                           │                 │             │
                                              │                 │             │
                                              └─────────────────┴─────────────┘

🆕 NEW: ANONYMOUS_SESSIONS TABLE
┌─────────────────┐
│ANONYMOUS_SESSIONS│
├─────────────────┤
│ id (UUID) PK    │
│ anonymous_user_id│
│ device_id       │
│ app_version     │
│ created_at      │
│ last_activity_at│
│ data_synced     │
│ migrated_to_user_id│
│ migrated_at     │
│ status          │
└─────────────────┘
```

---

## 📊 Chi tiết các bảng

### 1. **USERS** - Bảng người dùng chính

| Trường                   | Kiểu dữ liệu       | Bắt buộc  | Mô tả                                         |
| ------------------------ | ------------------ | --------- | --------------------------------------------- |
| id                       | UUID               | Có        | Primary Key                                   |
| email                    | VARCHAR(255)       | Không     | Email đăng nhập (🆕 nullable cho anonymous)   |
| password                 | VARCHAR(255)       | Không     | Mật khẩu (hashed) (🆕 nullable cho anonymous) |
| họ_tên                   | VARCHAR(255)       | Có        | Họ và tên đầy đủ                              |
| ngày_sinh                | DATE               | Không     | Ngày sinh                                     |
| giới_tính                | gender_enum        | Không     | Nam/Nữ/Khác                                   |
| ảnh_đại_diện             | VARCHAR(500)       | Không     | URL ảnh đại diện                              |
| **🆕 user_type**         | **user_type_enum** | **Có**    | **ANONYMOUS/REGISTERED/PREMIUM**              |
| **🆕 anonymous_user_id** | **VARCHAR(36)**    | **Không** | **UUID cho anonymous users**                  |
| ngày_tạo                 | TIMESTAMP          | Có        | Ngày tạo tài khoản                            |
| ngày_cập_nhật            | TIMESTAMP          | Có        | Ngày cập nhật cuối                            |

**Indexes:**

- `idx_users_email` (email) - UNIQUE
- `idx_users_created_at` (ngày_tạo)
- **🆕 `idx_users_user_type` (user_type)**
- **🆕 `idx_users_anonymous_id` (anonymous_user_id)**

### 2. **🆕 ANONYMOUS_SESSIONS** - Anonymous user sessions

| Trường              | Kiểu dữ liệu                  | Bắt buộc | Mô tả                    |
| ------------------- | ----------------------------- | -------- | ------------------------ |
| id                  | UUID                          | Có       | Primary Key              |
| anonymous_user_id   | VARCHAR(36)                   | Có       | Anonymous user ID (UUID) |
| device_id           | VARCHAR(100)                  | Không    | Device identifier        |
| app_version         | VARCHAR(20)                   | Không    | App version              |
| created_at          | TIMESTAMP                     | Có       | Session creation time    |
| last_activity_at    | TIMESTAMP                     | Có       | Last activity time       |
| data_synced         | BOOLEAN                       | Có       | Data synced to cloud     |
| migrated_to_user_id | BIGINT                        | Không    | Migrated to user ID      |
| migrated_at         | TIMESTAMP                     | Không    | Migration timestamp      |
| status              | anonymous_session_status_enum | Có       | Session status           |

**Indexes:**

- **🆕 `idx_anonymous_sessions_user_id` (anonymous_user_id)**
- **🆕 `idx_anonymous_sessions_last_activity` (last_activity_at)**
- **🆕 `idx_anonymous_sessions_migrated` (migrated_to_user_id)**

### 3. **FAMILIES** - Gia đình

| Trường        | Kiểu dữ liệu | Bắt buộc | Mô tả                 |
| ------------- | ------------ | -------- | --------------------- |
| id            | UUID         | Có       | Primary Key           |
| tên*gia*đình  | VARCHAR(255) | Có       | Tên hiển thị gia đình |
| owner_id      | UUID         | Có       | Foreign Key → USERS   |
| mã_invite     | VARCHAR(50)  | Có       | Mã mời unique         |
| ngày_tạo      | TIMESTAMP    | Có       | Ngày tạo gia đình     |
| ngày_cập_nhật | TIMESTAMP    | Có       | Ngày cập nhật cuối    |

**🆕 Note:** Families chỉ dành cho registered users

**Indexes:**

- `idx_families_owner_id` (owner_id)
- `idx_families_invite_code` (mã_invite) - UNIQUE
- `idx_families_created_at` (ngày_tạo)

### 4. **FAMILY_MEMBERS** - Thành viên gia đình

| Trường        | Kiểu dữ liệu | Bắt buộc | Mô tả                                    |
| ------------- | ------------ | -------- | ---------------------------------------- |
| id            | UUID         | Có       | Primary Key                              |
| family_id     | UUID         | Có       | Foreign Key → FAMILIES                   |
| user_id       | UUID         | Không    | Foreign Key → USERS (NULL nếu dependent) |
| managed_by    | UUID         | Không    | Foreign Key → USERS (ai quản lý)         |
| tên           | VARCHAR(255) | Có       | Tên thành viên                           |
| tuổi          | INTEGER      | Không    | Tuổi                                     |
| giới_tính     | GENDER_ENUM  | Không    | ENUM: MALE/FEMALE/OTHER                  |
| ảnh           | VARCHAR(500) | Không    | URL ảnh thành viên                       |
| member_type   | MEMBER_TYPE  | Có       | ENUM: DEPENDENT/INDEPENDENT              |
| can_login     | BOOLEAN      | Có       | Có thể đăng nhập không                   |
| ngày_tạo      | TIMESTAMP    | Có       | Ngày tạo                                 |
| ngày_cập_nhật | TIMESTAMP    | Có       | Ngày cập nhật cuối                       |

**🆕 Note:** Family members chỉ dành cho registered users

**Indexes:**

- `idx_family_members_family_id` (family_id)
- `idx_family_members_user_id` (user_id)
- `idx_family_members_managed_by` (managed_by)
- `idx_family_members_type` (member_type)
- `idx_family_members_unique` (family_id, tên, tuổi) - UNIQUE (tránh trùng trong family)

### 5. **FAMILY_INVITATIONS** - Lời mời gia đình

| Trường          | Kiểu dữ liệu      | Bắt buộc | Mô tả                                   |
| --------------- | ----------------- | -------- | --------------------------------------- |
| id              | UUID              | Có       | Primary Key                             |
| family_id       | UUID              | Có       | Foreign Key → FAMILIES                  |
| invited_by      | UUID              | Có       | Foreign Key → USERS                     |
| email           | VARCHAR(255)      | Có       | Email người được mời                    |
| member_name     | VARCHAR(255)      | Không    | Tên hiển thị                            |
| invitation_type | INVITATION_TYPE   | Có       | ENUM: CREATE_ACCOUNT/VIEW_ONLY          |
| trạng_thái      | INVITATION_STATUS | Có       | ENUM: PENDING/ACCEPTED/REJECTED/EXPIRED |
| mã_invite       | VARCHAR(50)       | Có       | Mã mời unique                           |
| hết_hạn         | TIMESTAMP         | Có       | Thời gian hết hạn                       |
| ngày_tạo        | TIMESTAMP         | Có       | Ngày tạo lời mời                        |
| ngày_cập_nhật   | TIMESTAMP         | Có       | Ngày cập nhật cuối                      |

**🆕 Note:** Family invitations chỉ dành cho registered users

**Indexes:**

- `idx_family_invitations_family_id` (family_id)
- `idx_family_invitations_invited_by` (invited_by)
- `idx_family_invitations_email` (email)
- `idx_family_invitations_status` (trạng_thái)
- `idx_family_invitations_invite_code` (mã_invite) - UNIQUE
- `idx_family_invitations_expires` (hết_hạn)

### 6. **MEDICINE_TYPES** - Loại thuốc

| Trường        | Kiểu dữ liệu | Bắt buộc | Mô tả              |
| ------------- | ------------ | -------- | ------------------ |
| id            | UUID         | Có       | Primary Key        |
| tên_loại      | VARCHAR(100) | Có       | Tên loại thuốc     |
| mô_tả         | TEXT         | Không    | Mô tả chi tiết     |
| màu_sắc       | VARCHAR(20)  | Không    | Màu sắc đại diện   |
| icon          | VARCHAR(100) | Không    | Icon đại diện      |
| ngày_tạo      | TIMESTAMP    | Có       | Ngày tạo           |
| ngày_cập_nhật | TIMESTAMP    | Có       | Ngày cập nhật cuối |

**Indexes:**

- `idx_medicine_types_name` (tên_loại)
- `idx_medicine_types_created` (ngày_tạo)

### 7. **MEDICINES** - Thuốc

| Trường                   | Kiểu dữ liệu       | Bắt buộc  | Mô tả                                           |
| ------------------------ | ------------------ | --------- | ----------------------------------------------- |
| id                       | UUID               | Có        | Primary Key                                     |
| tên_thuốc                | VARCHAR(200)       | Có        | Tên thuốc                                       |
| loại_thuốc_id            | UUID               | Có        | Foreign Key → MEDICINE_TYPES                    |
| liều_lượng               | VARCHAR(100)       | Không     | Liều lượng                                      |
| đơn_vị                   | VARCHAR(50)        | Không     | Đơn vị                                          |
| hướng_dẫn                | TEXT               | Không     | Hướng dẫn sử dụng                               |
| ảnh_thuốc                | VARCHAR(500)       | Không     | URL ảnh thuốc                                   |
| hạn_sử_dụng              | DATE               | Không     | Hạn sử dụng                                     |
| số_lượng                 | INTEGER            | Có        | Số lượng hiện tại                               |
| **🆕 user_type**         | **user_type_enum** | **Có**    | **ANONYMOUS/REGISTERED**                        |
| **🆕 anonymous_user_id** | **VARCHAR(36)**    | **Không** | **UUID cho anonymous users**                    |
| user_id                  | UUID               | Không     | Foreign Key → USERS (🆕 nullable cho anonymous) |
| family_id                | UUID               | Không     | Foreign Key → FAMILIES (registered only)        |
| ngày_tạo                 | TIMESTAMP          | Có        | Ngày tạo                                        |
| ngày_cập_nhật            | TIMESTAMP          | Có        | Ngày cập nhật cuối                              |

**Indexes:**

- `idx_medicines_name` (tên_thuốc)
- `idx_medicines_type` (loại_thuốc_id)
- `idx_medicines_user` (user_id)
- `idx_medicines_family` (family_id)
- `idx_medicines_expiry` (hạn_sử_dụng)
- **🆕 `idx_medicines_anonymous_user_id` (anonymous_user_id)**
- **🆕 `idx_medicines_user_type` (user_type)**

### 8. **PRESCRIPTIONS** - Đơn thuốc

| Trường                   | Kiểu dữ liệu       | Bắt buộc  | Mô tả                                           |
| ------------------------ | ------------------ | --------- | ----------------------------------------------- |
| id                       | UUID               | Có        | Primary Key                                     |
| **🆕 user_type**         | **user_type_enum** | **Có**    | **ANONYMOUS/REGISTERED**                        |
| **🆕 anonymous_user_id** | **VARCHAR(36)**    | **Không** | **UUID cho anonymous users**                    |
| patient_id               | UUID               | Không     | Foreign Key → USERS (🆕 nullable cho anonymous) |
| bác_sĩ                   | VARCHAR(200)       | Không     | Tên bác sĩ                                      |
| ngày*kê*đơn              | DATE               | Có        | Ngày kê đơn                                     |
| ngày*bắt*đầu             | DATE               | Có        | Ngày bắt đầu uống                               |
| ngày_kết_thúc            | DATE               | Không     | Ngày kết thúc uống                              |
| ghi_chú                  | TEXT               | Không     | Ghi chú                                         |
| ngày_tạo                 | TIMESTAMP          | Có        | Ngày tạo                                        |
| ngày_cập_nhật            | TIMESTAMP          | Có        | Ngày cập nhật cuối                              |

**Indexes:**

- `idx_prescriptions_patient` (patient_id)
- `idx_prescriptions_date` (ngày*kê*đơn)
- `idx_prescriptions_start_date` (ngày*bắt*đầu)
- **🆕 `idx_prescriptions_anonymous_user_id` (anonymous_user_id)**
- **🆕 `idx_prescriptions_user_type` (user_type)**

### 9. **PRESCRIPTION_ITEMS** - Chi tiết đơn thuốc

| Trường          | Kiểu dữ liệu | Bắt buộc | Mô tả                       |
| --------------- | ------------ | -------- | --------------------------- |
| id              | UUID         | Có       | Primary Key                 |
| prescription_id | UUID         | Có       | Foreign Key → PRESCRIPTIONS |
| medicine_id     | UUID         | Có       | Foreign Key → MEDICINES     |
| liều_lượng      | VARCHAR(100) | Có       | Liều lượng                  |
| tần_suất        | VARCHAR(100) | Có       | Tần suất uống               |
| thời_gian_uống  | VARCHAR(100) | Không    | Thời gian uống              |
| ghi_chú         | TEXT         | Không    | Ghi chú                     |
| ngày_tạo        | TIMESTAMP    | Có       | Ngày tạo                    |
| ngày_cập_nhật   | TIMESTAMP    | Có       | Ngày cập nhật cuối          |

**Indexes:**

- `idx_prescription_items_prescription` (prescription_id)
- `idx_prescription_items_medicine` (medicine_id)

### 10. **MEDICATION_SCHEDULES** - Lịch uống thuốc

| Trường          | Kiểu dữ liệu    | Bắt buộc | Mô tả                        |
| --------------- | --------------- | -------- | ---------------------------- |
| id              | UUID            | Có       | Primary Key                  |
| prescription_id | UUID            | Có       | Foreign Key → PRESCRIPTIONS  |
| member_id       | UUID            | Có       | Foreign Key → FAMILY_MEMBERS |
| medicine_id     | UUID            | Có       | Foreign Key → MEDICINES      |
| thời_gian_uống  | TIME            | Có       | Thời gian uống               |
| liều_lượng      | VARCHAR(100)    | Có       | Liều lượng                   |
| trạng_thái      | SCHEDULE_STATUS | Có       | ENUM: PENDING/TAKEN/SKIPPED  |
| ngày_tạo        | TIMESTAMP       | Có       | Ngày tạo                     |
| ngày_cập_nhật   | TIMESTAMP       | Có       | Ngày cập nhật cuối           |

**🆕 Note:** Schedules chỉ dành cho registered users với family members

**Indexes:**

- `idx_medication_schedules_prescription` (prescription_id)
- `idx_medication_schedules_member` (member_id)
- `idx_medication_schedules_medicine` (medicine_id)
- `idx_medication_schedules_status` (trạng_thái)
- `idx_medication_schedules_time` (thời_gian_uống)

### 11. **NOTIFICATIONS** - Thông báo

| Trường         | Kiểu dữ liệu        | Bắt buộc | Mô tả                     |
| -------------- | ------------------- | -------- | ------------------------- |
| id             | UUID                | Có       | Primary Key               |
| user_id        | UUID                | Có       | Foreign Key → USERS       |
| loại_thông_báo | NOTIFICATION_TYPE   | Có       | ENUM: REMINDER/ALERT/INFO |
| tiêu_đề        | VARCHAR(200)        | Có       | Tiêu đề thông báo         |
| nội_dung       | TEXT                | Có       | Nội dung thông báo        |
| trạng_thái     | NOTIFICATION_STATUS | Có       | ENUM: PENDING/SENT/READ   |
| ngày_tạo       | TIMESTAMP           | Có       | Ngày tạo                  |
| ngày_gửi       | TIMESTAMP           | Không    | Ngày gửi thông báo        |
| ngày_cập_nhật  | TIMESTAMP           | Có       | Ngày cập nhật cuối        |

**🆕 Note:** Notifications chỉ dành cho registered users

**Indexes:**

- `idx_notifications_user` (user_id)
- `idx_notifications_type` (loại_thông_báo)
- `idx_notifications_status` (trạng_thái)
- `idx_notifications_created` (ngày_tạo)

### 12. **AUDIT_LOGS** - Nhật ký hoạt động

| Trường                   | Kiểu dữ liệu    | Bắt buộc  | Mô tả                        |
| ------------------------ | --------------- | --------- | ---------------------------- |
| id                       | UUID            | Có        | Primary Key                  |
| user_id                  | UUID            | Không     | Foreign Key → USERS          |
| **🆕 anonymous_user_id** | **VARCHAR(36)** | **Không** | **UUID cho anonymous users** |
| action                   | AUDIT_ACTION    | Có        | Hành động thực hiện          |
| entity_type              | VARCHAR(50)     | Có        | Loại entity                  |
| entity_id                | VARCHAR(50)     | Có        | ID của entity                |
| old_values               | JSONB           | Không     | Giá trị cũ                   |
| new_values               | JSONB           | Không     | Giá trị mới                  |
| ip_address               | VARCHAR(45)     | Không     | IP address                   |
| user_agent               | TEXT            | Không     | User agent                   |
| ngày_tạo                 | TIMESTAMP       | Có        | Ngày tạo                     |

**Indexes:**

- `idx_audit_logs_user` (user_id)
- `idx_audit_logs_action` (action)
- `idx_audit_logs_entity` (entity_type, entity_id)
- `idx_audit_logs_created` (ngày_tạo)
- **🆕 `idx_audit_logs_anonymous_user_id` (anonymous_user_id)**

---

## 🆕 Database Migration Scripts

### **V20\_\_Add_anonymous_user_support.sql**

```sql
-- 1. Create all enums
CREATE TYPE user_type_enum AS ENUM ('ANONYMOUS', 'REGISTERED', 'PREMIUM');
CREATE TYPE anonymous_session_status_enum AS ENUM ('ACTIVE', 'EXPIRED', 'MIGRATED', 'CLEANED');
CREATE TYPE gender_enum AS ENUM ('MALE', 'FEMALE', 'OTHER');
CREATE TYPE member_type_enum AS ENUM ('DEPENDENT', 'INDEPENDENT');
CREATE TYPE family_role_enum AS ENUM ('OWNER', 'ADMIN', 'MEMBER');
CREATE TYPE invitation_type_enum AS ENUM ('CREATE_ACCOUNT', 'VIEW_ONLY');
CREATE TYPE invitation_status_enum AS ENUM ('PENDING', 'ACCEPTED', 'REJECTED', 'EXPIRED');
CREATE TYPE notification_type_enum AS ENUM ('REMINDER', 'ALERT', 'INFO');
CREATE TYPE notification_status_enum AS ENUM ('PENDING', 'SENT', 'READ');
CREATE TYPE schedule_status_enum AS ENUM ('PENDING', 'TAKEN', 'SKIPPED');
CREATE TYPE audit_action_enum AS ENUM ('CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'MIGRATE');

-- 2. Update users table
ALTER TABLE users ADD COLUMN user_type user_type_enum DEFAULT 'REGISTERED';
ALTER TABLE users ADD COLUMN anonymous_user_id VARCHAR(36);
ALTER TABLE users ALTER COLUMN email DROP NOT NULL;
ALTER TABLE users ALTER COLUMN password DROP NOT NULL;
ALTER TABLE users ALTER COLUMN giới_tính TYPE gender_enum USING giới_tính::gender_enum;

-- 3. Create anonymous_sessions table
CREATE TABLE anonymous_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    anonymous_user_id VARCHAR(36) NOT NULL,
    device_id VARCHAR(100),
    app_version VARCHAR(20),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_activity_at TIMESTAMP NOT NULL DEFAULT NOW(),
    data_synced BOOLEAN DEFAULT FALSE,
    migrated_to_user_id BIGINT REFERENCES users(id),
    migrated_at TIMESTAMP
);

-- Anonymous session status enum
CREATE TYPE anonymous_session_status_enum AS ENUM ('ACTIVE', 'EXPIRED', 'MIGRATED', 'CLEANED');

-- Add status column to anonymous_sessions table
ALTER TABLE anonymous_sessions ADD COLUMN status anonymous_session_status_enum DEFAULT 'ACTIVE';

-- 4. Update medicines table
ALTER TABLE medicines ADD COLUMN user_type user_type_enum DEFAULT 'REGISTERED';
ALTER TABLE medicines ADD COLUMN anonymous_user_id VARCHAR(36);
ALTER TABLE medicines ALTER COLUMN user_id DROP NOT NULL;

-- 5. Update prescriptions table
ALTER TABLE prescriptions ADD COLUMN user_type user_type_enum DEFAULT 'REGISTERED';
ALTER TABLE prescriptions ADD COLUMN anonymous_user_id VARCHAR(36);
ALTER TABLE prescriptions ALTER COLUMN patient_id DROP NOT NULL;

-- 6. Update audit_logs table
ALTER TABLE audit_logs ADD COLUMN anonymous_user_id VARCHAR(36);

-- 7. Add indexes
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_anonymous_id ON users(anonymous_user_id);
CREATE INDEX idx_anonymous_sessions_user_id ON anonymous_sessions(anonymous_user_id);
CREATE INDEX idx_anonymous_sessions_last_activity ON anonymous_sessions(last_activity_at);
CREATE INDEX idx_anonymous_sessions_migrated ON anonymous_sessions(migrated_to_user_id);
CREATE INDEX idx_medicines_anonymous_user_id ON medicines(anonymous_user_id);
CREATE INDEX idx_medicines_user_type ON medicines(user_type);
CREATE INDEX idx_prescriptions_anonymous_user_id ON prescriptions(anonymous_user_id);
CREATE INDEX idx_prescriptions_user_type ON prescriptions(user_type);
CREATE INDEX idx_audit_logs_anonymous_user_id ON audit_logs(anonymous_user_id);

-- 8. Update existing data
UPDATE users SET user_type = 'REGISTERED' WHERE user_type IS NULL;
UPDATE medicines SET user_type = 'REGISTERED' WHERE user_type IS NULL;
UPDATE prescriptions SET user_type = 'REGISTERED' WHERE user_type IS NULL;
```

---

## 🆕 Feature Matrix

### **Anonymous Users (Local Storage)**

- ✅ View medicine list
- ✅ Add medicine
- ✅ Edit medicine
- ✅ Delete medicine
- ✅ Create prescription
- ✅ View prescription
- ✅ Basic notifications (local)
- ❌ Family management
- ❌ Multi-device sync
- ❌ Cloud backup
- ❌ Data export
- ❌ Advanced analytics

### **Registered Users (Cloud Storage)**

- ✅ All anonymous features
- ✅ Family management
- ✅ Multi-device sync
- ✅ Cloud backup
- ✅ Family sharing
- ✅ Advanced analytics
- ✅ Data export
- ✅ User management
- ✅ Prescription scheduling
- ✅ Medication compliance tracking

---

## 🆕 Data Migration Strategy

### **Anonymous to Registered Migration**

```sql
-- Migration process
UPDATE medicines
SET user_type = 'REGISTERED',
    user_id = ?,
    anonymous_user_id = NULL
WHERE anonymous_user_id = ?;

UPDATE prescriptions
SET user_type = 'REGISTERED',
    patient_id = ?,
    anonymous_user_id = NULL
WHERE anonymous_user_id = ?;

UPDATE anonymous_sessions
SET migrated_to_user_id = ?,
    migrated_at = NOW()
WHERE anonymous_user_id = ?;
```

---

## 📊 Performance Considerations

### **Indexing Strategy**

- **Anonymous queries**: Optimized indexes for anonymous_user_id
- **Mixed queries**: Composite indexes for user_type + user_id/anonymous_user_id
- **Migration queries**: Indexes for migration tracking
- **Cleanup queries**: Indexes for expired session cleanup

### **Query Optimization**

- **Anonymous data**: Separate queries for anonymous vs registered data
- **Family data**: Scoped to family for registered users
- **Migration**: Batch processing for large data migration
- **Cleanup**: Scheduled cleanup of expired anonymous sessions

---

## 🔐 Security Considerations

### **Anonymous User Security**

- **UUID validation**: Validate anonymous user ID format
- **Session expiration**: 30-day session expiration
- **Rate limiting**: Prevent abuse of anonymous endpoints
- **Data isolation**: Anonymous data isolated from registered users
- **Migration security**: Secure migration process

### **Data Privacy**

- **Anonymous data**: No PII in anonymous sessions
- **Migration consent**: User consent for data migration
- **Data retention**: Automatic cleanup of expired anonymous data
- **Audit trail**: Track all anonymous user activities

---

## 🎯 Implementation Guidelines

### **Entity Design**

- **UserType enum**: Clear separation between user types
- **Nullable relationships**: Support for anonymous users
- **Migration support**: Fields for tracking data migration
- **Audit support**: Track all user activities

### **Service Layer**

- **Anonymous services**: Dedicated services for anonymous operations
- **Migration services**: Handle data migration from anonymous to registered
- **Session management**: Track and validate anonymous sessions
- **Feature gating**: Control access based on user type

### **API Design**

- **Anonymous endpoints**: `/api/v1/anonymous/*` for anonymous users
- **Authenticated endpoints**: `/api/v1/*` for registered users
- **Migration endpoints**: Handle data migration during registration
- **Session endpoints**: Manage anonymous sessions

---

## 📞 Contact & Support

**Database Lead**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 2.0  
**Status**: Updated with Authentication Optional Pattern
