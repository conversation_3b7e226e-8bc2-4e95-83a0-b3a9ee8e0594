# SRS - Dashboard & Báo cáo

## 1. <PERSON><PERSON> tả tổng quan

Dashboard cung cấp tổng quan về tình trạng tủ thuốc gia đình, hiển thị thống kê, biểu đồ và báo cáo chi tiết. Hỗ trợ xem dữ liệu theo thời gian thực và xuất báo cáo định kỳ.

## 2. Trường dữ liệu chi tiết

### 2.1. Dashboard Widgets Entity

| Trường           | Kiểu dữ liệu | Bắt buộc | Mô tả                    |
| ---------------- | ------------ | -------- | ------------------------ |
| id               | UUID         | Có       | Định danh widget         |
| family_id        | UUID         | Có       | ID gia đình              |
| user_id          | UUID         | Có       | ID user                  |
| tên_widget       | String       | <PERSON><PERSON>       | Tên widget               |
| loại_widget      | Enum         | Có       | STATS/CHART/TABLE/LIST   |
| dữ_liệu          | JSONB        | Có       | Cấu hình dữ liệu         |
| vị_trí           | JSONB        | Có       | Vị trí và kích thước     |
| is_active        | Boolean      | Có       | Widget có hiển thị không |
| refresh_interval | Integer      | Có       | Thời gian refresh (giây) |
| created_at       | DateTime     | Có       | Thời điểm tạo            |
| updated_at       | DateTime     | Có       | Thời điểm cập nhật cuối  |

### 2.2. Dashboard Reports Entity

| Trường             | Kiểu dữ liệu | Bắt buộc | Mô tả                             |
| ------------------ | ------------ | -------- | --------------------------------- |
| id                 | UUID         | Có       | Định danh báo cáo                 |
| family_id          | UUID         | Có       | ID gia đình                       |
| user_id            | UUID         | Có       | ID user tạo                       |
| tên_báo_cáo        | String       | Có       | Tên báo cáo                       |
| loại_báo_cáo       | Enum         | Có       | INVENTORY/COMPLIANCE/USAGE/EXPIRY |
| thời*gian_bắt*đầu  | DateTime     | Có       | Thời gian bắt đầu                 |
| thời_gian_kết_thúc | DateTime     | Có       | Thời gian kết thúc                |
| dữ_liệu            | JSONB        | Có       | Dữ liệu báo cáo                   |
| định_dạng          | Enum         | Có       | PDF/EXCEL/CSV                     |
| trạng_thái         | Enum         | Có       | GENERATING/COMPLETED/FAILED       |
| file_path          | String       | Không    | Đường dẫn file                    |
| created_at         | DateTime     | Có       | Thời điểm tạo                     |
| completed_at       | DateTime     | Không    | Thời điểm hoàn thành              |

### 2.3. Dashboard Alerts Entity

| Trường            | Kiểu dữ liệu | Bắt buộc | Mô tả                               |
| ----------------- | ------------ | -------- | ----------------------------------- |
| id                | UUID         | Có       | Định danh cảnh báo                  |
| family_id         | UUID         | Có       | ID gia đình                         |
| loại_cảnh_báo     | Enum         | Có       | LOW_STOCK/EXPIRY/COMPLIANCE/OVERDUE |
| mức_độ            | Enum         | Có       | HIGH/MEDIUM/LOW                     |
| tiêu_đề           | String       | Có       | Tiêu đề cảnh báo                    |
| nội_dung          | Text         | Có       | Nội dung chi tiết                   |
| dữ_liệu_liên_quan | JSONB        | Không    | Dữ liệu liên quan                   |
| trạng_thái        | Enum         | Có       | ACTIVE/RESOLVED/DISMISSED           |
| thời_gian_tạo     | DateTime     | Có       | Thời điểm tạo                       |
| thời_gian_xử_lý   | DateTime     | Không    | Thời điểm xử lý                     |
| created_at        | DateTime     | Có       | Thời điểm tạo                       |
| updated_at        | DateTime     | Có       | Thời điểm cập nhật cuối             |

## 3. Luồng nghiệp vụ chi tiết

### 3.1. Hiển thị Dashboard

- User đăng nhập vào hệ thống
- Hệ thống load dashboard với các widget mặc định
- Hiển thị thống kê tổng quan: số thuốc, đơn thuốc, thành viên
- Hiển thị biểu đồ và báo cáo theo thời gian thực

### 3.2. Tùy chỉnh Dashboard

- User chọn "Tùy chỉnh Dashboard"
- Thêm/xóa/sắp xếp lại các widget
- Cấu hình dữ liệu cho từng widget
- Lưu cấu hình dashboard

### 3.3. Tạo báo cáo

- User chọn "Tạo báo cáo"
- Chọn loại báo cáo: tồn kho, tuân thủ, sử dụng, hết hạn
- Chọn thời gian và định dạng xuất
- Hệ thống tạo báo cáo và gửi thông báo khi hoàn thành

### 3.4. Xem cảnh báo

- User xem danh sách cảnh báo trên dashboard
- Lọc theo loại và mức độ
- Xử lý cảnh báo: đánh dấu đã xử lý hoặc bỏ qua
- Nhận thông báo cảnh báo mới

### 3.5. Phân tích dữ liệu

- User xem biểu đồ phân tích theo thời gian
- So sánh dữ liệu giữa các tháng/quý/năm
- Xem xu hướng sử dụng thuốc
- Phân tích tuân thủ điều trị

### 3.6. Xuất dữ liệu

- User chọn dữ liệu cần xuất
- Chọn định dạng: PDF, Excel, CSV
- Tùy chỉnh nội dung báo cáo
- Tải xuống file

## 4. Yêu cầu UI/UX chi tiết

### 4.1. Bố cục màn hình chính

**Header:**

- Logo ứng dụng (góc trái)
- Tên màn hình "Dashboard" (giữa)
- Nút "Tùy chỉnh" (góc phải, màu xanh)

**Quick Stats Bar:**

- 4 card thống kê chính: Tổng thuốc, Đơn thuốc, Thành viên, Cảnh báo
- Mỗi card có icon, số liệu, phần trăm thay đổi
- Color coding: xanh (tăng), đỏ (giảm), xám (không đổi)

**Main Content:**

- Grid layout cho các widget
- Responsive: 1 cột (mobile), 2 cột (tablet), 3-4 cột (desktop)
- Drag & drop để sắp xếp

**Sidebar (Desktop):**

- Cảnh báo gần đây
- Quick actions: Tạo báo cáo, Xuất dữ liệu
- Calendar widget

### 4.2. Thiết kế giao diện

**Stats Card:**

- Background: màu trắng, border radius 12px, shadow nhẹ
- Header: Icon, Tên thống kê
- Body: Số liệu lớn (font 32px, bold), Đơn vị
- Footer: Phần trăm thay đổi, Mũi tên lên/xuống
- Border left: màu theo loại thống kê

**Chart Widget:**

- Background: màu trắng, border radius 12px, shadow nhẹ
- Header: Tên biểu đồ, Nút fullscreen, Nút cài đặt
- Body: Chart area với responsive design
- Footer: Legend, Nút xuất dữ liệu
- Border: màu xám nhạt

**Table Widget:**

- Background: màu trắng, border radius 12px, shadow nhẹ
- Header: Tên bảng, Nút refresh, Nút filter
- Body: Table với pagination
- Footer: Tổng số records, Nút xuất
- Border: màu xám nhạt

**Alert Banner:**

- Background: màu đỏ/cam/vàng tùy mức độ
- Icon cảnh báo, Tiêu đề, Nội dung ngắn
- Nút "Xem chi tiết" và "Đóng"
- Auto-hide sau 10 giây

### 4.3. Tương tác người dùng

**Tùy chỉnh Dashboard:**

- Click "Tùy chỉnh" → Mở edit mode
- Drag & drop widgets → Auto-save
- Click "+" → Add widget → Select type → Configure
- Click "X" → Remove widget → Confirm
- Click "Save" → Exit edit mode

**Tạo báo cáo:**

- Click "Tạo báo cáo" → Mở modal
- Select type → Set time range → Choose format → Submit
- Loading spinner → Background processing
- Notification khi hoàn thành → Download

**Xử lý cảnh báo:**

- Click alert → Mở detail modal
- Click "Đã xử lý" → Mark as resolved
- Click "Bỏ qua" → Dismiss alert
- Success message + update counter

**Xem biểu đồ:**

- Hover chart → Show tooltip
- Click legend → Toggle series
- Click data point → Show detail
- Zoom/pan cho interactive charts

### 4.4. Responsive Design

**Mobile (< 768px):**

- Stats cards 1 cột
- Widgets full width
- Swipe gestures
- Bottom navigation

**Tablet (768px - 1024px):**

- Stats cards 2 cột
- Widgets 2 cột
- Sidebar collapse

**Desktop (> 1024px):**

- Stats cards 4 cột
- Widgets 3-4 cột
- Sidebar visible
- Hover effects

### 4.5. Accessibility

- Alt text cho tất cả ảnh và charts
- ARIA labels cho interactive elements
- Keyboard navigation
- Screen reader support
- High contrast mode
- Focus indicators

### 4.6. User Flow chi tiết

```
1. Vào màn hình → Load dashboard → Hiển thị stats và widgets
2. Tùy chỉnh:
   Click "Tùy chỉnh" → Drag & drop → Save → Success
3. Tạo báo cáo:
   Click "Tạo báo cáo" → Fill form → Submit → Background process → Download
4. Xử lý cảnh báo:
   Click alert → View detail → Mark resolved → Success
5. Xem biểu đồ:
   Hover → Tooltip → Click → Detail modal → Close
6. Xuất dữ liệu:
   Click "Xuất" → Select format → Download
```

### 4.7. Error Handling

- Loading states cho tất cả widgets
- Error boundaries cho charts
- Network error handling
- Data validation
- Graceful degradation
- Retry mechanism

### 4.8. Visual Feedback

- Loading spinners cho widgets
- Success/error toast messages
- Progress bars cho reports
- Color coding theo loại dữ liệu
- Animation cho data changes
- Smooth transitions

### 4.9. Smart Features

- Auto-refresh widgets
- Smart data aggregation
- Predictive analytics
- Custom date ranges
- Export scheduling
- Real-time updates

## 5. Yêu cầu bảo mật, hiệu năng, mở rộng

### 5.1. Bảo mật

- Role-based access control
- Family isolation
- Data encryption
- Audit trail
- Input sanitization

### 5.2. Hiệu năng

- Lazy loading cho widgets
- Caching cho dashboard data
- Background processing cho reports
- Optimized queries
- Real-time updates

### 5.3. Mở rộng

- Support unlimited widgets per user
- Custom chart types
- Advanced analytics
- Integration với BI tools
- AI-powered insights

## 6. Trường hợp ngoại lệ & lưu ý

### 6.1. Business Rules

- Widgets chỉ hiển thị dữ liệu của family hiện tại
- Reports có thể được lên lịch tự động
- Alerts được tạo tự động dựa trên business rules
- Dashboard config được lưu per user
- Data retention policies apply

### 6.2. Validation Rules

- Widget config: valid JSON
- Report time range: max 1 year
- Export format: supported types only
- Alert levels: valid enum values
- Refresh interval: 30s - 1h

### 6.3. Error Scenarios

- Widget data unavailable
- Report generation failed
- Export format not supported
- Permission denied
- Network timeout

### 6.4. Edge Cases

- Family không có dữ liệu
- Large dataset performance
- Multiple concurrent reports
- Widget configuration errors
- Real-time sync issues
