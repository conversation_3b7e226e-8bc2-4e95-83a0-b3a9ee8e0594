# SRS - Quản lý Gia đình & Thành viên

## 1. <PERSON><PERSON> tả tổng quan

Chức năng quản lý gia đình và thành viên cho phép người dùng tạo, quản lý gia đình, mời thành viên tham gia, và phân quyền cho từng thành viên. Hệ thống hỗ trợ cả dependent members (trẻ em, người già không có tài khoản) và independent members (có tài khoản riêng).

## 2. Trường dữ liệu chi tiết

### 2.1. Family Entity

| Trường        | Kiểu dữ liệu | Bắt buộc | Mô tả                   |
| ------------- | ------------ | -------- | ----------------------- |
| id            | UUID         | C<PERSON>       | Định danh gia đình      |
| tên*gia*đình  | String       | Có       | Tên hiển thị gia đình   |
| owner_id      | UUID         | Có       | ID chủ gia đình         |
| mã_invite     | String       | Có       | Mã mời unique           |
| ngày_tạo      | DateTime     | Có       | Thời điểm tạo gia đình  |
| ngày_cập_nhật | DateTime     | Có       | Thời điểm cập nhật cuối |

### 2.2. Family Member Entity

| Trường        | Kiểu dữ liệu | Bắt buộc | Mô tả                           |
| ------------- | ------------ | -------- | ------------------------------- |
| id            | UUID         | Có       | Định danh thành viên            |
| family_id     | UUID         | Có       | ID gia đình                     |
| user_id       | UUID         | Không    | ID user (NULL nếu dependent)    |
| managed_by    | UUID         | Không    | ID user quản lý (cho dependent) |
| tên           | String       | Có       | Tên thành viên                  |
| tuổi          | Integer      | Không    | Tuổi thành viên                 |
| giới_tính     | Enum         | Không    | MALE/FEMALE/OTHER               |
| ảnh           | String(URL)  | Không    | Đường dẫn ảnh thành viên        |
| member_type   | Enum         | Có       | DEPENDENT/INDEPENDENT           |
| role_id       | UUID         | Có       | ID role trong gia đình          |
| can_login     | Boolean      | Có       | Có thể đăng nhập không          |
| ngày_tạo      | DateTime     | Có       | Thời điểm tạo                   |
| ngày_cập_nhật | DateTime     | Có       | Thời điểm cập nhật cuối         |

### 2.3. Family Role Entity

| Trường      | Kiểu dữ liệu | Bắt buộc | Mô tả                      |
| ----------- | ------------ | -------- | -------------------------- |
| id          | UUID         | Có       | Định danh role             |
| role_name   | String       | Có       | Tên role (OWNER, ADMIN...) |
| mô_tả       | Text         | Không    | Mô tả role                 |
| permissions | JSONB        | Có       | Quyền chi tiết             |
| is_active   | Boolean      | Có       | Role có hoạt động không    |
| created_at  | DateTime     | Có       | Thời điểm tạo              |

### 2.4. Family Invitation Entity

| Trường          | Kiểu dữ liệu | Bắt buộc | Mô tả                             |
| --------------- | ------------ | -------- | --------------------------------- |
| id              | UUID         | Có       | Định danh lời mời                 |
| family_id       | UUID         | Có       | ID gia đình                       |
| invited_by      | UUID         | Có       | ID user gửi lời mời               |
| email           | String       | Có       | Email người được mời              |
| member_name     | String       | Không    | Tên hiển thị                      |
| invitation_type | Enum         | Có       | CREATE_ACCOUNT/VIEW_ONLY          |
| trạng_thái      | Enum         | Có       | PENDING/ACCEPTED/REJECTED/EXPIRED |
| mã_invite       | String       | Có       | Mã mời unique                     |
| hết_hạn         | DateTime     | Có       | Thời gian hết hạn                 |
| ngày_tạo        | DateTime     | Có       | Thời điểm tạo lời mời             |
| ngày_cập_nhật   | DateTime     | Có       | Thời điểm cập nhật cuối           |

## 3. Luồng nghiệp vụ chi tiết

### 3.1. Tạo gia đình mới

- User chọn "Tạo gia đình mới"
- Nhập tên gia đình
- Hệ thống tự động tạo mã mời unique
- User trở thành OWNER của gia đình
- Chuyển về màn hình quản lý gia đình

### 3.2. Mời thành viên tham gia

- Owner/Admin chọn "Mời thành viên"
- Nhập email, tên hiển thị, loại lời mời (CREATE_ACCOUNT/VIEW_ONLY)
- Hệ thống gửi email với mã mời
- Lời mời có thời hạn 7 ngày
- Người được mời có thể accept/reject

### 3.3. Thêm dependent member

- Owner/Admin chọn "Thêm thành viên phụ thuộc"
- Nhập tên, tuổi, giới tính, ảnh (nếu có)
- Chọn người quản lý (managed_by)
- Hệ thống tạo member với type = DEPENDENT
- Dependent member không thể đăng nhập

### 3.4. Chuyển đổi member type

- Owner/Admin chọn member cần chuyển đổi
- Nếu dependent → independent: tạo tài khoản cho member
- Nếu independent → dependent: xóa tài khoản, chỉ định người quản lý
- Cập nhật member_type và các trường liên quan

### 3.5. Phân quyền thành viên

- Owner/Admin chọn member cần phân quyền
- Chọn role từ danh sách có sẵn (OWNER, ADMIN, MEMBER, VIEWER)
- Hệ thống cập nhật permissions cho member
- Member chỉ có thể thực hiện các hành động theo quyền

## 4. Yêu cầu UI/UX chi tiết

### 4.1. Bố cục màn hình chính

**Header:**

- Logo ứng dụng (góc trái)
- Tên màn hình "Quản lý Gia đình" (giữa)
- Nút "Tạo gia đình" (góc phải, màu xanh)

**Body:**

- Danh sách gia đình dạng grid (2 cột trên mobile, 3-4 cột trên tablet/desktop)
- Mỗi card gia đình có kích thước 300x400px

**Sidebar (Desktop):**

- Thống kê: Tổng số gia đình, thành viên
- Quick actions: Tạo gia đình, Mời thành viên

### 4.2. Thiết kế giao diện

**Card gia đình:**

- Background: màu trắng, border radius 12px, shadow nhẹ
- Header: Tên gia đình (font 20px, bold), Badge role (OWNER/ADMIN)
- Body: Số thành viên, Danh sách thành viên (avatar + tên)
- Footer: 4 nút hành động: Vào gia đình, Mời thành viên, Cài đặt, Xóa
- Border left: màu xanh (OWNER), cam (ADMIN), xám (MEMBER)

**Form tạo gia đình:**

- Modal popup với background overlay
- Form fields: Tên gia đình (text input)
- Preview mã mời được tạo
- Nút "Tạo" (màu xanh) và "Hủy" (màu xám)

**Form mời thành viên:**

- Modal popup với 3 sections:
  1. Thông tin người mời (email, tên hiển thị)
  2. Loại lời mời (CREATE_ACCOUNT/VIEW_ONLY)
  3. Preview email template
- Progress bar: 3/3 steps
- Nút "Gửi lời mời" và "Hủy"

**Form thêm dependent member:**

- Modal popup với form fields:
  - Tên (text input)
  - Tuổi (number input)
  - Giới tính (radio buttons)
  - Ảnh (upload)
  - Người quản lý (dropdown)
- Preview ảnh real-time
- Nút "Thêm" và "Hủy"

### 4.3. Tương tác người dùng

**Tạo gia đình:**

- Click "Tạo gia đình" → Mở modal
- Fill form → Preview mã mời → Submit
- Loading spinner khi tạo
- Success message + redirect đến gia đình mới

**Mời thành viên:**

- Click "Mời thành viên" → Mở modal
- Fill section 1 → Next → Select type → Next → Preview → Send
- Loading spinner khi gửi
- Success message + đóng modal

**Thêm dependent member:**

- Click "Thêm thành viên phụ thuộc" → Mở modal
- Fill form → Upload ảnh → Select manager → Submit
- Loading spinner khi thêm
- Success message + refresh danh sách

**Chuyển đổi member type:**

- Click "Chuyển đổi" → Confirm dialog
- "Chuyển dependent thành independent?" → Yes → Mở form tạo tài khoản
- Fill account details → Submit
- Success message + update member type

**Phân quyền:**

- Click "Phân quyền" → Mở modal role selection
- Select role → Preview permissions → Confirm
- Success message + update member permissions

### 4.4. Responsive Design

**Mobile (< 768px):**

- Grid 1 cột
- Card full width, height auto
- Modal fullscreen
- Swipe gestures để navigate

**Tablet (768px - 1024px):**

- Grid 2 cột
- Card 45% width
- Modal 90% width

**Desktop (> 1024px):**

- Grid 3-4 cột
- Card 300px fixed width
- Modal 600px width

### 4.5. Accessibility

- Alt text cho tất cả ảnh
- ARIA labels cho buttons và form fields
- Keyboard navigation support
- High contrast mode
- Screen reader friendly
- Focus indicators rõ ràng

### 4.6. User Flow chi tiết

```
1. Vào màn hình → Hiển thị danh sách gia đình
2. Tạo gia đình:
   Click "Tạo gia đình" → Fill form → Submit → Success → Redirect
3. Mời thành viên:
   Click "Mời thành viên" → Fill form → Send → Success
4. Thêm dependent:
   Click "Thêm dependent" → Fill form → Submit → Success
5. Chuyển đổi member:
   Click "Chuyển đổi" → Confirm → Fill account → Submit → Success
6. Phân quyền:
   Click "Phân quyền" → Select role → Confirm → Success
```

### 4.7. Error Handling

- Form validation real-time
- Email validation cho lời mời
- Duplicate member detection
- Permission validation
- Network error handling với retry
- Graceful degradation

### 4.8. Visual Feedback

- Hover effects trên cards
- Loading states cho tất cả actions
- Success/error toast messages
- Progress indicators cho multi-step forms
- Color coding: xanh (OWNER), cam (ADMIN), xám (MEMBER)
- Avatar preview với fallback

### 4.9. Smart Features

- Auto-suggest email khi mời thành viên
- Duplicate detection cho member names
- Auto-generate mã mời unique
- Smart role assignment suggestions
- Family member search/filter

## 5. Yêu cầu bảo mật, hiệu năng, mở rộng

### 5.1. Bảo mật

- Chỉ OWNER/ADMIN mới được mời thành viên
- Email verification cho lời mời
- Role-based access control
- Audit trail cho tất cả actions
- Data encryption cho thông tin nhạy cảm

### 5.2. Hiệu năng

- Lazy loading cho danh sách thành viên
- Caching cho family data
- Optimized queries cho large families
- Pagination cho member lists

### 5.3. Mở rộng

- Hỗ trợ unlimited families per user
- Hỗ trợ unlimited members per family
- Custom role creation
- Advanced permission system
- Multi-language support

## 6. Trường hợp ngoại lệ & lưu ý

### 6.1. Business Rules

- Không cho phép tạo 2 gia đình trùng tên cho cùng user
- Không cho phép thêm member trùng tên + tuổi trong cùng family
- Dependent member phải có managed_by
- Independent member phải có user_id
- OWNER không thể bị xóa khỏi gia đình

### 6.2. Validation Rules

- Email phải valid format
- Tên gia đình: 3-50 ký tự
- Tên member: 2-100 ký tự
- Tuổi: 0-150
- Mã mời: 8-20 ký tự, unique

### 6.3. Error Scenarios

- Email đã tồn tại trong family
- Lời mời hết hạn
- Không có quyền thực hiện action
- Family đã đạt giới hạn member
- Network error khi gửi email

### 6.4. Edge Cases

- User không có gia đình nào
- Family chỉ có 1 member (OWNER)
- Tất cả member đều dependent
- Member chuyển đổi type khi đang có active prescriptions
- Family deletion với active members
