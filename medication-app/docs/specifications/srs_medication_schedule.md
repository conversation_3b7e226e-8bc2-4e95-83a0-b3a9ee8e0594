# SRS - Lịch Uống Thuốc

## 1. <PERSON><PERSON> tả tổng quan

Chức năng lịch uống thuốc cho phép người dùng tạo, quản lý và theo dõi lịch uống thuốc cho từng thành viên trong gia đình. <PERSON><PERSON> thống hỗ trợ lịch uống thuốc từ đơn thuốc, lịch tự tạo, và gửi thông báo nhắc nhở.

## 2. Trường dữ liệu chi tiết

### 2.1. Medication Schedules Entity

| Trường          | Kiểu dữ liệu | Bắt buộc | Mô tả                               |
| --------------- | ------------ | -------- | ----------------------------------- |
| id              | UUID         | <PERSON><PERSON>       | Đ<PERSON><PERSON> danh lịch uống                 |
| family_id       | UUID         | Có       | ID gia đình                         |
| member_id       | UUID         | Có       | ID thành viên                       |
| prescription_id | UUID         | Không    | ID đơn thuốc (nếu có)               |
| medicine_id     | UUID         | Có       | ID thuốc                            |
| tên_lịch        | String       | Có       | Tên lịch uống                       |
| thời_gian       | Time         | Có       | Giờ uống thuốc                      |
| ngày_trong_tuần | String       | Có       | Các ngày trong tuần (1,2,3,4,5,6,7) |
| liều_lượng      | Integer      | Có       | Số lượng uống mỗi lần               |
| đơn_vị          | String       | Có       | Đơn vị (viên, ml, g...)             |
| hướng_dẫn       | Text         | Không    | Hướng dẫn sử dụng                   |
| ghi_chú         | Text         | Không    | Ghi chú bổ sung                     |
| trạng_thái      | Enum         | Có       | ACTIVE/PAUSED/COMPLETED/CANCELLED   |
| ngày*bắt*đầu    | Date         | Có       | Ngày bắt đầu lịch                   |
| ngày_kết_thúc   | Date         | Không    | Ngày kết thúc lịch                  |
| nhắc_nhở        | Boolean      | Có       | Có gửi nhắc nhở không               |
| thời_gian_nhắc  | Integer      | Có       | Số phút trước giờ uống              |
| created_by      | UUID         | Có       | ID user tạo lịch                    |
| created_at      | DateTime     | Có       | Thời điểm tạo                       |
| updated_at      | DateTime     | Có       | Thời điểm cập nhật cuối             |
| deleted_at      | DateTime     | Không    | Thời điểm xóa (soft delete)         |

### 2.2. Schedule Logs Entity

| Trường      | Kiểu dữ liệu | Bắt buộc | Mô tả                |
| ----------- | ------------ | -------- | -------------------- |
| id          | UUID         | Có       | Định danh log        |
| schedule_id | UUID         | Có       | ID lịch uống         |
| family_id   | UUID         | Có       | ID gia đình          |
| member_id   | UUID         | Có       | ID thành viên        |
| ngày_uống   | Date         | Có       | Ngày uống thuốc      |
| giờ_uống    | Time         | Có       | Giờ uống thuốc       |
| liều_lượng  | Integer      | Có       | Số lượng đã uống     |
| đơn_vị      | String       | Có       | Đơn vị               |
| trạng_thái  | Enum         | Có       | TAKEN/SKIPPED/MISSED |
| ghi_chú     | Text         | Không    | Ghi chú bổ sung      |
| created_by  | UUID         | Có       | ID user ghi nhận     |
| created_at  | DateTime     | Có       | Thời điểm tạo        |

### 2.3. Schedule Reminders Entity

| Trường      | Kiểu dữ liệu | Bắt buộc | Mô tả                       |
| ----------- | ------------ | -------- | --------------------------- |
| id          | UUID         | Có       | Định danh nhắc nhở          |
| schedule_id | UUID         | Có       | ID lịch uống                |
| family_id   | UUID         | Có       | ID gia đình                 |
| member_id   | UUID         | Có       | ID thành viên               |
| ngày_nhắc   | Date         | Có       | Ngày gửi nhắc nhở           |
| giờ_nhắc    | Time         | Có       | Giờ gửi nhắc nhở            |
| loại_nhắc   | Enum         | Có       | PUSH_NOTIFICATION/EMAIL/SMS |
| nội_dung    | Text         | Có       | Nội dung nhắc nhở           |
| trạng_thái  | Enum         | Có       | PENDING/SENT/FAILED         |
| sent_at     | DateTime     | Không    | Thời điểm gửi               |
| created_at  | DateTime     | Có       | Thời điểm tạo               |

## 3. Luồng nghiệp vụ chi tiết

### 3.1. Tạo lịch uống thuốc

- User chọn "Tạo lịch uống thuốc"
- Chọn thành viên và thuốc
- Nhập thông tin cơ bản: tên lịch, thời gian, ngày trong tuần
- Nhập liều lượng và đơn vị
- Nhập thời gian: ngày bắt đầu, ngày kết thúc (nếu có)
- Cấu hình nhắc nhở: bật/tắt, thời gian nhắc trước
- Hệ thống tạo lịch uống với trạng thái ACTIVE

### 3.2. Tạo lịch từ đơn thuốc

- User chọn đơn thuốc cần tạo lịch
- Hệ thống tự động tạo lịch cho từng thuốc trong đơn
- User có thể điều chỉnh thời gian và ngày uống
- Cấu hình nhắc nhở cho từng lịch
- Hệ thống tạo multiple schedules

### 3.3. Ghi nhận uống thuốc

- User chọn lịch uống cần ghi nhận
- Chọn trạng thái: TAKEN/SKIPPED/MISSED
- Nhập liều lượng thực tế đã uống
- Nhập ghi chú (nếu có)
- Hệ thống tạo schedule log

### 3.4. Cập nhật lịch uống

- User chọn lịch cần chỉnh sửa
- Có thể cập nhật tất cả thông tin
- Hệ thống lưu lịch sử thay đổi
- Cập nhật audit trail

### 3.5. Tạm dừng/Kích hoạt lịch

- User chọn lịch cần thay đổi trạng thái
- Chọn trạng thái mới: PAUSED/ACTIVE
- Nhập lý do (nếu có)
- Hệ thống cập nhật và tạo history

### 3.6. Xem báo cáo tuân thủ

- User xem thống kê tuân thủ theo thời gian
- Xem chi tiết từng lịch uống
- Xem biểu đồ tuân thủ
- Xuất báo cáo PDF

## 4. Yêu cầu UI/UX chi tiết

### 4.1. Bố cục màn hình chính

**Header:**

- Logo ứng dụng (góc trái)
- Tên màn hình "Lịch Uống Thuốc" (giữa)
- Nút "Tạo lịch" (góc phải, màu xanh)

**Calendar View:**

- Calendar widget hiển thị lịch uống theo ngày
- Color coding cho các loại thuốc
- Quick actions cho từng lịch

**List View:**

- Danh sách lịch uống theo thời gian
- Filter theo thành viên, trạng thái
- Search box tìm kiếm

**Sidebar (Desktop):**

- Thống kê: Tổng lịch, đang hoạt động, tuân thủ
- Quick actions: Tạo lịch, Báo cáo

### 4.2. Thiết kế giao diện

**Calendar Widget:**

- Monthly calendar view
- Color dots cho mỗi lịch uống
- Hover tooltip hiển thị chi tiết
- Click để xem chi tiết ngày

**Schedule Card:**

- Background: màu trắng, border radius 12px, shadow nhẹ
- Header: Tên lịch (font 18px, bold), Badge trạng thái
- Body: Thành viên, Thuốc, Thời gian, Ngày trong tuần
- Footer: Tiến độ tuân thủ, 3 nút: Ghi nhận, Chỉnh sửa, Tạm dừng
- Border left: màu theo loại thuốc

**Form tạo lịch:**

- Modal popup với tabs:
  1. Thông tin cơ bản
  2. Thời gian uống
  3. Cấu hình nhắc nhở
  4. Xác nhận
- Progress bar: 4/4 steps
- Preview lịch real-time

**Form ghi nhận:**

- Modal popup với form:
  - Hiển thị thông tin lịch
  - Radio buttons cho trạng thái
  - Input liều lượng thực tế
  - Textarea ghi chú
- Preview log được tạo
- Nút "Ghi nhận" và "Hủy"

### 4.3. Tương tác người dùng

**Tạo lịch:**

- Click "Tạo lịch" → Mở modal
- Fill tab 1 → Next → Fill tab 2 → Next → ... → Submit
- Loading spinner khi tạo
- Success message + refresh calendar

**Ghi nhận uống thuốc:**

- Click "Ghi nhận" → Mở modal
- Select status → Input quantity → Submit
- Loading spinner khi ghi nhận
- Success message + update progress

**Chỉnh sửa lịch:**

- Click "Chỉnh sửa" → Mở modal với data hiện tại
- Edit fields → Submit
- Loading spinner khi cập nhật
- Success message + update card

**Tạm dừng lịch:**

- Click "Tạm dừng" → Confirm dialog
- "Tạm dừng lịch này?" → Yes → Update status
- Success message + update badge

### 4.4. Responsive Design

**Mobile (< 768px):**

- Calendar full width
- List view 1 cột
- Modal fullscreen
- Swipe gestures

**Tablet (768px - 1024px):**

- Calendar 70% width
- List view 2 cột
- Modal 90% width

**Desktop (> 1024px):**

- Calendar 60% width
- List view 3 cột
- Modal 800px width
- Sidebar visible

### 4.5. Accessibility

- Alt text cho tất cả ảnh
- ARIA labels cho calendar events
- Keyboard navigation
- Screen reader support
- High contrast mode
- Focus indicators

### 4.6. User Flow chi tiết

```
1. Vào màn hình → Hiển thị calendar và list view
2. Tạo lịch:
   Click "Tạo lịch" → Fill 4 tabs → Submit → Success
3. Ghi nhận uống:
   Click "Ghi nhận" → Select status → Input quantity → Submit → Success
4. Chỉnh sửa lịch:
   Click "Chỉnh sửa" → Edit → Submit → Success
5. Tạm dừng lịch:
   Click "Tạm dừng" → Confirm → Success
6. Xem báo cáo:
   Click "Báo cáo" → Select period → Generate → Download
```

### 4.7. Error Handling

- Form validation real-time
- Duplicate schedule detection
- Time conflict validation
- Medicine availability check
- Network error handling
- Graceful degradation

### 4.8. Visual Feedback

- Hover effects trên calendar events
- Loading states cho tất cả actions
- Success/error toast messages
- Progress indicators cho multi-step forms
- Color coding theo loại thuốc
- Status badges: ACTIVE (xanh), PAUSED (cam), COMPLETED (xám)

### 4.9. Smart Features

- Auto-suggest medicine names
- Smart time suggestions
- Conflict detection
- Progress tracking
- Reminder integration
- Schedule templates

## 5. Yêu cầu bảo mật, hiệu năng, mở rộng

### 5.1. Bảo mật

- Role-based access control
- Family isolation
- Audit trail cho tất cả changes
- Data encryption
- Input sanitization

### 5.2. Hiệu năng

- Lazy loading cho calendar events
- Caching cho schedule data
- Background processing cho reminders
- Optimized queries
- Real-time updates

### 5.3. Mở rộng

- Support unlimited schedules per family
- Advanced reminder system
- Integration với health apps
- Schedule sharing
- AI-powered suggestions

## 6. Trường hợp ngoại lệ & lưu ý

### 6.1. Business Rules

- Không cho phép tạo lịch trùng thời gian cho cùng member
- Medicine phải có trong tủ thuốc
- End date phải >= start date
- Schedule logs không thể chỉnh sửa
- Reminders chỉ gửi cho active schedules

### 6.2. Validation Rules

- Tên lịch: 3-100 ký tự
- Thời gian: valid time format
- Liều lượng: > 0
- Ngày bắt đầu: >= ngày hiện tại
- Thời gian nhắc: 5-60 phút

### 6.3. Error Scenarios

- Lịch đã tồn tại
- Medicine không có trong tủ
- Time conflict
- Permission denied
- Network error

### 6.4. Edge Cases

- Family không có member nào
- Tất cả lịch đều tạm dừng
- Schedule với nhiều thuốc
- Time zone differences
- Medicine shortage
