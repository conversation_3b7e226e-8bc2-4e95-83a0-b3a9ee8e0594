# SRS - Quản lý Thuốc

## 1. <PERSON><PERSON> tả tổng quan

Chức năng quản lý thuốc cho phép người dùng thêm, chỉnh sửa, xóa và tìm kiếm thông tin thuốc trong tủ thuốc gia đình. Hệ thống hỗ trợ quản lý thuốc theo gia đình với phân quyền chi tiết và audit trail đầy đủ.

## 2. Trường dữ liệu chi tiết

### 2.1. Medicine Types Entity

| Trường     | Kiểu dữ liệu | Bắt buộc | Mô tả                   |
| ---------- | ------------ | -------- | ----------------------- |
| id         | UUID         | Có       | Định danh loại thuốc    |
| family_id  | UUID         | Có       | ID gia đình             |
| tên_loại   | String       | Có       | Tên loại thuốc          |
| mô_tả      | Text         | Không    | Mô tả chi tiết          |
| màu_sắc    | String       | Không    | Màu sắc đại diện        |
| icon       | String       | Không    | Icon đại diện           |
| is_active  | Boolean      | Có       | Loại có hoạt động không |
| created_by | UUID         | Có       | ID user tạo             |
| created_at | DateTime     | Có       | Thời điểm tạo           |
| updated_at | DateTime     | Có       | Thời điểm cập nhật cuối |

### 2.2. Medicines Entity

| Trường            | Kiểu dữ liệu | Bắt buộc | Mô tả                       |
| ----------------- | ------------ | -------- | --------------------------- |
| id                | UUID         | Có       | Định danh thuốc             |
| family_id         | UUID         | Có       | ID gia đình                 |
| type_id           | UUID         | Có       | ID loại thuốc               |
| tên_thuốc         | String       | Có       | Tên thuốc                   |
| tên_generic       | String       | Không    | Tên generic                 |
| nhà_sản_xuất      | String       | Không    | Tên nhà sản xuất            |
| thành_phần        | Text         | Không    | Thành phần chính            |
| công_dụng         | Text         | Không    | Công dụng chính             |
| chống*chỉ*định    | Text         | Không    | Chống chỉ định              |
| tác_dụng_phụ      | Text         | Không    | Tác dụng phụ                |
| cách_dùng         | Text         | Không    | Hướng dẫn sử dụng           |
| liều_lượng        | String       | Không    | Liều lượng khuyến nghị      |
| đơn_vị            | String       | Không    | Đơn vị (viên, ml, g...)     |
| hình_ảnh          | String(URL)  | Không    | Đường dẫn ảnh thuốc         |
| mã_vạch           | String       | Không    | Mã vạch sản phẩm            |
| nhiệt_độ_bảo_quản | String       | Không    | Nhiệt độ bảo quản           |
| hạn_sử_dụng       | Date         | Không    | Hạn sử dụng                 |
| trạng_thái        | Enum         | Có       | ACTIVE/INACTIVE/DELETED     |
| created_by        | UUID         | Có       | ID user tạo                 |
| created_at        | DateTime     | Có       | Thời điểm tạo               |
| updated_at        | DateTime     | Có       | Thời điểm cập nhật cuối     |
| deleted_at        | DateTime     | Không    | Thời điểm xóa (soft delete) |

### 2.3. Medicine Inventory Entity

| Trường      | Kiểu dữ liệu | Bắt buộc | Mô tả                   |
| ----------- | ------------ | -------- | ----------------------- |
| id          | UUID         | Có       | Định danh inventory     |
| medicine_id | UUID         | Có       | ID thuốc                |
| family_id   | UUID         | Có       | ID gia đình             |
| số_lượng    | Integer      | Có       | Số lượng hiện tại       |
| đơn_vị      | String       | Có       | Đơn vị (viên, chai...)  |
| vị_trí      | String       | Không    | Vị trí trong tủ thuốc   |
| ghi_chú     | Text         | Không    | Ghi chú bổ sung         |
| trạng_thái  | Enum         | Có       | AVAILABLE/LOW/EXPIRED   |
| created_at  | DateTime     | Có       | Thời điểm tạo           |
| updated_at  | DateTime     | Có       | Thời điểm cập nhật cuối |

### 2.4. Medicine Usage Logs Entity

| Trường       | Kiểu dữ liệu | Bắt buộc | Mô tả                 |
| ------------ | ------------ | -------- | --------------------- |
| id           | UUID         | Có       | Định danh log         |
| medicine_id  | UUID         | Có       | ID thuốc              |
| family_id    | UUID         | Có       | ID gia đình           |
| member_id    | UUID         | Có       | ID thành viên sử dụng |
| số_lượng     | Integer      | Có       | Số lượng sử dụng      |
| đơn_vị       | String       | Có       | Đơn vị                |
| ngày_sử_dụng | DateTime     | Có       | Thời điểm sử dụng     |
| lý_do        | String       | Không    | Lý do sử dụng         |
| ghi_chú      | Text         | Không    | Ghi chú bổ sung       |
| created_by   | UUID         | Có       | ID user tạo log       |
| created_at   | DateTime     | Có       | Thời điểm tạo         |

### 2.5. Inventory Transactions Entity

| Trường      | Kiểu dữ liệu | Bắt buộc | Mô tả                 |
| ----------- | ------------ | -------- | --------------------- |
| id          | UUID         | Có       | Định danh transaction |
| medicine_id | UUID         | Có       | ID thuốc              |
| family_id   | UUID         | Có       | ID gia đình           |
| loại        | Enum         | Có       | IN/OUT/ADJUSTMENT     |
| số_lượng    | Integer      | Có       | Số lượng thay đổi     |
| đơn_vị      | String       | Có       | Đơn vị                |
| lý_do       | String       | Không    | Lý do thay đổi        |
| ghi_chú     | Text         | Không    | Ghi chú bổ sung       |
| created_by  | UUID         | Có       | ID user thực hiện     |
| created_at  | DateTime     | Có       | Thời điểm tạo         |

## 3. Luồng nghiệp vụ chi tiết

### 3.1. Tạo loại thuốc mới

- User chọn "Tạo loại thuốc mới"
- Nhập tên loại, mô tả, chọn màu sắc và icon
- Hệ thống tạo loại thuốc cho gia đình hiện tại
- Loại thuốc mới có thể được sử dụng ngay

### 3.2. Thêm thuốc mới

- User chọn "Thêm thuốc mới"
- Chọn loại thuốc từ danh sách
- Nhập thông tin cơ bản: tên, nhà sản xuất, thành phần
- Nhập thông tin chi tiết: công dụng, chống chỉ định, tác dụng phụ
- Nhập thông tin sử dụng: cách dùng, liều lượng, đơn vị
- Upload ảnh thuốc (nếu có)
- Nhập thông tin bảo quản: nhiệt độ, hạn sử dụng
- Hệ thống tạo thuốc và inventory record

### 3.3. Cập nhật thông tin thuốc

- User chọn thuốc cần chỉnh sửa
- Có thể cập nhật tất cả thông tin
- Hệ thống lưu lịch sử thay đổi
- Cập nhật audit trail

### 3.4. Quản lý tồn kho

- User chọn thuốc cần quản lý tồn kho
- Có thể thêm/bớt số lượng
- Chọn loại transaction: IN/OUT/ADJUSTMENT
- Nhập lý do và ghi chú
- Hệ thống cập nhật inventory và tạo transaction log

### 3.5. Ghi nhận sử dụng thuốc

- User chọn thuốc đã sử dụng
- Chọn thành viên sử dụng
- Nhập số lượng và đơn vị
- Nhập lý do sử dụng
- Hệ thống tạo usage log và cập nhật inventory

### 3.6. Tìm kiếm và lọc thuốc

- User có thể tìm kiếm theo tên, nhà sản xuất
- Lọc theo loại thuốc, trạng thái tồn kho
- Lọc theo hạn sử dụng (sắp hết hạn, đã hết hạn)
- Sắp xếp theo tên, ngày tạo, số lượng

## 4. Yêu cầu UI/UX chi tiết

### 4.1. Bố cục màn hình chính

**Header:**

- Logo ứng dụng (góc trái)
- Tên màn hình "Quản lý Thuốc" (giữa)
- Nút "Thêm thuốc" (góc phải, màu xanh)

**Search & Filter Bar:**

- Search box với icon tìm kiếm
- Dropdown lọc theo loại thuốc
- Dropdown lọc theo trạng thái
- Nút "Lọc nâng cao" (mở modal)

**Body:**

- Grid layout cho danh sách thuốc
- Mỗi card thuốc 300x400px
- Responsive: 1 cột (mobile), 2 cột (tablet), 3-4 cột (desktop)

**Sidebar (Desktop):**

- Thống kê: Tổng số thuốc, loại thuốc, thuốc sắp hết hạn
- Quick actions: Thêm thuốc, Quản lý loại thuốc

### 4.2. Thiết kế giao diện

**Card thuốc:**

- Background: màu trắng, border radius 12px, shadow nhẹ
- Header: Ảnh thuốc (placeholder nếu không có), Badge trạng thái
- Body: Tên thuốc (font 18px, bold), Nhà sản xuất, Loại thuốc (badge màu)
- Footer: Số lượng tồn kho, Hạn sử dụng, 3 nút: Chi tiết, Chỉnh sửa, Xóa
- Border left: màu theo loại thuốc

**Form thêm thuốc:**

- Modal popup với tabs:
  1. Thông tin cơ bản
  2. Thông tin chi tiết
  3. Thông tin sử dụng
  4. Thông tin bảo quản
  5. Tồn kho ban đầu
- Progress bar: 5/5 steps
- Preview ảnh real-time
- Validation real-time

**Form quản lý tồn kho:**

- Modal popup với form:
  - Hiển thị thông tin thuốc hiện tại
  - Dropdown loại transaction
  - Input số lượng thay đổi
  - Input lý do và ghi chú
- Preview số lượng sau thay đổi
- Nút "Xác nhận" và "Hủy"

**Form ghi nhận sử dụng:**

- Modal popup với form:
  - Chọn thành viên sử dụng
  - Input số lượng sử dụng
  - Input lý do sử dụng
  - Ghi chú bổ sung
- Validation số lượng không vượt quá tồn kho
- Nút "Ghi nhận" và "Hủy"

### 4.3. Tương tác người dùng

**Thêm thuốc:**

- Click "Thêm thuốc" → Mở modal
- Fill tab 1 → Next → Fill tab 2 → Next → ... → Submit
- Loading spinner khi tạo
- Success message + refresh danh sách

**Chỉnh sửa thuốc:**

- Click "Chỉnh sửa" → Mở modal với data hiện tại
- Edit fields → Submit
- Loading spinner khi cập nhật
- Success message + update card

**Quản lý tồn kho:**

- Click "Quản lý tồn kho" → Mở modal
- Select transaction type → Input quantity → Submit
- Loading spinner khi cập nhật
- Success message + update inventory display

**Ghi nhận sử dụng:**

- Click "Ghi nhận sử dụng" → Mở modal
- Select member → Input quantity → Submit
- Loading spinner khi ghi nhận
- Success message + update inventory

**Tìm kiếm:**

- Type in search box → Real-time results
- Debounce 300ms
- Highlight search terms
- "Không tìm thấy kết quả" message

### 4.4. Responsive Design

**Mobile (< 768px):**

- Grid 1 cột
- Card full width
- Modal fullscreen
- Swipe gestures

**Tablet (768px - 1024px):**

- Grid 2 cột
- Card 45% width
- Modal 90% width

**Desktop (> 1024px):**

- Grid 3-4 cột
- Card 300px fixed width
- Modal 800px width
- Sidebar visible

### 4.5. Accessibility

- Alt text cho tất cả ảnh thuốc
- ARIA labels cho form fields
- Keyboard navigation
- Screen reader support
- High contrast mode
- Focus indicators

### 4.6. User Flow chi tiết

```
1. Vào màn hình → Hiển thị danh sách thuốc
2. Thêm thuốc:
   Click "Thêm thuốc" → Fill 5 tabs → Submit → Success
3. Chỉnh sửa:
   Click "Chỉnh sửa" → Edit → Submit → Success
4. Quản lý tồn kho:
   Click "Quản lý tồn kho" → Select type → Input quantity → Submit → Success
5. Ghi nhận sử dụng:
   Click "Ghi nhận sử dụng" → Select member → Input quantity → Submit → Success
6. Tìm kiếm:
   Type in search box → Real-time results
```

### 4.7. Error Handling

- Form validation real-time
- Duplicate medicine detection
- Inventory validation (không âm)
- Expiry date validation
- Network error handling
- Graceful degradation

### 4.8. Visual Feedback

- Hover effects trên cards
- Loading states cho tất cả actions
- Success/error toast messages
- Progress indicators cho multi-step forms
- Color coding theo loại thuốc
- Status badges: AVAILABLE (xanh), LOW (cam), EXPIRED (đỏ)

### 4.9. Smart Features

- Auto-suggest medicine names
- Barcode scanning
- Image recognition cho thuốc
- Auto-categorization
- Expiry date reminders
- Low stock alerts

## 5. Yêu cầu bảo mật, hiệu năng, mở rộng

### 5.1. Bảo mật

- Role-based access control
- Family isolation
- Audit trail cho tất cả changes
- Data encryption
- Input sanitization

### 5.2. Hiệu năng

- Lazy loading cho danh sách thuốc
- Image optimization
- Caching cho medicine data
- Pagination cho large lists
- Optimized search queries

### 5.3. Mở rộng

- Support unlimited medicines per family
- Advanced categorization
- Integration với pharmacy APIs
- Barcode database
- Medicine interaction checking

## 6. Trường hợp ngoại lệ & lưu ý

### 6.1. Business Rules

- Không cho phép tạo thuốc trùng tên trong cùng family
- Inventory không được âm
- Expired medicines được đánh dấu tự động
- Usage logs không thể chỉnh sửa sau khi tạo
- Medicine types có thể được tái sử dụng

### 6.2. Validation Rules

- Tên thuốc: 2-200 ký tự
- Số lượng: >= 0
- Hạn sử dụng: >= ngày hiện tại
- Ảnh: max 5MB, format JPG/PNG
- Mã vạch: unique trong family

### 6.3. Error Scenarios

- Thuốc đã tồn tại
- Số lượng không đủ để sử dụng
- Hạn sử dụng đã qua
- Network error khi upload ảnh
- Permission denied

### 6.4. Edge Cases

- Family không có loại thuốc nào
- Tất cả thuốc đều hết hạn
- Inventory = 0 cho tất cả thuốc
- Medicine với nhiều ảnh
- Medicine không có thông tin chi tiết
