# SRS - <PERSON><PERSON> thống Thông báo

## 1. <PERSON><PERSON> tả tổng quan

Hệ thống thông báo cho phép gửi và quản lý các loại thông báo khác nhau cho người dùng và thành viên gia đình. Hỗ trợ push notification, email, SMS và in-app notifications với tùy chỉnh theo từng loại thông báo.

## 2. Trường dữ liệu chi tiết

### 2.1. Notification Types Entity

| Trường         | Kiểu dữ liệu | Bắt buộc | Mô tả                               |
| -------------- | ------------ | -------- | ----------------------------------- |
| id             | UUID         | Có       | Định danh loại thông báo            |
| family_id      | UUID         | Có       | ID gia đình                         |
| tên_loại       | String       | Có       | Tên loại thông báo                  |
| mô_tả          | Text         | Không    | Mô tả chi tiết                      |
| template_title | String       | Có       | Template tiêu đề                    |
| template_body  | Text         | Có       | Template nội dung                   |
| kênh_gửi       | JSONB        | Có       | Các kênh gửi (PUSH/EMAIL/SMS)       |
| thời_gian_gửi  | String       | Có       | Thời gian gửi (IMMEDIATE/SCHEDULED) |
| độ_ưu_tiên     | Enum         | Có       | HIGH/MEDIUM/LOW                     |
| is_active      | Boolean      | Có       | Loại có hoạt động không             |
| created_by     | UUID         | Có       | ID user tạo                         |
| created_at     | DateTime     | Có       | Thời điểm tạo                       |
| updated_at     | DateTime     | Có       | Thời điểm cập nhật cuối             |

### 2.2. Notifications Entity

| Trường            | Kiểu dữ liệu | Bắt buộc | Mô tả                       |
| ----------------- | ------------ | -------- | --------------------------- |
| id                | UUID         | Có       | Định danh thông báo         |
| family_id         | UUID         | Có       | ID gia đình                 |
| type_id           | UUID         | Có       | ID loại thông báo           |
| recipient_id      | UUID         | Có       | ID người nhận               |
| sender_id         | UUID         | Có       | ID người gửi                |
| tiêu_đề           | String       | Có       | Tiêu đề thông báo           |
| nội_dung          | Text         | Có       | Nội dung thông báo          |
| dữ_liệu           | JSONB        | Không    | Dữ liệu bổ sung             |
| kênh_gửi          | Enum         | Có       | PUSH_NOTIFICATION/EMAIL/SMS |
| trạng_thái        | Enum         | Có       | PENDING/SENT/FAILED/READ    |
| độ_ưu_tiên        | Enum         | Có       | HIGH/MEDIUM/LOW             |
| thời_gian_gửi     | DateTime     | Có       | Thời gian gửi               |
| thời*gian*đọc     | DateTime     | Không    | Thời gian đọc               |
| thời_gian_hết_hạn | DateTime     | Không    | Thời gian hết hạn           |
| created_at        | DateTime     | Có       | Thời điểm tạo               |
| updated_at        | DateTime     | Có       | Thời điểm cập nhật cuối     |

### 2.3. Notification Settings Entity

| Trường            | Kiểu dữ liệu | Bắt buộc | Mô tả                   |
| ----------------- | ------------ | -------- | ----------------------- |
| id                | UUID         | Có       | Định danh cài đặt       |
| family_id         | UUID         | Có       | ID gia đình             |
| member_id         | UUID         | Có       | ID thành viên           |
| type_id           | UUID         | Có       | ID loại thông báo       |
| push_enabled      | Boolean      | Có       | Bật push notification   |
| email_enabled     | Boolean      | Có       | Bật email notification  |
| sms_enabled       | Boolean      | Có       | Bật SMS notification    |
| quiet_hours_start | Time         | Không    | Giờ bắt đầu im lặng     |
| quiet_hours_end   | Time         | Không    | Giờ kết thúc im lặng    |
| timezone          | String       | Có       | Múi giờ                 |
| created_at        | DateTime     | Có       | Thời điểm tạo           |
| updated_at        | DateTime     | Có       | Thời điểm cập nhật cuối |

### 2.4. Notification Templates Entity

| Trường            | Kiểu dữ liệu | Bắt buộc | Mô tả                   |
| ----------------- | ------------ | -------- | ----------------------- |
| id                | UUID         | Có       | Định danh template      |
| family_id         | UUID         | Có       | ID gia đình             |
| type_id           | UUID         | Có       | ID loại thông báo       |
| tên_template      | String       | Có       | Tên template            |
| tiêu_đề_template  | String       | Có       | Template tiêu đề        |
| nội_dung_template | Text         | Có       | Template nội dung       |
| biến_số           | JSONB        | Không    | Danh sách biến số       |
| ngôn_ngữ          | String       | Có       | Ngôn ngữ (vi/en)        |
| is_default        | Boolean      | Có       | Template mặc định       |
| created_at        | DateTime     | Có       | Thời điểm tạo           |
| updated_at        | DateTime     | Có       | Thời điểm cập nhật cuối |

## 3. Luồng nghiệp vụ chi tiết

### 3.1. Tạo loại thông báo

- User chọn "Tạo loại thông báo mới"
- Nhập thông tin cơ bản: tên, mô tả, template
- Chọn kênh gửi: PUSH/EMAIL/SMS
- Cấu hình thời gian gửi và độ ưu tiên
- Hệ thống tạo notification type

### 3.2. Gửi thông báo

- User chọn "Gửi thông báo"
- Chọn loại thông báo và người nhận
- Nhập tiêu đề và nội dung (hoặc sử dụng template)
- Chọn kênh gửi và thời gian gửi
- Hệ thống tạo notification và gửi

### 3.3. Gửi thông báo hàng loạt

- User chọn "Gửi thông báo hàng loạt"
- Chọn loại thông báo và danh sách người nhận
- Nhập nội dung chung
- Hệ thống tạo multiple notifications

### 3.4. Cấu hình cài đặt thông báo

- User chọn "Cài đặt thông báo"
- Bật/tắt các kênh gửi cho từng loại
- Cấu hình giờ im lặng
- Chọn múi giờ
- Hệ thống lưu settings

### 3.5. Tạo template thông báo

- User chọn "Tạo template"
- Nhập tên và nội dung template
- Định nghĩa các biến số
- Chọn ngôn ngữ
- Hệ thống tạo template

### 3.6. Quản lý thông báo

- User xem danh sách thông báo đã gửi
- Lọc theo loại, trạng thái, thời gian
- Xem chi tiết từng thông báo
- Thống kê hiệu quả gửi

## 4. Yêu cầu UI/UX chi tiết

### 4.1. Bố cục màn hình chính

**Header:**

- Logo ứng dụng (góc trái)
- Tên màn hình "Hệ thống Thông báo" (giữa)
- Nút "Gửi thông báo" (góc phải, màu xanh)

**Tab Navigation:**

- Tab 1: Thông báo đã gửi
- Tab 2: Loại thông báo
- Tab 3: Template
- Tab 4: Cài đặt

**Body:**

- Danh sách thông báo/loại/template theo tab
- Filter và search cho mỗi tab
- Pagination cho danh sách dài

**Sidebar (Desktop):**

- Thống kê: Tổng thông báo, đã gửi, thất bại
- Quick actions: Gửi thông báo, Tạo loại mới

### 4.2. Thiết kế giao diện

**Notification Card:**

- Background: màu trắng, border radius 12px, shadow nhẹ
- Header: Tiêu đề (font 16px, bold), Badge trạng thái
- Body: Nội dung, Người nhận, Thời gian gửi
- Footer: Kênh gửi, 2 nút: Xem chi tiết, Gửi lại
- Border left: màu theo độ ưu tiên (đỏ: HIGH, cam: MEDIUM, xanh: LOW)

**Form gửi thông báo:**

- Modal popup với tabs:
  1. Chọn loại và người nhận
  2. Nội dung thông báo
  3. Cấu hình gửi
  4. Xác nhận
- Progress bar: 4/4 steps
- Preview thông báo real-time

**Form tạo loại thông báo:**

- Modal popup với form:
  - Input tên và mô tả
  - Textarea template
  - Checkbox kênh gửi
  - Dropdown độ ưu tiên
- Preview template
- Nút "Tạo" và "Hủy"

**Settings Panel:**

- Accordion layout cho từng loại thông báo
- Toggle switches cho các kênh
- Time picker cho giờ im lặng
- Dropdown múi giờ
- Nút "Lưu cài đặt"

### 4.3. Tương tác người dùng

**Gửi thông báo:**

- Click "Gửi thông báo" → Mở modal
- Fill tab 1 → Next → Fill tab 2 → Next → ... → Submit
- Loading spinner khi gửi
- Success message + refresh danh sách

**Tạo loại thông báo:**

- Click "Tạo loại mới" → Mở modal
- Fill form → Preview → Submit
- Loading spinner khi tạo
- Success message + update danh sách

**Cấu hình cài đặt:**

- Click "Cài đặt" → Mở panel
- Toggle switches → Set time → Save
- Loading spinner khi lưu
- Success message + update settings

**Xem chi tiết:**

- Click "Xem chi tiết" → Mở modal
- Hiển thị đầy đủ thông tin thông báo
- Nút "Đóng"

### 4.4. Responsive Design

**Mobile (< 768px):**

- Tab navigation dạng dropdown
- List 1 cột
- Modal fullscreen
- Swipe gestures

**Tablet (768px - 1024px):**

- Tab navigation horizontal
- List 2 cột
- Modal 90% width

**Desktop (> 1024px):**

- Tab navigation horizontal
- List 3 cột
- Modal 800px width
- Sidebar visible

### 4.5. Accessibility

- Alt text cho tất cả ảnh
- ARIA labels cho form fields
- Keyboard navigation
- Screen reader support
- High contrast mode
- Focus indicators

### 4.6. User Flow chi tiết

```
1. Vào màn hình → Hiển thị tab "Thông báo đã gửi"
2. Gửi thông báo:
   Click "Gửi thông báo" → Fill 4 tabs → Submit → Success
3. Tạo loại mới:
   Click "Tạo loại mới" → Fill form → Submit → Success
4. Cấu hình cài đặt:
   Click "Cài đặt" → Toggle switches → Save → Success
5. Xem chi tiết:
   Click "Xem chi tiết" → View details → Close
6. Gửi lại:
   Click "Gửi lại" → Confirm → Success
```

### 4.7. Error Handling

- Form validation real-time
- Template validation
- Recipient validation
- Network error handling
- Retry mechanism
- Graceful degradation

### 4.8. Visual Feedback

- Hover effects trên cards
- Loading states cho tất cả actions
- Success/error toast messages
- Progress indicators cho multi-step forms
- Color coding theo độ ưu tiên
- Status badges: PENDING (cam), SENT (xanh), FAILED (đỏ), READ (xám)

### 4.9. Smart Features

- Auto-suggest recipient names
- Template auto-complete
- Smart scheduling
- Bulk operations
- Analytics dashboard
- A/B testing

## 5. Yêu cầu bảo mật, hiệu năng, mở rộng

### 5.1. Bảo mật

- Role-based access control
- Family isolation
- Data encryption
- Rate limiting
- Input sanitization

### 5.2. Hiệu năng

- Background processing cho notifications
- Queue system cho bulk sending
- Caching cho templates
- Optimized queries
- Real-time updates

### 5.3. Mở rộng

- Support unlimited notifications per family
- Multi-channel delivery
- Advanced analytics
- Integration với third-party services
- AI-powered personalization

## 6. Trường hợp ngoại lệ & lưu ý

### 6.1. Business Rules

- Không cho phép tạo loại thông báo trùng tên trong cùng family
- Recipient phải là member của family
- Template phải có ít nhất 1 biến số
- Quiet hours được áp dụng cho tất cả notifications
- Failed notifications được retry tối đa 3 lần

### 6.2. Validation Rules

- Tên loại: 3-100 ký tự
- Tiêu đề: 5-200 ký tự
- Nội dung: 10-1000 ký tự
- Template: valid syntax
- Timezone: valid format

### 6.3. Error Scenarios

- Loại thông báo đã tồn tại
- Recipient không hợp lệ
- Template syntax error
- Network failure
- Rate limit exceeded

### 6.4. Edge Cases

- Family không có member nào
- Tất cả notifications đều failed
- Template với nhiều biến số
- Timezone differences
- Recipient offline
