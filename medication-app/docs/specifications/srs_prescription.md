# SRS - Qu<PERSON>n lý Đơn thuốc

## 1. <PERSON><PERSON> tả tổng quan

Chức năng quản lý đơn thuốc cho phép người dùng tạo, chỉnh sửa, theo dõi và quản lý các đơn thuốc cho từng thành viên trong gia đình. Hệ thống hỗ trợ đơn thuốc từ bác sĩ, tự kê đơn, và theo dõi tiến trình điều trị.

## 2. Trường dữ liệu chi tiết

### 2.1. Prescriptions Entity

| Trường        | Kiểu dữ liệu | Bắt buộc | Mô tả                                 |
| ------------- | ------------ | -------- | ------------------------------------- |
| id            | UUID         | Có       | <PERSON><PERSON><PERSON> danh đơn thuốc                   |
| family_id     | UUID         | Có       | ID gia đình                           |
| member_id     | UUID         | Có       | ID thành viên được kê đơn             |
| tên_đơn_thuốc | String       | Có       | Tên đơn thuốc                         |
| loại_đơn      | Enum         | Có       | DOCTOR_PRESCRIPTION/SELF_PRESCRIPTION |
| bác_sĩ        | String       | Không    | Tên bác sĩ kê đơn                     |
| bệnh_viện     | String       | Không    | Tên bệnh viện/phòng khám              |
| chẩn_đoán     | Text         | Không    | Chẩn đoán bệnh                        |
| triệu_chứng   | Text         | Không    | Triệu chứng chính                     |
| ngày*kê*đơn   | Date         | Có       | Ngày bác sĩ kê đơn                    |
| ngày*bắt*đầu  | Date         | Có       | Ngày bắt đầu uống thuốc               |
| ngày_kết_thúc | Date         | Không    | Ngày kết thúc điều trị                |
| trạng_thái    | Enum         | Có       | ACTIVE/COMPLETED/PAUSED/CANCELLED     |
| độ_ưu_tiên    | Enum         | Có       | HIGH/MEDIUM/LOW                       |
| ghi_chú       | Text         | Không    | Ghi chú bổ sung                       |
| created_by    | UUID         | Có       | ID user tạo đơn thuốc                 |
| created_at    | DateTime     | Có       | Thời điểm tạo                         |
| updated_at    | DateTime     | Có       | Thời điểm cập nhật cuối               |
| deleted_at    | DateTime     | Không    | Thời điểm xóa (soft delete)           |

### 2.2. Prescription Medicines Entity

| Trường           | Kiểu dữ liệu | Bắt buộc | Mô tả                                 |
| ---------------- | ------------ | -------- | ------------------------------------- |
| id               | UUID         | Có       | Định danh thuốc trong đơn             |
| prescription_id  | UUID         | Có       | ID đơn thuốc                          |
| medicine_id      | UUID         | Có       | ID thuốc                              |
| liều_lượng       | String       | Có       | Số viên/lần, số lần/ngày              |
| thời_gian_uống   | String       | Có       | Sáng/Trưa/Tối/Trước ngủ hoặc tùy chọn |
| số*lượng_ban*đầu | Integer      | Có       | Số lượng thuốc ban đầu                |
| số_lượng_còn_lại | Integer      | Có       | Số lượng thuốc còn lại                |
| đơn_vị           | String       | Có       | Đơn vị (viên, ml, g...)               |
| hướng_dẫn        | Text         | Không    | Hướng dẫn sử dụng chi tiết            |
| ghi_chú          | Text         | Không    | Ghi chú bổ sung                       |
| trạng_thái       | Enum         | Có       | ACTIVE/COMPLETED/PAUSED               |
| created_at       | DateTime     | Có       | Thời điểm tạo                         |
| updated_at       | DateTime     | Có       | Thời điểm cập nhật cuối               |

### 2.3. Prescription Schedule Entity

| Trường                   | Kiểu dữ liệu | Bắt buộc | Mô tả                               |
| ------------------------ | ------------ | -------- | ----------------------------------- |
| id                       | UUID         | Có       | Định danh lịch uống                 |
| prescription_id          | UUID         | Có       | ID đơn thuốc                        |
| prescription_medicine_id | UUID         | Có       | ID thuốc trong đơn                  |
| thời_gian                | Time         | Có       | Giờ uống thuốc                      |
| ngày_trong_tuần          | String       | Có       | Các ngày trong tuần (1,2,3,4,5,6,7) |
| liều_lượng               | Integer      | Có       | Số lượng uống mỗi lần               |
| đơn_vị                   | String       | Có       | Đơn vị                              |
| ghi_chú                  | Text         | Không    | Ghi chú bổ sung                     |
| is_active                | Boolean      | Có       | Lịch có hoạt động không             |
| created_at               | DateTime     | Có       | Thời điểm tạo                       |
| updated_at               | DateTime     | Có       | Thời điểm cập nhật cuối             |

### 2.4. Prescription History Entity

| Trường          | Kiểu dữ liệu | Bắt buộc | Mô tả                                         |
| --------------- | ------------ | -------- | --------------------------------------------- |
| id              | UUID         | Có       | Định danh lịch sử                             |
| prescription_id | UUID         | Có       | ID đơn thuốc                                  |
| action_type     | Enum         | Có       | CREATED/UPDATED/STATUS_CHANGED/MEDICINE_ADDED |
| old_value       | JSONB        | Không    | Giá trị cũ                                    |
| new_value       | JSONB        | Không    | Giá trị mới                                   |
| description     | String       | Có       | Mô tả thay đổi                                |
| created_by      | UUID         | Có       | ID user thực hiện                             |
| created_at      | DateTime     | Có       | Thời điểm tạo                                 |

## 3. Luồng nghiệp vụ chi tiết

### 3.1. Tạo đơn thuốc mới

- User chọn "Tạo đơn thuốc mới"
- Chọn thành viên được kê đơn
- Nhập thông tin cơ bản: tên đơn, loại đơn, bác sĩ, bệnh viện
- Nhập thông tin bệnh: chẩn đoán, triệu chứng
- Nhập thời gian: ngày kê đơn, ngày bắt đầu, ngày kết thúc
- Chọn độ ưu tiên và ghi chú
- Hệ thống tạo đơn thuốc với trạng thái ACTIVE

### 3.2. Thêm thuốc vào đơn

- User chọn đơn thuốc cần thêm thuốc
- Chọn thuốc từ danh sách thuốc trong tủ
- Nhập liều lượng và thời gian uống
- Nhập số lượng ban đầu và đơn vị
- Nhập hướng dẫn sử dụng và ghi chú
- Hệ thống tạo prescription_medicine record

### 3.3. Tạo lịch uống thuốc

- User chọn thuốc trong đơn cần tạo lịch
- Chọn thời gian uống (giờ cụ thể)
- Chọn các ngày trong tuần
- Nhập liều lượng và đơn vị
- Hệ thống tạo schedule records

### 3.4. Cập nhật trạng thái đơn

- User chọn đơn thuốc cần cập nhật
- Chọn trạng thái mới: COMPLETED/PAUSED/CANCELLED
- Nhập lý do thay đổi (nếu có)
- Hệ thống cập nhật và tạo history record

### 3.5. Theo dõi tiến trình

- User xem danh sách đơn thuốc theo trạng thái
- Xem chi tiết từng đơn với danh sách thuốc
- Xem lịch sử thay đổi của đơn
- Xem thống kê tuân thủ điều trị

### 3.6. Sao chép đơn thuốc

- User chọn đơn thuốc cần sao chép
- Chọn thành viên mới (nếu khác)
- Điều chỉnh thông tin cần thiết
- Hệ thống tạo đơn mới với thuốc tương tự

## 4. Yêu cầu UI/UX chi tiết

### 4.1. Bố cục màn hình chính

**Header:**

- Logo ứng dụng (góc trái)
- Tên màn hình "Quản lý Đơn thuốc" (giữa)
- Nút "Tạo đơn thuốc" (góc phải, màu xanh)

**Filter Bar:**

- Dropdown chọn thành viên
- Dropdown lọc theo trạng thái
- Dropdown lọc theo loại đơn
- Search box tìm kiếm theo tên đơn

**Body:**

- Tab layout: Tất cả, Đang điều trị, Hoàn thành, Tạm dừng
- Grid layout cho danh sách đơn thuốc
- Mỗi card đơn thuốc 350x450px

**Sidebar (Desktop):**

- Thống kê: Tổng đơn thuốc, đang điều trị, hoàn thành
- Quick actions: Tạo đơn thuốc, Sao chép đơn

### 4.2. Thiết kế giao diện

**Card đơn thuốc:**

- Background: màu trắng, border radius 12px, shadow nhẹ
- Header: Tên đơn thuốc (font 20px, bold), Badge trạng thái
- Body: Thành viên, Bác sĩ, Ngày bắt đầu, Số thuốc trong đơn
- Footer: Tiến độ điều trị (progress bar), 3 nút: Chi tiết, Chỉnh sửa, Sao chép
- Border left: màu theo độ ưu tiên (đỏ: HIGH, cam: MEDIUM, xanh: LOW)

**Form tạo đơn thuốc:**

- Modal popup với tabs:
  1. Thông tin cơ bản
  2. Thông tin bệnh
  3. Thời gian điều trị
  4. Thêm thuốc
  5. Tạo lịch uống
- Progress bar: 5/5 steps
- Preview đơn thuốc real-time

**Form thêm thuốc:**

- Modal popup với form:
  - Chọn thuốc từ dropdown
  - Input liều lượng và thời gian uống
  - Input số lượng và đơn vị
  - Textarea hướng dẫn và ghi chú
- Preview thuốc được thêm
- Nút "Thêm" và "Hủy"

**Form tạo lịch uống:**

- Modal popup với form:
  - Chọn thuốc từ danh sách trong đơn
  - Time picker cho giờ uống
  - Checkbox cho các ngày trong tuần
  - Input liều lượng và đơn vị
- Preview lịch uống
- Nút "Tạo lịch" và "Hủy"

### 4.3. Tương tác người dùng

**Tạo đơn thuốc:**

- Click "Tạo đơn thuốc" → Mở modal
- Fill tab 1 → Next → Fill tab 2 → Next → ... → Submit
- Loading spinner khi tạo
- Success message + redirect đến đơn mới

**Thêm thuốc:**

- Click "Thêm thuốc" → Mở modal
- Select medicine → Fill form → Submit
- Loading spinner khi thêm
- Success message + update đơn thuốc

**Tạo lịch uống:**

- Click "Tạo lịch uống" → Mở modal
- Select medicine → Set time → Select days → Submit
- Loading spinner khi tạo
- Success message + update schedule

**Cập nhật trạng thái:**

- Click "Cập nhật trạng thái" → Mở modal
- Select new status → Input reason → Submit
- Loading spinner khi cập nhật
- Success message + update card

**Sao chép đơn:**

- Click "Sao chép" → Mở modal
- Select new member → Adjust info → Submit
- Loading spinner khi sao chép
- Success message + redirect đến đơn mới

### 4.4. Responsive Design

**Mobile (< 768px):**

- Grid 1 cột
- Card full width
- Modal fullscreen
- Swipe gestures

**Tablet (768px - 1024px):**

- Grid 2 cột
- Card 45% width
- Modal 90% width

**Desktop (> 1024px):**

- Grid 3 cột
- Card 350px fixed width
- Modal 900px width
- Sidebar visible

### 4.5. Accessibility

- Alt text cho tất cả ảnh
- ARIA labels cho form fields
- Keyboard navigation
- Screen reader support
- High contrast mode
- Focus indicators

### 4.6. User Flow chi tiết

```
1. Vào màn hình → Hiển thị danh sách đơn thuốc
2. Tạo đơn thuốc:
   Click "Tạo đơn thuốc" → Fill 5 tabs → Submit → Success → Redirect
3. Thêm thuốc:
   Click "Thêm thuốc" → Select medicine → Fill form → Submit → Success
4. Tạo lịch uống:
   Click "Tạo lịch uống" → Select medicine → Set schedule → Submit → Success
5. Cập nhật trạng thái:
   Click "Cập nhật trạng thái" → Select status → Submit → Success
6. Sao chép đơn:
   Click "Sao chép" → Select member → Adjust → Submit → Success
```

### 4.7. Error Handling

- Form validation real-time
- Duplicate prescription detection
- Medicine availability check
- Date validation (end date >= start date)
- Network error handling
- Graceful degradation

### 4.8. Visual Feedback

- Hover effects trên cards
- Loading states cho tất cả actions
- Success/error toast messages
- Progress indicators cho multi-step forms
- Color coding theo độ ưu tiên
- Status badges: ACTIVE (xanh), COMPLETED (xám), PAUSED (cam), CANCELLED (đỏ)

### 4.9. Smart Features

- Auto-suggest medicine names
- Smart scheduling suggestions
- Duplicate detection
- Progress tracking
- Reminder integration
- Prescription templates

## 5. Yêu cầu bảo mật, hiệu năng, mở rộng

### 5.1. Bảo mật

- Role-based access control
- Family isolation
- Audit trail cho tất cả changes
- Data encryption
- Input sanitization

### 5.2. Hiệu năng

- Lazy loading cho danh sách đơn thuốc
- Caching cho prescription data
- Pagination cho large lists
- Optimized queries
- Background processing cho schedules

### 5.3. Mở rộng

- Support unlimited prescriptions per family
- Advanced scheduling
- Integration với pharmacy systems
- Prescription sharing
- Telemedicine integration

## 6. Trường hợp ngoại lệ & lưu ý

### 6.1. Business Rules

- Không cho phép tạo đơn thuốc trùng tên cho cùng member
- Medicine trong đơn phải có trong tủ thuốc
- End date phải >= start date
- Prescription history không thể chỉnh sửa
- Schedule phải có ít nhất 1 ngày trong tuần

### 6.2. Validation Rules

- Tên đơn thuốc: 3-200 ký tự
- Ngày kê đơn: <= ngày hiện tại
- Ngày kết thúc: >= ngày bắt đầu
- Liều lượng: > 0
- Số lượng: >= 0

### 6.3. Error Scenarios

- Đơn thuốc đã tồn tại
- Medicine không có trong tủ
- Date validation failed
- Permission denied
- Network error

### 6.4. Edge Cases

- Family không có member nào
- Tất cả đơn thuốc đều hoàn thành
- Prescription với nhiều medicine
- Schedule conflict
- Medicine shortage
