# SRS - Qu<PERSON>n lý người dùng (<PERSON><PERSON> sơ nội bộ)

## 1. <PERSON><PERSON> tả tổng quan

Chức năng quản lý hồ sơ thành viên cho phép người dùng tạo, chỉnh sửa, xóa và chuyển đổi giữa các hồ sơ thành viên trong gia đình. Mỗi thành viên có thể được gán nhiều đơn thuốc khác nhau, giúp cá nhân hóa việc quản lý sức khỏe cho từng người.

## 2. Trường dữ liệu chi tiết

| Tr<PERSON>ờng        | Kiểu dữ liệu | Bắt buộc | Mô tả                                 |
| ------------- | ------------ | -------- | ------------------------------------- |
| id            | UUID         | <PERSON><PERSON>       | <PERSON><PERSON><PERSON> danh duy nhất cho hồ sơ          |
| tên           | String       | Có       | Tên thành viên                        |
| tuổi          | Integer      | Có       | Tuổi thành viên                       |
| giới_tính     | Enum         | Có       | Nam/Nữ/Khác                           |
| ảnh_đại_diện  | String(URL)  | Không    | Đường dẫn ảnh đại diện                |
| đơn_thuốc     | List<UUID>   | Không    | Danh sách id đơn thuốc của thành viên |
| ngày_tạo      | DateTime     | Có       | Thời điểm tạo hồ sơ                   |
| ngày_cập_nhật | DateTime     | Có       | Thời điểm cập nhật gần nhất           |

## 3. Luồng nghiệp vụ chi tiết

### 3.1. Tạo hồ sơ thành viên

- Người dùng chọn "Thêm thành viên mới"
- Nhập tên, tuổi, giới tính, (tùy chọn) ảnh đại diện
- Lưu hồ sơ, sinh id tự động, ghi nhận ngày tạo
- Chuyển về màn hình danh sách hồ sơ

### 3.2. Chỉnh sửa hồ sơ

- Người dùng chọn hồ sơ cần sửa
- Thay đổi thông tin, lưu lại, cập nhật ngày_cập_nhật

### 3.3. Xóa hồ sơ

- Người dùng chọn xóa, xác nhận lại thao tác
- Nếu hồ sơ có đơn thuốc, cảnh báo trước khi xóa
- Xóa hồ sơ và các liên kết đơn thuốc (không xóa đơn thuốc gốc)

### 3.4. Chuyển đổi hồ sơ

- Người dùng chọn hồ sơ để chuyển đổi
- Hệ thống hiển thị thông tin, đơn thuốc liên quan đến hồ sơ đó

## 4. Yêu cầu UI/UX chi tiết

### 4.1. Bố cục màn hình chính

**Header:**

- Logo ứng dụng (góc trái)
- Tên màn hình "Quản lý thành viên" (giữa)
- Nút "Thêm thành viên" (góc phải, màu xanh)

**Body:**

- Danh sách thành viên dạng grid (2 cột trên mobile, 3-4 cột trên tablet/desktop)
- Mỗi card thành viên có kích thước 200x250px

**Footer:**

- Thông tin tổng số thành viên
- Nút "Quay lại" (nếu có)

### 4.2. Thiết kế giao diện

**Card thành viên:**

- Background: màu trắng, border radius 12px, shadow nhẹ
- Ảnh đại diện: 80x80px, border radius 50%, border 3px màu xanh nhạt
- Tên: font size 18px, font weight 600, màu đen
- Tuổi & giới tính: font size 14px, màu xám
- Số đơn thuốc: badge màu xanh, font size 12px
- 3 nút hành động: Sửa (màu xanh), Xóa (màu đỏ), Chọn (màu tím)

**Form thêm/sửa thành viên:**

- Modal popup với background overlay
- Form fields: Tên (text input), Tuổi (number input), Giới tính (radio buttons)
- Upload ảnh: drag & drop hoặc chọn file
- Preview ảnh real-time
- Nút "Lưu" (màu xanh) và "Hủy" (màu xám)

### 4.3. Tương tác người dùng

**Thêm thành viên:**

- Click nút "+" → Mở modal form
- Fill form → Preview ảnh → Validate → Submit
- Loading spinner khi lưu
- Success message + đóng modal

**Chỉnh sửa:**

- Click nút "Sửa" → Mở modal với data hiện tại
- Chỉnh sửa → Preview → Lưu
- Auto-save draft nếu user đóng modal

**Xóa thành viên:**

- Click nút "Xóa" → Confirm dialog
- Warning nếu có đơn thuốc: "Thành viên này có X đơn thuốc. Bạn có chắc muốn xóa?"
- 2 options: "Xóa" (màu đỏ) và "Hủy"

**Chuyển đổi thành viên:**

- Click nút "Chọn" → Highlight card → Update global state
- Hiển thị badge "Đang chọn" trên card
- Cập nhật header với tên thành viên hiện tại

### 4.4. Responsive Design

**Mobile (< 768px):**

- Grid 1 cột
- Card size: 100% width, height auto
- Modal fullscreen
- Swipe gestures để chuyển đổi thành viên

**Tablet (768px - 1024px):**

- Grid 2 cột
- Card size: 45% width
- Modal 80% width

**Desktop (> 1024px):**

- Grid 3-4 cột
- Card size: 200px fixed
- Modal 500px width

### 4.5. Accessibility

- Alt text cho tất cả ảnh
- ARIA labels cho buttons
- Keyboard navigation support
- High contrast mode
- Screen reader friendly
- Focus indicators rõ ràng

### 4.6. User Flow chi tiết

```
1. Vào màn hình → Hiển thị danh sách thành viên
2. Thêm mới:
   Click "+" → Fill form → Upload ảnh → Validate → Save → Success
3. Chỉnh sửa:
   Click "Sửa" → Edit form → Save → Success
4. Xóa:
   Click "Xóa" → Confirm → Delete → Success
5. Chuyển đổi:
   Click "Chọn" → Highlight → Update global state
```

### 4.7. Error Handling

- Form validation real-time
- Error messages rõ ràng, có icon
- Retry mechanism cho network errors
- Graceful degradation khi không có ảnh

## 5. Yêu cầu bảo mật, hiệu năng, mở rộng

- Thông tin cá nhân được mã hóa khi lưu trữ
- Chỉ người dùng đã xác thực mới được thao tác hồ sơ
- Hỗ trợ mở rộng số lượng thành viên không giới hạn

## 6. Trường hợp ngoại lệ & lưu ý

- Không cho phép tạo hai hồ sơ trùng tên và tuổi
- Nếu nhập thiếu trường bắt buộc, báo lỗi rõ ràng
- Nếu xóa hồ sơ cuối cùng, cảnh báo không thể thao tác tiếp
