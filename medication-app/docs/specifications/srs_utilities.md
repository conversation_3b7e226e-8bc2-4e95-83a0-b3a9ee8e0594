# SRS - Tiện ích & Cài đặt

## 1. <PERSON><PERSON> tả tổng quan

Chức năng tiện ích và cài đặt cung cấp các công cụ hỗ trợ và tùy chỉnh hệ thống cho người dùng. Bao gồm quản lý tà<PERSON>, cài đặt ứng dụng, xuất/nhập dữ liệu, và các tiện ích bổ sung.

## 2. Trường dữ liệu chi tiết

### 2.1. User Settings Entity

| Trường       | Kiểu dữ liệu | Bắt buộc | Mô tả                                      |
| ------------ | ------------ | -------- | ------------------------------------------ |
| id           | UUID         | Có       | Định danh cài đặt                          |
| user_id      | UUID         | Có       | ID user                                    |
| family_id    | UUID         | Có       | ID gia đình                                |
| loại*cài*đặt | String       | Có       | Loại cài đặt (THEME/LANGUAGE/NOTIFICATION) |
| khóa         | String       | Có       | Khóa cài đặt                               |
| giá_trị      | JSONB        | Có       | Giá trị cài đặt                            |
| mô_tả        | Text         | Không    | Mô tả cài đặt                              |
| is_active    | Boolean      | Có       | Cài đặt có hoạt động không                 |
| created_at   | DateTime     | Có       | Thời điểm tạo                              |
| updated_at   | DateTime     | Có       | Thời điểm cập nhật cuối                    |

### 2.2. Data Exports Entity

| Trường       | Kiểu dữ liệu | Bắt buộc | Mô tả                               |
| ------------ | ------------ | -------- | ----------------------------------- |
| id           | UUID         | Có       | Định danh xuất dữ liệu              |
| family_id    | UUID         | Có       | ID gia đình                         |
| user_id      | UUID         | Có       | ID user thực hiện                   |
| tên_file     | String       | Có       | Tên file xuất                       |
| loại_xuất    | Enum         | Có       | FULL/BACKUP/SELECTIVE               |
| định_dạng    | Enum         | Có       | JSON/CSV/EXCEL/PDF                  |
| phạm_vi      | JSONB        | Có       | Phạm vi dữ liệu xuất                |
| trạng_thái   | Enum         | Có       | PENDING/PROCESSING/COMPLETED/FAILED |
| file_path    | String       | Không    | Đường dẫn file                      |
| file_size    | Integer      | Không    | Kích thước file (bytes)             |
| expires_at   | DateTime     | Có       | Thời gian hết hạn                   |
| created_at   | DateTime     | Có       | Thời điểm tạo                       |
| completed_at | DateTime     | Không    | Thời điểm hoàn thành                |

### 2.3. Data Imports Entity

| Trường       | Kiểu dữ liệu | Bắt buộc | Mô tả                                          |
| ------------ | ------------ | -------- | ---------------------------------------------- |
| id           | UUID         | Có       | Định danh nhập dữ liệu                         |
| family_id    | UUID         | Có       | ID gia đình                                    |
| user_id      | UUID         | Có       | ID user thực hiện                              |
| tên_file     | String       | Có       | Tên file nhập                                  |
| loại_nhập    | Enum         | Có       | FULL/MERGE/UPDATE                              |
| định_dạng    | Enum         | Có       | JSON/CSV/EXCEL                                 |
| file_path    | String       | Có       | Đường dẫn file                                 |
| file_size    | Integer      | Có       | Kích thước file (bytes)                        |
| trạng_thái   | Enum         | Có       | PENDING/VALIDATING/PROCESSING/COMPLETED/FAILED |
| kết_quả      | JSONB        | Không    | Kết quả nhập dữ liệu                           |
| lỗi          | Text         | Không    | Thông tin lỗi                                  |
| created_at   | DateTime     | Có       | Thời điểm tạo                                  |
| completed_at | DateTime     | Không    | Thời điểm hoàn thành                           |

### 2.4. System Logs Entity

| Trường     | Kiểu dữ liệu | Bắt buộc | Mô tả                    |
| ---------- | ------------ | -------- | ------------------------ |
| id         | UUID         | Có       | Định danh log            |
| family_id  | UUID         | Có       | ID gia đình              |
| user_id    | UUID         | Có       | ID user thực hiện        |
| loại_log   | Enum         | Có       | INFO/WARNING/ERROR/DEBUG |
| mô_đun     | String       | Có       | Mô-đun tạo log           |
| hành_động  | String       | Có       | Hành động thực hiện      |
| mô_tả      | Text         | Có       | Mô tả chi tiết           |
| dữ_liệu    | JSONB        | Không    | Dữ liệu bổ sung          |
| ip_address | String       | Không    | Địa chỉ IP               |
| user_agent | String       | Không    | User agent               |
| created_at | DateTime     | Có       | Thời điểm tạo            |

## 3. Luồng nghiệp vụ chi tiết

### 3.1. Quản lý tài khoản

- User chọn "Quản lý tài khoản"
- Xem và chỉnh sửa thông tin cá nhân
- Thay đổi mật khẩu
- Cập nhật ảnh đại diện
- Xóa tài khoản (với xác nhận)

### 3.2. Cài đặt ứng dụng

- User chọn "Cài đặt ứng dụng"
- Tùy chỉnh giao diện: theme, ngôn ngữ, font size
- Cấu hình thông báo: push, email, SMS
- Cài đặt bảo mật: 2FA, session timeout
- Lưu cài đặt

### 3.3. Xuất dữ liệu

- User chọn "Xuất dữ liệu"
- Chọn loại xuất: toàn bộ, backup, có chọn lọc
- Chọn định dạng: JSON, CSV, Excel, PDF
- Chọn phạm vi dữ liệu
- Hệ thống tạo file và gửi thông báo khi hoàn thành

### 3.4. Nhập dữ liệu

- User chọn "Nhập dữ liệu"
- Upload file (JSON, CSV, Excel)
- Chọn loại nhập: toàn bộ, merge, update
- Xem preview dữ liệu
- Xác nhận và thực hiện nhập

### 3.5. Sao lưu và khôi phục

- User chọn "Sao lưu dữ liệu"
- Tạo backup tự động hoặc thủ công
- Lưu trữ backup an toàn
- Khôi phục dữ liệu từ backup
- Xác nhận trước khi khôi phục

### 3.6. Xem log hệ thống

- User chọn "Log hệ thống"
- Xem danh sách log theo thời gian
- Lọc theo loại, mô-đun, user
- Tìm kiếm trong log
- Xuất log để phân tích

## 4. Yêu cầu UI/UX chi tiết

### 4.1. Bố cục màn hình chính

**Header:**

- Logo ứng dụng (góc trái)
- Tên màn hình "Tiện ích & Cài đặt" (giữa)
- Nút "Trợ giúp" (góc phải, màu xanh)

**Tab Navigation:**

- Tab 1: Tài khoản
- Tab 2: Cài đặt
- Tab 3: Dữ liệu
- Tab 4: Hệ thống

**Body:**

- Form hoặc danh sách theo tab
- Responsive layout
- Validation real-time

**Sidebar (Desktop):**

- Quick actions
- Thông tin phiên bản
- Liên kết hỗ trợ

### 4.2. Thiết kế giao diện

**Account Card:**

- Background: màu trắng, border radius 12px, shadow nhẹ
- Header: Ảnh đại diện, Tên user, Email
- Body: Form thông tin cá nhân
- Footer: 3 nút: Lưu thay đổi, Đổi mật khẩu, Xóa tài khoản
- Border: màu xanh nhạt

**Settings Panel:**

- Accordion layout cho từng nhóm cài đặt
- Toggle switches cho boolean settings
- Dropdown cho enum settings
- Color picker cho theme
- Preview real-time

**Data Export Form:**

- Modal popup với form:
  - Radio buttons cho loại xuất
  - Dropdown cho định dạng
  - Checkbox cho phạm vi dữ liệu
  - Progress bar khi xuất
- Preview settings
- Nút "Xuất" và "Hủy"

**Data Import Form:**

- Modal popup với form:
  - File upload area
  - Radio buttons cho loại nhập
  - Preview dữ liệu
  - Validation results
- Nút "Nhập" và "Hủy"

### 4.3. Tương tác người dùng

**Quản lý tài khoản:**

- Click "Chỉnh sửa" → Edit form → Save
- Click "Đổi mật khẩu" → Fill form → Confirm
- Click "Xóa tài khoản" → Confirm dialog → Delete
- Loading spinner cho tất cả actions

**Cài đặt ứng dụng:**

- Toggle switches → Auto-save
- Dropdown selection → Auto-save
- Color picker → Preview → Save
- Success message cho mỗi thay đổi

**Xuất dữ liệu:**

- Click "Xuất dữ liệu" → Mở modal
- Select options → Submit → Background process
- Notification khi hoàn thành → Download
- Progress indicator

**Nhập dữ liệu:**

- Click "Nhập dữ liệu" → Mở modal
- Upload file → Validate → Preview → Confirm
- Progress bar → Success/error message
- Download template nếu cần

### 4.4. Responsive Design

**Mobile (< 768px):**

- Tab navigation dạng dropdown
- Form full width
- Modal fullscreen
- Swipe gestures

**Tablet (768px - 1024px):**

- Tab navigation horizontal
- Form 80% width
- Modal 90% width

**Desktop (> 1024px):**

- Tab navigation horizontal
- Form 60% width
- Modal 800px width
- Sidebar visible

### 4.5. Accessibility

- Alt text cho tất cả ảnh
- ARIA labels cho form fields
- Keyboard navigation
- Screen reader support
- High contrast mode
- Focus indicators

### 4.6. User Flow chi tiết

```
1. Vào màn hình → Hiển thị tab "Tài khoản"
2. Quản lý tài khoản:
   Click "Chỉnh sửa" → Edit → Save → Success
3. Cài đặt ứng dụng:
   Toggle switches → Auto-save → Success
4. Xuất dữ liệu:
   Click "Xuất" → Fill form → Submit → Background → Download
5. Nhập dữ liệu:
   Click "Nhập" → Upload → Validate → Confirm → Success
6. Xem log:
   Click "Log" → Filter → Search → Export
```

### 4.7. Error Handling

- Form validation real-time
- File validation (size, format)
- Data validation cho import
- Network error handling
- Graceful degradation
- Retry mechanism

### 4.8. Visual Feedback

- Loading spinners cho tất cả actions
- Success/error toast messages
- Progress bars cho file operations
- Color coding theo trạng thái
- Animation cho transitions
- Preview real-time

### 4.9. Smart Features

- Auto-save settings
- Smart file validation
- Data preview
- Template download
- Backup scheduling
- Log analytics

## 5. Yêu cầu bảo mật, hiệu năng, mở rộng

### 5.1. Bảo mật

- Role-based access control
- Data encryption
- File upload validation
- Session management
- Audit trail

### 5.2. Hiệu năng

- Background processing cho exports
- File compression
- Caching cho settings
- Optimized queries
- Progress tracking

### 5.3. Mở rộng

- Support multiple file formats
- Advanced backup options
- Custom export templates
- Integration với cloud storage
- API access

## 6. Trường hợp ngoại lệ & lưu ý

### 6.1. Business Rules

- Settings được lưu per user per family
- Exports có thời hạn 7 ngày
- Imports require validation
- Logs được lưu 90 ngày
- Backup được tạo tự động hàng tuần

### 6.2. Validation Rules

- File size: max 50MB
- File format: supported types only
- Data format: valid schema
- Settings: valid values
- Password: strong requirements

### 6.3. Error Scenarios

- File upload failed
- Data validation failed
- Export generation failed
- Import conflicts
- Permission denied

### 6.4. Edge Cases

- Large file uploads
- Network timeout
- Disk space full
- Data corruption
- Concurrent operations
