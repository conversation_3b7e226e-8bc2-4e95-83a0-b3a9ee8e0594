# 📋 User Cases Documentation - Medication App

## 🎯 Tổng quan

Tài liệu này chứa tất cả User Cases cho ứng dụng Quản lý Tủ thuốc Gia đình, đư<PERSON><PERSON> thiết kế theo **Authentication Optional Pattern** - hỗ trợ cả Anonymous và Registered users.

**🆕 NEW: Authentication Optional Pattern** - Users có thể sử dụng app ngay lập tức mà không cần đăng ký

## 📊 Trạng thái hiện tại

### **✅ Đã hoàn thành:**

- **Authentication Module**: 4/4 cases (100%) - **🆕 Updated với Anonymous support**
- **Family Management Module**: 4/4 cases (100%)
- **Medicine Management Module**: 4/4 cases (100%)
- **Tổng cộng**: 12/24 cases (50%)

### **🔄 Đang thực hiện:**

- **Prescription Management**: 4 cases
- **Notification & Scheduling**: 4 cases
- **Dashboard & Analytics**: 4 cases

---

## 🆕 Authentication Optional Pattern

### **User Types:**

```java
public enum UserType {
    ANONYMOUS,    // Không đăng nhập, dùng local storage
    REGISTERED,   // Đã đăng nhập, có family
    PREMIUM       // Premium features (future)
}
```

### **Feature Matrix:**

| Feature              | Anonymous | Registered | Premium |
| -------------------- | --------- | ---------- | ------- |
| View medicines       | ✅        | ✅         | ✅      |
| Add medicines        | ✅        | ✅         | ✅      |
| Create prescriptions | ✅        | ✅         | ✅      |
| View schedules       | ✅        | ✅         | ✅      |
| Family management    | ❌        | ✅         | ✅      |
| Multi-device sync    | ❌        | ✅         | ✅      |
| Cloud backup         | ❌        | ✅         | ✅      |
| Data export          | ❌        | ✅         | ✅      |
| Advanced analytics   | ❌        | ❌         | ✅      |

### **User Journey:**

#### **Anonymous User Flow:**

```
1. Download app → Start immediately (no registration)
2. Add medicines → Stored locally
3. Create prescriptions → Stored locally
4. Set schedules → Stored locally
5. Use app normally → All data local

6. Want family features → Register account
7. Data migration → Local → Cloud
8. Full features → Family management, sync, etc.
```

#### **Registered User Flow:**

```
1. Register/Login → Full features
2. Create family → Multi-user support
3. Sync data → Multi-device
4. Share with family → Collaboration
5. Export data → Backup & analysis
```

---

## 📁 Cấu trúc User Cases

### **1. Authentication Module (4 cases)**

- **UC-001**: User Registration - **🆕 Updated với Anonymous data migration**
- **UC-002**: User Login
- **UC-003**: Password Reset
- **UC-004**: JWT Token Refresh

### **2. Family Management Module (4 cases)**

- **UC-005**: Create Family
- **UC-006**: Invite Family Member
- **UC-007**: Accept Family Invitation
- **UC-008**: Manage Family Roles

### **3. Medicine Management Module (4 cases)**

- **UC-009**: Add Medicine Type
- **UC-010**: Add Medicine to Inventory
- **UC-011**: Update Medicine Quantity
- **UC-012**: Remove Expired Medicine

### **4. Prescription Management Module (4 cases)**

- **UC-013**: Create Prescription
- **UC-014**: Assign Medicine to Prescription
- **UC-015**: Schedule Medication
- **UC-016**: Track Medication Compliance

### **5. Notification & Scheduling Module (4 cases)**

- **UC-017**: Set Medication Reminder
- **UC-018**: Track Medication Compliance
- **UC-019**: Manage Notification Settings
- **UC-020**: Send Notifications

### **6. Dashboard & Analytics Module (4 cases)**

- **UC-021**: View Dashboard Overview
- **UC-022**: Generate Analytics Report
- **UC-023**: Export Family Data
- **UC-024**: Manage System Settings

---

## 🆕 Anonymous User Support

### **Anonymous APIs:**

```java
// Anonymous endpoints (no authentication required)
@RestController
@RequestMapping("/api/anonymous")
public class AnonymousController {
    @PostMapping("/session")           // Register anonymous session
    @GetMapping("/medicines")          // Get medicines
    @PostMapping("/medicines")         // Add medicine
    @PutMapping("/medicines/{id}")     // Update medicine
    @DeleteMapping("/medicines/{id}")  // Delete medicine
    // ... other anonymous endpoints
}
```

### **Data Migration:**

```java
// Enhanced registration với data migration
@PostMapping("/api/auth/register")
public ResponseEntity<ApiResponse<RegisterResponse>> register(
        @RequestBody RegisterRequest request,
        @RequestHeader(value = "X-Anonymous-User-Id", required = false) String anonymousUserId) {

    // 1. Create user account
    User user = userService.createUser(request);

    // 2. Migrate anonymous data if exists
    if (anonymousUserId != null) {
        dataMigrationService.migrateLocalToCloud(user.getId(), anonymousUserId);
    }

    // 3. Return response with migration info
    return ResponseEntity.ok(RegisterResponse.builder()
            .userId(user.getId())
            .dataMigrated(true)
            .build());
}
```

---

## 📊 Chi tiết từng Module

### **🔐 Authentication Module**

#### **UC-001: User Registration** ✅ **Updated**

- **File**: `authentication/UC-001-authentication-user-registration.md`
- **Status**: ✅ **HOÀN THÀNH** - **🆕 Updated với Anonymous data migration**
- **Key Features**:
  - Email validation và uniqueness check
  - Password strength validation
  - **🆕 Anonymous data migration support**
  - **🆕 Migration status tracking**
  - Comprehensive error handling

#### **UC-002: User Login** ✅

- **File**: `authentication/UC-002-authentication-user-login.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - JWT token authentication
  - Password verification với BCrypt
  - Remember me functionality
  - Last login tracking

#### **UC-003: Password Reset** ✅

- **File**: `authentication/UC-003-authentication-password-reset.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - Email-based password reset
  - Token expiration (1 hour)
  - Rate limiting
  - Session invalidation

#### **UC-004: JWT Token Refresh** ✅

- **File**: `authentication/UC-004-authentication-jwt-token-refresh.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - Automatic token refresh
  - Token rotation
  - Session tracking
  - Performance optimization

### **👨‍👩‍👧‍👦 Family Management Module**

#### **UC-005: Create Family** ✅

- **File**: `family-management/UC-005-family-management-create-family.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - Family creation với role-based permissions
  - User tự động trở thành OWNER
  - Family settings initialization
  - Audit logging

#### **UC-006: Invite Family Member** ✅

- **File**: `family-management/UC-006-family-management-invite-family-member.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - Role-based permissions
  - Email invitation system
  - Family context validation
  - Invitation expiration

#### **UC-007: Accept Family Invitation** ✅

- **File**: `family-management/UC-007-family-management-accept-family-invitation.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - Invitation acceptance flow
  - Role assignment
  - Family member activation
  - Notification system

#### **UC-008: Manage Family Roles** ✅

- **File**: `family-management/UC-008-family-management-manage-family-roles.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - Role management
  - Permission updates
  - Role validation
  - Audit trail

### **💊 Medicine Management Module**

#### **UC-009: Add Medicine Type** ✅

- **File**: `medicine-management/UC-009-medicine-management-add-medicine-type.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - Medicine type creation
  - Category management
  - Role-based permissions
  - Family context validation

#### **UC-010: Add Medicine to Inventory** ✅

- **File**: `medicine-management/UC-010-medicine-management-add-medicine-to-inventory.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - Inventory management
  - Expiry date tracking
  - Batch number support
  - Transaction logging

#### **UC-011: Update Medicine Quantity** ✅

- **File**: `medicine-management/UC-011-medicine-management-update-medicine-quantity.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - Flexible action types (ADD, REMOVE, SET)
  - Quantity validation
  - Transaction tracking
  - Low stock monitoring

#### **UC-012: Remove Expired Medicine** ✅

- **File**: `medicine-management/UC-012-medicine-management-remove-expired-medicine.md`
- **Status**: ✅ **HOÀN THÀNH**
- **Key Features**:
  - Safety-focused disposal tracking
  - Soft delete protection
  - Disposal method options
  - Inventory cleanup

---

## 🆕 Technical Architecture

### **Anonymous Session Management:**

```java
// Anonymous session validation
public boolean isValidAnonymousSession(String anonymousUserId) {
    // Check if session exists
    // Check if not migrated
    // Check if not expired (30 days)
    // Update last activity
}
```

### **Data Migration Strategy:**

```sql
-- Migration process
UPDATE medicines
SET user_type = 'REGISTERED',
    user_id = ?,
    anonymous_user_id = NULL
WHERE anonymous_user_id = ?;

UPDATE prescriptions
SET user_type = 'REGISTERED',
    patient_id = ?,
    anonymous_user_id = NULL
WHERE anonymous_user_id = ?;
```

### **Feature Gating:**

```java
@Component
public class FeatureAccessService {

    public boolean canCreateFamily(UserType userType) {
        return userType != ANONYMOUS;
    }

    public boolean canSyncData(UserType userType) {
        return userType != ANONYMOUS;
    }

    public boolean canExportData(UserType userType) {
        return userType != ANONYMOUS;
    }
}
```

---

## 📈 Quality Metrics

### **Documentation Quality**

- **Completeness**: 100% cho mỗi user case
- **Accuracy**: Validated với database schema
- **Clarity**: Clear và understandable
- **Consistency**: Standardized format
- **🆕 Anonymous Support**: Comprehensive anonymous user coverage

### **Technical Quality**

- **API Design**: RESTful, consistent patterns
- **Database Design**: Normalized, optimized
- **Security**: Enterprise-level security
- **Performance**: Optimized cho production
- **🆕 Migration Support**: Seamless data migration

### **Business Value**

- **User Experience**: Intuitive flows
- **Scalability**: Designed for growth
- **Maintainability**: Easy to understand và modify
- **Compliance**: Audit trail, data protection
- **🆕 Lower Barrier to Entry**: Anonymous users can start immediately

---

## 🚀 Implementation Priority

### **Phase 1: Anonymous Support (Tuần 1-2)**

1. **Anonymous Session Management**
2. **Anonymous APIs**
3. **Data Migration System**
4. **Feature Gating**

### **Phase 2: Core Features (Tuần 3-4)**

1. **Authentication với Migration**
2. **Medicine Management (Both Modes)**
3. **Prescription Management (Both Modes)**

### **Phase 3: Family Features (Tuần 5-6)**

1. **Family Management (Registered Only)**
2. **Multi-user Support**
3. **Advanced Features**

---

## 📞 Contact & Support

**Documentation Lead**: <EMAIL>  
**Technical Review**: <EMAIL>  
**Business Validation**: <EMAIL>

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 2.0  
**Status**: Updated with Authentication Optional Pattern
