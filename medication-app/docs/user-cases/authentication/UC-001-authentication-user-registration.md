# UC-001: User Registration

## 📋 Overview

### **UC-ID**: UC-001

### **Title**: User Registration

### **Actor**: Anonymous User hoặc New User

### **Precondition**: User chưa có tài khoản trong hệ thống

### **Postcondition**: User có tài khoản mới và có thể đăng nhập

### **Priority**: High

### **Business Value**: Cho phép người dùng tạo tài khoản để sử dụng ứng dụng quản lý tủ thuốc gia đình

**🆕 NEW: Hỗ trợ data migration từ anonymous user**

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Registration Screen
- **User Actions**:
  1. Nh<PERSON>p email address
  2. Nhập password
  3. Nhập confirm password
  4. Nhập full name
  5. Chọn date of birth
  6. Chọn gender (optional)
  7. Nh<PERSON><PERSON> phone number (optional)
  8. **🆕 Chọn có migrate anonymous data hay không (nếu có anonymous data)**
  9. Click "Register" button
- **UI Elements**:
  - Email input field
  - Password input field
  - Confirm password input field
  - Full name input field
  - Date picker for birth date
  - Gender dropdown
  - Phone number input field
  - **🆕 Migration option checkbox**
  - Register button
  - "Already have account? Login" link
- **Validation**:
  - Email format validation
  - Password strength validation (min 8 chars, 1 uppercase, 1 lowercase, 1 number)
  - Password confirmation match
  - Required fields validation
  - Phone number format validation

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/auth/register`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  🆕 X-Anonymous-User-Id: {anonymous_user_id} (optional)
  ```
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "SecurePass123",
    "fullName": "Nguyễn Văn A",
    "dateOfBirth": "1990-01-01",
    "gender": "MALE",
    "phoneNumber": "+***********",
    🆕 "migrateAnonymousData": true
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `AuthController.register()`
- **Service Layer**: `AuthService.registerUser()`
- **🆕 Migration Service**: `AnonymousUserService.migrateData()`
- **Validation**:
  - Email uniqueness check
  - Password strength validation
  - Required fields validation
  - Email format validation
  - **🆕 Anonymous user ID validation (nếu có)**
- **Business Rules**:
  - Email phải unique trong hệ thống
  - Password phải đủ mạnh
  - User được tạo với status ACTIVE
  - Tự động tạo user profile
  - **🆕 Nếu có anonymous data và user chọn migrate: migrate tất cả data**

### **Step 4: Database Operations**

- **Repository**: `UserRepository`, `UserProfileRepository`
- **🆕 Migration Repository**: `AnonymousSessionRepository`, `MedicineRepository`, `PrescriptionRepository`
- **Queries**:

  ```sql
  -- Check email uniqueness
  SELECT COUNT(*) FROM users WHERE email = ? AND deleted_at IS NULL;

  -- Insert new user
  INSERT INTO users (email, password_hash, status, user_type, created_at, updated_at)
  VALUES (?, ?, 'ACTIVE', 'REGISTERED', NOW(), NOW());

  -- Insert user profile
  INSERT INTO user_profiles (user_id, full_name, date_of_birth, gender, phone_number, created_at, updated_at)
  VALUES (?, ?, ?, ?, ?, NOW(), NOW());

  🆕 -- Migrate anonymous data (nếu có)
  UPDATE medicines
  SET user_type = 'REGISTERED', user_id = ?, anonymous_user_id = NULL
  WHERE anonymous_user_id = ?;

  UPDATE prescriptions
  SET user_type = 'REGISTERED', patient_id = ?, anonymous_user_id = NULL
  WHERE anonymous_user_id = ?;

  UPDATE anonymous_sessions
  SET migrated_to_user_id = ?, migrated_at = NOW()
  WHERE anonymous_user_id = ?;
  ```

- **Tables**: `users`, `user_profiles`, **🆕 `medicines`, `prescriptions`, `anonymous_sessions`**
- **Constraints**:
  - Email unique constraint
  - User profile required cho mỗi user
  - **🆕 Migration constraints**

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "User registered successfully",
    "data": {
      "userId": 123,
      "email": "<EMAIL>",
      "fullName": "Nguyễn Văn A",
      "status": "ACTIVE",
      🆕 "dataMigrated": true,
      🆕 "migratedItems": {
        "medicines": 5,
        "prescriptions": 2
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Hiển thị success message
  - **🆕 Hiển thị migration status**
  - Redirect đến login screen
- **Navigation**: Chuyển đến Login Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/auth/register
Content-Type: application/json
Accept: application/json
🆕 X-Anonymous-User-Id: {anonymous_user_id} (optional)

{
  "email": "string (required, valid email format)",
  "password": "string (required, min 8 chars, 1 uppercase, 1 lowercase, 1 number)",
  "fullName": "string (required, max 100 chars)",
  "dateOfBirth": "date (required, format: YYYY-MM-DD)",
  "gender": "enum (optional: MALE, FEMALE, OTHER)",
  "phoneNumber": "string (optional, valid phone format)",
  🆕 "migrateAnonymousData": "boolean (optional, default: true)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "userId": "number",
    "email": "string",
    "fullName": "string",
    "status": "string",
    🆕 "dataMigrated": "boolean",
    🆕 "migratedItems": {
      "medicines": "number",
      "prescriptions": "number"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Insert new user
INSERT INTO users (email, password_hash, status, user_type, created_at, updated_at)
VALUES (?, ?, 'ACTIVE', 'REGISTERED', NOW(), NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Check email uniqueness
SELECT COUNT(*) FROM users WHERE email = ? AND deleted_at IS NULL;

-- Insert user profile
INSERT INTO user_profiles (user_id, full_name, date_of_birth, gender, phone_number, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, NOW(), NOW());

🆕 -- Check if anonymous data exists
SELECT COUNT(*) FROM medicines WHERE anonymous_user_id = ?;
SELECT COUNT(*) FROM prescriptions WHERE anonymous_user_id = ?;

🆕 -- Migrate anonymous data
UPDATE medicines
SET user_type = 'REGISTERED', user_id = ?, anonymous_user_id = NULL
WHERE anonymous_user_id = ?;

UPDATE prescriptions
SET user_type = 'REGISTERED', patient_id = ?, anonymous_user_id = NULL
WHERE anonymous_user_id = ?;

UPDATE anonymous_sessions
SET migrated_to_user_id = ?, migrated_at = NOW()
WHERE anonymous_user_id = ?;
```

#### **Indexes Used**

- `users_email_idx`: Unique index on email for fast lookup
- `users_status_idx`: Index on status for filtering
- `user_profiles_user_id_idx`: Index on user_id for foreign key
- **🆕 `medicines_anonymous_user_id_idx`: Index on anonymous_user_id for migration**
- **🆕 `prescriptions_anonymous_user_id_idx`: Index on anonymous_user_id for migration**

### **Error Handling**

#### **Error Codes**

| Code       | Message               | Description                         |
| ---------- | --------------------- | ----------------------------------- |
| 400        | Bad Request           | Invalid input data                  |
| 409        | Conflict              | Email already exists                |
| 422        | Unprocessable Entity  | Validation failed                   |
| 500        | Internal Server Error | Database error                      |
| **🆕 409** | **Migration Failed**  | **Anonymous data migration failed** |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "EMAIL_EXISTS",
    "message": "Email already registered",
    "details": "Please use a different email or try logging in"
  }
}

🆕 {
  "success": false,
  "error": {
    "code": "MIGRATION_FAILED",
    "message": "Failed to migrate anonymous data",
    "details": "Your account was created but data migration failed. Please contact support."
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Light (2 queries max)
- **Caching**: No caching required
- **Optimization**: Email uniqueness check uses index
- **🆕 Migration Performance**: Batch processing for large data migration

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User chưa có tài khoản
2. **Steps**:
   - Nhập valid email: <EMAIL>
   - Nhập strong password: SecurePass123
   - Nhập matching confirm password
   - Nhập full name: Nguyễn Văn A
   - Chọn date of birth: 1990-01-01
   - Click Register
3. **Expected Result**:
   - Success message hiển thị
   - Redirect to login screen
   - User được tạo trong database

### **🆕 Anonymous Migration Path**

1. **Prerequisites**: User có anonymous data
2. **Steps**:
   - Nhập valid email: <EMAIL>
   - Nhập strong password: SecurePass123
   - Nhập matching confirm password
   - Nhập full name: Nguyễn Văn A
   - Chọn date of birth: 1990-01-01
   - **🆕 Check "Migrate my data" option**
   - Click Register
3. **Expected Result**:
   - Success message hiển thị với migration info
   - Anonymous data được migrate
   - Redirect to login screen
   - User được tạo trong database

### **Error Cases**

1. **Invalid Input**:
   - Email format không hợp lệ
   - Password quá yếu
   - Required fields bị bỏ trống
2. **Unauthorized Access**: Không áp dụng (public endpoint)
3. **Database Error**:
   - Database connection failed
   - Constraint violation
4. **Network Error**:
   - API timeout
   - Network unavailable
5. **🆕 Migration Error**:
   - Anonymous data migration failed
   - Anonymous user ID invalid
   - Migration timeout

### **Edge Cases**

1. **Boundary Values**:
   - Email với 254 characters (max length)
   - Password với 128 characters (max length)
   - Full name với 100 characters (max length)
2. **Concurrent Access**:
   - 2 users cùng register với email giống nhau
3. **Large Data**:
   - Special characters trong name
   - Unicode characters
4. **Special Characters**:
   - Email với dots, plus signs
   - Name với accents, special chars
5. **🆕 Migration Edge Cases**:
   - Large amount of anonymous data
   - Concurrent migration attempts
   - Partial migration failure

### **Performance Tests**

1. **Load Testing**: 100 concurrent registrations
2. **Stress Testing**: 1000 concurrent registrations
3. **Endurance Testing**: Continuous registration for 1 hour
4. **🆕 Migration Testing**: Large data migration performance

## 🔐 Security Considerations

### **Authentication**

- Endpoint không yêu cầu authentication
- Public registration endpoint

### **Authorization**

- Không áp dụng authorization cho registration

### **Data Validation**

- Email format validation
- Password strength validation
- Input sanitization
- SQL injection prevention
- **🆕 Anonymous user ID validation**

### **Data Protection**

- Password được hash bằng BCrypt
- Email được normalize
- Personal data được encrypt
- **🆕 Anonymous data isolation during migration**

## 📊 Business Rules

### **Validation Rules**

1. Email phải unique trong hệ thống
2. Password phải có ít nhất 8 ký tự, 1 chữ hoa, 1 chữ thường, 1 số
3. Full name không được để trống
4. Date of birth phải hợp lệ và user phải ít nhất 13 tuổi
5. Phone number phải đúng format (nếu có)
6. **🆕 Anonymous user ID phải hợp lệ UUID format (nếu có)**

### **Business Logic**

1. User được tạo với status ACTIVE
2. Tự động tạo user profile
3. Email verification không bắt buộc (có thể thêm sau)
4. Welcome email được gửi (optional)
5. **🆕 Nếu có anonymous data và user chọn migrate: migrate tất cả data**
6. **🆕 Nếu migration thất bại: tạo user nhưng báo lỗi migration**

### **Constraints**

1. Email unique constraint
2. User profile required cho mỗi user
3. Date of birth không được trong tương lai
4. Phone number format validation
5. **🆕 Anonymous data migration atomic operation**

## 🔗 Related User Cases

### **Prerequisites**

- Không có prerequisites

### **Dependencies**

- UC-002: User Login (sau khi register thành công)

### **Related Features**

- Email Verification: Optional feature cho tương lai
- Welcome Email: Gửi email chào mừng
- Terms & Conditions: Accept terms before registration
- **🆕 Anonymous User Management: UC-025 (Anonymous Session Management)**

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng BCrypt để hash password
- Email được normalize trước khi lưu
- Tạo audit log cho registration
- Implement rate limiting để prevent spam
- **🆕 Implement atomic migration để đảm bảo data consistency**
- **🆕 Handle migration failures gracefully**

### **Future Enhancements**

- Email verification required
- Social login integration (Google, Facebook)
- Phone number verification
- CAPTCHA integration
- **🆕 Advanced migration options (selective migration)**
- **🆕 Migration rollback capability**

### **Known Limitations**

- Không có email verification
- Không có phone verification
- Không có social login
- Không có CAPTCHA protection
- **🆕 Migration chỉ support medicines và prescriptions**
- **🆕 Không có migration rollback**

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 2.0  
**Status**: Updated with Anonymous Data Migration Support
