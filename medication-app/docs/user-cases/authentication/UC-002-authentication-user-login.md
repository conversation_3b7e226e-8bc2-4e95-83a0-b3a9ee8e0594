# UC-002: User Login

## 📋 Overview

### **UC-ID**: UC-002

### **Title**: User Login

### **Actor**: Registered User

### **Precondition**: User đã có tài khoản trong hệ thống

### **Postcondition**: User đăng nhập thành công và nhận được JWT token

### **Priority**: High

### **Business Value**: Cho phép người dùng truy cập vào ứng dụng và các tính năng quản lý tủ thuốc gia đình

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Login Screen
- **User Actions**:
  1. Nhập email address
  2. Nhập password
  3. Click "Login" button
  4. (Optional) Check "Remember me" checkbox
- **UI Elements**:
  - Email input field
  - Password input field
  - Login button
  - "Remember me" checkbox
  - "Forgot password?" link
  - "Don't have account? Register" link
- **Validation**:
  - Email format validation
  - Required fields validation
  - Password field not empty

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/auth/login`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  ```
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "SecurePass123",
    "rememberMe": true
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `AuthController.login()`
- **Service Layer**: `AuthService.authenticateUser()`
- **Validation**:
  - Email format validation
  - Required fields validation
- **Business Rules**:
  - Kiểm tra email tồn tại
  - Verify password hash
  - Kiểm tra user status (ACTIVE)
  - Generate JWT token
  - Update last login time

### **Step 4: Database Operations**

- **Repository**: `UserRepository`
- **Queries**:

  ```sql
  -- Find user by email
  SELECT u.id, u.email, u.password_hash, u.status, u.created_at,
         up.full_name, up.date_of_birth, up.gender
  FROM users u
  LEFT JOIN user_profiles up ON u.id = up.user_id
  WHERE u.email = ? AND u.deleted_at IS NULL;

  -- Update last login time
  UPDATE users
  SET last_login_at = NOW(), updated_at = NOW()
  WHERE id = ?;
  ```

- **Tables**: `users`, `user_profiles`
- **Constraints**:
  - Email unique constraint
  - User status check

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Login successful",
    "data": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 3600,
      "user": {
        "id": 123,
        "email": "<EMAIL>",
        "fullName": "Nguyễn Văn A",
        "status": "ACTIVE"
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Store JWT token locally
  - Update user state
  - Show welcome message
- **Navigation**: Chuyển đến Home/Dashboard Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/auth/login
Content-Type: application/json
Accept: application/json

{
  "email": "string (required, valid email format)",
  "password": "string (required)",
  "rememberMe": "boolean (optional, default: false)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "accessToken": "string (JWT token)",
    "refreshToken": "string (JWT refresh token)",
    "expiresIn": "number (seconds)",
    "user": {
      "id": "number",
      "email": "string",
      "fullName": "string",
      "status": "string"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Find user by email with profile
SELECT u.id, u.email, u.password_hash, u.status, u.created_at,
       up.full_name, up.date_of_birth, up.gender
FROM users u
LEFT JOIN user_profiles up ON u.id = up.user_id
WHERE u.email = ? AND u.deleted_at IS NULL;
```

#### **Related Queries**

```sql
-- Update last login time
UPDATE users
SET last_login_at = NOW(), updated_at = NOW()
WHERE id = ?;

-- Get user families (for dashboard)
SELECT f.id, f.name, fm.role
FROM families f
JOIN family_members fm ON f.id = fm.family_id
WHERE fm.user_id = ? AND fm.status = 'ACTIVE';
```

#### **Indexes Used**

- `users_email_idx`: Unique index on email for fast lookup
- `users_status_idx`: Index on status for filtering
- `family_members_user_id_idx`: Index on user_id for family lookup

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description         |
| ---- | --------------------- | ------------------- |
| 400  | Bad Request           | Invalid input data  |
| 401  | Unauthorized          | Invalid credentials |
| 403  | Forbidden             | Account disabled    |
| 404  | Not Found             | User not found      |
| 500  | Internal Server Error | Database error      |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Invalid email or password",
    "details": "Please check your credentials and try again"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 1 second
- **Database Load**: Light (2 queries max)
- **Caching**: JWT token caching
- **Optimization**: Email lookup uses unique index

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User đã có tài khoản
2. **Steps**:
   - Nhập valid email: <EMAIL>
   - Nhập correct password: SecurePass123
   - Click Login
3. **Expected Result**:
   - Success response với JWT token
   - Redirect to dashboard
   - User state updated

### **Error Cases**

1. **Invalid Input**:
   - Email format không hợp lệ
   - Password bị bỏ trống
2. **Unauthorized Access**:
   - Email không tồn tại
   - Password sai
   - Account bị disabled
3. **Database Error**:
   - Database connection failed
   - User record corrupted
4. **Network Error**:
   - API timeout
   - Network unavailable

### **Edge Cases**

1. **Boundary Values**:
   - Email với 254 characters
   - Password với 128 characters
2. **Concurrent Access**:
   - Multiple login attempts
   - Token refresh conflicts
3. **Large Data**:
   - User với nhiều families
   - Complex user profile
4. **Special Characters**:
   - Email với dots, plus signs
   - Password với special chars

### **Performance Tests**

1. **Load Testing**: 100 concurrent logins
2. **Stress Testing**: 1000 concurrent logins
3. **Endurance Testing**: Continuous login for 1 hour

## 🔐 Security Considerations

### **Authentication**

- Password verification với BCrypt
- JWT token generation
- Session management

### **Authorization**

- Token-based authorization
- Role-based access control
- Permission validation

### **Data Validation**

- Email format validation
- Password verification
- Input sanitization
- SQL injection prevention

### **Data Protection**

- JWT token encryption
- Secure password comparison
- HTTPS communication
- Token expiration

## 📊 Business Rules

### **Validation Rules**

1. Email phải tồn tại trong hệ thống
2. Password phải đúng với user
3. User status phải là ACTIVE
4. Account không bị locked

### **Business Logic**

1. Generate access token (1 hour expiry)
2. Generate refresh token (7 days expiry)
3. Update last login timestamp
4. Load user families for dashboard
5. Create audit log cho login

### **Constraints**

1. User phải có status ACTIVE
2. Password hash verification
3. Token expiration handling
4. Rate limiting cho login attempts

## 🔗 Related User Cases

### **Prerequisites**

- UC-001: User Registration (user phải có tài khoản)

### **Dependencies**

- UC-004: JWT Token Refresh (khi token hết hạn)
- UC-021: View Family Dashboard (sau khi login)

### **Related Features**

- Remember Me: Extended session
- Forgot Password: Password reset
- Multi-factor Authentication: Enhanced security
- Session Management: Active sessions

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng BCrypt để verify password
- JWT token chứa user ID và roles
- Implement rate limiting (5 attempts per 15 minutes)
- Store refresh token securely
- Log failed login attempts

### **Future Enhancements**

- Multi-factor authentication (SMS/Email)
- Social login integration
- Biometric authentication
- Single sign-on (SSO)

### **Known Limitations**

- Không có multi-factor authentication
- Không có social login
- Không có biometric auth
- Session không persistent across devices

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
