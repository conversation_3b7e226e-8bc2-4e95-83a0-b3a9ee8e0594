# UC-003: Password Reset

## 📋 Overview

### **UC-ID**: UC-003

### **Title**: Password Reset

### **Actor**: Registered User (forgot password)

### **Precondition**: User đã có tài khoản nhưng quên mật khẩu

### **Postcondition**: User có thể đặt lại mật khẩu và đăng nhập thành công

### **Priority**: Medium

### **Business Value**: Cho phép người dùng khôi phục quyền truy cập vào tài khoản khi quên mật khẩu

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Forgot Password Screen
- **User Actions**:
  1. Click "Forgot Password?" link từ login screen
  2. Nhập email address
  3. Click "Send Reset Link" button
  4. Nhận email với reset link
  5. Click link trong email
  6. <PERSON><PERSON><PERSON><PERSON> new password
  7. <PERSON><PERSON><PERSON><PERSON> confirm new password
  8. Click "Reset Password" button
- **UI Elements**:
  - Email input field
  - Send Reset Link button
  - Password input field
  - Confirm password input field
  - Reset Password button
  - Back to Login link
  - Form validation messages
- **Validation**:
  - Email format validation
  - Email exists in system
  - Password strength validation
  - Password confirmation match

### **Step 2: API Call Details**

#### **Step 2a: Request Password Reset**

- **Endpoint**: `POST /api/auth/forgot-password`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  ```
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```

#### **Step 2b: Reset Password**

- **Endpoint**: `POST /api/auth/reset-password`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  ```
- **Request Body**:
  ```json
  {
    "token": "reset_token_from_email",
    "newPassword": "NewSecurePass123",
    "confirmPassword": "NewSecurePass123"
  }
  ```

### **Step 3: Backend Processing**

#### **Step 3a: Request Password Reset**

- **Controller**: `AuthController.forgotPassword()`
- **Service Layer**: `AuthService.requestPasswordReset()`
- **Validation**:
  - Email format validation
  - Email exists check
  - Rate limiting check
- **Business Rules**:
  - Email phải tồn tại trong hệ thống
  - Rate limiting: 3 requests per hour per email
  - Generate secure reset token
  - Send reset email

#### **Step 3b: Reset Password**

- **Controller**: `AuthController.resetPassword()`
- **Service Layer**: `AuthService.resetPassword()`
- **Validation**:
  - Token validation
  - Token expiration check
  - Password strength validation
- **Business Rules**:
  - Token phải hợp lệ và chưa hết hạn
  - Password phải đủ mạnh
  - Update password hash
  - Invalidate all existing sessions

### **Step 4: Database Operations**

#### **Step 4a: Request Password Reset**

- **Repository**: `UserRepository`, `PasswordResetRepository`
- **Queries**:

  ```sql
  -- Check if user exists
  SELECT id, email, status FROM users
  WHERE email = ? AND deleted_at IS NULL;

  -- Check rate limiting
  SELECT COUNT(*) FROM password_resets
  WHERE email = ? AND created_at > NOW() - INTERVAL '1 hour';

  -- Insert password reset record
  INSERT INTO password_resets (email, token, expires_at, created_at)
  VALUES (?, ?, NOW() + INTERVAL '1 hour', NOW());
  ```

#### **Step 4b: Reset Password**

- **Repository**: `UserRepository`, `PasswordResetRepository`
- **Queries**:

  ```sql
  -- Validate reset token
  SELECT email, expires_at FROM password_resets
  WHERE token = ? AND used = false AND expires_at > NOW();

  -- Update user password
  UPDATE users
  SET password_hash = ?, updated_at = NOW()
  WHERE email = ?;

  -- Mark token as used
  UPDATE password_resets
  SET used = true, used_at = NOW()
  WHERE token = ?;

  -- Invalidate all user sessions
  UPDATE user_sessions
  SET invalidated_at = NOW()
  WHERE user_id = (SELECT id FROM users WHERE email = ?);
  ```

### **Step 5: Response Handling**

#### **Step 5a: Request Password Reset**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Password reset link sent to your email",
    "data": {
      "email": "<EMAIL>"
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Disable form temporarily
  - Show countdown timer

#### **Step 5b: Reset Password**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Password reset successfully",
    "data": {
      "userId": 123,
      "email": "<EMAIL>"
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Redirect to login screen
- **Navigation**: Chuyển đến Login Screen

## 🔧 Technical Details

### **API Specification**

#### **Request Password Reset**

```http
POST /api/auth/forgot-password
Content-Type: application/json
Accept: application/json

{
  "email": "string (required, valid email format)"
}
```

#### **Reset Password**

```http
POST /api/auth/reset-password
Content-Type: application/json
Accept: application/json

{
  "token": "string (required, reset token from email)",
  "newPassword": "string (required, min 8 chars, 1 uppercase, 1 lowercase, 1 number)",
  "confirmPassword": "string (required, must match newPassword)"
}
```

### **Database Operations**

#### **Primary Queries**

```sql
-- Request password reset
INSERT INTO password_resets (email, token, expires_at, created_at)
VALUES (?, ?, NOW() + INTERVAL '1 hour', NOW());

-- Reset password
UPDATE users
SET password_hash = ?, updated_at = NOW()
WHERE email = ?;
```

#### **Related Queries**

```sql
-- Check rate limiting
SELECT COUNT(*) FROM password_resets
WHERE email = ? AND created_at > NOW() - INTERVAL '1 hour';

-- Validate token
SELECT email, expires_at FROM password_resets
WHERE token = ? AND used = false AND expires_at > NOW();

-- Invalidate sessions
UPDATE user_sessions
SET invalidated_at = NOW()
WHERE user_id = (SELECT id FROM users WHERE email = ?);
```

#### **Indexes Used**

- `password_resets_email_idx`: Index on email for rate limiting
- `password_resets_token_idx`: Unique index on token for validation
- `password_resets_expires_at_idx`: Index on expires_at for cleanup
- `users_email_idx`: Index on email for user lookup

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                   |
| ---- | --------------------- | ----------------------------- |
| 400  | Bad Request           | Invalid input data            |
| 404  | Not Found             | Email not found               |
| 429  | Too Many Requests     | Rate limit exceeded           |
| 410  | Gone                  | Token expired or already used |
| 422  | Unprocessable Entity  | Validation failed             |
| 500  | Internal Server Error | Database error                |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many reset requests",
    "details": "Please wait 1 hour before requesting another reset"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Light (2-3 queries)
- **Caching**: No caching required
- **Optimization**: Indexes on email and token

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có tài khoản
2. **Steps**:
   - Nhập valid email
   - Click Send Reset Link
   - Check email và click link
   - Nhập new password
   - Click Reset Password
3. **Expected Result**:
   - Reset email được gửi
   - Password được reset thành công
   - Redirect to login

### **Error Cases**

1. **Invalid Input**:
   - Email format không hợp lệ
   - Email không tồn tại
   - Password quá yếu
2. **Rate Limiting**:
   - Quá nhiều requests trong 1 giờ
3. **Token Issues**:
   - Token hết hạn
   - Token đã được sử dụng
   - Token không hợp lệ
4. **Network Error**:
   - Email service unavailable
   - API timeout

### **Edge Cases**

1. **Boundary Values**:
   - Email với 254 characters
   - Password với 128 characters
2. **Concurrent Access**:
   - Multiple reset requests
   - Token conflicts
3. **Time-based**:
   - Token expiration
   - Rate limiting windows
4. **Special Characters**:
   - Email với special chars
   - Password với special chars

### **Performance Tests**

1. **Load Testing**: 100 concurrent reset requests
2. **Stress Testing**: 500 concurrent reset requests
3. **Endurance Testing**: Continuous reset requests for 1 hour

## 🔐 Security Considerations

### **Authentication**

- Token-based reset mechanism
- Secure token generation
- Token expiration handling

### **Authorization**

- No authentication required for reset request
- Token validation for password reset

### **Data Validation**

- Email format validation
- Password strength validation
- Token format validation

### **Data Protection**

- Secure token generation (cryptographically random)
- Token expiration (1 hour)
- Rate limiting protection
- Session invalidation

## 📊 Business Rules

### **Validation Rules**

1. Email phải tồn tại trong hệ thống
2. Rate limiting: 3 requests per hour per email
3. Token expiration: 1 hour
4. Password phải đủ mạnh (8+ chars, uppercase, lowercase, number)
5. Token chỉ được sử dụng 1 lần

### **Business Logic**

1. Generate secure reset token
2. Send reset email với link
3. Validate token khi reset
4. Update password hash
5. Invalidate all user sessions
6. Create audit log

### **Constraints**

1. Token unique và secure
2. Token expiration enforced
3. Rate limiting enforced
4. Password strength requirements
5. Session invalidation required

## 🔗 Related User Cases

### **Prerequisites**

- UC-001: User Registration (user phải có tài khoản)

### **Dependencies**

- UC-002: User Login (sau khi reset password)

### **Related Features**

- Email Service: Send reset emails
- Rate Limiting: Prevent abuse
- Session Management: Invalidate sessions
- Audit Logging: Track reset attempts

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng cryptographically secure random token
- Implement rate limiting để prevent abuse
- Send email với secure reset link
- Invalidate all user sessions sau khi reset
- Create audit log cho security tracking

### **Future Enhancements**

- SMS verification thay vì email
- Multi-factor authentication
- Security questions
- Account recovery options

### **Known Limitations**

- Phụ thuộc vào email service
- Không có SMS verification
- Không có security questions
- Không có account recovery

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
