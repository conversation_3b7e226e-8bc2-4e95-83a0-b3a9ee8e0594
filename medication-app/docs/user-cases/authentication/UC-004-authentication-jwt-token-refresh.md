# UC-004: JWT Token Refresh

## 📋 Overview

### **UC-ID**: UC-004

### **Title**: JWT Token Refresh

### **Actor**: Authenticated User (with expired access token)

### **Precondition**: User đã đăng nhập và có refresh token hợp lệ

### **Postcondition**: User nhận được access token mới và có thể tiếp tục sử dụng ứng dụng

### **Priority**: Medium

### **Business Value**: Đảm bảo user experience liền mạch bằng cách tự động làm mới token mà không cần đăng nhập lại

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Automatic (Background Process)
- **User Actions**:
  1. User đang sử dụng ứng dụng
  2. Access token hết hạn (sau 1 giờ)
  3. App tự động gửi refresh request
  4. User không cần thao tác gì
- **UI Elements**:
  - Loading indicator (optional)
  - Seamless experience
  - No user interaction required
- **Validation**:
  - Refresh token validation
  - Token expiration check
  - User session validation

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/auth/refresh`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [REFRESH_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `AuthController.refreshToken()`
- **Service Layer**: `AuthService.refreshAccessToken()`
- **Validation**:
  - Refresh token format validation
  - Token signature verification
  - Token expiration check
  - User status validation
- **Business Rules**:
  - Refresh token phải hợp lệ và chưa hết hạn
  - User phải có status ACTIVE
  - Generate new access token (1 hour expiry)
  - Generate new refresh token (7 days expiry)
  - Invalidate old refresh token

### **Step 4: Database Operations**

- **Repository**: `UserRepository`, `UserSessionRepository`
- **Queries**:

  ```sql
  -- Validate user status
  SELECT id, email, status FROM users
  WHERE id = ? AND deleted_at IS NULL;

  -- Check if refresh token is blacklisted
  SELECT COUNT(*) FROM token_blacklist
  WHERE token = ? AND blacklisted_at IS NOT NULL;

  -- Update user session
  UPDATE user_sessions
  SET last_refresh_at = NOW(), updated_at = NOW()
  WHERE user_id = ? AND refresh_token = ?;

  -- Blacklist old refresh token
  INSERT INTO token_blacklist (token, user_id, blacklisted_at, reason)
  VALUES (?, ?, NOW(), 'REFRESHED');
  ```

- **Tables**: `users`, `user_sessions`, `token_blacklist`
- **Constraints**:
  - User status check
  - Token blacklist validation
  - Session tracking

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Token refreshed successfully",
    "data": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 3600,
      "refreshExpiresIn": 604800,
      "user": {
        "id": 123,
        "email": "<EMAIL>",
        "fullName": "Nguyễn Văn A"
      }
    }
  }
  ```
- **Error Response**: Redirect to login screen
- **UI Update**:
  - Update stored tokens
  - Continue user session
  - No visible interruption
- **Navigation**: Stay on current screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/auth/refresh
Content-Type: application/json
Accept: application/json
Authorization: Bearer [REFRESH_TOKEN]

{
  "refreshToken": "string (required, valid JWT refresh token)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "accessToken": "string (JWT access token)",
    "refreshToken": "string (JWT refresh token)",
    "expiresIn": "number (seconds)",
    "refreshExpiresIn": "number (seconds)",
    "user": {
      "id": "number",
      "email": "string",
      "fullName": "string"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Update user session
UPDATE user_sessions
SET last_refresh_at = NOW(), updated_at = NOW()
WHERE user_id = ? AND refresh_token = ?;
```

#### **Related Queries**

```sql
-- Validate user status
SELECT id, email, status FROM users
WHERE id = ? AND deleted_at IS NULL;

-- Check token blacklist
SELECT COUNT(*) FROM token_blacklist
WHERE token = ? AND blacklisted_at IS NOT NULL;

-- Blacklist old token
INSERT INTO token_blacklist (token, user_id, blacklisted_at, reason)
VALUES (?, ?, NOW(), 'REFRESHED');
```

#### **Indexes Used**

- `user_sessions_user_id_idx`: Index on user_id for session lookup
- `user_sessions_refresh_token_idx`: Index on refresh_token for validation
- `token_blacklist_token_idx`: Index on token for blacklist check
- `users_id_idx`: Index on id for user validation

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description              |
| ---- | --------------------- | ------------------------ |
| 400  | Bad Request           | Invalid refresh token    |
| 401  | Unauthorized          | Token expired or invalid |
| 403  | Forbidden             | User account disabled    |
| 404  | Not Found             | User not found           |
| 500  | Internal Server Error | Database error           |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "REFRESH_TOKEN_EXPIRED",
    "message": "Refresh token has expired",
    "details": "Please login again to continue"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 500ms
- **Database Load**: Light (3 queries max)
- **Caching**: Token validation caching
- **Optimization**: Indexes on token fields

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có valid refresh token
2. **Steps**:
   - Access token expires
   - App automatically sends refresh request
   - Backend validates refresh token
   - New tokens generated
3. **Expected Result**:
   - New access token received
   - User session continues seamlessly
   - No user interaction required

### **Error Cases**

1. **Invalid Token**:
   - Refresh token không hợp lệ
   - Token signature verification failed
2. **Expired Token**:
   - Refresh token đã hết hạn
   - Token blacklisted
3. **User Issues**:
   - User account disabled
   - User not found
4. **Network Error**:
   - API timeout
   - Network unavailable

### **Edge Cases**

1. **Boundary Values**:
   - Token với 1 second left
   - Token với maximum length
2. **Concurrent Access**:
   - Multiple refresh requests
   - Race conditions
3. **Security**:
   - Token reuse attempts
   - Token tampering
4. **Performance**:
   - High frequency refresh
   - Database load

### **Performance Tests**

1. **Load Testing**: 1000 concurrent refresh requests
2. **Stress Testing**: 5000 concurrent refresh requests
3. **Endurance Testing**: Continuous refresh for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token validation
- Token signature verification
- Token expiration check

### **Authorization**

- User status validation
- Session tracking
- Token blacklisting

### **Data Validation**

- Token format validation
- User existence check
- Session validation

### **Data Protection**

- Secure token generation
- Token rotation
- Session invalidation
- Blacklist management

## 📊 Business Rules

### **Validation Rules**

1. Refresh token phải hợp lệ và chưa hết hạn
2. User phải có status ACTIVE
3. Token không được blacklisted
4. Session phải tồn tại và active

### **Business Logic**

1. Generate new access token (1 hour expiry)
2. Generate new refresh token (7 days expiry)
3. Blacklist old refresh token
4. Update session tracking
5. Create audit log

### **Constraints**

1. Refresh token expiration (7 days)
2. Access token expiration (1 hour)
3. Token blacklist enforcement
4. Session tracking required

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập trước)

### **Dependencies**

- UC-002: User Login (fallback khi refresh fails)

### **Related Features**

- Session Management: Track user sessions
- Token Blacklist: Prevent token reuse
- Audit Logging: Track refresh attempts
- Auto-logout: Handle refresh failures

## 📝 Notes & Considerations

### **Implementation Notes**

- Implement automatic token refresh trong app
- Handle refresh failures gracefully
- Implement token blacklist để prevent reuse
- Create audit log cho security tracking
- Implement exponential backoff cho retry

### **Future Enhancements**

- Refresh token rotation
- Device-based token management
- Token analytics
- Advanced session management

### **Known Limitations**

- Refresh token có thể bị compromised
- Không có device tracking
- Không có token analytics
- Không có advanced session management

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
