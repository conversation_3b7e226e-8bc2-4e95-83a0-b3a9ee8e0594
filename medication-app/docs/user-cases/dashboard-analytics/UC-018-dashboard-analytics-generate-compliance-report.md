# UC-018: Generate Compliance Report

## 📋 Overview

### **UC-ID**: UC-018

### **Title**: Generate Compliance Report

### **Actor**: Family Member (ADMIN/OWNER role)

### **Precondition**: User đã đăng nhập, có family context, có medication schedules và compliance data

### **Postcondition**: Compliance report được tạo và có thể được exported hoặc shared

### **Priority**: Medium

### **Business Value**: Cung cấp báo cáo chi tiết về việc tuân thủ uống thuốc để đánh giá hiệu quả điều trị và chia sẻ với bác sĩ

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Dashboard / Analytics / Compliance Reports
- **User Actions**:
  1. Navigate to Dashboard Analytics
  2. Click "Generate Report" button
  3. Select report type (INDIVIDUAL, FAMILY, PRESCRIPTION)
  4. Choose date range (LAST_7_DAYS, LAST_30_DAYS, LAST_3_MONTHS, CUSTOM)
  5. Select family members (nếu family report)
  6. Select prescriptions (nếu prescription report)
  7. Choose report format (PDF, EXCEL, CSV)
  8. Add custom filters (optional)
  9. Click "Generate Report" button
  10. Wait for report generation
- **UI Elements**:
  - Report type selector
  - Date range picker
  - Member selector (family)
  - Prescription selector
  - Format dropdown
  - Custom filters panel
  - Generate Report button
  - Progress indicator
  - Report preview
- **Validation**:
  - Report type required
  - Date range required
  - Valid date range
  - Format selection required

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/{familyId}/reports/compliance`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "reportType": "FAMILY",
    "dateRange": "LAST_30_DAYS",
    "startDate": "2024-11-01",
    "endDate": "2024-11-30",
    "memberIds": [123, 456],
    "prescriptionIds": [789],
    "format": "PDF",
    "filters": {
      "minComplianceRate": 80,
      "includeMissedDoses": true,
      "includeNotes": true
    }
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `ComplianceReportController.generateReport()`
- **Service Layer**: `ComplianceReportService.generateReport()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Report parameters validation
- **Business Rules**:
  - User phải là family member với ADMIN/OWNER role
  - Valid date range
  - Sufficient data for report
  - Generate report data
  - Create report file
  - Store report metadata
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `ComplianceReportRepository`, `MedicationScheduleRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Get compliance data for date range
  SELECT
    ms.id as schedule_id,
    mt.name as medicine_name,
    mt.category as medicine_category,
    p.name as prescription_name,
    u.name as member_name,
    msi.scheduled_date,
    msi.scheduled_time,
    msi.status,
    msi.taken_at,
    msi.notes,
    ms.compliance_rate
  FROM medication_schedule_instances msi
  JOIN medication_schedules ms ON msi.schedule_id = ms.id
  JOIN prescription_medicines pm ON ms.prescription_medicine_id = pm.id
  JOIN medicine_types mt ON pm.medicine_type_id = mt.id
  JOIN prescriptions p ON pm.prescription_id = p.id
  JOIN users u ON ms.created_by = u.id
  WHERE ms.family_id = ?
    AND msi.scheduled_date BETWEEN ? AND ?
    AND msi.deleted_at IS NULL
  ORDER BY msi.scheduled_date, msi.scheduled_time;

  -- Get family compliance summary
  SELECT
    COUNT(DISTINCT ms.id) as total_schedules,
    AVG(ms.compliance_rate) as avg_compliance_rate,
    COUNT(CASE WHEN msi.status = 'TAKEN' THEN 1 END) as total_taken,
    COUNT(CASE WHEN msi.status = 'MISSED' THEN 1 END) as total_missed,
    COUNT(CASE WHEN msi.status = 'SKIPPED' THEN 1 END) as total_skipped
  FROM medication_schedules ms
  LEFT JOIN medication_schedule_instances msi ON ms.id = msi.schedule_id
  WHERE ms.family_id = ?
    AND ms.status = 'ACTIVE'
    AND (msi.scheduled_date BETWEEN ? AND ? OR msi.scheduled_date IS NULL);

  -- Create report record
  INSERT INTO compliance_reports (family_id, user_id, report_type, date_range, start_date, end_date, format, filters, file_path, status, created_at, updated_at)
  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'GENERATED', NOW(), NOW())
  RETURNING id;

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'GENERATE_COMPLIANCE_REPORT', 'COMPLIANCE_REPORT', ?, ?, NOW());
  ```

- **Tables**: `medication_schedule_instances`, `medication_schedules`, `prescription_medicines`, `medicine_types`, `prescriptions`, `users`, `compliance_reports`, `audit_logs`
- **Constraints**:
  - Valid date range
  - Sufficient data
  - Report generation
  - File storage

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Compliance report generated successfully",
    "data": {
      "report": {
        "id": 234,
        "reportType": "FAMILY",
        "dateRange": "LAST_30_DAYS",
        "startDate": "2024-11-01",
        "endDate": "2024-11-30",
        "format": "PDF",
        "filePath": "/reports/compliance_234.pdf",
        "fileSize": "2.5MB",
        "status": "GENERATED",
        "createdAt": "2024-12-01T10:00:00Z"
      },
      "summary": {
        "totalSchedules": 5,
        "avgComplianceRate": 85.7,
        "totalTaken": 128,
        "totalMissed": 12,
        "totalSkipped": 8,
        "periodDays": 30
      },
      "downloadUrl": "https://api.medication.com/reports/download/234",
      "nextSteps": {
        "downloadReport": true,
        "shareReport": true,
        "scheduleReport": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Display report summary
  - Show download link
  - Update report list
- **Navigation**: Redirect to Report Details Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/{familyId}/reports/compliance
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "reportType": "enum (required: INDIVIDUAL, FAMILY, PRESCRIPTION)",
  "dateRange": "enum (required: LAST_7_DAYS, LAST_30_DAYS, LAST_3_MONTHS, CUSTOM)",
  "startDate": "date (required if CUSTOM, format: YYYY-MM-DD)",
  "endDate": "date (required if CUSTOM, format: YYYY-MM-DD)",
  "memberIds": "array (optional, for FAMILY report)",
  "prescriptionIds": "array (optional, for PRESCRIPTION report)",
  "format": "enum (required: PDF, EXCEL, CSV)",
  "filters": "object (optional)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "report": {
      "id": "number",
      "reportType": "string",
      "dateRange": "string",
      "startDate": "string (YYYY-MM-DD)",
      "endDate": "string (YYYY-MM-DD)",
      "format": "string",
      "filePath": "string",
      "fileSize": "string",
      "status": "string (GENERATED)",
      "createdAt": "string (ISO 8601)"
    },
    "summary": {
      "totalSchedules": "number",
      "avgComplianceRate": "number (0-100)",
      "totalTaken": "number",
      "totalMissed": "number",
      "totalSkipped": "number",
      "periodDays": "number"
    },
    "downloadUrl": "string",
    "nextSteps": {
      "downloadReport": "boolean",
      "shareReport": "boolean",
      "scheduleReport": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Get compliance data for date range
SELECT
  ms.id as schedule_id,
  mt.name as medicine_name,
  mt.category as medicine_category,
  p.name as prescription_name,
  u.name as member_name,
  msi.scheduled_date,
  msi.scheduled_time,
  msi.status,
  msi.taken_at,
  msi.notes,
  ms.compliance_rate
FROM medication_schedule_instances msi
JOIN medication_schedules ms ON msi.schedule_id = ms.id
JOIN prescription_medicines pm ON ms.prescription_medicine_id = pm.id
JOIN medicine_types mt ON pm.medicine_type_id = mt.id
JOIN prescriptions p ON pm.prescription_id = p.id
JOIN users u ON ms.created_by = u.id
WHERE ms.family_id = ?
  AND msi.scheduled_date BETWEEN ? AND ?
  AND msi.deleted_at IS NULL
ORDER BY msi.scheduled_date, msi.scheduled_time;
```

#### **Related Queries**

```sql
-- Get family compliance summary
SELECT
  COUNT(DISTINCT ms.id) as total_schedules,
  AVG(ms.compliance_rate) as avg_compliance_rate,
  COUNT(CASE WHEN msi.status = 'TAKEN' THEN 1 END) as total_taken,
  COUNT(CASE WHEN msi.status = 'MISSED' THEN 1 END) as total_missed,
  COUNT(CASE WHEN msi.status = 'SKIPPED' THEN 1 END) as total_skipped
FROM medication_schedules ms
LEFT JOIN medication_schedule_instances msi ON ms.id = msi.schedule_id
WHERE ms.family_id = ?
  AND ms.status = 'ACTIVE'
  AND (msi.scheduled_date BETWEEN ? AND ? OR msi.scheduled_date IS NULL);

-- Create report record
INSERT INTO compliance_reports (family_id, user_id, report_type, date_range, start_date, end_date, format, filters, file_path, status, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'GENERATED', NOW(), NOW())
RETURNING id;
```

#### **Indexes Used**

- `medication_schedule_instances_date_idx`: Index on scheduled_date
- `medication_schedules_family_idx`: Index on family_id
- `medication_schedules_status_idx`: Index on status
- `compliance_reports_family_idx`: Index on family_id
- `compliance_reports_user_idx`: Index on user_id

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description               |
| ---- | --------------------- | ------------------------- |
| 400  | Bad Request           | Invalid report parameters |
| 401  | Unauthorized          | User not authenticated    |
| 403  | Forbidden             | Insufficient permissions  |
| 404  | Not Found             | Family not found          |
| 422  | Unprocessable Entity  | Validation failed         |
| 500  | Internal Server Error | Report generation failed  |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_DATA",
    "message": "Insufficient data for report generation",
    "details": "No compliance data found for the selected date range"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 10 seconds
- **Database Load**: Heavy (complex queries)
- **Caching**: Report data caching
- **Optimization**: Async report generation

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền ADMIN, có compliance data
2. **Steps**:
   - Report Type: FAMILY
   - Date Range: LAST_30_DAYS
   - Format: PDF
   - Click Generate Report
3. **Expected Result**:
   - Report được tạo thành công
   - Summary được hiển thị
   - Download link available

### **Error Cases**

1. **Invalid Input**:
   - Invalid date range
   - Invalid report type
   - Invalid format
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không có quyền ADMIN
3. **Business Logic**:
   - Không có data cho date range
   - Family không tồn tại
   - Report generation failed
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Date range 1 day
   - Date range 1 year
   - No compliance data
2. **Concurrent Access**:
   - 2 users cùng generate report
   - Race conditions
3. **Large Data**:
   - Large date range
   - Many schedules
   - High volume data
4. **Special Characters**:
   - Medicine names với Unicode
   - Notes với special chars

### **Performance Tests**

1. **Load Testing**: 10 concurrent report generations
2. **Stress Testing**: 30 concurrent report generations
3. **Endurance Testing**: Continuous generations for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member with ADMIN/OWNER role
- Role-based access control
- Family context validation

### **Data Validation**

- Date range validation
- Report parameters validation
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Report privacy protection
- Audit logging
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. User phải có quyền ADMIN/OWNER
2. Report type phải hợp lệ
3. Date range phải hợp lệ
4. Format phải được support
5. Sufficient data required

### **Business Logic**

1. Validate user permissions
2. Check data availability
3. Generate report data
4. Create report file
5. Store report metadata
6. Create audit log

### **Constraints**

1. Admin/OWNER role required
2. Valid date range
3. Sufficient data
4. Report generation
5. File storage

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-016: Track Medication Compliance (có compliance data)

### **Dependencies**

- UC-015: Schedule Medication (có schedules)
- UC-017: Set Medication Reminder (có reminders)

### **Related Features**

- Report Management: View, download reports
- Analytics Dashboard: View compliance statistics
- Data Export: Export data in various formats
- Report Scheduling: Schedule automatic reports

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng async processing cho report generation
- Implement file storage system
- Create comprehensive report templates
- Handle large data sets efficiently
- Implement report caching

### **Future Enhancements**

- Advanced analytics
- Interactive reports
- Real-time reporting
- Automated report scheduling

### **Known Limitations**

- Không có advanced analytics
- Không có interactive reports
- Không có real-time reporting
- Không có automated scheduling

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
