# UC-005: Create Family

## 📋 Overview

### **UC-ID**: UC-005

### **Title**: Create Family

### **Actor**: Authenticated User

### **Precondition**: User đã đăng nhập và chưa có family nào hoặc muốn tạo family mới

### **Postcondition**: Family mới được tạo và user trở thành owner của family đó

### **Priority**: High

### **Business Value**: Cho phép người dùng tạo gia đình mới để quản lý thuốc và đơn thuốc cho các thành viên gia đình

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Create Family Screen / Family Management Screen
- **User Actions**:
  1. Click "Create New Family" button
  2. Nhập family name
  3. Nhập family description (optional)
  4. Chọn family avatar/icon (optional)
  5. Click "Create Family" button
- **UI Elements**:
  - Family name input field
  - Family description textarea
  - Family avatar picker
  - Create Family button
  - Cancel button
  - Form validation messages
- **Validation**:
  - Family name required (3-50 characters)
  - Family name unique cho user
  - Description max 200 characters
  - Avatar size limit (2MB)

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "name": "Gia đình Nguyễn",
    "description": "Gia đình 4 người với 2 con nhỏ",
    "avatarUrl": "https://example.com/avatar.jpg"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `FamilyController.createFamily()`
- **Service Layer**: `FamilyService.createFamily()`
- **Validation**:
  - Family name validation
  - User authentication check
  - Family name uniqueness check
- **Business Rules**:
  - User phải authenticated
  - Family name unique cho user
  - User tự động trở thành OWNER
  - Tạo family member record cho user

### **Step 4: Database Operations**

- **Repository**: `FamilyRepository`, `FamilyMemberRepository`
- **Queries**:

  ```sql
  -- Check family name uniqueness for user
  SELECT COUNT(*) FROM families f
  JOIN family_members fm ON f.id = fm.family_id
  WHERE f.name = ? AND fm.user_id = ? AND f.deleted_at IS NULL;

  -- Insert new family
  INSERT INTO families (name, description, avatar_url, created_at, updated_at)
  VALUES (?, ?, ?, NOW(), NOW())
  RETURNING id;

  -- Insert family member (user as owner)
  INSERT INTO family_members (family_id, user_id, role, status, created_at, updated_at)
  VALUES (?, ?, 'OWNER', 'ACTIVE', NOW(), NOW());

  -- Create family settings
  INSERT INTO family_settings (family_id, notification_enabled, reminder_enabled, created_at, updated_at)
  VALUES (?, true, true, NOW(), NOW());
  ```

- **Tables**: `families`, `family_members`, `family_settings`
- **Constraints**:
  - Family name unique per user
  - Foreign key constraints
  - User can only be OWNER of one family

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Family created successfully",
    "data": {
      "family": {
        "id": 456,
        "name": "Gia đình Nguyễn",
        "description": "Gia đình 4 người với 2 con nhỏ",
        "avatarUrl": "https://example.com/avatar.jpg",
        "createdAt": "2024-12-01T10:00:00Z"
      },
      "member": {
        "id": 789,
        "role": "OWNER",
        "status": "ACTIVE"
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Update family list
  - Switch to family dashboard
- **Navigation**: Chuyển đến Family Dashboard

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "name": "string (required, 3-50 chars, unique per user)",
  "description": "string (optional, max 200 chars)",
  "avatarUrl": "string (optional, valid URL)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "family": {
      "id": "number",
      "name": "string",
      "description": "string",
      "avatarUrl": "string",
      "createdAt": "string (ISO 8601)",
      "updatedAt": "string (ISO 8601)"
    },
    "member": {
      "id": "number",
      "role": "string (OWNER)",
      "status": "string (ACTIVE)"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Insert new family
INSERT INTO families (name, description, avatar_url, created_at, updated_at)
VALUES (?, ?, ?, NOW(), NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Check family name uniqueness
SELECT COUNT(*) FROM families f
JOIN family_members fm ON f.id = fm.family_id
WHERE f.name = ? AND fm.user_id = ? AND f.deleted_at IS NULL;

-- Insert family member
INSERT INTO family_members (family_id, user_id, role, status, created_at, updated_at)
VALUES (?, ?, 'OWNER', 'ACTIVE', NOW(), NOW());

-- Create family settings
INSERT INTO family_settings (family_id, notification_enabled, reminder_enabled, created_at, updated_at)
VALUES (?, true, true, NOW(), NOW());
```

#### **Indexes Used**

- `families_name_idx`: Index on name for uniqueness check
- `family_members_family_id_idx`: Index on family_id for fast lookup
- `family_members_user_id_idx`: Index on user_id for user's families
- `family_members_role_idx`: Index on role for role-based queries

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                |
| ---- | --------------------- | -------------------------- |
| 400  | Bad Request           | Invalid input data         |
| 401  | Unauthorized          | User not authenticated     |
| 409  | Conflict              | Family name already exists |
| 422  | Unprocessable Entity  | Validation failed          |
| 500  | Internal Server Error | Database error             |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "FAMILY_NAME_EXISTS",
    "message": "Family name already exists",
    "details": "Please choose a different name for your family"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (4 queries)
- **Caching**: Family list caching
- **Optimization**: Indexes on family name and user relationships

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User đã đăng nhập
2. **Steps**:
   - Nhập family name: "Gia đình Nguyễn"
   - Nhập description: "Gia đình 4 người với 2 con nhỏ"
   - Click Create Family
3. **Expected Result**:
   - Family được tạo thành công
   - User trở thành OWNER
   - Redirect to family dashboard

### **Error Cases**

1. **Invalid Input**:
   - Family name quá ngắn (< 3 chars)
   - Family name quá dài (> 50 chars)
   - Description quá dài (> 200 chars)
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - JWT token expired
3. **Database Error**:
   - Family name đã tồn tại
   - Database connection failed
4. **Network Error**:
   - API timeout
   - Network unavailable

### **Edge Cases**

1. **Boundary Values**:
   - Family name với 3 characters (min)
   - Family name với 50 characters (max)
   - Description với 200 characters (max)
2. **Concurrent Access**:
   - 2 users cùng tạo family với tên giống nhau
3. **Large Data**:
   - Family name với special characters
   - Description với Unicode characters
4. **Special Characters**:
   - Family name với accents, emojis
   - Description với HTML tags

### **Performance Tests**

1. **Load Testing**: 50 concurrent family creations
2. **Stress Testing**: 200 concurrent family creations
3. **Endurance Testing**: Continuous family creation for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- Any authenticated user can create family
- User automatically becomes OWNER
- Role-based access control

### **Data Validation**

- Family name sanitization
- Input validation
- SQL injection prevention
- XSS prevention

### **Data Protection**

- Family data isolation
- User privacy protection
- Audit logging

## 📊 Business Rules

### **Validation Rules**

1. Family name phải unique cho mỗi user
2. Family name 3-50 characters
3. Description tối đa 200 characters
4. User chỉ có thể là OWNER của 1 family
5. Avatar URL phải hợp lệ (nếu có)

### **Business Logic**

1. User tự động trở thành OWNER
2. Tạo family settings mặc định
3. Enable notifications và reminders
4. Create audit log
5. Send welcome notification

### **Constraints**

1. Family name unique per user
2. User can be OWNER of only one family
3. Family member status ACTIVE
4. Family settings required

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)

### **Dependencies**

- UC-006: Invite Family Member (sau khi tạo family)
- UC-021: View Family Dashboard (sau khi tạo family)

### **Related Features**

- Family Settings: Configure family preferences
- Family Avatar: Upload family photo
- Family Invitations: Invite members
- Family Analytics: View family statistics

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Create audit log cho family creation
- Send welcome email/notification
- Update user's current family context
- Cache family data for performance

### **Future Enhancements**

- Family templates (predefined settings)
- Family categories (work, home, etc.)
- Family sharing features
- Family backup/restore

### **Known Limitations**

- User chỉ có thể là OWNER của 1 family
- Không có family templates
- Không có family categories
- Không có family backup

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
