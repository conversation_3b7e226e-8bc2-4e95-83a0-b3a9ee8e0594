# UC-006: Invite Family Member

## 📋 Overview

### **UC-ID**: UC-006

### **Title**: Invite Family Member

### **Actor**: Family Owner/Admin

### **Precondition**: User đã đăng nhập và có quyền mời thành viên trong family

### **Postcondition**: Invitation được gửi và user được mời có thể chấp nhận để tham gia family

### **Priority**: High

### **Business Value**: Cho phép mở rộng family để quản lý thuốc cho nhiều thành viên gia đình

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Family Management Screen / Invite Member Screen
- **User Actions**:
  1. Navigate to Family Management
  2. Click "Invite Member" button
  3. Nhập email address của người được mời
  4. Chọn role (MEMBER, VIEWER)
  5. <PERSON><PERSON><PERSON>p message (optional)
  6. Click "Send Invitation" button
- **UI Elements**:
  - Email input field
  - Role dropdown (MEMBER, VIEWER)
  - Message textarea
  - Send Invitation button
  - Cancel button
  - Form validation messages
- **Validation**:
  - Email format validation
  - Email not already in family
  - Role selection required
  - Message max 200 characters

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/{familyId}/invitations`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "role": "MEMBER",
    "message": "Mời bạn tham gia gia đình để quản lý thuốc cùng nhau"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `FamilyController.inviteMember()`
- **Service Layer**: `FamilyService.inviteMember()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Role-based permission check (OWNER/ADMIN)
  - Email format validation
  - Email not already in family
- **Business Rules**:
  - User phải là family member với role OWNER/ADMIN
  - Email không được đã có trong family
  - Generate unique invitation token
  - Send invitation email
  - Create invitation record

### **Step 4: Database Operations**

- **Repository**: `FamilyRepository`, `FamilyInvitationRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Check if email already in family
  SELECT COUNT(*) FROM family_members fm
  JOIN users u ON fm.user_id = u.id
  WHERE fm.family_id = ? AND u.email = ? AND fm.status = 'ACTIVE';

  -- Check if invitation already exists
  SELECT COUNT(*) FROM family_invitations
  WHERE family_id = ? AND email = ? AND status = 'PENDING';

  -- Insert invitation record
  INSERT INTO family_invitations (family_id, invited_by, email, role, message, token, expires_at, created_at)
  VALUES (?, ?, ?, ?, ?, ?, NOW() + INTERVAL '7 days', NOW())
  RETURNING id;

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'INVITE_MEMBER', 'FAMILY_INVITATION', ?, ?, NOW());
  ```

- **Tables**: `family_members`, `family_invitations`, `users`, `audit_logs`
- **Constraints**:
  - Role-based permissions
  - Email uniqueness per family
  - Invitation expiration (7 days)

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Invitation sent successfully",
    "data": {
      "invitation": {
        "id": 789,
        "email": "<EMAIL>",
        "role": "MEMBER",
        "status": "PENDING",
        "expiresAt": "2024-12-08T10:00:00Z",
        "createdAt": "2024-12-01T10:00:00Z"
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Add invitation to pending list
  - Update invitation count
- **Navigation**: Stay on Family Management Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/{familyId}/invitations
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "email": "string (required, valid email format, not in family)",
  "role": "enum (required: MEMBER, VIEWER)",
  "message": "string (optional, max 200 chars)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "invitation": {
      "id": "number",
      "email": "string",
      "role": "string",
      "status": "string (PENDING)",
      "expiresAt": "string (ISO 8601)",
      "createdAt": "string (ISO 8601)"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Insert invitation record
INSERT INTO family_invitations (family_id, invited_by, email, role, message, token, expires_at, created_at)
VALUES (?, ?, ?, ?, ?, ?, NOW() + INTERVAL '7 days', NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Check user permission
SELECT fm.role FROM family_members fm
WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

-- Check email uniqueness
SELECT COUNT(*) FROM family_members fm
JOIN users u ON fm.user_id = u.id
WHERE fm.family_id = ? AND u.email = ? AND fm.status = 'ACTIVE';

-- Check existing invitation
SELECT COUNT(*) FROM family_invitations
WHERE family_id = ? AND email = ? AND status = 'PENDING';
```

#### **Indexes Used**

- `family_invitations_family_id_idx`: Index on family_id for family filtering
- `family_invitations_email_idx`: Index on email for uniqueness check
- `family_invitations_token_idx`: Unique index on token for invitation link
- `family_members_family_user_idx`: Composite index on family_id, user_id

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                                  |
| ---- | --------------------- | -------------------------------------------- |
| 400  | Bad Request           | Invalid input data                           |
| 401  | Unauthorized          | User not authenticated                       |
| 403  | Forbidden             | Insufficient permissions                     |
| 404  | Not Found             | Family not found                             |
| 409  | Conflict              | Email already in family or invitation exists |
| 422  | Unprocessable Entity  | Validation failed                            |
| 500  | Internal Server Error | Database error                               |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "EMAIL_ALREADY_IN_FAMILY",
    "message": "User is already a member of this family",
    "details": "Please choose a different email address"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (4 queries max)
- **Caching**: Family member list caching
- **Optimization**: Indexes on family_id and email

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có role OWNER/ADMIN trong family
2. **Steps**:
   - Nhập email: <EMAIL>
   - Chọn role: MEMBER
   - Nhập message: "Mời bạn tham gia"
   - Click Send Invitation
3. **Expected Result**:
   - Invitation được tạo thành công
   - Email được gửi
   - Invitation hiển thị trong pending list

### **Error Cases**

1. **Invalid Input**:
   - Email format không hợp lệ
   - Role không hợp lệ
   - Message quá dài
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
   - User có role MEMBER/VIEWER
3. **Business Logic**:
   - Email đã có trong family
   - Invitation đã tồn tại
   - Family không tồn tại
4. **Network Error**:
   - Email service unavailable
   - API timeout

### **Edge Cases**

1. **Boundary Values**:
   - Email với 254 characters
   - Message với 200 characters (max)
   - Invitation với 7 days expiration
2. **Concurrent Access**:
   - 2 users cùng invite email giống nhau
   - Multiple invitations cho cùng email
3. **Large Data**:
   - Family với nhiều invitations
   - User với nhiều families
4. **Special Characters**:
   - Email với special chars
   - Message với Unicode characters

### **Performance Tests**

1. **Load Testing**: 50 concurrent invitations
2. **Stress Testing**: 200 concurrent invitations
3. **Endurance Testing**: Continuous invitations for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access (OWNER/ADMIN only)
- Family context validation

### **Data Validation**

- Email format validation
- Input sanitization
- SQL injection prevention
- XSS prevention

### **Data Protection**

- Secure invitation token generation
- Invitation expiration (7 days)
- Family data isolation
- Audit logging

## 📊 Business Rules

### **Validation Rules**

1. Email phải hợp lệ và chưa có trong family
2. Role phải là MEMBER hoặc VIEWER
3. Message tối đa 200 characters
4. Invitation expiration 7 days
5. User phải có role OWNER/ADMIN

### **Business Logic**

1. Generate unique invitation token
2. Send invitation email với link
3. Create invitation record
4. Track invitation status
5. Create audit log

### **Constraints**

1. Email unique per family
2. Role-based permissions
3. Invitation expiration
4. One pending invitation per email per family

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)

### **Dependencies**

- UC-007: Accept Family Invitation (để accept invitation)

### **Related Features**

- Invitation Management: View, cancel invitations
- Email Notifications: Send invitation emails
- Invitation Analytics: Track invitation success rates
- Bulk Invitations: Invite multiple users

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Generate secure invitation token
- Send email với invitation link
- Create audit log cho invitation
- Implement invitation expiration cleanup

### **Future Enhancements**

- Bulk invitations
- Invitation templates
- Invitation analytics
- Social media invitations

### **Known Limitations**

- Chỉ có thể invite qua email
- Không có bulk invitations
- Không có invitation templates
- Không có social media integration

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
