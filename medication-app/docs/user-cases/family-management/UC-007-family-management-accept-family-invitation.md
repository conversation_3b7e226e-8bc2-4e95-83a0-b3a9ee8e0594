# UC-007: Accept Family Invitation

## 📋 Overview

### **UC-ID**: UC-007

### **Title**: Accept Family Invitation

### **Actor**: Invited User (with valid invitation)

### **Precondition**: User đã đăng nhập và có invitation hợp lệ

### **Postcondition**: User trở thành family member và có thể truy cập family features

### **Priority**: High

### **Business Value**: Cho phép user được mời tham gia family để quản lý thuốc cùng nhau

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Invitation Screen / Email Link / Family Management
- **User Actions**:
  1. Nhận email invitation
  2. Click invitation link trong email
  3. Hoặc navigate to Family Management > Pending Invitations
  4. Click "Accept Invitation" button
  5. Confirm acceptance
- **UI Elements**:
  - Invitation details display
  - Family information
  - Role information
  - Accept button
  - Decline button
  - Invitation expiry warning
- **Validation**:
  - Invitation token validation
  - Invitation expiry check
  - User authentication check

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/invitations/{invitationId}/accept`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "invitationId": 789,
    "token": "invitation_token_from_email"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `FamilyController.acceptInvitation()`
- **Service Layer**: `FamilyService.acceptInvitation()`
- **Validation**:
  - User authentication check
  - Invitation token validation
  - Invitation expiry check
  - Invitation status check
- **Business Rules**:
  - Invitation phải hợp lệ và chưa hết hạn
  - Invitation status phải là PENDING
  - User chưa là member của family
  - Create family member record
  - Update invitation status

### **Step 4: Database Operations**

- **Repository**: `FamilyInvitationRepository`, `FamilyMemberRepository`
- **Queries**:

  ```sql
  -- Validate invitation
  SELECT id, family_id, email, role, status, expires_at
  FROM family_invitations
  WHERE id = ? AND token = ? AND status = 'PENDING' AND expires_at > NOW();

  -- Check if user already member
  SELECT COUNT(*) FROM family_members fm
  JOIN users u ON fm.user_id = u.id
  WHERE fm.family_id = ? AND u.email = ? AND fm.status = 'ACTIVE';

  -- Get user ID
  SELECT id FROM users WHERE email = ? AND deleted_at IS NULL;

  -- Create family member record
  INSERT INTO family_members (family_id, user_id, role, status, joined_at, created_at)
  VALUES (?, ?, ?, 'ACTIVE', NOW(), NOW())
  RETURNING id;

  -- Update invitation status
  UPDATE family_invitations
  SET status = 'ACCEPTED', accepted_at = NOW(), updated_at = NOW()
  WHERE id = ?;

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'ACCEPT_INVITATION', 'FAMILY_MEMBER', ?, ?, NOW());
  ```

- **Tables**: `family_invitations`, `family_members`, `users`, `audit_logs`
- **Constraints**:
  - Invitation validation
  - User uniqueness per family
  - Family member creation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Successfully joined family",
    "data": {
      "family": {
        "id": 123,
        "name": "Nguyễn Family",
        "description": "Gia đình Nguyễn"
      },
      "member": {
        "id": 456,
        "role": "MEMBER",
        "joinedAt": "2024-12-01T10:00:00Z"
      },
      "permissions": {
        "canViewMedicines": true,
        "canAddMedicines": true,
        "canEditMedicines": false,
        "canDeleteMedicines": false
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Add family to user's family list
  - Update family member count
  - Remove invitation from pending list
- **Navigation**: Redirect to Family Dashboard

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/invitations/{invitationId}/accept
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "invitationId": "number (required, valid invitation ID)",
  "token": "string (required, invitation token from email)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "family": {
      "id": "number",
      "name": "string",
      "description": "string"
    },
    "member": {
      "id": "number",
      "role": "string",
      "joinedAt": "string (ISO 8601)"
    },
    "permissions": {
      "canViewMedicines": "boolean",
      "canAddMedicines": "boolean",
      "canEditMedicines": "boolean",
      "canDeleteMedicines": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Create family member record
INSERT INTO family_members (family_id, user_id, role, status, joined_at, created_at)
VALUES (?, ?, ?, 'ACTIVE', NOW(), NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Validate invitation
SELECT id, family_id, email, role, status, expires_at
FROM family_invitations
WHERE id = ? AND token = ? AND status = 'PENDING' AND expires_at > NOW();

-- Check existing membership
SELECT COUNT(*) FROM family_members fm
JOIN users u ON fm.user_id = u.id
WHERE fm.family_id = ? AND u.email = ? AND fm.status = 'ACTIVE';

-- Update invitation status
UPDATE family_invitations
SET status = 'ACCEPTED', accepted_at = NOW(), updated_at = NOW()
WHERE id = ?;
```

#### **Indexes Used**

- `family_invitations_id_token_idx`: Composite index on id, token
- `family_invitations_status_expires_idx`: Index on status, expires_at
- `family_members_family_user_idx`: Composite index on family_id, user_id
- `users_email_idx`: Index on email for user lookup

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                        |
| ---- | --------------------- | ---------------------------------- |
| 400  | Bad Request           | Invalid invitation data            |
| 401  | Unauthorized          | User not authenticated             |
| 404  | Not Found             | Invitation not found               |
| 410  | Gone                  | Invitation expired or already used |
| 409  | Conflict              | User already member of family      |
| 422  | Unprocessable Entity  | Validation failed                  |
| 500  | Internal Server Error | Database error                     |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INVITATION_EXPIRED",
    "message": "Invitation has expired",
    "details": "Please request a new invitation"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (6 queries max)
- **Caching**: Family data caching
- **Optimization**: Indexes on invitation fields

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có valid invitation
2. **Steps**:
   - Click invitation link
   - Click Accept button
   - Confirm acceptance
3. **Expected Result**:
   - User trở thành family member
   - Invitation status updated
   - Redirect to family dashboard

### **Error Cases**

1. **Invalid Invitation**:
   - Invitation không tồn tại
   - Token không hợp lệ
   - Invitation đã được sử dụng
2. **Expired Invitation**:
   - Invitation đã hết hạn
   - Token expired
3. **Already Member**:
   - User đã là member của family
   - Duplicate membership
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Invitation với 1 minute left
   - Invitation với maximum expiry
2. **Concurrent Access**:
   - 2 users cùng accept invitation
   - Race conditions
3. **Large Data**:
   - Family với nhiều members
   - User với nhiều families
4. **Special Characters**:
   - Family name với Unicode
   - User email với special chars

### **Performance Tests**

1. **Load Testing**: 50 concurrent acceptances
2. **Stress Testing**: 200 concurrent acceptances
3. **Endurance Testing**: Continuous acceptances for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- Invitation token validation
- User email match check
- Family context validation

### **Data Validation**

- Invitation token format
- Expiry date validation
- Status validation
- Email verification

### **Data Protection**

- Secure invitation tokens
- Family data isolation
- Audit logging
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. Invitation phải hợp lệ và chưa hết hạn
2. Invitation status phải là PENDING
3. User chưa là member của family
4. User email phải match invitation email

### **Business Logic**

1. Validate invitation token
2. Check invitation expiry
3. Create family member record
4. Update invitation status
5. Create audit log

### **Constraints**

1. One membership per user per family
2. Invitation expiration enforced
3. Token uniqueness required
4. Family context validation

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-006: Invite Family Member (invitation phải tồn tại)

### **Dependencies**

- UC-008: Manage Family Roles (để manage roles)
- UC-009: Add Medicine Type (để add medicines)

### **Related Features**

- Family Management: View family details
- Member Management: View family members
- Permission System: Role-based access
- Invitation Management: Track invitations

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Validate invitation token security
- Create audit log cho tracking
- Update family member count
- Send notification to family owner

### **Future Enhancements**

- Bulk invitation acceptance
- Invitation templates
- Social media invitations
- Advanced permission system

### **Known Limitations**

- Chỉ có thể accept qua email link
- Không có bulk acceptance
- Không có invitation templates
- Không có social media integration

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
