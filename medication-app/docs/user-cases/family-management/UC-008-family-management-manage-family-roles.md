# UC-008: Manage Family Roles

## 📋 Overview

### **UC-ID**: UC-008

### **Title**: Manage Family Roles

### **Actor**: Family Owner/Admin

### **Precondition**: User đã đăng nhập và có quyền quản lý roles trong family

### **Postcondition**: Family member roles được cập nhật và permissions được áp dụng

### **Priority**: Medium

### **Business Value**: Cho phép quản lý quyền truy cập và phân quyền trong family để đảm bảo an toàn và hiệu quả

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Family Management Screen / Member Management
- **User Actions**:
  1. Navigate to Family Management
  2. Click "Manage Members" tab
  3. Select family member từ list
  4. Click "Edit Role" button
  5. Chọn new role từ dropdown
  6. Nhập reason for change (optional)
  7. Click "Update Role" button
  8. Confirm role change
- **UI Elements**:
  - Family members list
  - Member details display
  - Role dropdown (OWNER, ADMIN, MEMBER, VIEWER)
  - Reason textarea
  - Update Role button
  - Cancel button
  - Confirmation dialog
- **Validation**:
  - User permission check
  - Role hierarchy validation
  - Cannot change own role to lower level
  - Cannot remove last owner

### **Step 2: API Call Details**

- **Endpoint**: `PUT /api/families/{familyId}/members/{memberId}/role`
- **Method**: PUT
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "newRole": "ADMIN",
    "reason": "Promote to help manage family medicines"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `FamilyController.updateMemberRole()`
- **Service Layer**: `FamilyService.updateMemberRole()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Role-based permission check (OWNER/ADMIN)
  - Role hierarchy validation
- **Business Rules**:
  - User phải là OWNER hoặc ADMIN
  - Không thể thay đổi role của OWNER khác
  - Không thể hạ cấp role của chính mình
  - Phải có ít nhất 1 OWNER trong family
  - Create role change audit log

### **Step 4: Database Operations**

- **Repository**: `FamilyMemberRepository`, `FamilyRoleRepository`
- **Queries**:

  ```sql
  -- Check user permission to manage roles
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Get target member details
  SELECT fm.id, fm.user_id, fm.role, u.email, u.full_name
  FROM family_members fm
  JOIN users u ON fm.user_id = u.id
  WHERE fm.family_id = ? AND fm.id = ? AND fm.status = 'ACTIVE';

  -- Check if this would remove last owner
  SELECT COUNT(*) FROM family_members
  WHERE family_id = ? AND role = 'OWNER' AND status = 'ACTIVE';

  -- Check if target member is another owner
  SELECT role FROM family_members
  WHERE family_id = ? AND id = ? AND status = 'ACTIVE';

  -- Update member role
  UPDATE family_members
  SET role = ?, updated_at = NOW()
  WHERE id = ? AND family_id = ?;

  -- Create role change history
  INSERT INTO family_role_history (family_id, member_id, old_role, new_role, changed_by, reason, created_at)
  VALUES (?, ?, ?, ?, ?, ?, NOW());

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'UPDATE_MEMBER_ROLE', 'FAMILY_MEMBER', ?, ?, NOW());
  ```

- **Tables**: `family_members`, `family_role_history`, `users`, `audit_logs`
- **Constraints**:
  - Role hierarchy validation
  - Owner protection
  - Audit trail required

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Member role updated successfully",
    "data": {
      "member": {
        "id": 456,
        "userId": 789,
        "email": "<EMAIL>",
        "fullName": "Nguyễn Văn B",
        "oldRole": "MEMBER",
        "newRole": "ADMIN",
        "updatedAt": "2024-12-01T10:00:00Z"
      },
      "permissions": {
        "canViewMedicines": true,
        "canAddMedicines": true,
        "canEditMedicines": true,
        "canDeleteMedicines": true,
        "canManageMembers": true,
        "canManageRoles": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Update member role in list
  - Update permissions display
  - Refresh member count by role
- **Navigation**: Stay on Member Management Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
PUT /api/families/{familyId}/members/{memberId}/role
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "newRole": "enum (required: OWNER, ADMIN, MEMBER, VIEWER)",
  "reason": "string (optional, max 200 chars)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "member": {
      "id": "number",
      "userId": "number",
      "email": "string",
      "fullName": "string",
      "oldRole": "string",
      "newRole": "string",
      "updatedAt": "string (ISO 8601)"
    },
    "permissions": {
      "canViewMedicines": "boolean",
      "canAddMedicines": "boolean",
      "canEditMedicines": "boolean",
      "canDeleteMedicines": "boolean",
      "canManageMembers": "boolean",
      "canManageRoles": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Update member role
UPDATE family_members
SET role = ?, updated_at = NOW()
WHERE id = ? AND family_id = ?;
```

#### **Related Queries**

```sql
-- Check user permission
SELECT fm.role FROM family_members fm
WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

-- Get target member
SELECT fm.id, fm.user_id, fm.role, u.email, u.full_name
FROM family_members fm
JOIN users u ON fm.user_id = u.id
WHERE fm.family_id = ? AND fm.id = ? AND fm.status = 'ACTIVE';

-- Create role history
INSERT INTO family_role_history (family_id, member_id, old_role, new_role, changed_by, reason, created_at)
VALUES (?, ?, ?, ?, ?, ?, NOW());
```

#### **Indexes Used**

- `family_members_family_id_idx`: Index on family_id for family filtering
- `family_members_role_idx`: Index on role for role-based queries
- `family_members_user_id_idx`: Index on user_id for user lookup
- `family_role_history_member_idx`: Index on member_id for history

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description              |
| ---- | --------------------- | ------------------------ |
| 400  | Bad Request           | Invalid role data        |
| 401  | Unauthorized          | User not authenticated   |
| 403  | Forbidden             | Insufficient permissions |
| 404  | Not Found             | Member not found         |
| 409  | Conflict              | Cannot change owner role |
| 422  | Unprocessable Entity  | Validation failed        |
| 500  | Internal Server Error | Database error           |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "message": "You don't have permission to manage roles",
    "details": "Only OWNER and ADMIN can manage member roles"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (6 queries max)
- **Caching**: Member list caching
- **Optimization**: Indexes on family_id and role

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có role OWNER/ADMIN, target member tồn tại
2. **Steps**:
   - Select member: "Nguyễn Văn B"
   - Change role: MEMBER → ADMIN
   - Enter reason: "Promote to help manage"
   - Click Update Role
3. **Expected Result**:
   - Role được cập nhật thành công
   - Permissions được cập nhật
   - Audit log được tạo

### **Error Cases**

1. **Invalid Input**:
   - Role không hợp lệ
   - Reason quá dài
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải OWNER/ADMIN
3. **Business Logic**:
   - Không thể thay đổi role của OWNER khác
   - Không thể hạ cấp role của chính mình
   - Không thể remove last owner
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Reason với 200 characters (max)
   - Role với special characters
2. **Concurrent Access**:
   - 2 admins cùng thay đổi role
   - Race conditions
3. **Large Data**:
   - Family với nhiều members
   - User với nhiều role changes
4. **Special Characters**:
   - Reason với Unicode characters
   - Member name với special chars

### **Performance Tests**

1. **Load Testing**: 50 concurrent role updates
2. **Stress Testing**: 200 concurrent role updates
3. **Endurance Testing**: Continuous updates for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be OWNER or ADMIN
- Role hierarchy validation
- Cannot change owner roles
- Self-protection rules

### **Data Validation**

- Role format validation
- Permission boundary checks
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Role change audit trail
- Family data isolation
- Permission enforcement
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. User phải có role OWNER hoặc ADMIN
2. Không thể thay đổi role của OWNER khác
3. Không thể hạ cấp role của chính mình
4. Phải có ít nhất 1 OWNER trong family
5. Reason tối đa 200 characters

### **Business Logic**

1. Validate user permissions
2. Check role hierarchy rules
3. Update member role
4. Create role change history
5. Create audit log

### **Constraints**

1. Role hierarchy: OWNER > ADMIN > MEMBER > VIEWER
2. Owner protection rules
3. Self-protection rules
4. Minimum owner requirement

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-007: Accept Family Invitation (member phải tồn tại)

### **Dependencies**

- UC-009: Add Medicine Type (để test permissions)
- UC-010: Add Medicine to Inventory (để test permissions)

### **Related Features**

- Permission System: Role-based access control
- Member Management: View, edit, remove members
- Audit Trail: Track role changes
- Security: Protect family data

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Implement role hierarchy validation
- Create comprehensive audit trail
- Update permissions immediately
- Send notification to affected member

### **Future Enhancements**

- Bulk role management
- Role templates
- Advanced permission system
- Role analytics

### **Known Limitations**

- Không có bulk role updates
- Không có role templates
- Không có advanced permissions
- Không có role analytics

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
