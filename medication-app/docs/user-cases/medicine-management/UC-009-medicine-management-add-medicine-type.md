# UC-009: Add Medicine Type

## 📋 Overview

### **UC-ID**: UC-009

### **Title**: Add Medicine Type

### **Actor**: Authenticated User (Family Member with ADMIN/OWNER role)

### **Precondition**: User đã đăng nhập và có quyền quản lý medicine types trong family

### **Postcondition**: Medicine type mới được tạo và có thể sử dụng để thêm medicine vào inventory

### **Priority**: High

### **Business Value**: Cho phép người dùng tạo loại thuốc mới để quản lý thuốc một cách có tổ chức và dễ dàng tìm kiếm

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Medicine Management Screen / Add Medicine Type Screen
- **User Actions**:
  1. Navigate to Medicine Management
  2. Click "Add Medicine Type" button
  3. Nhập medicine type name
  4. Nhập description (optional)
  5. Chọn category (Tablet, Liquid, Injection, etc.)
  6. Nhập active ingredient (optional)
  7. Nhập dosage form (optional)
  8. Click "Save Medicine Type" button
- **UI Elements**:
  - Medicine type name input field
  - Description textarea
  - Category dropdown
  - Active ingredient input field
  - Dosage form input field
  - Save button
  - Cancel button
  - Form validation messages
- **Validation**:
  - Medicine type name required (2-100 characters)
  - Medicine type name unique trong family
  - Category required
  - Description max 500 characters
  - Active ingredient max 200 characters

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/{familyId}/medicine-types`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "name": "Paracetamol 500mg",
    "description": "Thuốc giảm đau, hạ sốt",
    "category": "TABLET",
    "activeIngredient": "Paracetamol",
    "dosageForm": "Viên nén",
    "strength": "500mg"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `MedicineTypeController.createMedicineType()`
- **Service Layer**: `MedicineTypeService.createMedicineType()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Role-based permission check (ADMIN/OWNER)
  - Medicine type name uniqueness check
- **Business Rules**:
  - User phải là family member
  - User phải có role ADMIN hoặc OWNER
  - Medicine type name unique trong family
  - Category phải hợp lệ

### **Step 4: Database Operations**

- **Repository**: `MedicineTypeRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Check medicine type name uniqueness in family
  SELECT COUNT(*) FROM medicine_types
  WHERE family_id = ? AND name = ? AND deleted_at IS NULL;

  -- Insert new medicine type
  INSERT INTO medicine_types (family_id, name, description, category, active_ingredient, dosage_form, strength, created_at, updated_at)
  VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
  RETURNING id;

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'CREATE', 'MEDICINE_TYPE', ?, ?, NOW());
  ```

- **Tables**: `medicine_types`, `family_members`, `audit_logs`
- **Constraints**:
  - Foreign key family_id -> families.id
  - Medicine type name unique per family
  - Category enum validation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Medicine type created successfully",
    "data": {
      "medicineType": {
        "id": 123,
        "name": "Paracetamol 500mg",
        "description": "Thuốc giảm đau, hạ sốt",
        "category": "TABLET",
        "activeIngredient": "Paracetamol",
        "dosageForm": "Viên nén",
        "strength": "500mg",
        "createdAt": "2024-12-01T10:00:00Z"
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Add medicine type to list
  - Enable "Add Medicine" button
- **Navigation**: Stay on Medicine Management Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/{familyId}/medicine-types
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "name": "string (required, 2-100 chars, unique per family)",
  "description": "string (optional, max 500 chars)",
  "category": "enum (required: TABLET, LIQUID, INJECTION, CREAM, DROPS, INHALER, SUPPOSITORY)",
  "activeIngredient": "string (optional, max 200 chars)",
  "dosageForm": "string (optional, max 100 chars)",
  "strength": "string (optional, max 50 chars)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "medicineType": {
      "id": "number",
      "name": "string",
      "description": "string",
      "category": "string",
      "activeIngredient": "string",
      "dosageForm": "string",
      "strength": "string",
      "createdAt": "string (ISO 8601)",
      "updatedAt": "string (ISO 8601)"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Insert new medicine type
INSERT INTO medicine_types (family_id, name, description, category, active_ingredient, dosage_form, strength, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Check user permission
SELECT fm.role FROM family_members fm
WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

-- Check name uniqueness
SELECT COUNT(*) FROM medicine_types
WHERE family_id = ? AND name = ? AND deleted_at IS NULL;

-- Create audit log
INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
VALUES (?, ?, 'CREATE', 'MEDICINE_TYPE', ?, ?, NOW());
```

#### **Indexes Used**

- `medicine_types_family_id_idx`: Index on family_id for family filtering
- `medicine_types_name_idx`: Index on name for uniqueness check
- `medicine_types_category_idx`: Index on category for filtering
- `family_members_family_user_idx`: Composite index on family_id, user_id

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                       |
| ---- | --------------------- | --------------------------------- |
| 400  | Bad Request           | Invalid input data                |
| 401  | Unauthorized          | User not authenticated            |
| 403  | Forbidden             | Insufficient permissions          |
| 404  | Not Found             | Family not found                  |
| 409  | Conflict              | Medicine type name already exists |
| 422  | Unprocessable Entity  | Validation failed                 |
| 500  | Internal Server Error | Database error                    |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "message": "You don't have permission to add medicine types",
    "details": "Only ADMIN and OWNER roles can add medicine types"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 1 second
- **Database Load**: Light (3 queries max)
- **Caching**: Medicine type list caching
- **Optimization**: Indexes on family_id and name

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có role ADMIN/OWNER trong family
2. **Steps**:
   - Nhập name: "Paracetamol 500mg"
   - Nhập description: "Thuốc giảm đau, hạ sốt"
   - Chọn category: TABLET
   - Click Save
3. **Expected Result**:
   - Medicine type được tạo thành công
   - Hiển thị trong medicine type list
   - Audit log được tạo

### **Error Cases**

1. **Invalid Input**:
   - Name quá ngắn (< 2 chars)
   - Name quá dài (> 100 chars)
   - Category không hợp lệ
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
   - User có role MEMBER/VIEWER
3. **Database Error**:
   - Medicine type name đã tồn tại
   - Family không tồn tại
4. **Network Error**:
   - API timeout
   - Network unavailable

### **Edge Cases**

1. **Boundary Values**:
   - Name với 2 characters (min)
   - Name với 100 characters (max)
   - Description với 500 characters (max)
2. **Concurrent Access**:
   - 2 users cùng tạo medicine type với tên giống nhau
3. **Large Data**:
   - Name với special characters
   - Description với Unicode characters
4. **Special Characters**:
   - Name với accents, numbers, symbols
   - Active ingredient với chemical formulas

### **Performance Tests**

1. **Load Testing**: 100 concurrent medicine type creations
2. **Stress Testing**: 500 concurrent medicine type creations
3. **Endurance Testing**: Continuous creation for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access (ADMIN/OWNER only)
- Family context validation

### **Data Validation**

- Input sanitization
- SQL injection prevention
- XSS prevention
- Category enum validation

### **Data Protection**

- Family data isolation
- Audit logging
- Data encryption

## 📊 Business Rules

### **Validation Rules**

1. Medicine type name phải unique trong family
2. Name 2-100 characters
3. Category phải hợp lệ
4. Description tối đa 500 characters
5. Active ingredient tối đa 200 characters

### **Business Logic**

1. User phải có role ADMIN hoặc OWNER
2. Medicine type được tạo trong family context
3. Audit log được tạo
4. Medicine type có thể được sử dụng để thêm medicine

### **Constraints**

1. Medicine type name unique per family
2. Category enum validation
3. Family membership required
4. Role-based permissions

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)

### **Dependencies**

- UC-010: Add Medicine to Inventory (sử dụng medicine type)

### **Related Features**

- Medicine Type Management: Edit, delete medicine types
- Medicine Type Search: Search medicine types
- Medicine Type Categories: Manage categories
- Medicine Type Templates: Predefined types

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Create audit log cho medicine type creation
- Validate family membership và permissions
- Cache medicine type list for performance
- Implement soft delete cho medicine types

### **Future Enhancements**

- Medicine type templates
- Medicine type import/export
- Medicine type sharing between families
- Medicine type analytics

### **Known Limitations**

- Medicine type chỉ có thể được tạo bởi ADMIN/OWNER
- Không có medicine type templates
- Không có medicine type sharing
- Không có medicine type analytics

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
