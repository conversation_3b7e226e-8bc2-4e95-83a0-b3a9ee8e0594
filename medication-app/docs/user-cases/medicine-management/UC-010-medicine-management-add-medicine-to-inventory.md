# UC-010: Add Medicine to Inventory

## 📋 Overview

### **UC-ID**: UC-010

### **Title**: Add Medicine to Inventory

### **Actor**: Family Member (ADMIN/OWNER/MEMBER role)

### **Precondition**: User đã đăng nhập, có family context, và medicine type đã tồn tại

### **Postcondition**: Medicine mới được thêm vào inventory với quantity và expiry tracking

### **Priority**: High

### **Business Value**: Cho phép người dùng thêm thuốc vào tủ để quản lý tồn kho và theo dõi hạn sử dụng

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Medicine Management Screen / Add Medicine Screen
- **User Actions**:
  1. Navigate to Medicine Management
  2. Click "Add Medicine" button
  3. Chọn medicine type từ dropdown
  4. Nhập quantity
  5. Nhập expiry date
  6. Nh<PERSON>p batch number (optional)
  7. Nhập notes (optional)
  8. Click "Add to Inventory" button
- **UI Elements**:
  - Medicine type dropdown
  - Quantity input field
  - Expiry date picker
  - Batch number input field
  - Notes textarea
  - Add to Inventory button
  - Cancel button
  - Form validation messages
- **Validation**:
  - Medicine type required
  - Quantity > 0
  - Expiry date > today
  - Batch number format validation
  - Notes max 500 characters

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/{familyId}/medicines`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "medicineTypeId": 123,
    "quantity": 30,
    "expiryDate": "2025-06-01",
    "batchNumber": "BATCH2024001",
    "notes": "Mua từ nhà thuốc ABC"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `MedicineController.addMedicine()`
- **Service Layer**: `MedicineService.addMedicineToInventory()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Medicine type exists và belongs to family
  - Quantity validation
  - Expiry date validation
- **Business Rules**:
  - User phải là family member
  - Medicine type phải tồn tại trong family
  - Quantity phải > 0
  - Expiry date phải > today
  - Create inventory record
  - Create usage log entry

### **Step 4: Database Operations**

- **Repository**: `MedicineRepository`, `MedicineTypeRepository`, `InventoryTransactionRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Validate medicine type belongs to family
  SELECT id, name FROM medicine_types
  WHERE id = ? AND family_id = ? AND deleted_at IS NULL;

  -- Check if medicine already exists in inventory
  SELECT id, quantity FROM medicines
  WHERE family_id = ? AND medicine_type_id = ? AND batch_number = ? AND deleted_at IS NULL;

  -- Insert new medicine record
  INSERT INTO medicines (family_id, medicine_type_id, quantity, expiry_date, batch_number, notes, created_at, updated_at)
  VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
  RETURNING id;

  -- Create inventory transaction
  INSERT INTO inventory_transactions (family_id, medicine_id, transaction_type, quantity, previous_quantity, new_quantity, performed_by, created_at)
  VALUES (?, ?, 'ADD', ?, 0, ?, ?, NOW());

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'ADD_MEDICINE', 'MEDICINE', ?, ?, NOW());
  ```

- **Tables**: `medicines`, `medicine_types`, `family_members`, `inventory_transactions`, `audit_logs`
- **Constraints**:
  - Foreign key constraints
  - Quantity > 0
  - Expiry date validation
  - Family context validation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Medicine added to inventory successfully",
    "data": {
      "medicine": {
        "id": 456,
        "medicineType": {
          "id": 123,
          "name": "Paracetamol 500mg",
          "category": "TABLET"
        },
        "quantity": 30,
        "expiryDate": "2025-06-01",
        "batchNumber": "BATCH2024001",
        "notes": "Mua từ nhà thuốc ABC",
        "createdAt": "2024-12-01T10:00:00Z"
      },
      "inventory": {
        "totalQuantity": 30,
        "daysUntilExpiry": 182
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Add medicine to inventory list
  - Update inventory statistics
  - Refresh medicine count
- **Navigation**: Stay on Medicine Management Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/{familyId}/medicines
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "medicineTypeId": "number (required, existing medicine type in family)",
  "quantity": "number (required, > 0)",
  "expiryDate": "date (required, format: YYYY-MM-DD, > today)",
  "batchNumber": "string (optional, max 50 chars)",
  "notes": "string (optional, max 500 chars)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "medicine": {
      "id": "number",
      "medicineType": {
        "id": "number",
        "name": "string",
        "category": "string"
      },
      "quantity": "number",
      "expiryDate": "string (YYYY-MM-DD)",
      "batchNumber": "string",
      "notes": "string",
      "createdAt": "string (ISO 8601)",
      "updatedAt": "string (ISO 8601)"
    },
    "inventory": {
      "totalQuantity": "number",
      "daysUntilExpiry": "number"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Insert new medicine record
INSERT INTO medicines (family_id, medicine_type_id, quantity, expiry_date, batch_number, notes, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Validate medicine type
SELECT id, name FROM medicine_types
WHERE id = ? AND family_id = ? AND deleted_at IS NULL;

-- Check existing medicine
SELECT id, quantity FROM medicines
WHERE family_id = ? AND medicine_type_id = ? AND batch_number = ? AND deleted_at IS NULL;

-- Create inventory transaction
INSERT INTO inventory_transactions (family_id, medicine_id, transaction_type, quantity, previous_quantity, new_quantity, performed_by, created_at)
VALUES (?, ?, 'ADD', ?, 0, ?, ?, NOW());
```

#### **Indexes Used**

- `medicines_family_id_idx`: Index on family_id for family filtering
- `medicines_medicine_type_id_idx`: Index on medicine_type_id for type filtering
- `medicines_expiry_date_idx`: Index on expiry_date for expiry tracking
- `medicines_batch_number_idx`: Index on batch_number for uniqueness

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description              |
| ---- | --------------------- | ------------------------ |
| 400  | Bad Request           | Invalid input data       |
| 401  | Unauthorized          | User not authenticated   |
| 403  | Forbidden             | Insufficient permissions |
| 404  | Not Found             | Medicine type not found  |
| 409  | Conflict              | Medicine already exists  |
| 422  | Unprocessable Entity  | Validation failed        |
| 500  | Internal Server Error | Database error           |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "MEDICINE_TYPE_NOT_FOUND",
    "message": "Medicine type not found",
    "details": "Please select a valid medicine type"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (5 queries max)
- **Caching**: Medicine type list caching
- **Optimization**: Indexes on family_id and medicine_type_id

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền trong family, medicine type đã tồn tại
2. **Steps**:
   - Chọn medicine type: "Paracetamol 500mg"
   - Nhập quantity: 30
   - Chọn expiry date: 2025-06-01
   - Nhập batch number: "BATCH2024001"
   - Click Add to Inventory
3. **Expected Result**:
   - Medicine được thêm thành công
   - Inventory được cập nhật
   - Transaction log được tạo

### **Error Cases**

1. **Invalid Input**:
   - Quantity <= 0
   - Expiry date <= today
   - Medicine type không tồn tại
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
3. **Business Logic**:
   - Medicine type không thuộc family
   - Medicine đã tồn tại với batch number
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Quantity = 1 (minimum)
   - Quantity = 999999 (maximum)
   - Expiry date = tomorrow
   - Expiry date = 10 years from now
2. **Concurrent Access**:
   - 2 users cùng thêm medicine
   - Race conditions
3. **Large Data**:
   - Notes với 500 characters
   - Batch number với 50 characters
4. **Special Characters**:
   - Notes với Unicode characters
   - Batch number với special chars

### **Performance Tests**

1. **Load Testing**: 100 concurrent medicine additions
2. **Stress Testing**: 500 concurrent medicine additions
3. **Endurance Testing**: Continuous additions for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access control
- Family context validation

### **Data Validation**

- Input sanitization
- SQL injection prevention
- XSS prevention
- Date validation

### **Data Protection**

- Family data isolation
- Audit logging
- Transaction tracking
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. Medicine type phải tồn tại trong family
2. Quantity phải > 0
3. Expiry date phải > today
4. Batch number unique per medicine type per family
5. Notes tối đa 500 characters

### **Business Logic**

1. Create medicine inventory record
2. Create inventory transaction log
3. Update inventory statistics
4. Create audit log
5. Send expiry notifications (if needed)

### **Constraints**

1. Medicine type belongs to family
2. Quantity positive integer
3. Expiry date future date
4. Batch number format validation
5. Family context required

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-009: Add Medicine Type (medicine type phải tồn tại)

### **Dependencies**

- UC-011: Update Medicine Quantity (để update quantity)
- UC-012: Remove Expired Medicine (để remove expired)

### **Related Features**

- Inventory Management: View, edit, delete medicines
- Expiry Tracking: Monitor expiry dates
- Batch Management: Track batch numbers
- Inventory Analytics: View inventory statistics

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Create inventory transaction log
- Update inventory statistics
- Create audit log cho tracking
- Implement expiry date validation

### **Future Enhancements**

- Barcode scanning
- Bulk medicine import
- Medicine templates
- Advanced inventory analytics

### **Known Limitations**

- Không có barcode scanning
- Không có bulk import
- Không có medicine templates
- Không có advanced analytics

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
