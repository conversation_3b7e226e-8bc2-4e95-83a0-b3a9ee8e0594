# UC-011: Update Medicine Quantity

## 📋 Overview

### **UC-ID**: UC-011

### **Title**: Update Medicine Quantity

### **Actor**: Family Member (ADMIN/OWNER/MEMBER role)

### **Precondition**: User đã đăng nhập, có family context, và medicine đã tồn tại trong inventory

### **Postcondition**: Medicine quantity được cập nhật và inventory transaction được ghi lại

### **Priority**: High

### **Business Value**: Cho phép người dùng cập nhật số lượng thuốc khi sử dụng hoặc bổ sung để duy trì inventory chính xác

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Medicine Management Screen / Medicine Details / Inventory Management
- **User Actions**:
  1. Navigate to Medicine Management
  2. Select medicine từ inventory list
  3. Click "Update Quantity" button
  4. Chọn action type (ADD, REMOVE, SET)
  5. Nhập quantity change
  6. Nhập reason (optional)
  7. Click "Update Quantity" button
  8. Confirm update
- **UI Elements**:
  - Medicine details display
  - Current quantity display
  - Action type dropdown (ADD, REMOVE, SET)
  - Quantity input field
  - Reason textarea
  - Update Quantity button
  - Cancel button
  - Confirmation dialog
- **Validation**:
  - Quantity validation (positive for ADD, valid for REMOVE/SET)
  - Action type required
  - Reason max 200 characters
  - Cannot remove more than available

### **Step 2: API Call Details**

- **Endpoint**: `PUT /api/families/{familyId}/medicines/{medicineId}/quantity`
- **Method**: PUT
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "actionType": "REMOVE",
    "quantity": 5,
    "reason": "Used for headache treatment"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `MedicineController.updateQuantity()`
- **Service Layer**: `MedicineService.updateMedicineQuantity()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Medicine exists và belongs to family
  - Quantity validation based on action type
- **Business Rules**:
  - User phải là family member
  - Medicine phải tồn tại trong family
  - Quantity phải hợp lệ theo action type
  - Create inventory transaction record
  - Update medicine quantity
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `MedicineRepository`, `InventoryTransactionRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Get current medicine details
  SELECT id, quantity, medicine_type_id, batch_number
  FROM medicines
  WHERE id = ? AND family_id = ? AND deleted_at IS NULL;

  -- Calculate new quantity based on action type
  -- For ADD: new_quantity = current_quantity + quantity
  -- For REMOVE: new_quantity = current_quantity - quantity
  -- For SET: new_quantity = quantity

  -- Update medicine quantity
  UPDATE medicines
  SET quantity = ?, updated_at = NOW()
  WHERE id = ? AND family_id = ?;

  -- Create inventory transaction
  INSERT INTO inventory_transactions (family_id, medicine_id, transaction_type, quantity, previous_quantity, new_quantity, performed_by, reason, created_at)
  VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW());

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'UPDATE_MEDICINE_QUANTITY', 'MEDICINE', ?, ?, NOW());

  -- Check if quantity is low (for notifications)
  SELECT COUNT(*) FROM medicines
  WHERE id = ? AND quantity <= 10;
  ```

- **Tables**: `medicines`, `family_members`, `inventory_transactions`, `audit_logs`
- **Constraints**:
  - Quantity >= 0
  - Family context validation
  - Transaction tracking required

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Medicine quantity updated successfully",
    "data": {
      "medicine": {
        "id": 456,
        "medicineType": {
          "id": 123,
          "name": "Paracetamol 500mg",
          "category": "TABLET"
        },
        "previousQuantity": 30,
        "newQuantity": 25,
        "batchNumber": "BATCH2024001",
        "updatedAt": "2024-12-01T10:00:00Z"
      },
      "transaction": {
        "id": 789,
        "actionType": "REMOVE",
        "quantity": 5,
        "reason": "Used for headache treatment",
        "createdAt": "2024-12-01T10:00:00Z"
      },
      "inventory": {
        "totalQuantity": 25,
        "isLowStock": false,
        "daysUntilExpiry": 182
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Update quantity display
  - Update inventory statistics
  - Show low stock warning (if applicable)
- **Navigation**: Stay on Medicine Management Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
PUT /api/families/{familyId}/medicines/{medicineId}/quantity
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "actionType": "enum (required: ADD, REMOVE, SET)",
  "quantity": "number (required, > 0 for ADD, <= current for REMOVE, >= 0 for SET)",
  "reason": "string (optional, max 200 chars)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "medicine": {
      "id": "number",
      "medicineType": {
        "id": "number",
        "name": "string",
        "category": "string"
      },
      "previousQuantity": "number",
      "newQuantity": "number",
      "batchNumber": "string",
      "updatedAt": "string (ISO 8601)"
    },
    "transaction": {
      "id": "number",
      "actionType": "string",
      "quantity": "number",
      "reason": "string",
      "createdAt": "string (ISO 8601)"
    },
    "inventory": {
      "totalQuantity": "number",
      "isLowStock": "boolean",
      "daysUntilExpiry": "number"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Update medicine quantity
UPDATE medicines
SET quantity = ?, updated_at = NOW()
WHERE id = ? AND family_id = ?;
```

#### **Related Queries**

```sql
-- Get current medicine details
SELECT id, quantity, medicine_type_id, batch_number
FROM medicines
WHERE id = ? AND family_id = ? AND deleted_at IS NULL;

-- Create inventory transaction
INSERT INTO inventory_transactions (family_id, medicine_id, transaction_type, quantity, previous_quantity, new_quantity, performed_by, reason, created_at)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW());

-- Check low stock
SELECT COUNT(*) FROM medicines
WHERE id = ? AND quantity <= 10;
```

#### **Indexes Used**

- `medicines_family_id_idx`: Index on family_id for family filtering
- `medicines_id_idx`: Index on id for medicine lookup
- `inventory_transactions_medicine_idx`: Index on medicine_id for transaction history
- `medicines_quantity_idx`: Index on quantity for low stock queries

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                       |
| ---- | --------------------- | --------------------------------- |
| 400  | Bad Request           | Invalid quantity data             |
| 401  | Unauthorized          | User not authenticated            |
| 403  | Forbidden             | Insufficient permissions          |
| 404  | Not Found             | Medicine not found                |
| 409  | Conflict              | Insufficient quantity for removal |
| 422  | Unprocessable Entity  | Validation failed                 |
| 500  | Internal Server Error | Database error                    |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_QUANTITY",
    "message": "Not enough medicine available",
    "details": "Current quantity: 5, Requested removal: 10"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (6 queries max)
- **Caching**: Medicine data caching
- **Optimization**: Indexes on family_id and medicine_id

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền, medicine tồn tại với quantity > 0
2. **Steps**:
   - Select medicine: "Paracetamol 500mg"
   - Action: REMOVE
   - Quantity: 5
   - Reason: "Used for headache"
   - Click Update Quantity
3. **Expected Result**:
   - Quantity được cập nhật thành công
   - Transaction được tạo
   - Inventory statistics được cập nhật

### **Error Cases**

1. **Invalid Input**:
   - Quantity <= 0
   - Action type không hợp lệ
   - Reason quá dài
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
3. **Business Logic**:
   - Medicine không tồn tại
   - Insufficient quantity for removal
   - Negative quantity result
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Quantity = 1 (minimum)
   - Quantity = 999999 (maximum)
   - Remove all quantity (quantity = 0)
2. **Concurrent Access**:
   - 2 users cùng update quantity
   - Race conditions
3. **Large Data**:
   - Reason với 200 characters
   - Medicine với large quantity
4. **Special Characters**:
   - Reason với Unicode characters
   - Medicine name với special chars

### **Performance Tests**

1. **Load Testing**: 100 concurrent quantity updates
2. **Stress Testing**: 500 concurrent quantity updates
3. **Endurance Testing**: Continuous updates for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access control
- Family context validation

### **Data Validation**

- Quantity format validation
- Action type validation
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Transaction tracking
- Audit logging
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. Medicine phải tồn tại trong family
2. Quantity phải > 0 cho ADD action
3. Quantity phải <= current quantity cho REMOVE action
4. Quantity phải >= 0 cho SET action
5. Reason tối đa 200 characters

### **Business Logic**

1. Validate user permissions
2. Check medicine existence
3. Calculate new quantity
4. Update medicine record
5. Create transaction log
6. Check low stock status

### **Constraints**

1. Quantity cannot be negative
2. Cannot remove more than available
3. Family context required
4. Transaction tracking required

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-010: Add Medicine to Inventory (medicine phải tồn tại)

### **Dependencies**

- UC-012: Remove Expired Medicine (để remove expired)
- UC-015: Schedule Medication (để track usage)

### **Related Features**

- Inventory Management: View, track medicines
- Transaction History: View quantity changes
- Low Stock Alerts: Monitor inventory levels
- Usage Analytics: Track medicine consumption

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Implement quantity validation logic
- Create comprehensive transaction log
- Update inventory statistics
- Send low stock notifications

### **Future Enhancements**

- Barcode scanning for quantity updates
- Bulk quantity updates
- Automatic usage tracking
- Advanced inventory analytics

### **Known Limitations**

- Không có barcode scanning
- Không có bulk updates
- Không có automatic usage tracking
- Không có advanced analytics

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
