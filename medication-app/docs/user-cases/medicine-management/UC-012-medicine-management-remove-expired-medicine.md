# UC-012: Remove Expired Medicine

## 📋 Overview

### **UC-ID**: UC-012

### **Title**: Remove Expired Medicine

### **Actor**: Family Member (ADMIN/OWNER/MEMBER role)

### **Precondition**: User đã đăng nhập, có family context, và medicine đã hết hạn

### **Postcondition**: Expired medicine được xóa khỏi inventory và disposal record được tạo

### **Priority**: Medium

### **Business Value**: Đ<PERSON><PERSON> bảo an toàn bằng cách loại bỏ thuốc hết hạn và duy trì inventory chính xác

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Medicine Management Screen / Expired Medicines / Inventory Management
- **User Actions**:
  1. Navigate to Medicine Management
  2. Click "Expired Medicines" tab
  3. Select expired medicine từ list
  4. Click "Remove Expired" button
  5. Confirm disposal method (TRASH, RETURN, DONATE)
  6. Nhập disposal notes (optional)
  7. Click "Confirm Removal" button
  8. Confirm final removal
- **UI Elements**:
  - Expired medicines list
  - Medicine details display
  - Expiry date warning
  - Disposal method dropdown
  - Disposal notes textarea
  - Confirm Removal button
  - Cancel button
  - Confirmation dialog
- **Validation**:
  - Medicine must be expired
  - Disposal method required
  - Notes max 500 characters
  - Confirmation required

### **Step 2: API Call Details**

- **Endpoint**: `DELETE /api/families/{familyId}/medicines/{medicineId}/expired`
- **Method**: DELETE
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "disposalMethod": "TRASH",
    "disposalNotes": "Disposed expired paracetamol safely"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `MedicineController.removeExpiredMedicine()`
- **Service Layer**: `MedicineService.removeExpiredMedicine()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Medicine exists và belongs to family
  - Medicine is actually expired
- **Business Rules**:
  - User phải là family member
  - Medicine phải tồn tại trong family
  - Medicine phải đã hết hạn
  - Create disposal record
  - Soft delete medicine record
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `MedicineRepository`, `MedicineDisposalRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Get expired medicine details
  SELECT id, quantity, medicine_type_id, batch_number, expiry_date
  FROM medicines
  WHERE id = ? AND family_id = ? AND expiry_date <= NOW() AND deleted_at IS NULL;

  -- Create disposal record
  INSERT INTO medicine_disposals (family_id, medicine_id, disposal_method, disposal_notes, disposed_by, disposed_at, created_at)
  VALUES (?, ?, ?, ?, ?, NOW(), NOW())
  RETURNING id;

  -- Soft delete medicine record
  UPDATE medicines
  SET deleted_at = NOW(), updated_at = NOW()
  WHERE id = ? AND family_id = ?;

  -- Create inventory transaction
  INSERT INTO inventory_transactions (family_id, medicine_id, transaction_type, quantity, previous_quantity, new_quantity, performed_by, reason, created_at)
  VALUES (?, ?, 'REMOVE_EXPIRED', ?, ?, 0, ?, ?, NOW());

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'REMOVE_EXPIRED_MEDICINE', 'MEDICINE', ?, ?, NOW());

  -- Update family statistics
  UPDATE families
  SET total_medicines = total_medicines - ?, updated_at = NOW()
  WHERE id = ?;
  ```

- **Tables**: `medicines`, `medicine_disposals`, `family_members`, `inventory_transactions`, `audit_logs`, `families`
- **Constraints**:
  - Medicine must be expired
  - Soft delete only
  - Disposal tracking required

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Expired medicine removed successfully",
    "data": {
      "medicine": {
        "id": 456,
        "medicineType": {
          "id": 123,
          "name": "Paracetamol 500mg",
          "category": "TABLET"
        },
        "quantity": 15,
        "batchNumber": "BATCH2024001",
        "expiryDate": "2024-11-30",
        "removedAt": "2024-12-01T10:00:00Z"
      },
      "disposal": {
        "id": 789,
        "method": "TRASH",
        "notes": "Disposed expired paracetamol safely",
        "disposedAt": "2024-12-01T10:00:00Z"
      },
      "inventory": {
        "totalMedicines": 45,
        "expiredMedicines": 2,
        "expiringSoon": 5
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Remove medicine from expired list
  - Update inventory statistics
  - Update expired medicines count
- **Navigation**: Stay on Expired Medicines Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
DELETE /api/families/{familyId}/medicines/{medicineId}/expired
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "disposalMethod": "enum (required: TRASH, RETURN, DONATE)",
  "disposalNotes": "string (optional, max 500 chars)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "medicine": {
      "id": "number",
      "medicineType": {
        "id": "number",
        "name": "string",
        "category": "string"
      },
      "quantity": "number",
      "batchNumber": "string",
      "expiryDate": "string (YYYY-MM-DD)",
      "removedAt": "string (ISO 8601)"
    },
    "disposal": {
      "id": "number",
      "method": "string",
      "notes": "string",
      "disposedAt": "string (ISO 8601)"
    },
    "inventory": {
      "totalMedicines": "number",
      "expiredMedicines": "number",
      "expiringSoon": "number"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Soft delete medicine record
UPDATE medicines
SET deleted_at = NOW(), updated_at = NOW()
WHERE id = ? AND family_id = ?;
```

#### **Related Queries**

```sql
-- Get expired medicine details
SELECT id, quantity, medicine_type_id, batch_number, expiry_date
FROM medicines
WHERE id = ? AND family_id = ? AND expiry_date <= NOW() AND deleted_at IS NULL;

-- Create disposal record
INSERT INTO medicine_disposals (family_id, medicine_id, disposal_method, disposal_notes, disposed_by, disposed_at, created_at)
VALUES (?, ?, ?, ?, ?, NOW(), NOW())
RETURNING id;

-- Create inventory transaction
INSERT INTO inventory_transactions (family_id, medicine_id, transaction_type, quantity, previous_quantity, new_quantity, performed_by, reason, created_at)
VALUES (?, ?, 'REMOVE_EXPIRED', ?, ?, 0, ?, ?, NOW());
```

#### **Indexes Used**

- `medicines_family_id_idx`: Index on family_id for family filtering
- `medicines_expiry_date_idx`: Index on expiry_date for expired queries
- `medicines_deleted_at_idx`: Index on deleted_at for soft delete
- `medicine_disposals_medicine_idx`: Index on medicine_id for disposal history

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description              |
| ---- | --------------------- | ------------------------ |
| 400  | Bad Request           | Invalid disposal data    |
| 401  | Unauthorized          | User not authenticated   |
| 403  | Forbidden             | Insufficient permissions |
| 404  | Not Found             | Medicine not found       |
| 409  | Conflict              | Medicine not expired     |
| 422  | Unprocessable Entity  | Validation failed        |
| 500  | Internal Server Error | Database error           |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "MEDICINE_NOT_EXPIRED",
    "message": "Medicine is not expired",
    "details": "Expiry date: 2025-06-01, Current date: 2024-12-01"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (6 queries max)
- **Caching**: Expired medicines list caching
- **Optimization**: Indexes on expiry_date and family_id

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền, medicine đã hết hạn
2. **Steps**:
   - Select expired medicine: "Paracetamol 500mg"
   - Disposal method: TRASH
   - Notes: "Disposed safely"
   - Click Confirm Removal
3. **Expected Result**:
   - Medicine được xóa thành công
   - Disposal record được tạo
   - Inventory statistics được cập nhật

### **Error Cases**

1. **Invalid Input**:
   - Disposal method không hợp lệ
   - Notes quá dài
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
3. **Business Logic**:
   - Medicine không tồn tại
   - Medicine chưa hết hạn
   - Medicine đã được xóa
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Medicine expired today
   - Medicine expired 1 year ago
   - Notes với 500 characters (max)
2. **Concurrent Access**:
   - 2 users cùng remove expired medicine
   - Race conditions
3. **Large Data**:
   - Family với nhiều expired medicines
   - Multiple disposal records
4. **Special Characters**:
   - Notes với Unicode characters
   - Medicine name với special chars

### **Performance Tests**

1. **Load Testing**: 50 concurrent removals
2. **Stress Testing**: 200 concurrent removals
3. **Endurance Testing**: Continuous removals for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access control
- Family context validation

### **Data Validation**

- Expiry date validation
- Disposal method validation
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Soft delete protection
- Disposal tracking
- Audit logging

## 📊 Business Rules

### **Validation Rules**

1. Medicine phải tồn tại trong family
2. Medicine phải đã hết hạn (expiry_date <= today)
3. Disposal method phải hợp lệ
4. Notes tối đa 500 characters
5. Confirmation required

### **Business Logic**

1. Validate user permissions
2. Check medicine expiry
3. Create disposal record
4. Soft delete medicine
5. Update inventory statistics
6. Create audit log

### **Constraints**

1. Only expired medicines can be removed
2. Soft delete only (no hard delete)
3. Disposal tracking required
4. Family context required

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-010: Add Medicine to Inventory (medicine phải tồn tại)

### **Dependencies**

- UC-011: Update Medicine Quantity (để track usage)
- UC-017: Set Medication Reminder (để prevent expiry)

### **Related Features**

- Expiry Management: Monitor expiry dates
- Disposal Tracking: Track disposal methods
- Inventory Cleanup: Maintain clean inventory
- Safety Compliance: Ensure safe disposal

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Implement soft delete để preserve data
- Create comprehensive disposal tracking
- Update inventory statistics
- Send disposal notifications

### **Future Enhancements**

- Bulk expired medicine removal
- Disposal method templates
- Disposal analytics
- Safety compliance reporting

### **Known Limitations**

- Không có bulk removal
- Không có disposal templates
- Không có disposal analytics
- Không có compliance reporting

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
