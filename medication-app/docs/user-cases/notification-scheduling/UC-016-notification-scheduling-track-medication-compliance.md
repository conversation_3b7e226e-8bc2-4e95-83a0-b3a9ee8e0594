# UC-016: Track Medication Compliance

## 📋 Overview

### **UC-ID**: UC-016

### **Title**: Track Medication Compliance

### **Actor**: Family Member (ADMIN/OWNER/MEMBER role)

### **Precondition**: User đã đăng nhập, có family context, medication schedule đã tồn tại

### **Postcondition**: Medication compliance được track và update, compliance statistics được cập nhật

### **Priority**: High

### **Business Value**: <PERSON> dõi việc tuân thủ uống thuốc để đảm bảo hiệu quả điều trị và báo cáo cho bác sĩ

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Medication Schedule Screen / Compliance Tracking
- **User Actions**:
  1. Navigate to Medication Schedule
  2. View pending medication instances
  3. Click "Take Medication" button cho specific instance
  4. Confirm medication taken
  5. Select compliance status (TAKEN, SKIPPED, MISSED)
  6. Add notes (optional)
  7. Click "Update Compliance" button
  8. View updated compliance statistics
- **UI Elements**:
  - Medication schedule list
  - Pending instances
  - Take Medication button
  - Compliance status selector
  - Notes textarea
  - Update Compliance button
  - Compliance statistics
  - Progress indicators
- **Validation**:
  - Instance must be pending
  - Status selection required
  - Notes max 200 characters
  - Time validation (within 2 hours of scheduled time)

### **Step 2: API Call Details**

- **Endpoint**: `PUT /api/families/{familyId}/schedules/{scheduleId}/instances/{instanceId}/compliance`
- **Method**: PUT
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "status": "TAKEN",
    "notes": "Took with breakfast",
    "takenAt": "2024-12-01T08:15:00Z"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `MedicationScheduleController.updateCompliance()`
- **Service Layer**: `MedicationScheduleService.updateCompliance()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Schedule instance exists
  - Instance status validation
  - Time validation
- **Business Rules**:
  - User phải là family member
  - Instance phải tồn tại và pending
  - Status phải hợp lệ
  - Time phải trong valid range
  - Update instance status
  - Update schedule statistics
  - Create compliance log
  - Update family compliance metrics

### **Step 4: Database Operations**

- **Repository**: `MedicationScheduleInstanceRepository`, `MedicationScheduleRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Validate schedule instance exists
  SELECT msi.id, msi.scheduled_date, msi.scheduled_time, msi.status, ms.schedule_type
  FROM medication_schedule_instances msi
  JOIN medication_schedules ms ON msi.schedule_id = ms.id
  WHERE msi.id = ? AND ms.family_id = ? AND msi.deleted_at IS NULL;

  -- Update instance compliance
  UPDATE medication_schedule_instances
  SET status = ?, notes = ?, taken_at = ?, updated_at = NOW()
  WHERE id = ? AND status = 'PENDING';

  -- Create compliance log
  INSERT INTO medication_usage_logs (schedule_instance_id, user_id, family_id, action, status, notes, created_at)
  VALUES (?, ?, ?, 'COMPLIANCE_UPDATE', ?, ?, NOW());

  -- Update schedule statistics
  UPDATE medication_schedules
  SET completed_instances = completed_instances + 1,
      compliance_rate = (completed_instances + 1) * 100.0 / total_instances,
      updated_at = NOW()
  WHERE id = ?;

  -- Update family compliance metrics
  UPDATE families
  SET total_compliance_rate = (
    SELECT AVG(ms.compliance_rate)
    FROM medication_schedules ms
    WHERE ms.family_id = ? AND ms.status = 'ACTIVE'
  ),
  updated_at = NOW()
  WHERE id = ?;

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'UPDATE_COMPLIANCE', 'MEDICATION_SCHEDULE_INSTANCE', ?, ?, NOW());
  ```

- **Tables**: `medication_schedule_instances`, `medication_usage_logs`, `medication_schedules`, `families`, `audit_logs`
- **Constraints**:
  - Instance exists and pending
  - Valid status transition
  - Time validation
  - Statistics update

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Compliance updated successfully",
    "data": {
      "instance": {
        "id": 1234,
        "scheduledDate": "2024-12-01",
        "scheduledTime": "08:00",
        "status": "TAKEN",
        "takenAt": "2024-12-01T08:15:00Z",
        "notes": "Took with breakfast"
      },
      "schedule": {
        "id": 789,
        "complianceRate": 85.7,
        "completedInstances": 6,
        "totalInstances": 7,
        "missedInstances": 1
      },
      "family": {
        "totalComplianceRate": 78.5,
        "activeSchedules": 3
      },
      "nextSteps": {
        "viewSchedule": true,
        "setReminders": true,
        "generateReport": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Update instance status
  - Refresh compliance statistics
  - Update progress indicators
  - Show next steps
- **Navigation**: Stay on Schedule Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
PUT /api/families/{familyId}/schedules/{scheduleId}/instances/{instanceId}/compliance
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "status": "enum (required: TAKEN, SKIPPED, MISSED)",
  "notes": "string (optional, max 200 chars)",
  "takenAt": "datetime (optional, ISO 8601 format)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "instance": {
      "id": "number",
      "scheduledDate": "string (YYYY-MM-DD)",
      "scheduledTime": "string (HH:MM)",
      "status": "string (TAKEN, SKIPPED, MISSED)",
      "takenAt": "string (ISO 8601)",
      "notes": "string"
    },
    "schedule": {
      "id": "number",
      "complianceRate": "number (0-100)",
      "completedInstances": "number",
      "totalInstances": "number",
      "missedInstances": "number"
    },
    "family": {
      "totalComplianceRate": "number (0-100)",
      "activeSchedules": "number"
    },
    "nextSteps": {
      "viewSchedule": "boolean",
      "setReminders": "boolean",
      "generateReport": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Update instance compliance
UPDATE medication_schedule_instances
SET status = ?, notes = ?, taken_at = ?, updated_at = NOW()
WHERE id = ? AND status = 'PENDING';
```

#### **Related Queries**

```sql
-- Validate instance
SELECT msi.id, msi.scheduled_date, msi.scheduled_time, msi.status, ms.schedule_type
FROM medication_schedule_instances msi
JOIN medication_schedules ms ON msi.schedule_id = ms.id
WHERE msi.id = ? AND ms.family_id = ? AND msi.deleted_at IS NULL;

-- Update schedule statistics
UPDATE medication_schedules
SET completed_instances = completed_instances + 1,
    compliance_rate = (completed_instances + 1) * 100.0 / total_instances,
    updated_at = NOW()
WHERE id = ?;

-- Update family metrics
UPDATE families
SET total_compliance_rate = (
  SELECT AVG(ms.compliance_rate)
  FROM medication_schedules ms
  WHERE ms.family_id = ? AND ms.status = 'ACTIVE'
),
updated_at = NOW()
WHERE id = ?;
```

#### **Indexes Used**

- `medication_schedule_instances_id_idx`: Primary key index
- `medication_schedule_instances_status_idx`: Index on status for pending instances
- `medication_schedules_id_idx`: Primary key index
- `medication_usage_logs_instance_idx`: Index on schedule_instance_id
- `audit_logs_entity_idx`: Index on entity_type, entity_id

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                 |
| ---- | --------------------- | --------------------------- |
| 400  | Bad Request           | Invalid compliance data     |
| 401  | Unauthorized          | User not authenticated      |
| 403  | Forbidden             | Insufficient permissions    |
| 404  | Not Found             | Schedule instance not found |
| 409  | Conflict              | Instance already processed  |
| 422  | Unprocessable Entity  | Validation failed           |
| 500  | Internal Server Error | Database error              |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INSTANCE_ALREADY_PROCESSED",
    "message": "Instance has already been processed",
    "details": "Cannot update compliance for completed instance"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (6 queries max)
- **Caching**: Compliance statistics caching
- **Optimization**: Batch statistics updates

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền, schedule instance pending
2. **Steps**:
   - Instance: "Paracetamol 500mg at 08:00"
   - Status: TAKEN
   - Notes: "Took with breakfast"
   - Click Update Compliance
3. **Expected Result**:
   - Instance status updated
   - Compliance rate calculated
   - Statistics updated
   - Success message shown

### **Error Cases**

1. **Invalid Input**:
   - Invalid status
   - Notes quá dài
   - Invalid time format
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
3. **Business Logic**:
   - Instance không tồn tại
   - Instance đã processed
   - Time validation failed
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Notes với 200 characters (max)
   - Time exactly at scheduled time
   - Time 2 hours after scheduled time
2. **Concurrent Access**:
   - 2 users cùng update compliance
   - Race conditions
3. **Large Data**:
   - Many instances in schedule
   - High compliance rates
4. **Special Characters**:
   - Notes với Unicode characters
   - Special characters in notes

### **Performance Tests**

1. **Load Testing**: 50 concurrent compliance updates
2. **Stress Testing**: 200 concurrent compliance updates
3. **Endurance Testing**: Continuous updates for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access control
- Family context validation

### **Data Validation**

- Status validation
- Time validation
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Compliance privacy protection
- Audit logging
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. Instance phải tồn tại và pending
2. Status phải hợp lệ (TAKEN, SKIPPED, MISSED)
3. Notes tối đa 200 characters
4. Time phải trong valid range (2 hours after scheduled)

### **Business Logic**

1. Validate user permissions
2. Check instance existence and status
3. Update instance compliance
4. Calculate compliance statistics
5. Update family metrics
6. Create audit log

### **Constraints**

1. Instance must be pending
2. Valid status transition
3. Time validation
4. Statistics calculation
5. Family context required

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-015: Schedule Medication (schedule phải tồn tại)

### **Dependencies**

- UC-017: Set Medication Reminder (để set reminders)
- UC-018: Generate Compliance Report (để generate reports)

### **Related Features**

- Compliance Tracking: View compliance history
- Reminder System: Set medication reminders
- Report Generation: Generate compliance reports
- Analytics Dashboard: View compliance statistics

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Calculate compliance rate accurately
- Update family metrics efficiently
- Create comprehensive audit log
- Handle concurrent updates

### **Future Enhancements**

- Advanced compliance analytics
- Predictive compliance modeling
- Gamification features
- Integration with health apps

### **Known Limitations**

- Không có advanced analytics
- Không có predictive modeling
- Không có gamification
- Không có health app integration

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
