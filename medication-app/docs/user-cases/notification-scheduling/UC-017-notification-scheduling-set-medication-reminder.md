# UC-017: Set Medication Reminder

## 📋 Overview

### **UC-ID**: UC-017

### **Title**: Set Medication Reminder

### **Actor**: Family Member (ADMIN/OWNER/MEMBER role)

### **Precondition**: User đã đăng nhập, có family context, medication schedule đã tồn tại

### **Postcondition**: Medication reminder được tạo và có thể được gửi qua multiple channels

### **Priority**: High

### **Business Value**: <PERSON><PERSON>m bảo người dùng không quên uống thuốc đúng giờ thông qua hệ thống nhắc nhở đa kênh

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Medication Schedule Screen / Reminder Settings
- **User Actions**:
  1. Navigate to Medication Schedule
  2. Click "Set Reminder" button cho specific schedule
  3. Select reminder channels (PUSH, SMS, EMAIL, IN_APP)
  4. Set reminder timing (BEFORE_15MIN, BEFORE_30MIN, BEFORE_1HOUR, EXACT_TIME)
  5. Configure reminder frequency (EVERY_TIME, DAILY_SUMMARY, WEEKLY_SUMMARY)
  6. Set quiet hours (optional)
  7. Add custom message (optional)
  8. Click "Save Reminder" button
  9. Confirm reminder settings
- **UI Elements**:
  - Schedule list
  - Set Reminder button
  - Channel checkboxes
  - Timing dropdown
  - Frequency selector
  - Quiet hours picker
  - Custom message textarea
  - Save Reminder button
  - Cancel button
  - Reminder preview
- **Validation**:
  - At least one channel selected
  - Timing selection required
  - Frequency selection required
  - Custom message max 100 characters
  - Quiet hours validation

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/{familyId}/schedules/{scheduleId}/reminders`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "channels": ["PUSH", "SMS"],
    "timing": "BEFORE_15MIN",
    "frequency": "EVERY_TIME",
    "quietHoursStart": "22:00",
    "quietHoursEnd": "07:00",
    "customMessage": "Time to take your medicine!",
    "isActive": true
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `MedicationReminderController.createReminder()`
- **Service Layer**: `MedicationReminderService.createReminder()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Schedule exists
  - Reminder settings validation
- **Business Rules**:
  - User phải là family member
  - Schedule phải tồn tại
  - At least one channel required
  - Valid timing and frequency
  - Create reminder settings
  - Schedule reminder jobs
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `MedicationReminderRepository`, `MedicationScheduleRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Validate schedule exists
  SELECT ms.id, ms.schedule_type, ms.times, ms.days_of_week, ms.start_date, ms.end_date
  FROM medication_schedules ms
  WHERE ms.id = ? AND ms.family_id = ? AND ms.status = 'ACTIVE';

  -- Check if reminder already exists
  SELECT COUNT(*) FROM medication_reminders
  WHERE schedule_id = ? AND status = 'ACTIVE';

  -- Create medication reminder
  INSERT INTO medication_reminders (schedule_id, user_id, family_id, channels, timing, frequency, quiet_hours_start, quiet_hours_end, custom_message, is_active, status, created_at, updated_at)
  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'ACTIVE', NOW(), NOW())
  RETURNING id;

  -- Schedule reminder jobs for existing instances
  INSERT INTO notification_queue (reminder_id, schedule_instance_id, user_id, family_id, channel, scheduled_time, message, status, created_at)
  SELECT ?, msi.id, ?, ?, ?,
         CASE
           WHEN ? = 'BEFORE_15MIN' THEN msi.scheduled_time - INTERVAL '15 minutes'
           WHEN ? = 'BEFORE_30MIN' THEN msi.scheduled_time - INTERVAL '30 minutes'
           WHEN ? = 'BEFORE_1HOUR' THEN msi.scheduled_time - INTERVAL '1 hour'
           ELSE msi.scheduled_time
         END,
         COALESCE(?, 'Time to take your medication'),
         'PENDING', NOW()
  FROM medication_schedule_instances msi
  WHERE msi.schedule_id = ? AND msi.status = 'PENDING';

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'CREATE_REMINDER', 'MEDICATION_REMINDER', ?, ?, NOW());
  ```

- **Tables**: `medication_reminders`, `notification_queue`, `medication_schedules`, `audit_logs`
- **Constraints**:
  - Schedule exists
  - Valid reminder settings
  - Unique reminder per schedule
  - Notification queue generation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Medication reminder created successfully",
    "data": {
      "reminder": {
        "id": 567,
        "schedule": {
          "id": 789,
          "medicineType": "Paracetamol 500mg",
          "scheduleType": "DAILY"
        },
        "channels": ["PUSH", "SMS"],
        "timing": "BEFORE_15MIN",
        "frequency": "EVERY_TIME",
        "quietHoursStart": "22:00",
        "quietHoursEnd": "07:00",
        "customMessage": "Time to take your medicine!",
        "isActive": true,
        "status": "ACTIVE",
        "createdAt": "2024-12-01T10:00:00Z"
      },
      "notifications": {
        "scheduled": 21,
        "pending": 21,
        "sent": 0
      },
      "nextSteps": {
        "testReminder": true,
        "viewSchedule": true,
        "editSettings": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Display reminder settings
  - Show notification count
  - Update schedule status
- **Navigation**: Redirect to Reminder Details Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/{familyId}/schedules/{scheduleId}/reminders
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "channels": "array (required: PUSH, SMS, EMAIL, IN_APP)",
  "timing": "enum (required: BEFORE_15MIN, BEFORE_30MIN, BEFORE_1HOUR, EXACT_TIME)",
  "frequency": "enum (required: EVERY_TIME, DAILY_SUMMARY, WEEKLY_SUMMARY)",
  "quietHoursStart": "string (optional, format: HH:MM)",
  "quietHoursEnd": "string (optional, format: HH:MM)",
  "customMessage": "string (optional, max 100 chars)",
  "isActive": "boolean (default: true)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "reminder": {
      "id": "number",
      "schedule": {
        "id": "number",
        "medicineType": "string",
        "scheduleType": "string"
      },
      "channels": "array",
      "timing": "string",
      "frequency": "string",
      "quietHoursStart": "string",
      "quietHoursEnd": "string",
      "customMessage": "string",
      "isActive": "boolean",
      "status": "string (ACTIVE)",
      "createdAt": "string (ISO 8601)"
    },
    "notifications": {
      "scheduled": "number",
      "pending": "number",
      "sent": "number"
    },
    "nextSteps": {
      "testReminder": "boolean",
      "viewSchedule": "boolean",
      "editSettings": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Create medication reminder
INSERT INTO medication_reminders (schedule_id, user_id, family_id, channels, timing, frequency, quiet_hours_start, quiet_hours_end, custom_message, is_active, status, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'ACTIVE', NOW(), NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Validate schedule
SELECT ms.id, ms.schedule_type, ms.times, ms.days_of_week, ms.start_date, ms.end_date
FROM medication_schedules ms
WHERE ms.id = ? AND ms.family_id = ? AND ms.status = 'ACTIVE';

-- Schedule notification jobs
INSERT INTO notification_queue (reminder_id, schedule_instance_id, user_id, family_id, channel, scheduled_time, message, status, created_at)
SELECT ?, msi.id, ?, ?, ?,
       CASE
         WHEN ? = 'BEFORE_15MIN' THEN msi.scheduled_time - INTERVAL '15 minutes'
         WHEN ? = 'BEFORE_30MIN' THEN msi.scheduled_time - INTERVAL '30 minutes'
         WHEN ? = 'BEFORE_1HOUR' THEN msi.scheduled_time - INTERVAL '1 hour'
         ELSE msi.scheduled_time
       END,
       COALESCE(?, 'Time to take your medication'),
       'PENDING', NOW()
FROM medication_schedule_instances msi
WHERE msi.schedule_id = ? AND msi.status = 'PENDING';
```

#### **Indexes Used**

- `medication_reminders_schedule_idx`: Index on schedule_id
- `medication_reminders_status_idx`: Index on status for active reminders
- `notification_queue_reminder_idx`: Index on reminder_id
- `notification_queue_scheduled_time_idx`: Index on scheduled_time for processing

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description              |
| ---- | --------------------- | ------------------------ |
| 400  | Bad Request           | Invalid reminder data    |
| 401  | Unauthorized          | User not authenticated   |
| 403  | Forbidden             | Insufficient permissions |
| 404  | Not Found             | Schedule not found       |
| 409  | Conflict              | Reminder already exists  |
| 422  | Unprocessable Entity  | Validation failed        |
| 500  | Internal Server Error | Database error           |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "REMINDER_ALREADY_EXISTS",
    "message": "Reminder already exists for this schedule",
    "details": "Please update existing reminder or delete it first"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 3 seconds
- **Database Load**: Heavy (6 queries max)
- **Caching**: Reminder settings caching
- **Optimization**: Batch notification scheduling

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền, schedule tồn tại
2. **Steps**:
   - Schedule: "Paracetamol 500mg daily"
   - Channels: ["PUSH", "SMS"]
   - Timing: BEFORE_15MIN
   - Frequency: EVERY_TIME
   - Click Save Reminder
3. **Expected Result**:
   - Reminder được tạo thành công
   - 21 notifications được schedule
   - Next steps được hiển thị

### **Error Cases**

1. **Invalid Input**:
   - No channels selected
   - Invalid timing
   - Invalid frequency
   - Custom message quá dài
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
3. **Business Logic**:
   - Schedule không tồn tại
   - Reminder đã tồn tại
   - Invalid quiet hours
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Custom message với 100 characters (max)
   - Quiet hours validation
   - All channels selected
2. **Concurrent Access**:
   - 2 users cùng tạo reminder
   - Race conditions
3. **Large Data**:
   - Long schedule duration
   - Multiple reminders per schedule
4. **Special Characters**:
   - Custom message với Unicode characters
   - Special characters in message

### **Performance Tests**

1. **Load Testing**: 20 concurrent reminder creations
2. **Stress Testing**: 50 concurrent reminder creations
3. **Endurance Testing**: Continuous creations for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access control
- Family context validation

### **Data Validation**

- Channel validation
- Timing validation
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Reminder privacy protection
- Audit logging
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. Schedule phải tồn tại
2. At least one channel required
3. Timing phải hợp lệ
4. Frequency phải hợp lệ
5. Custom message tối đa 100 characters

### **Business Logic**

1. Validate user permissions
2. Check schedule existence
3. Prevent duplicate reminders
4. Schedule notification jobs
5. Create audit log

### **Constraints**

1. One active reminder per schedule
2. Valid reminder settings
3. Notification queue generation
4. Family context required

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-015: Schedule Medication (schedule phải tồn tại)

### **Dependencies**

- UC-016: Track Medication Compliance (để track compliance)
- UC-018: Generate Compliance Report (để generate reports)

### **Related Features**

- Reminder Management: View, edit reminders
- Notification System: Send notifications
- Compliance Tracking: Track medication adherence
- Schedule Analytics: View reminder statistics

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Schedule notification jobs efficiently
- Implement batch notification scheduling
- Create comprehensive audit log
- Handle quiet hours logic

### **Future Enhancements**

- Smart reminder timing
- Personalized messages
- Advanced notification channels
- Reminder analytics

### **Known Limitations**

- Không có smart timing
- Không có personalized messages
- Không có advanced channels
- Không có reminder analytics

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
