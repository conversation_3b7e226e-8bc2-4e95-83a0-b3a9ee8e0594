# UC-013: Create Prescription

## 📋 Overview

### **UC-ID**: UC-013

### **Title**: Create Prescription

### **Actor**: Family Member (ADMIN/OWNER/MEMBER role)

### **Precondition**: User đã đăng nhập, có family context, và có quyền tạo prescription

### **Postcondition**: Prescription mới được tạo với thông tin chi tiết và có thể được assign medicines

### **Priority**: High

### **Business Value**: Cho phép tạo đơn thuốc để quản lý việc điều trị và theo dõi lịch uống thuốc cho thành viên gia đình

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Prescription Management Screen / Create Prescription
- **User Actions**:
  1. Navigate to Prescription Management
  2. Click "Create New Prescription" button
  3. N<PERSON><PERSON>p prescription name
  4. <PERSON>ọn patient từ family members
  5. <PERSON><PERSON><PERSON>p doctor name
  6. <PERSON><PERSON><PERSON>p diagnosis/condition
  7. <PERSON><PERSON><PERSON> start date
  8. Chọn end date (optional)
  9. <PERSON><PERSON><PERSON><PERSON> notes (optional)
  10. <PERSON>lick "Create Prescription" button
- **UI Elements**:
  - Prescription name input field
  - Patient dropdown (family members)
  - Doctor name input field
  - Diagnosis textarea
  - Start date picker
  - End date picker (optional)
  - Notes textarea (optional)
  - Create Prescription button
  - Cancel button
  - Form validation messages
- **Validation**:
  - Prescription name required
  - Patient selection required
  - Doctor name required
  - Start date required
  - End date >= start date
  - Notes max 500 characters

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/{familyId}/prescriptions`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "name": "Headache Treatment",
    "patientId": 789,
    "doctorName": "Dr. Nguyễn Văn A",
    "diagnosis": "Tension headache",
    "startDate": "2024-12-01",
    "endDate": "2024-12-07",
    "notes": "Take with food, avoid alcohol"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `PrescriptionController.createPrescription()`
- **Service Layer**: `PrescriptionService.createPrescription()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Patient belongs to family
  - Date validation
- **Business Rules**:
  - User phải là family member
  - Patient phải thuộc family
  - Start date phải >= today
  - End date phải >= start date
  - Create prescription record
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `PrescriptionRepository`, `FamilyMemberRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Validate patient belongs to family
  SELECT id, full_name FROM family_members fm
  JOIN users u ON fm.user_id = u.id
  WHERE fm.family_id = ? AND fm.id = ? AND fm.status = 'ACTIVE';

  -- Create prescription record
  INSERT INTO prescriptions (family_id, name, patient_id, doctor_name, diagnosis, start_date, end_date, notes, status, created_by, created_at, updated_at)
  VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'ACTIVE', ?, NOW(), NOW())
  RETURNING id;

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'CREATE_PRESCRIPTION', 'PRESCRIPTION', ?, ?, NOW());

  -- Update family statistics
  UPDATE families
  SET total_prescriptions = total_prescriptions + 1, updated_at = NOW()
  WHERE id = ?;
  ```

- **Tables**: `prescriptions`, `family_members`, `users`, `audit_logs`, `families`
- **Constraints**:
  - Patient belongs to family
  - Date validation
  - Family context validation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Prescription created successfully",
    "data": {
      "prescription": {
        "id": 123,
        "name": "Headache Treatment",
        "patient": {
          "id": 789,
          "fullName": "Nguyễn Văn B"
        },
        "doctorName": "Dr. Nguyễn Văn A",
        "diagnosis": "Tension headache",
        "startDate": "2024-12-01",
        "endDate": "2024-12-07",
        "notes": "Take with food, avoid alcohol",
        "status": "ACTIVE",
        "createdAt": "2024-12-01T10:00:00Z"
      },
      "nextSteps": {
        "assignMedicines": true,
        "scheduleMedication": true,
        "setReminders": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Add prescription to list
  - Update prescription count
  - Show next steps guidance
- **Navigation**: Redirect to Prescription Details Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/{familyId}/prescriptions
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "name": "string (required, max 100 chars)",
  "patientId": "number (required, family member ID)",
  "doctorName": "string (required, max 100 chars)",
  "diagnosis": "string (required, max 500 chars)",
  "startDate": "date (required, format: YYYY-MM-DD, >= today)",
  "endDate": "date (optional, format: YYYY-MM-DD, >= startDate)",
  "notes": "string (optional, max 500 chars)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "prescription": {
      "id": "number",
      "name": "string",
      "patient": {
        "id": "number",
        "fullName": "string"
      },
      "doctorName": "string",
      "diagnosis": "string",
      "startDate": "string (YYYY-MM-DD)",
      "endDate": "string (YYYY-MM-DD)",
      "notes": "string",
      "status": "string (ACTIVE)",
      "createdAt": "string (ISO 8601)"
    },
    "nextSteps": {
      "assignMedicines": "boolean",
      "scheduleMedication": "boolean",
      "setReminders": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Create prescription record
INSERT INTO prescriptions (family_id, name, patient_id, doctor_name, diagnosis, start_date, end_date, notes, status, created_by, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'ACTIVE', ?, NOW(), NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Validate patient
SELECT id, full_name FROM family_members fm
JOIN users u ON fm.user_id = u.id
WHERE fm.family_id = ? AND fm.id = ? AND fm.status = 'ACTIVE';

-- Create audit log
INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
VALUES (?, ?, 'CREATE_PRESCRIPTION', 'PRESCRIPTION', ?, ?, NOW());

-- Update family stats
UPDATE families
SET total_prescriptions = total_prescriptions + 1, updated_at = NOW()
WHERE id = ?;
```

#### **Indexes Used**

- `prescriptions_family_id_idx`: Index on family_id for family filtering
- `prescriptions_patient_id_idx`: Index on patient_id for patient lookup
- `prescriptions_status_idx`: Index on status for active prescriptions
- `prescriptions_start_date_idx`: Index on start_date for date filtering

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description               |
| ---- | --------------------- | ------------------------- |
| 400  | Bad Request           | Invalid prescription data |
| 401  | Unauthorized          | User not authenticated    |
| 403  | Forbidden             | Insufficient permissions  |
| 404  | Not Found             | Patient not found         |
| 409  | Conflict              | Invalid date range        |
| 422  | Unprocessable Entity  | Validation failed         |
| 500  | Internal Server Error | Database error            |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INVALID_DATE_RANGE",
    "message": "End date must be after start date",
    "details": "Start date: 2024-12-01, End date: 2024-11-30"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (5 queries max)
- **Caching**: Family members list caching
- **Optimization**: Indexes on family_id and patient_id

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền, patient tồn tại trong family
2. **Steps**:
   - Name: "Headache Treatment"
   - Patient: "Nguyễn Văn B"
   - Doctor: "Dr. Nguyễn Văn A"
   - Diagnosis: "Tension headache"
   - Start date: 2024-12-01
   - End date: 2024-12-07
   - Click Create Prescription
3. **Expected Result**:
   - Prescription được tạo thành công
   - Redirect to prescription details
   - Show next steps guidance

### **Error Cases**

1. **Invalid Input**:
   - Name quá dài
   - Patient không được chọn
   - Doctor name empty
   - Invalid date range
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
3. **Business Logic**:
   - Patient không thuộc family
   - Start date < today
   - End date < start date
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Name với 100 characters (max)
   - Diagnosis với 500 characters (max)
   - Notes với 500 characters (max)
   - Start date = today
2. **Concurrent Access**:
   - 2 users cùng tạo prescription
   - Race conditions
3. **Large Data**:
   - Family với nhiều prescriptions
   - Patient với nhiều prescriptions
4. **Special Characters**:
   - Name với Unicode characters
   - Doctor name với special chars

### **Performance Tests**

1. **Load Testing**: 50 concurrent prescription creations
2. **Stress Testing**: 200 concurrent prescription creations
3. **Endurance Testing**: Continuous creations for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access control
- Family context validation

### **Data Validation**

- Input sanitization
- Date validation
- SQL injection prevention
- XSS prevention

### **Data Protection**

- Family data isolation
- Patient privacy protection
- Audit logging
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. Prescription name phải có và <= 100 characters
2. Patient phải thuộc family
3. Doctor name phải có và <= 100 characters
4. Diagnosis phải có và <= 500 characters
5. Start date phải >= today
6. End date phải >= start date (nếu có)

### **Business Logic**

1. Validate user permissions
2. Check patient belongs to family
3. Validate date ranges
4. Create prescription record
5. Update family statistics
6. Create audit log

### **Constraints**

1. Patient must belong to family
2. Start date >= today
3. End date >= start date
4. Family context required
5. Audit trail required

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-007: Accept Family Invitation (patient phải là family member)

### **Dependencies**

- UC-014: Assign Medicine to Prescription (để add medicines)
- UC-015: Schedule Medication (để schedule)

### **Related Features**

- Prescription Management: View, edit, delete prescriptions
- Medicine Assignment: Assign medicines to prescriptions
- Medication Scheduling: Schedule medication times
- Compliance Tracking: Track medication adherence

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Validate patient belongs to family
- Implement date validation logic
- Create comprehensive audit log
- Update family statistics

### **Future Enhancements**

- Prescription templates
- Bulk prescription creation
- Doctor contact integration
- Prescription analytics

### **Known Limitations**

- Không có prescription templates
- Không có bulk creation
- Không có doctor integration
- Không có prescription analytics

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
