# UC-014: Assign Medicine to Prescription

## 📋 Overview

### **UC-ID**: UC-014

### **Title**: Assign Medicine to Prescription

### **Actor**: Family Member (ADMIN/OWNER/MEMBER role)

### **Precondition**: User đã đăng nhập, có family context, prescription đã tồn tại, và medicine type đã có trong family

### **Postcondition**: Medicine được gán vào prescription với dosage và schedule details

### **Priority**: High

### **Business Value**: Cho phép gán thuốc vào đơn thuốc để quản lý liều lượng và lịch uống thuốc chi tiết

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Prescription Details Screen / Assign Medicine
- **User Actions**:
  1. Navigate to Prescription Details
  2. Click "Assign Medicine" button
  3. Chọn medicine type từ dropdown
  4. Nhập dosage (amount per dose)
  5. Chọn dosage unit (TABLET, CAPSULE, ML, MG, etc.)
  6. Nhập frequency (times per day)
  7. Chọn timing (BEFORE_MEAL, AFTER_MEAL, EMPTY_STOMACH)
  8. Nhập duration (days)
  9. Nhập instructions (optional)
  10. Click "Assign Medicine" button
- **UI Elements**:
  - Medicine type dropdown
  - Dosage input field
  - Dosage unit dropdown
  - Frequency input field
  - Timing dropdown
  - Duration input field
  - Instructions textarea
  - Assign Medicine button
  - Cancel button
  - Form validation messages
- **Validation**:
  - Medicine type required
  - Dosage > 0
  - Frequency > 0
  - Duration > 0
  - Instructions max 500 characters

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/{familyId}/prescriptions/{prescriptionId}/medicines`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "medicineTypeId": 123,
    "dosage": 1,
    "dosageUnit": "TABLET",
    "frequency": 3,
    "timing": "AFTER_MEAL",
    "duration": 7,
    "instructions": "Take with water, avoid dairy products"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `PrescriptionController.assignMedicine()`
- **Service Layer**: `PrescriptionService.assignMedicineToPrescription()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Prescription exists và belongs to family
  - Medicine type exists trong family
- **Business Rules**:
  - User phải là family member
  - Prescription phải tồn tại trong family
  - Medicine type phải tồn tại trong family
  - Dosage và frequency phải hợp lệ
  - Create prescription medicine record
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `PrescriptionRepository`, `PrescriptionMedicineRepository`, `MedicineTypeRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Validate prescription belongs to family
  SELECT id, name, status FROM prescriptions
  WHERE id = ? AND family_id = ? AND deleted_at IS NULL;

  -- Validate medicine type belongs to family
  SELECT id, name, category FROM medicine_types
  WHERE id = ? AND family_id = ? AND deleted_at IS NULL;

  -- Check if medicine already assigned to prescription
  SELECT COUNT(*) FROM prescription_medicines
  WHERE prescription_id = ? AND medicine_type_id = ? AND deleted_at IS NULL;

  -- Create prescription medicine record
  INSERT INTO prescription_medicines (prescription_id, medicine_type_id, dosage, dosage_unit, frequency, timing, duration, instructions, created_at, updated_at)
  VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
  RETURNING id;

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'ASSIGN_MEDICINE_TO_PRESCRIPTION', 'PRESCRIPTION_MEDICINE', ?, ?, NOW());

  -- Update prescription statistics
  UPDATE prescriptions
  SET total_medicines = total_medicines + 1, updated_at = NOW()
  WHERE id = ?;
  ```

- **Tables**: `prescription_medicines`, `prescriptions`, `medicine_types`, `family_members`, `audit_logs`
- **Constraints**:
  - Prescription belongs to family
  - Medicine type belongs to family
  - Unique medicine per prescription
  - Dosage validation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Medicine assigned to prescription successfully",
    "data": {
      "prescriptionMedicine": {
        "id": 456,
        "prescription": {
          "id": 123,
          "name": "Headache Treatment"
        },
        "medicineType": {
          "id": 123,
          "name": "Paracetamol 500mg",
          "category": "TABLET"
        },
        "dosage": 1,
        "dosageUnit": "TABLET",
        "frequency": 3,
        "timing": "AFTER_MEAL",
        "duration": 7,
        "instructions": "Take with water, avoid dairy products",
        "createdAt": "2024-12-01T10:00:00Z"
      },
      "schedule": {
        "totalDoses": 21,
        "dailyDoses": 3,
        "estimatedEndDate": "2024-12-08"
      },
      "nextSteps": {
        "scheduleMedication": true,
        "setReminders": true,
        "checkInventory": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Add medicine to prescription list
  - Update prescription medicine count
  - Show schedule information
- **Navigation**: Stay on Prescription Details Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/{familyId}/prescriptions/{prescriptionId}/medicines
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "medicineTypeId": "number (required, existing medicine type in family)",
  "dosage": "number (required, > 0)",
  "dosageUnit": "enum (required: TABLET, CAPSULE, ML, MG, DROP, PUFF)",
  "frequency": "number (required, > 0)",
  "timing": "enum (required: BEFORE_MEAL, AFTER_MEAL, EMPTY_STOMACH, ANY_TIME)",
  "duration": "number (required, > 0)",
  "instructions": "string (optional, max 500 chars)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "prescriptionMedicine": {
      "id": "number",
      "prescription": {
        "id": "number",
        "name": "string"
      },
      "medicineType": {
        "id": "number",
        "name": "string",
        "category": "string"
      },
      "dosage": "number",
      "dosageUnit": "string",
      "frequency": "number",
      "timing": "string",
      "duration": "number",
      "instructions": "string",
      "createdAt": "string (ISO 8601)"
    },
    "schedule": {
      "totalDoses": "number",
      "dailyDoses": "number",
      "estimatedEndDate": "string (YYYY-MM-DD)"
    },
    "nextSteps": {
      "scheduleMedication": "boolean",
      "setReminders": "boolean",
      "checkInventory": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Create prescription medicine record
INSERT INTO prescription_medicines (prescription_id, medicine_type_id, dosage, dosage_unit, frequency, timing, duration, instructions, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Validate prescription
SELECT id, name, status FROM prescriptions
WHERE id = ? AND family_id = ? AND deleted_at IS NULL;

-- Validate medicine type
SELECT id, name, category FROM medicine_types
WHERE id = ? AND family_id = ? AND deleted_at IS NULL;

-- Check existing assignment
SELECT COUNT(*) FROM prescription_medicines
WHERE prescription_id = ? AND medicine_type_id = ? AND deleted_at IS NULL;

-- Update prescription stats
UPDATE prescriptions
SET total_medicines = total_medicines + 1, updated_at = NOW()
WHERE id = ?;
```

#### **Indexes Used**

- `prescription_medicines_prescription_id_idx`: Index on prescription_id for prescription filtering
- `prescription_medicines_medicine_type_id_idx`: Index on medicine_type_id for medicine lookup
- `prescription_medicines_unique_idx`: Unique index on prescription_id, medicine_type_id
- `prescriptions_family_id_idx`: Index on family_id for family filtering

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                             |
| ---- | --------------------- | --------------------------------------- |
| 400  | Bad Request           | Invalid medicine data                   |
| 401  | Unauthorized          | User not authenticated                  |
| 403  | Forbidden             | Insufficient permissions                |
| 404  | Not Found             | Prescription or medicine type not found |
| 409  | Conflict              | Medicine already assigned               |
| 422  | Unprocessable Entity  | Validation failed                       |
| 500  | Internal Server Error | Database error                          |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "MEDICINE_ALREADY_ASSIGNED",
    "message": "Medicine is already assigned to this prescription",
    "details": "Paracetamol 500mg is already assigned to Headache Treatment"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (6 queries max)
- **Caching**: Medicine type list caching
- **Optimization**: Indexes on prescription_id and medicine_type_id

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền, prescription tồn tại, medicine type tồn tại
2. **Steps**:
   - Medicine: "Paracetamol 500mg"
   - Dosage: 1 TABLET
   - Frequency: 3 times/day
   - Timing: AFTER_MEAL
   - Duration: 7 days
   - Instructions: "Take with water"
   - Click Assign Medicine
3. **Expected Result**:
   - Medicine được assign thành công
   - Schedule được tính toán
   - Next steps được hiển thị

### **Error Cases**

1. **Invalid Input**:
   - Dosage <= 0
   - Frequency <= 0
   - Duration <= 0
   - Instructions quá dài
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
3. **Business Logic**:
   - Prescription không tồn tại
   - Medicine type không thuộc family
   - Medicine đã được assign
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Dosage = 0.5 (fractional)
   - Frequency = 1 (minimum)
   - Duration = 365 (maximum)
   - Instructions với 500 characters (max)
2. **Concurrent Access**:
   - 2 users cùng assign medicine
   - Race conditions
3. **Large Data**:
   - Prescription với nhiều medicines
   - Family với nhiều medicine types
4. **Special Characters**:
   - Instructions với Unicode characters
   - Medicine name với special chars

### **Performance Tests**

1. **Load Testing**: 50 concurrent assignments
2. **Stress Testing**: 200 concurrent assignments
3. **Endurance Testing**: Continuous assignments for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access control
- Family context validation

### **Data Validation**

- Dosage format validation
- Frequency validation
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Prescription privacy protection
- Audit logging
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. Prescription phải tồn tại trong family
2. Medicine type phải tồn tại trong family
3. Dosage phải > 0
4. Frequency phải > 0
5. Duration phải > 0
6. Instructions tối đa 500 characters

### **Business Logic**

1. Validate user permissions
2. Check prescription existence
3. Check medicine type existence
4. Prevent duplicate assignments
5. Create prescription medicine record
6. Calculate schedule information

### **Constraints**

1. Prescription belongs to family
2. Medicine type belongs to family
3. Unique medicine per prescription
4. Positive dosage and frequency
5. Family context required

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-013: Create Prescription (prescription phải tồn tại)
- UC-009: Add Medicine Type (medicine type phải tồn tại)

### **Dependencies**

- UC-015: Schedule Medication (để schedule)
- UC-017: Set Medication Reminder (để set reminders)

### **Related Features**

- Prescription Management: View prescription details
- Medicine Assignment: View assigned medicines
- Medication Scheduling: Schedule medication times
- Inventory Check: Check medicine availability

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Validate prescription và medicine type belong to family
- Prevent duplicate medicine assignments
- Calculate schedule information
- Create comprehensive audit log

### **Future Enhancements**

- Bulk medicine assignment
- Medicine templates
- Dosage calculators
- Drug interaction checking

### **Known Limitations**

- Không có bulk assignment
- Không có medicine templates
- Không có dosage calculators
- Không có drug interaction checking

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
