# UC-015: Schedule Medication

## 📋 Overview

### **UC-ID**: UC-015

### **Title**: Schedule Medication

### **Actor**: Family Member (ADMIN/OWNER/MEMBER role)

### **Precondition**: User đã đăng nhập, có family context, prescription đã tồn tại với assigned medicines

### **Postcondition**: Medication schedule được tạo với specific times và có thể được tracked

### **Priority**: High

### **Business Value**: Cho phép lên lịch uống thuốc chi tiết để đảm bảo tuân thủ điều trị và nhắc nhở đúng thời gian

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Prescription Details Screen / Schedule Medication
- **User Actions**:
  1. Navigate to Prescription Details
  2. Click "Schedule Medication" button
  3. Select prescription medicine từ list
  4. Chọn schedule type (DAILY, WEEKLY, CUSTOM)
  5. Set specific times cho mỗi dose
  6. Chọn days of week (nếu weekly)
  7. Set start date và end date
  8. Nhập special instructions (optional)
  9. Click "Create Schedule" button
  10. Confirm schedule creation
- **UI Elements**:
  - Prescription medicines list
  - Schedule type dropdown
  - Time picker cho mỗi dose
  - Day selector (weekly)
  - Date range picker
  - Instructions textarea
  - Create Schedule button
  - Cancel button
  - Schedule preview
- **Validation**:
  - Medicine selection required
  - Schedule type required
  - Times must be valid
  - Date range validation
  - Instructions max 500 characters

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/{familyId}/prescriptions/{prescriptionId}/medicines/{medicineId}/schedule`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "scheduleType": "DAILY",
    "times": ["08:00", "14:00", "20:00"],
    "daysOfWeek": [
      "MONDAY",
      "TUESDAY",
      "WEDNESDAY",
      "THURSDAY",
      "FRIDAY",
      "SATURDAY",
      "SUNDAY"
    ],
    "startDate": "2024-12-01",
    "endDate": "2024-12-07",
    "instructions": "Take 30 minutes after meals"
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `PrescriptionController.scheduleMedication()`
- **Service Layer**: `PrescriptionService.scheduleMedication()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Prescription medicine exists
  - Schedule validation
- **Business Rules**:
  - User phải là family member
  - Prescription medicine phải tồn tại
  - Times phải hợp lệ và không trùng lặp
  - Date range phải hợp lệ
  - Create medication schedule
  - Generate schedule instances
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `PrescriptionMedicineRepository`, `MedicationScheduleRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Validate prescription medicine exists
  SELECT pm.id, pm.dosage, pm.frequency, pm.duration, p.name as prescription_name
  FROM prescription_medicines pm
  JOIN prescriptions p ON pm.prescription_id = p.id
  WHERE pm.id = ? AND p.family_id = ? AND pm.deleted_at IS NULL;

  -- Check if schedule already exists
  SELECT COUNT(*) FROM medication_schedules
  WHERE prescription_medicine_id = ? AND status = 'ACTIVE';

  -- Create medication schedule
  INSERT INTO medication_schedules (prescription_medicine_id, schedule_type, times, days_of_week, start_date, end_date, instructions, status, created_by, created_at, updated_at)
  VALUES (?, ?, ?, ?, ?, ?, ?, 'ACTIVE', ?, NOW(), NOW())
  RETURNING id;

  -- Generate schedule instances for the date range
  INSERT INTO medication_schedule_instances (schedule_id, scheduled_date, scheduled_time, status, created_at)
  SELECT ?, date_series.date, ?, 'PENDING', NOW()
  FROM generate_series(?, ?, '1 day'::interval) AS date_series(date)
  WHERE EXTRACT(DOW FROM date_series.date) = ANY(?);

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'SCHEDULE_MEDICATION', 'MEDICATION_SCHEDULE', ?, ?, NOW());

  -- Update prescription statistics
  UPDATE prescriptions
  SET scheduled_medicines = scheduled_medicines + 1, updated_at = NOW()
  WHERE id = ?;
  ```

- **Tables**: `medication_schedules`, `medication_schedule_instances`, `prescription_medicines`, `prescriptions`, `audit_logs`
- **Constraints**:
  - Prescription medicine exists
  - Valid date range
  - Unique schedule per medicine
  - Schedule instance generation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Medication schedule created successfully",
    "data": {
      "schedule": {
        "id": 789,
        "prescriptionMedicine": {
          "id": 456,
          "medicineType": {
            "name": "Paracetamol 500mg",
            "category": "TABLET"
          },
          "dosage": 1,
          "frequency": 3
        },
        "scheduleType": "DAILY",
        "times": ["08:00", "14:00", "20:00"],
        "daysOfWeek": [
          "MONDAY",
          "TUESDAY",
          "WEDNESDAY",
          "THURSDAY",
          "FRIDAY",
          "SATURDAY",
          "SUNDAY"
        ],
        "startDate": "2024-12-01",
        "endDate": "2024-12-07",
        "instructions": "Take 30 minutes after meals",
        "status": "ACTIVE",
        "createdAt": "2024-12-01T10:00:00Z"
      },
      "instances": {
        "total": 21,
        "pending": 21,
        "completed": 0,
        "missed": 0
      },
      "nextSteps": {
        "setReminders": true,
        "trackCompliance": true,
        "viewSchedule": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Display schedule summary
  - Show schedule instances count
  - Update prescription status
- **Navigation**: Redirect to Schedule Details Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/{familyId}/prescriptions/{prescriptionId}/medicines/{medicineId}/schedule
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "scheduleType": "enum (required: DAILY, WEEKLY, CUSTOM)",
  "times": "array (required, valid time format HH:MM)",
  "daysOfWeek": "array (optional: MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY)",
  "startDate": "date (required, format: YYYY-MM-DD)",
  "endDate": "date (required, format: YYYY-MM-DD, >= startDate)",
  "instructions": "string (optional, max 500 chars)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "schedule": {
      "id": "number",
      "prescriptionMedicine": {
        "id": "number",
        "medicineType": {
          "name": "string",
          "category": "string"
        },
        "dosage": "number",
        "frequency": "number"
      },
      "scheduleType": "string",
      "times": "array",
      "daysOfWeek": "array",
      "startDate": "string (YYYY-MM-DD)",
      "endDate": "string (YYYY-MM-DD)",
      "instructions": "string",
      "status": "string (ACTIVE)",
      "createdAt": "string (ISO 8601)"
    },
    "instances": {
      "total": "number",
      "pending": "number",
      "completed": "number",
      "missed": "number"
    },
    "nextSteps": {
      "setReminders": "boolean",
      "trackCompliance": "boolean",
      "viewSchedule": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Create medication schedule
INSERT INTO medication_schedules (prescription_medicine_id, schedule_type, times, days_of_week, start_date, end_date, instructions, status, created_by, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, ?, 'ACTIVE', ?, NOW(), NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Validate prescription medicine
SELECT pm.id, pm.dosage, pm.frequency, pm.duration, p.name as prescription_name
FROM prescription_medicines pm
JOIN prescriptions p ON pm.prescription_id = p.id
WHERE pm.id = ? AND p.family_id = ? AND pm.deleted_at IS NULL;

-- Generate schedule instances
INSERT INTO medication_schedule_instances (schedule_id, scheduled_date, scheduled_time, status, created_at)
SELECT ?, date_series.date, ?, 'PENDING', NOW()
FROM generate_series(?, ?, '1 day'::interval) AS date_series(date)
WHERE EXTRACT(DOW FROM date_series.date) = ANY(?);

-- Update prescription stats
UPDATE prescriptions
SET scheduled_medicines = scheduled_medicines + 1, updated_at = NOW()
WHERE id = ?;
```

#### **Indexes Used**

- `medication_schedules_prescription_medicine_idx`: Index on prescription_medicine_id
- `medication_schedules_status_idx`: Index on status for active schedules
- `medication_schedule_instances_schedule_idx`: Index on schedule_id for instances
- `medication_schedule_instances_date_time_idx`: Index on scheduled_date, scheduled_time

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                     |
| ---- | --------------------- | ------------------------------- |
| 400  | Bad Request           | Invalid schedule data           |
| 401  | Unauthorized          | User not authenticated          |
| 403  | Forbidden             | Insufficient permissions        |
| 404  | Not Found             | Prescription medicine not found |
| 409  | Conflict              | Schedule already exists         |
| 422  | Unprocessable Entity  | Validation failed               |
| 500  | Internal Server Error | Database error                  |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "SCHEDULE_ALREADY_EXISTS",
    "message": "Schedule already exists for this medicine",
    "details": "Please update existing schedule or create new prescription"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 3 seconds
- **Database Load**: Heavy (7 queries max)
- **Caching**: Schedule data caching
- **Optimization**: Batch insert for schedule instances

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền, prescription medicine tồn tại
2. **Steps**:
   - Medicine: "Paracetamol 500mg"
   - Schedule: DAILY
   - Times: ["08:00", "14:00", "20:00"]
   - Start date: 2024-12-01
   - End date: 2024-12-07
   - Click Create Schedule
3. **Expected Result**:
   - Schedule được tạo thành công
   - 21 instances được tạo
   - Next steps được hiển thị

### **Error Cases**

1. **Invalid Input**:
   - Invalid time format
   - End date < start date
   - Empty times array
   - Instructions quá dài
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
3. **Business Logic**:
   - Prescription medicine không tồn tại
   - Schedule đã tồn tại
   - Invalid date range
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Start date = end date
   - Single time slot
   - Maximum date range (1 year)
   - Instructions với 500 characters (max)
2. **Concurrent Access**:
   - 2 users cùng tạo schedule
   - Race conditions
3. **Large Data**:
   - Long date range (365 days)
   - Multiple times per day
   - Complex weekly schedule
4. **Special Characters**:
   - Instructions với Unicode characters
   - Medicine name với special chars

### **Performance Tests**

1. **Load Testing**: 30 concurrent schedule creations
2. **Stress Testing**: 100 concurrent schedule creations
3. **Endurance Testing**: Continuous creations for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access control
- Family context validation

### **Data Validation**

- Time format validation
- Date range validation
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Schedule privacy protection
- Audit logging
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. Prescription medicine phải tồn tại
2. Schedule type phải hợp lệ
3. Times array không được rỗng
4. Start date <= end date
5. Instructions tối đa 500 characters

### **Business Logic**

1. Validate user permissions
2. Check prescription medicine existence
3. Prevent duplicate schedules
4. Generate schedule instances
5. Update prescription statistics
6. Create audit log

### **Constraints**

1. One active schedule per medicine
2. Valid date range
3. Valid time format
4. Family context required
5. Instance generation required

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-013: Create Prescription (prescription phải tồn tại)
- UC-014: Assign Medicine to Prescription (medicine phải được assign)

### **Dependencies**

- UC-016: Track Medication Compliance (để track)
- UC-017: Set Medication Reminder (để set reminders)

### **Related Features**

- Schedule Management: View, edit schedules
- Compliance Tracking: Track medication adherence
- Reminder System: Set medication reminders
- Schedule Analytics: View schedule statistics

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Generate schedule instances efficiently
- Implement batch insert cho instances
- Create comprehensive audit log
- Update prescription statistics

### **Future Enhancements**

- Schedule templates
- Bulk schedule creation
- Advanced scheduling algorithms
- Schedule optimization

### **Known Limitations**

- Không có schedule templates
- Không có bulk creation
- Không có advanced algorithms
- Không có schedule optimization

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
