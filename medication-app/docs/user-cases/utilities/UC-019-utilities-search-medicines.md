# UC-019: Search Medicines

## 📋 Overview

### **UC-ID**: UC-019

### **Title**: Search Medicines

### **Actor**: Family Member (ADMIN/OWNER/MEMBER role)

### **Precondition**: User đã đăng nhập, có family context

### **Postcondition**: Medicine search results được hiển thị với filtering và sorting options

### **Priority**: Medium

### **Business Value**: Cho phép tìm kiếm nhanh chóng medicines trong inventory và medicine types để quản lý hiệu quả

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Search Screen / Medicine Search
- **User Actions**:
  1. Navigate to Search Screen
  2. Select "Medicine Search" tab
  3. Enter search query trong search box
  4. Select search scope (INVENTORY, MEDICINE_TYPES, ALL)
  5. Apply filters (category, status, expiry date, quantity)
  6. Choose sort order (name, expiry date, quantity, added date)
  7. Click "Search" button
  8. View search results
  9. Click on result để view details
- **UI Elements**:
  - Search input box
  - Search scope selector
  - Filter panel
  - Sort dropdown
  - Search button
  - Results list
  - Pagination controls
  - Advanced search toggle
- **Validation**:
  - Search query required (min 2 characters)
  - Valid search scope
  - Valid filters
  - Valid sort order

### **Step 2: API Call Details**

- **Endpoint**: `GET /api/families/{familyId}/medicines/search`
- **Method**: GET
- **Headers**:
  ```
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Query Parameters**:
  ```
  q=paracetamol&scope=ALL&category=TABLET&status=ACTIVE&sortBy=name&sortOrder=ASC&page=1&size=20
  ```

### **Step 3: Backend Processing**

- **Controller**: `MedicineSearchController.searchMedicines()`
- **Service Layer**: `MedicineSearchService.searchMedicines()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Search parameters validation
- **Business Rules**:
  - User phải là family member
  - Search query tối thiểu 2 characters
  - Scope validation
  - Filter validation
  - Pagination limits
  - Search across inventory và medicine types
  - Apply family context filters

### **Step 4: Database Operations**

- **Repository**: `MedicineSearchRepository`, `MedicineInventoryRepository`, `MedicineTypeRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Search in medicine inventory
  SELECT
    mi.id,
    mi.quantity,
    mi.expiry_date,
    mi.status,
    mi.added_date,
    mt.id as medicine_type_id,
    mt.name as medicine_name,
    mt.category as medicine_category,
    mt.description as medicine_description,
    mt.manufacturer as medicine_manufacturer,
    'INVENTORY' as source
  FROM medicine_inventory mi
  JOIN medicine_types mt ON mi.medicine_type_id = mt.id
  WHERE mi.family_id = ?
    AND mi.deleted_at IS NULL
    AND (
      LOWER(mt.name) LIKE LOWER(?) OR
      LOWER(mt.description) LIKE LOWER(?) OR
      LOWER(mt.manufacturer) LIKE LOWER(?)
    )
    AND (? IS NULL OR mt.category = ?)
    AND (? IS NULL OR mi.status = ?)
    AND (? IS NULL OR mi.expiry_date >= ?)
    AND (? IS NULL OR mi.quantity >= ?)

  UNION ALL

  -- Search in medicine types
  SELECT
    NULL as id,
    NULL as quantity,
    NULL as expiry_date,
    'ACTIVE' as status,
    mt.created_at as added_date,
    mt.id as medicine_type_id,
    mt.name as medicine_name,
    mt.category as medicine_category,
    mt.description as medicine_description,
    mt.manufacturer as medicine_manufacturer,
    'MEDICINE_TYPE' as source
  FROM medicine_types mt
  WHERE mt.deleted_at IS NULL
    AND (
      LOWER(mt.name) LIKE LOWER(?) OR
      LOWER(mt.description) LIKE LOWER(?) OR
      LOWER(mt.manufacturer) LIKE LOWER(?)
    )
    AND (? IS NULL OR mt.category = ?)
    AND mt.id NOT IN (
      SELECT DISTINCT medicine_type_id
      FROM medicine_inventory
      WHERE family_id = ? AND deleted_at IS NULL
    )

  ORDER BY
    CASE WHEN ? = 'name' THEN medicine_name END ASC,
    CASE WHEN ? = 'name' AND ? = 'DESC' THEN medicine_name END DESC,
    CASE WHEN ? = 'expiry_date' THEN expiry_date END ASC,
    CASE WHEN ? = 'expiry_date' AND ? = 'DESC' THEN expiry_date END DESC,
    CASE WHEN ? = 'quantity' THEN quantity END ASC,
    CASE WHEN ? = 'quantity' AND ? = 'DESC' THEN quantity END DESC,
    CASE WHEN ? = 'added_date' THEN added_date END ASC,
    CASE WHEN ? = 'added_date' AND ? = 'DESC' THEN added_date END DESC
  LIMIT ? OFFSET ?;

  -- Get total count for pagination
  SELECT COUNT(*) as total_count
  FROM (
    -- Same search query without ORDER BY and LIMIT
  ) as search_results;
  ```

- **Tables**: `medicine_inventory`, `medicine_types`, `family_members`
- **Constraints**:
  - Family context required
  - Valid search parameters
  - Pagination limits
  - Search scope validation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Search completed successfully",
    "data": {
      "query": "paracetamol",
      "scope": "ALL",
      "filters": {
        "category": "TABLET",
        "status": "ACTIVE"
      },
      "sortBy": "name",
      "sortOrder": "ASC",
      "pagination": {
        "page": 1,
        "size": 20,
        "totalElements": 45,
        "totalPages": 3
      },
      "results": [
        {
          "id": 123,
          "medicineType": {
            "id": 456,
            "name": "Paracetamol 500mg",
            "category": "TABLET",
            "description": "Pain relief medication",
            "manufacturer": "ABC Pharma"
          },
          "quantity": 50,
          "expiryDate": "2025-06-15",
          "status": "ACTIVE",
          "addedDate": "2024-12-01T10:00:00Z",
          "source": "INVENTORY"
        },
        {
          "id": null,
          "medicineType": {
            "id": 789,
            "name": "Paracetamol 1000mg",
            "category": "TABLET",
            "description": "Strong pain relief",
            "manufacturer": "XYZ Pharma"
          },
          "quantity": null,
          "expiryDate": null,
          "status": "ACTIVE",
          "addedDate": "2024-11-15T08:00:00Z",
          "source": "MEDICINE_TYPE"
        }
      ],
      "summary": {
        "inventoryCount": 15,
        "medicineTypeCount": 30,
        "totalCount": 45
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show search results
  - Display pagination
  - Update result count
  - Show filters applied
- **Navigation**: Stay on Search Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
GET /api/families/{familyId}/medicines/search?q={query}&scope={scope}&category={category}&status={status}&sortBy={sortBy}&sortOrder={sortOrder}&page={page}&size={size}
Accept: application/json
Authorization: Bearer [JWT_TOKEN]
```

#### **Query Parameters**

| Parameter | Type    | Required | Description                                             |
| --------- | ------- | -------- | ------------------------------------------------------- |
| q         | string  | Yes      | Search query (min 2 chars)                              |
| scope     | enum    | No       | INVENTORY, MEDICINE_TYPES, ALL (default: ALL)           |
| category  | enum    | No       | Medicine category filter                                |
| status    | enum    | No       | Inventory status filter                                 |
| sortBy    | enum    | No       | name, expiry_date, quantity, added_date (default: name) |
| sortOrder | enum    | No       | ASC, DESC (default: ASC)                                |
| page      | integer | No       | Page number (default: 1)                                |
| size      | integer | No       | Page size (default: 20, max: 100)                       |

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "query": "string",
    "scope": "string",
    "filters": "object",
    "sortBy": "string",
    "sortOrder": "string",
    "pagination": {
      "page": "number",
      "size": "number",
      "totalElements": "number",
      "totalPages": "number"
    },
    "results": [
      {
        "id": "number (nullable)",
        "medicineType": {
          "id": "number",
          "name": "string",
          "category": "string",
          "description": "string",
          "manufacturer": "string"
        },
        "quantity": "number (nullable)",
        "expiryDate": "string (nullable, YYYY-MM-DD)",
        "status": "string",
        "addedDate": "string (ISO 8601)",
        "source": "string (INVENTORY, MEDICINE_TYPE)"
      }
    ],
    "summary": {
      "inventoryCount": "number",
      "medicineTypeCount": "number",
      "totalCount": "number"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Search in medicine inventory
SELECT
  mi.id,
  mi.quantity,
  mi.expiry_date,
  mi.status,
  mi.added_date,
  mt.id as medicine_type_id,
  mt.name as medicine_name,
  mt.category as medicine_category,
  mt.description as medicine_description,
  mt.manufacturer as medicine_manufacturer,
  'INVENTORY' as source
FROM medicine_inventory mi
JOIN medicine_types mt ON mi.medicine_type_id = mt.id
WHERE mi.family_id = ?
  AND mi.deleted_at IS NULL
  AND (
    LOWER(mt.name) LIKE LOWER(?) OR
    LOWER(mt.description) LIKE LOWER(?) OR
    LOWER(mt.manufacturer) LIKE LOWER(?)
  )
  AND (? IS NULL OR mt.category = ?)
  AND (? IS NULL OR mi.status = ?)
  AND (? IS NULL OR mi.expiry_date >= ?)
  AND (? IS NULL OR mi.quantity >= ?)
```

#### **Related Queries**

```sql
-- Search in medicine types (not in inventory)
SELECT
  NULL as id,
  NULL as quantity,
  NULL as expiry_date,
  'ACTIVE' as status,
  mt.created_at as added_date,
  mt.id as medicine_type_id,
  mt.name as medicine_name,
  mt.category as medicine_category,
  mt.description as medicine_description,
  mt.manufacturer as medicine_manufacturer,
  'MEDICINE_TYPE' as source
FROM medicine_types mt
WHERE mt.deleted_at IS NULL
  AND (
    LOWER(mt.name) LIKE LOWER(?) OR
    LOWER(mt.description) LIKE LOWER(?) OR
    LOWER(mt.manufacturer) LIKE LOWER(?)
  )
  AND (? IS NULL OR mt.category = ?)
  AND mt.id NOT IN (
    SELECT DISTINCT medicine_type_id
    FROM medicine_inventory
    WHERE family_id = ? AND deleted_at IS NULL
  )
```

#### **Indexes Used**

- `medicine_inventory_family_idx`: Index on family_id
- `medicine_inventory_medicine_type_idx`: Index on medicine_type_id
- `medicine_types_name_idx`: Index on name for search
- `medicine_types_category_idx`: Index on category
- `medicine_inventory_status_idx`: Index on status
- `medicine_inventory_expiry_idx`: Index on expiry_date

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description               |
| ---- | --------------------- | ------------------------- |
| 400  | Bad Request           | Invalid search parameters |
| 401  | Unauthorized          | User not authenticated    |
| 403  | Forbidden             | Insufficient permissions  |
| 404  | Not Found             | Family not found          |
| 422  | Unprocessable Entity  | Validation failed         |
| 500  | Internal Server Error | Search failed             |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INVALID_SEARCH_QUERY",
    "message": "Search query must be at least 2 characters",
    "details": "Please enter a longer search term"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 2 seconds
- **Database Load**: Medium (complex search queries)
- **Caching**: Search results caching
- **Optimization**: Full-text search indexes

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền, có medicine data
2. **Steps**:
   - Query: "paracetamol"
   - Scope: ALL
   - Category: TABLET
   - Sort: name ASC
   - Click Search
3. **Expected Result**:
   - Results được hiển thị
   - Pagination working
   - Filters applied
   - Sort order correct

### **Error Cases**

1. **Invalid Input**:
   - Query quá ngắn (< 2 chars)
   - Invalid scope
   - Invalid sort order
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
3. **Business Logic**:
   - Family không tồn tại
   - No search results
   - Invalid pagination
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Query với 2 characters (min)
   - Query với 100 characters (max)
   - Page size = 1
   - Page size = 100 (max)
2. **Concurrent Access**:
   - 2 users cùng search
   - Race conditions
3. **Large Data**:
   - Many search results
   - Large pagination
4. **Special Characters**:
   - Query với Unicode characters
   - Medicine names với special chars

### **Performance Tests**

1. **Load Testing**: 50 concurrent searches
2. **Stress Testing**: 200 concurrent searches
3. **Endurance Testing**: Continuous searches for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access control
- Family context validation

### **Data Validation**

- Search query validation
- Parameter sanitization
- Input validation
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Search privacy protection
- Result filtering
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. Search query tối thiểu 2 characters
2. Valid search scope
3. Valid filters
4. Valid sort order
5. Pagination limits

### **Business Logic**

1. Validate user permissions
2. Apply family context filters
3. Search across inventory và medicine types
4. Apply filters và sorting
5. Handle pagination
6. Return formatted results

### **Constraints**

1. Family context required
2. Valid search parameters
3. Pagination limits (max 100 per page)
4. Search scope validation
5. Result filtering

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-009: Add Medicine Type (có medicine types)
- UC-010: Add Medicine to Inventory (có inventory)

### **Dependencies**

- UC-020: Export Family Data (để export search results)
- UC-021: Manage Notifications (để set search alerts)

### **Related Features**

- Medicine Management: View, edit medicines
- Inventory Management: Track quantities
- Export Functionality: Export search results
- Advanced Search: Filters và sorting

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng full-text search cho performance
- Implement result caching
- Handle large result sets efficiently
- Apply proper indexing strategy
- Implement search suggestions

### **Future Enhancements**

- Advanced search filters
- Search suggestions
- Search history
- Saved searches
- Search analytics

### **Known Limitations**

- Không có advanced filters
- Không có search suggestions
- Không có search history
- Không có saved searches

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
