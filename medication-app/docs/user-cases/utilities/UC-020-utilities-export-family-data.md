# UC-020: Export Family Data

## 📋 Overview

### **UC-ID**: UC-020

### **Title**: Export Family Data

### **Actor**: Family Member (ADMIN/OWNER role)

### **Precondition**: User đã đăng nhập, có family context với ADMIN/OWNER role

### **Postcondition**: Family data được export thành file và có thể được downloaded

### **Priority**: Medium

### **Business Value**: Cho phép backup và chia sẻ dữ liệu family với bác sĩ hoặc chuyển sang hệ thống khác

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Settings / Data Management / Export Data
- **User Actions**:
  1. Navigate to Settings
  2. Click "Data Management" section
  3. Select "Export Family Data"
  4. Choose export scope (ALL, MEDICINES, PRESCRIPTIONS, COMPLIANCE, CUSTOM)
  5. Select date range (optional)
  6. Choose export format (JSON, CSV, EXCEL)
  7. Include sensitive data toggle (optional)
  8. Add export notes (optional)
  9. Click "Export Data" button
  10. Wait for export completion
- **UI Elements**:
  - Export scope selector
  - Date range picker
  - Format dropdown
  - Sensitive data toggle
  - Notes textarea
  - Export button
  - Progress indicator
  - Export history
- **Validation**:
  - Export scope required
  - Valid date range
  - Format selection required
  - Notes max 500 characters

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/{familyId}/export`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "exportScope": "ALL",
    "startDate": "2024-01-01",
    "endDate": "2024-12-01",
    "format": "JSON",
    "includeSensitiveData": false,
    "notes": "Backup for doctor consultation",
    "sections": ["medicines", "prescriptions", "compliance", "family"]
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `DataExportController.exportFamilyData()`
- **Service Layer**: `DataExportService.exportFamilyData()`
- **Validation**:
  - User authentication check
  - Family membership validation với ADMIN/OWNER role
  - Export parameters validation
- **Business Rules**:
  - User phải là family member với ADMIN/OWNER role
  - Valid export scope
  - Date range validation
  - Data privacy compliance
  - Generate export file
  - Store export metadata
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `DataExportRepository`, `FamilyRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Get family basic data
  SELECT
    f.id, f.name, f.description, f.created_at, f.updated_at,
    u.name as owner_name, u.email as owner_email
  FROM families f
  JOIN users u ON f.owner_id = u.id
  WHERE f.id = ? AND f.deleted_at IS NULL;

  -- Get family members
  SELECT
    fm.id, fm.role, fm.status, fm.joined_at,
    u.id as user_id, u.name, u.email, u.phone
  FROM family_members fm
  JOIN users u ON fm.user_id = u.id
  WHERE fm.family_id = ? AND fm.deleted_at IS NULL;

  -- Get medicine inventory
  SELECT
    mi.id, mi.quantity, mi.expiry_date, mi.status, mi.added_date,
    mt.id as medicine_type_id, mt.name, mt.category, mt.description, mt.manufacturer
  FROM medicine_inventory mi
  JOIN medicine_types mt ON mi.medicine_type_id = mt.id
  WHERE mi.family_id = ? AND mi.deleted_at IS NULL
    AND (? IS NULL OR mi.added_date BETWEEN ? AND ?);

  -- Get prescriptions
  SELECT
    p.id, p.name, p.description, p.prescribed_by, p.prescribed_date, p.status,
    pm.id as prescription_medicine_id, pm.dosage, pm.frequency, pm.duration,
    mt.id as medicine_type_id, mt.name as medicine_name
  FROM prescriptions p
  JOIN prescription_medicines pm ON p.id = pm.prescription_id
  JOIN medicine_types mt ON pm.medicine_type_id = mt.id
  WHERE p.family_id = ? AND p.deleted_at IS NULL
    AND (? IS NULL OR p.prescribed_date BETWEEN ? AND ?);

  -- Get compliance data
  SELECT
    ms.id as schedule_id, ms.schedule_type, ms.compliance_rate,
    msi.id as instance_id, msi.scheduled_date, msi.scheduled_time, msi.status, msi.taken_at,
    mt.name as medicine_name, p.name as prescription_name
  FROM medication_schedules ms
  JOIN medication_schedule_instances msi ON ms.id = msi.schedule_id
  JOIN prescription_medicines pm ON ms.prescription_medicine_id = pm.id
  JOIN medicine_types mt ON pm.medicine_type_id = mt.id
  JOIN prescriptions p ON pm.prescription_id = p.id
  WHERE ms.family_id = ? AND ms.deleted_at IS NULL
    AND (? IS NULL OR msi.scheduled_date BETWEEN ? AND ?);

  -- Create export record
  INSERT INTO data_exports (family_id, user_id, export_scope, start_date, end_date, format, include_sensitive_data, notes, file_path, file_size, status, created_at, updated_at)
  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'COMPLETED', NOW(), NOW())
  RETURNING id;

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'EXPORT_FAMILY_DATA', 'DATA_EXPORT', ?, ?, NOW());
  ```

- **Tables**: `families`, `family_members`, `users`, `medicine_inventory`, `medicine_types`, `prescriptions`, `prescription_medicines`, `medication_schedules`, `medication_schedule_instances`, `data_exports`, `audit_logs`
- **Constraints**:
  - Admin/OWNER role required
  - Valid export scope
  - Data privacy compliance
  - File generation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Family data exported successfully",
    "data": {
      "export": {
        "id": 345,
        "exportScope": "ALL",
        "startDate": "2024-01-01",
        "endDate": "2024-12-01",
        "format": "JSON",
        "includeSensitiveData": false,
        "notes": "Backup for doctor consultation",
        "filePath": "/exports/family_123_export_345.json",
        "fileSize": "2.8MB",
        "status": "COMPLETED",
        "createdAt": "2024-12-01T10:00:00Z"
      },
      "summary": {
        "familyMembers": 4,
        "medicines": 25,
        "prescriptions": 8,
        "complianceRecords": 156,
        "totalRecords": 193
      },
      "downloadUrl": "https://api.medication.com/exports/download/345",
      "expiresAt": "2024-12-08T10:00:00Z",
      "nextSteps": {
        "downloadFile": true,
        "shareExport": true,
        "scheduleExport": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Display export summary
  - Show download link
  - Update export history
- **Navigation**: Redirect to Export Details Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/{familyId}/export
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "exportScope": "enum (required: ALL, MEDICINES, PRESCRIPTIONS, COMPLIANCE, CUSTOM)",
  "startDate": "date (optional, format: YYYY-MM-DD)",
  "endDate": "date (optional, format: YYYY-MM-DD)",
  "format": "enum (required: JSON, CSV, EXCEL)",
  "includeSensitiveData": "boolean (default: false)",
  "notes": "string (optional, max 500 chars)",
  "sections": "array (optional, for CUSTOM scope)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "export": {
      "id": "number",
      "exportScope": "string",
      "startDate": "string (YYYY-MM-DD)",
      "endDate": "string (YYYY-MM-DD)",
      "format": "string",
      "includeSensitiveData": "boolean",
      "notes": "string",
      "filePath": "string",
      "fileSize": "string",
      "status": "string (COMPLETED)",
      "createdAt": "string (ISO 8601)"
    },
    "summary": {
      "familyMembers": "number",
      "medicines": "number",
      "prescriptions": "number",
      "complianceRecords": "number",
      "totalRecords": "number"
    },
    "downloadUrl": "string",
    "expiresAt": "string (ISO 8601)",
    "nextSteps": {
      "downloadFile": "boolean",
      "shareExport": "boolean",
      "scheduleExport": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Get family basic data
SELECT
  f.id, f.name, f.description, f.created_at, f.updated_at,
  u.name as owner_name, u.email as owner_email
FROM families f
JOIN users u ON f.owner_id = u.id
WHERE f.id = ? AND f.deleted_at IS NULL;
```

#### **Related Queries**

```sql
-- Get medicine inventory with date filter
SELECT
  mi.id, mi.quantity, mi.expiry_date, mi.status, mi.added_date,
  mt.id as medicine_type_id, mt.name, mt.category, mt.description, mt.manufacturer
FROM medicine_inventory mi
JOIN medicine_types mt ON mi.medicine_type_id = mt.id
WHERE mi.family_id = ? AND mi.deleted_at IS NULL
  AND (? IS NULL OR mi.added_date BETWEEN ? AND ?);

-- Create export record
INSERT INTO data_exports (family_id, user_id, export_scope, start_date, end_date, format, include_sensitive_data, notes, file_path, file_size, status, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'COMPLETED', NOW(), NOW())
RETURNING id;
```

#### **Indexes Used**

- `families_id_idx`: Primary key index
- `family_members_family_idx`: Index on family_id
- `medicine_inventory_family_idx`: Index on family_id
- `prescriptions_family_idx`: Index on family_id
- `medication_schedules_family_idx`: Index on family_id
- `data_exports_family_idx`: Index on family_id

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description               |
| ---- | --------------------- | ------------------------- |
| 400  | Bad Request           | Invalid export parameters |
| 401  | Unauthorized          | User not authenticated    |
| 403  | Forbidden             | Insufficient permissions  |
| 404  | Not Found             | Family not found          |
| 422  | Unprocessable Entity  | Validation failed         |
| 500  | Internal Server Error | Export failed             |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "message": "Only family admin can export data",
    "details": "Please contact family admin for data export"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 30 seconds
- **Database Load**: Heavy (multiple complex queries)
- **Caching**: Export data caching
- **Optimization**: Async export processing

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền ADMIN, có family data
2. **Steps**:
   - Export Scope: ALL
   - Format: JSON
   - Include sensitive data: false
   - Click Export Data
3. **Expected Result**:
   - Export được tạo thành công
   - File được generate
   - Download link available
   - Summary được hiển thị

### **Error Cases**

1. **Invalid Input**:
   - Invalid export scope
   - Invalid date range
   - Invalid format
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không có quyền ADMIN
3. **Business Logic**:
   - Family không tồn tại
   - No data to export
   - Export generation failed
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Large date range (1 year)
   - No data in date range
   - Maximum file size
2. **Concurrent Access**:
   - 2 users cùng export
   - Race conditions
3. **Large Data**:
   - Many family members
   - Large medicine inventory
   - Many prescriptions
4. **Special Characters**:
   - Family name với Unicode
   - Notes với special chars

### **Performance Tests**

1. **Load Testing**: 10 concurrent exports
2. **Stress Testing**: 30 concurrent exports
3. **Endurance Testing**: Continuous exports for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member with ADMIN/OWNER role
- Role-based access control
- Family context validation

### **Data Validation**

- Export parameters validation
- Date range validation
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Sensitive data handling
- Export file security
- Data privacy compliance

## 📊 Business Rules

### **Validation Rules**

1. User phải có quyền ADMIN/OWNER
2. Export scope phải hợp lệ
3. Date range phải hợp lệ
4. Format phải được support
5. Notes tối đa 500 characters

### **Business Logic**

1. Validate user permissions
2. Check data availability
3. Generate export data
4. Create export file
5. Store export metadata
6. Create audit log

### **Constraints**

1. Admin/OWNER role required
2. Valid export scope
3. Data privacy compliance
4. File generation
5. Export limits

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-008: Manage Family Roles (có quyền ADMIN)

### **Dependencies**

- UC-019: Search Medicines (để search trước export)
- UC-023: Backup Restore Data (để backup)

### **Related Features**

- Data Management: View, manage exports
- Backup System: Regular backups
- Privacy Controls: Data protection
- Export History: Track exports

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng async processing cho export generation
- Implement file storage system
- Handle large data sets efficiently
- Apply data privacy controls
- Create comprehensive audit log

### **Future Enhancements**

- Scheduled exports
- Export templates
- Advanced filtering
- Export analytics

### **Known Limitations**

- Không có scheduled exports
- Không có export templates
- Không có advanced filtering
- Không có export analytics

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
