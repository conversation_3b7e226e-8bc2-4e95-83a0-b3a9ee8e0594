# UC-021: Manage Notifications

## 📋 Overview

### **UC-ID**: UC-021

### **Title**: Manage Notifications

### **Actor**: Family Member (ADMIN/OWNER/MEMBER role)

### **Precondition**: User đã đăng nhập, có family context

### **Postcondition**: Notification settings được cập nhật và notifications được quản lý

### **Priority**: Medium

### **Business Value**: Cho phép người dùng tùy chỉnh notification preferences để nhận thông báo phù hợp với nhu cầu

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Settings / Notifications / Manage Notifications
- **User Actions**:
  1. Navigate to Settings
  2. Click "Notifications" section
  3. Select "Manage Notifications"
  4. View current notification settings
  5. Toggle notification types (MEDICATION_REMINDER, EXPIRY_ALERT, LOW_STOCK, COMPLIANCE_REPORT)
  6. Configure channels (PUSH, SMS, EMAIL, IN_APP)
  7. Set quiet hours (start time, end time)
  8. Choose notification frequency (IMMEDIATE, DAILY_SUMMARY, WEEKLY_SUMMARY)
  9. Add custom message templates (optional)
  10. Click "Save Settings" button
- **UI Elements**:
  - Notification type toggles
  - Channel checkboxes
  - Quiet hours picker
  - Frequency selector
  - Custom message templates
  - Save Settings button
  - Reset to Default button
  - Notification history
- **Validation**:
  - At least one notification type selected
  - At least one channel selected
  - Valid quiet hours
  - Valid frequency

### **Step 2: API Call Details**

- **Endpoint**: `PUT /api/families/{familyId}/users/{userId}/notifications/settings`
- **Method**: PUT
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "notificationTypes": {
      "MEDICATION_REMINDER": true,
      "EXPIRY_ALERT": true,
      "LOW_STOCK": false,
      "COMPLIANCE_REPORT": true
    },
    "channels": {
      "PUSH": true,
      "SMS": false,
      "EMAIL": true,
      "IN_APP": true
    },
    "quietHours": {
      "enabled": true,
      "startTime": "22:00",
      "endTime": "07:00"
    },
    "frequency": "IMMEDIATE",
    "customTemplates": {
      "MEDICATION_REMINDER": "Time to take your medicine!",
      "EXPIRY_ALERT": "Medicine expiring soon"
    }
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `NotificationController.updateSettings()`
- **Service Layer**: `NotificationService.updateSettings()`
- **Validation**:
  - User authentication check
  - Family membership validation
  - Notification settings validation
- **Business Rules**:
  - User phải là family member
  - Valid notification types
  - Valid channels
  - Quiet hours validation
  - Update notification settings
  - Update notification queue
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `NotificationSettingsRepository`, `NotificationQueueRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Get current notification settings
  SELECT
    ns.id, ns.notification_types, ns.channels, ns.quiet_hours,
    ns.frequency, ns.custom_templates, ns.is_active
  FROM notification_settings ns
  WHERE ns.family_id = ? AND ns.user_id = ? AND ns.deleted_at IS NULL;

  -- Update notification settings
  UPDATE notification_settings
  SET notification_types = ?, channels = ?, quiet_hours = ?,
      frequency = ?, custom_templates = ?, updated_at = NOW()
  WHERE family_id = ? AND user_id = ? AND deleted_at IS NULL;

  -- Insert if not exists
  INSERT INTO notification_settings (family_id, user_id, notification_types, channels, quiet_hours, frequency, custom_templates, is_active, created_at, updated_at)
  SELECT ?, ?, ?, ?, ?, ?, ?, true, NOW(), NOW()
  WHERE NOT EXISTS (
    SELECT 1 FROM notification_settings
    WHERE family_id = ? AND user_id = ? AND deleted_at IS NULL
  );

  -- Update existing notification queue based on new settings
  UPDATE notification_queue
  SET status = 'CANCELLED', updated_at = NOW()
  WHERE user_id = ? AND family_id = ? AND status = 'PENDING'
    AND (
      (notification_type = 'MEDICATION_REMINDER' AND ? = false) OR
      (notification_type = 'EXPIRY_ALERT' AND ? = false) OR
      (notification_type = 'LOW_STOCK' AND ? = false) OR
      (notification_type = 'COMPLIANCE_REPORT' AND ? = false)
    );

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'UPDATE_NOTIFICATION_SETTINGS', 'NOTIFICATION_SETTINGS', ?, ?, NOW());
  ```

- **Tables**: `notification_settings`, `notification_queue`, `family_members`, `audit_logs`
- **Constraints**:
  - User exists in family
  - Valid notification settings
  - Queue update validation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Notification settings updated successfully",
    "data": {
      "settings": {
        "id": 567,
        "notificationTypes": {
          "MEDICATION_REMINDER": true,
          "EXPIRY_ALERT": true,
          "LOW_STOCK": false,
          "COMPLIANCE_REPORT": true
        },
        "channels": {
          "PUSH": true,
          "SMS": false,
          "EMAIL": true,
          "IN_APP": true
        },
        "quietHours": {
          "enabled": true,
          "startTime": "22:00",
          "endTime": "07:00"
        },
        "frequency": "IMMEDIATE",
        "customTemplates": {
          "MEDICATION_REMINDER": "Time to take your medicine!",
          "EXPIRY_ALERT": "Medicine expiring soon"
        },
        "isActive": true,
        "updatedAt": "2024-12-01T10:00:00Z"
      },
      "summary": {
        "activeNotifications": 3,
        "cancelledNotifications": 5,
        "pendingNotifications": 12
      },
      "nextSteps": {
        "testNotification": true,
        "viewHistory": true,
        "scheduleNotification": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Update settings display
  - Show notification summary
  - Update notification history
- **Navigation**: Stay on Notifications Settings Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
PUT /api/families/{familyId}/users/{userId}/notifications/settings
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "notificationTypes": "object (required)",
  "channels": "object (required)",
  "quietHours": "object (optional)",
  "frequency": "enum (required: IMMEDIATE, DAILY_SUMMARY, WEEKLY_SUMMARY)",
  "customTemplates": "object (optional)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "settings": {
      "id": "number",
      "notificationTypes": "object",
      "channels": "object",
      "quietHours": "object",
      "frequency": "string",
      "customTemplates": "object",
      "isActive": "boolean",
      "updatedAt": "string (ISO 8601)"
    },
    "summary": {
      "activeNotifications": "number",
      "cancelledNotifications": "number",
      "pendingNotifications": "number"
    },
    "nextSteps": {
      "testNotification": "boolean",
      "viewHistory": "boolean",
      "scheduleNotification": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Update notification settings
UPDATE notification_settings
SET notification_types = ?, channels = ?, quiet_hours = ?,
    frequency = ?, custom_templates = ?, updated_at = NOW()
WHERE family_id = ? AND user_id = ? AND deleted_at IS NULL;
```

#### **Related Queries**

```sql
-- Update notification queue based on settings
UPDATE notification_queue
SET status = 'CANCELLED', updated_at = NOW()
WHERE user_id = ? AND family_id = ? AND status = 'PENDING'
  AND (
    (notification_type = 'MEDICATION_REMINDER' AND ? = false) OR
    (notification_type = 'EXPIRY_ALERT' AND ? = false) OR
    (notification_type = 'LOW_STOCK' AND ? = false) OR
    (notification_type = 'COMPLIANCE_REPORT' AND ? = false)
  );

-- Get notification summary
SELECT
  COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_count,
  COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled_count,
  COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_count
FROM notification_queue
WHERE user_id = ? AND family_id = ?;
```

#### **Indexes Used**

- `notification_settings_family_user_idx`: Index on family_id, user_id
- `notification_settings_user_idx`: Index on user_id
- `notification_queue_user_family_idx`: Index on user_id, family_id
- `notification_queue_status_idx`: Index on status

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                   |
| ---- | --------------------- | ----------------------------- |
| 400  | Bad Request           | Invalid notification settings |
| 401  | Unauthorized          | User not authenticated        |
| 403  | Forbidden             | Insufficient permissions      |
| 404  | Not Found             | User not found in family      |
| 422  | Unprocessable Entity  | Validation failed             |
| 500  | Internal Server Error | Settings update failed        |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INVALID_QUIET_HOURS",
    "message": "Invalid quiet hours configuration",
    "details": "End time must be after start time"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 3 seconds
- **Database Load**: Medium (5 queries max)
- **Caching**: Settings caching
- **Optimization**: Batch queue updates

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền, có notification settings
2. **Steps**:
   - Enable MEDICATION_REMINDER, EXPIRY_ALERT
   - Disable LOW_STOCK
   - Set channels: PUSH, EMAIL
   - Set quiet hours: 22:00-07:00
   - Click Save Settings
3. **Expected Result**:
   - Settings được cập nhật
   - Queue được update
   - Summary được hiển thị

### **Error Cases**

1. **Invalid Input**:
   - No notification types selected
   - No channels selected
   - Invalid quiet hours
   - Invalid frequency
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không phải family member
3. **Business Logic**:
   - User không tồn tại trong family
   - Invalid settings combination
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - All notification types disabled
   - All channels disabled
   - Quiet hours 00:00-23:59
   - Custom templates với max length
2. **Concurrent Access**:
   - 2 users cùng update settings
   - Race conditions
3. **Large Data**:
   - Many pending notifications
   - Large custom templates
4. **Special Characters**:
   - Custom templates với Unicode
   - Special characters in templates

### **Performance Tests**

1. **Load Testing**: 20 concurrent settings updates
2. **Stress Testing**: 50 concurrent settings updates
3. **Endurance Testing**: Continuous updates for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member
- Role-based access control
- Family context validation

### **Data Validation**

- Notification settings validation
- Input sanitization
- Template validation
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Settings privacy protection
- Audit logging
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. At least one notification type required
2. At least one channel required
3. Valid quiet hours (end > start)
4. Valid frequency
5. Custom templates max length

### **Business Logic**

1. Validate user permissions
2. Check notification settings
3. Update settings
4. Update notification queue
5. Create audit log

### **Constraints**

1. User must be family member
2. Valid notification types
3. Valid channels
4. Queue update validation
5. Settings consistency

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)

### **Dependencies**

- UC-017: Set Medication Reminder (để set reminders)
- UC-022: View Audit Logs (để view logs)

### **Related Features**

- Notification System: Send notifications
- Settings Management: View, edit settings
- Audit Logging: Track changes
- Notification History: View history

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Update notification queue efficiently
- Handle quiet hours logic
- Create comprehensive audit log
- Implement settings validation

### **Future Enhancements**

- Advanced notification rules
- Smart notification timing
- Notification analytics
- Custom notification flows

### **Known Limitations**

- Không có advanced rules
- Không có smart timing
- Không có analytics
- Không có custom flows

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
