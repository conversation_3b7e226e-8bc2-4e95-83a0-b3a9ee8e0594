# UC-022: View Audit Logs

## 📋 Overview

### **UC-ID**: UC-022

### **Title**: View Audit Logs

### **Actor**: Family Member (ADMIN/OWNER role)

### **Precondition**: User đã đăng nhập, có family context với ADMIN/OWNER role

### **Postcondition**: Audit logs được hiển thị với filtering và export options

### **Priority**: Low

### **Business Value**: <PERSON>ung cấp khả năng theo dõi và kiểm tra tất cả các hoạt động trong family để đảm bảo tính minh bạch và bảo mật

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Settings / Security / Audit Logs
- **User Actions**:
  1. Navigate to Settings
  2. Click "Security" section
  3. Select "Audit Logs"
  4. View audit logs list
  5. Apply filters (date range, user, action type, entity type)
  6. Choose sort order (timestamp, user, action)
  7. Select page size (10, 25, 50, 100)
  8. Search by keywords (optional)
  9. Click on log entry để view details
  10. Export logs (optional)
- **UI Elements**:
  - Date range picker
  - User filter dropdown
  - Action type filter
  - Entity type filter
  - Search box
  - Sort dropdown
  - Page size selector
  - Log entries list
  - Pagination controls
  - Export button
- **Validation**:
  - Valid date range
  - Valid filters
  - Valid sort order
  - Valid page size

### **Step 2: API Call Details**

- **Endpoint**: `GET /api/families/{familyId}/audit-logs`
- **Method**: GET
- **Headers**:
  ```
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Query Parameters**:
  ```
  startDate=2024-11-01&endDate=2024-11-30&userId=123&actionType=CREATE&entityType=MEDICINE&sortBy=timestamp&sortOrder=DESC&page=1&size=25&search=paracetamol
  ```

### **Step 3: Backend Processing**

- **Controller**: `AuditLogController.getAuditLogs()`
- **Service Layer**: `AuditLogService.getAuditLogs()`
- **Validation**:
  - User authentication check
  - Family membership validation với ADMIN/OWNER role
  - Filter parameters validation
- **Business Rules**:
  - User phải là family member với ADMIN/OWNER role
  - Valid date range
  - Valid filters
  - Pagination limits
  - Family context filtering
  - Data privacy compliance

### **Step 4: Database Operations**

- **Repository**: `AuditLogRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Get audit logs with filters
  SELECT
    al.id,
    al.user_id,
    al.action,
    al.entity_type,
    al.entity_id,
    al.details,
    al.created_at,
    u.name as user_name,
    u.email as user_email
  FROM audit_logs al
  JOIN users u ON al.user_id = u.id
  WHERE al.family_id = ?
    AND al.deleted_at IS NULL
    AND (? IS NULL OR al.created_at >= ?)
    AND (? IS NULL OR al.created_at <= ?)
    AND (? IS NULL OR al.user_id = ?)
    AND (? IS NULL OR al.action = ?)
    AND (? IS NULL OR al.entity_type = ?)
    AND (? IS NULL OR (
      LOWER(al.details) LIKE LOWER(?) OR
      LOWER(al.action) LIKE LOWER(?) OR
      LOWER(al.entity_type) LIKE LOWER(?)
    ))
  ORDER BY
    CASE WHEN ? = 'timestamp' THEN al.created_at END DESC,
    CASE WHEN ? = 'timestamp' AND ? = 'ASC' THEN al.created_at END ASC,
    CASE WHEN ? = 'user' THEN u.name END ASC,
    CASE WHEN ? = 'user' AND ? = 'DESC' THEN u.name END DESC,
    CASE WHEN ? = 'action' THEN al.action END ASC,
    CASE WHEN ? = 'action' AND ? = 'DESC' THEN al.action END DESC
  LIMIT ? OFFSET ?;

  -- Get total count for pagination
  SELECT COUNT(*) as total_count
  FROM audit_logs al
  WHERE al.family_id = ?
    AND al.deleted_at IS NULL
    AND (? IS NULL OR al.created_at >= ?)
    AND (? IS NULL OR al.created_at <= ?)
    AND (? IS NULL OR al.user_id = ?)
    AND (? IS NULL OR al.action = ?)
    AND (? IS NULL OR al.entity_type = ?)
    AND (? IS NULL OR (
      LOWER(al.details) LIKE LOWER(?) OR
      LOWER(al.action) LIKE LOWER(?) OR
      LOWER(al.entity_type) LIKE LOWER(?)
    ));

  -- Get audit log statistics
  SELECT
    COUNT(*) as total_logs,
    COUNT(DISTINCT al.user_id) as unique_users,
    COUNT(DISTINCT al.action) as unique_actions,
    COUNT(DISTINCT al.entity_type) as unique_entities,
    MIN(al.created_at) as earliest_log,
    MAX(al.created_at) as latest_log
  FROM audit_logs al
  WHERE al.family_id = ? AND al.deleted_at IS NULL;
  ```

- **Tables**: `audit_logs`, `users`, `family_members`
- **Constraints**:
  - Admin/OWNER role required
  - Family context filtering
  - Valid date range
  - Pagination limits

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Audit logs retrieved successfully",
    "data": {
      "filters": {
        "startDate": "2024-11-01",
        "endDate": "2024-11-30",
        "userId": 123,
        "actionType": "CREATE",
        "entityType": "MEDICINE",
        "search": "paracetamol"
      },
      "pagination": {
        "page": 1,
        "size": 25,
        "totalElements": 156,
        "totalPages": 7
      },
      "statistics": {
        "totalLogs": 156,
        "uniqueUsers": 4,
        "uniqueActions": 8,
        "uniqueEntities": 5,
        "earliestLog": "2024-11-01T00:00:00Z",
        "latestLog": "2024-11-30T23:59:59Z"
      },
      "logs": [
        {
          "id": 1234,
          "user": {
            "id": 123,
            "name": "John Doe",
            "email": "<EMAIL>"
          },
          "action": "CREATE_MEDICINE",
          "entityType": "MEDICINE_INVENTORY",
          "entityId": 456,
          "details": "Added Paracetamol 500mg to inventory",
          "timestamp": "2024-11-15T10:30:00Z"
        },
        {
          "id": 1235,
          "user": {
            "id": 124,
            "name": "Jane Smith",
            "email": "<EMAIL>"
          },
          "action": "UPDATE_COMPLIANCE",
          "entityType": "MEDICATION_SCHEDULE_INSTANCE",
          "entityId": 789,
          "details": "Marked medication as taken",
          "timestamp": "2024-11-15T09:15:00Z"
        }
      ],
      "nextSteps": {
        "exportLogs": true,
        "viewDetails": true,
        "setAlerts": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show audit logs list
  - Display pagination
  - Show statistics
  - Update filters
- **Navigation**: Stay on Audit Logs Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
GET /api/families/{familyId}/audit-logs?startDate={startDate}&endDate={endDate}&userId={userId}&actionType={actionType}&entityType={entityType}&sortBy={sortBy}&sortOrder={sortOrder}&page={page}&size={size}&search={search}
Accept: application/json
Authorization: Bearer [JWT_TOKEN]
```

#### **Query Parameters**

| Parameter  | Type    | Required | Description                                  |
| ---------- | ------- | -------- | -------------------------------------------- |
| startDate  | date    | No       | Start date (YYYY-MM-DD)                      |
| endDate    | date    | No       | End date (YYYY-MM-DD)                        |
| userId     | integer | No       | Filter by user ID                            |
| actionType | enum    | No       | Filter by action type                        |
| entityType | enum    | No       | Filter by entity type                        |
| sortBy     | enum    | No       | timestamp, user, action (default: timestamp) |
| sortOrder  | enum    | No       | ASC, DESC (default: DESC)                    |
| page       | integer | No       | Page number (default: 1)                     |
| size       | integer | No       | Page size (default: 25, max: 100)            |
| search     | string  | No       | Search keywords                              |

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "filters": "object",
    "pagination": {
      "page": "number",
      "size": "number",
      "totalElements": "number",
      "totalPages": "number"
    },
    "statistics": {
      "totalLogs": "number",
      "uniqueUsers": "number",
      "uniqueActions": "number",
      "uniqueEntities": "number",
      "earliestLog": "string (ISO 8601)",
      "latestLog": "string (ISO 8601)"
    },
    "logs": [
      {
        "id": "number",
        "user": {
          "id": "number",
          "name": "string",
          "email": "string"
        },
        "action": "string",
        "entityType": "string",
        "entityId": "number",
        "details": "string",
        "timestamp": "string (ISO 8601)"
      }
    ],
    "nextSteps": {
      "exportLogs": "boolean",
      "viewDetails": "boolean",
      "setAlerts": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Get audit logs with filters
SELECT
  al.id,
  al.user_id,
  al.action,
  al.entity_type,
  al.entity_id,
  al.details,
  al.created_at,
  u.name as user_name,
  u.email as user_email
FROM audit_logs al
JOIN users u ON al.user_id = u.id
WHERE al.family_id = ?
  AND al.deleted_at IS NULL
  AND (? IS NULL OR al.created_at >= ?)
  AND (? IS NULL OR al.created_at <= ?)
  AND (? IS NULL OR al.user_id = ?)
  AND (? IS NULL OR al.action = ?)
  AND (? IS NULL OR al.entity_type = ?)
  AND (? IS NULL OR (
    LOWER(al.details) LIKE LOWER(?) OR
    LOWER(al.action) LIKE LOWER(?) OR
    LOWER(al.entity_type) LIKE LOWER(?)
  ))
ORDER BY
  CASE WHEN ? = 'timestamp' THEN al.created_at END DESC,
  CASE WHEN ? = 'timestamp' AND ? = 'ASC' THEN al.created_at END ASC,
  CASE WHEN ? = 'user' THEN u.name END ASC,
  CASE WHEN ? = 'user' AND ? = 'DESC' THEN u.name END DESC,
  CASE WHEN ? = 'action' THEN al.action END ASC,
  CASE WHEN ? = 'action' AND ? = 'DESC' THEN al.action END DESC
LIMIT ? OFFSET ?;
```

#### **Related Queries**

```sql
-- Get audit log statistics
SELECT
  COUNT(*) as total_logs,
  COUNT(DISTINCT al.user_id) as unique_users,
  COUNT(DISTINCT al.action) as unique_actions,
  COUNT(DISTINCT al.entity_type) as unique_entities,
  MIN(al.created_at) as earliest_log,
  MAX(al.created_at) as latest_log
FROM audit_logs al
WHERE al.family_id = ? AND al.deleted_at IS NULL;
```

#### **Indexes Used**

- `audit_logs_family_idx`: Index on family_id
- `audit_logs_user_idx`: Index on user_id
- `audit_logs_action_idx`: Index on action
- `audit_logs_entity_idx`: Index on entity_type, entity_id
- `audit_logs_created_at_idx`: Index on created_at
- `audit_logs_family_created_idx`: Composite index on family_id, created_at

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description               |
| ---- | --------------------- | ------------------------- |
| 400  | Bad Request           | Invalid filter parameters |
| 401  | Unauthorized          | User not authenticated    |
| 403  | Forbidden             | Insufficient permissions  |
| 404  | Not Found             | Family not found          |
| 422  | Unprocessable Entity  | Validation failed         |
| 500  | Internal Server Error | Database error            |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "message": "Only family admin can view audit logs",
    "details": "Please contact family admin for access"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 3 seconds
- **Database Load**: Medium (complex queries with filters)
- **Caching**: Audit log caching
- **Optimization**: Pagination và indexing

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền ADMIN, có audit logs
2. **Steps**:
   - Date range: Last 30 days
   - Action type: CREATE
   - Sort: timestamp DESC
   - Page size: 25
   - Click View Logs
3. **Expected Result**:
   - Logs được hiển thị
   - Pagination working
   - Statistics displayed
   - Filters applied

### **Error Cases**

1. **Invalid Input**:
   - Invalid date range
   - Invalid filters
   - Invalid sort order
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không có quyền ADMIN
3. **Business Logic**:
   - Family không tồn tại
   - No audit logs
   - Invalid pagination
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Large date range (1 year)
   - No logs in date range
   - Maximum page size (100)
   - Empty search results
2. **Concurrent Access**:
   - 2 users cùng view logs
   - Race conditions
3. **Large Data**:
   - Many audit logs
   - Large pagination
   - Complex filters
4. **Special Characters**:
   - Search với Unicode characters
   - Special characters in details

### **Performance Tests**

1. **Load Testing**: 30 concurrent log views
2. **Stress Testing**: 100 concurrent log views
3. **Endurance Testing**: Continuous views for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member with ADMIN/OWNER role
- Role-based access control
- Family context validation

### **Data Validation**

- Filter parameters validation
- Date range validation
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Audit log privacy protection
- Sensitive data filtering
- Data integrity

## 📊 Business Rules

### **Validation Rules**

1. User phải có quyền ADMIN/OWNER
2. Valid date range
3. Valid filters
4. Valid sort order
5. Pagination limits

### **Business Logic**

1. Validate user permissions
2. Apply family context filters
3. Apply search filters
4. Handle pagination
5. Return formatted results

### **Constraints**

1. Admin/OWNER role required
2. Family context filtering
3. Valid date range
4. Pagination limits (max 100 per page)
5. Data privacy compliance

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-008: Manage Family Roles (có quyền ADMIN)

### **Dependencies**

- UC-020: Export Family Data (để export logs)
- UC-021: Manage Notifications (để set alerts)

### **Related Features**

- Security Management: View security logs
- Data Export: Export audit logs
- Alert System: Set audit alerts
- Compliance Tracking: Track activities

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng efficient indexing cho performance
- Implement proper filtering
- Handle large data sets
- Apply data privacy controls
- Create comprehensive search

### **Future Enhancements**

- Advanced analytics
- Real-time monitoring
- Automated alerts
- Compliance reporting

### **Known Limitations**

- Không có advanced analytics
- Không có real-time monitoring
- Không có automated alerts
- Không có compliance reporting

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
