# UC-023: Backup Restore Data

## 📋 Overview

### **UC-ID**: UC-023

### **Title**: Backup Restore Data

### **Actor**: Family Member (ADMIN/OWNER role)

### **Precondition**: User đã đăng nhập, có family context với ADMIN/OWNER role

### **Postcondition**: Family data được backup hoặc restore thành công

### **Priority**: Low

### **Business Value**: <PERSON><PERSON><PERSON> bảo an toàn dữ liệu family thông qua backup định kỳ và khả năng khôi phục khi cần thiết

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Settings / Data Management / Backup & Restore
- **User Actions**:
  1. Navigate to Settings
  2. Click "Data Management" section
  3. Select "Backup & Restore"
  4. Choose operation (BACKUP, RESTORE)
  5. For backup: Select backup type (FULL, INCREMENTAL, SELECTIVE)
  6. For backup: Choose backup schedule (<PERSON><PERSON><PERSON>, DAIL<PERSON>, WEEKLY, MONTHLY)
  7. For backup: Set retention period (7 days, 30 days, 90 days, 1 year)
  8. For restore: Select backup file từ list
  9. For restore: Choose restore mode (FULL, SELECTIVE)
  10. Confirm operation và click "Execute"
- **UI Elements**:
  - Operation selector (Backup/Restore)
  - Backup type dropdown
  - Schedule selector
  - Retention period picker
  - Backup file list
  - Restore mode selector
  - Execute button
  - Progress indicator
  - Backup history
- **Validation**:
  - Operation selection required
  - Valid backup type
  - Valid schedule
  - Valid retention period
  - Valid backup file (for restore)

### **Step 2: API Call Details**

- **Endpoint**: `POST /api/families/{familyId}/backup-restore`
- **Method**: POST
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "operation": "BACKUP",
    "backupType": "FULL",
    "schedule": "MANUAL",
    "retentionPeriod": 30,
    "includeSensitiveData": true,
    "compression": true,
    "encryption": true
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `BackupRestoreController.executeOperation()`
- **Service Layer**: `BackupRestoreService.executeOperation()`
- **Validation**:
  - User authentication check
  - Family membership validation với ADMIN/OWNER role
  - Operation parameters validation
- **Business Rules**:
  - User phải là family member với ADMIN/OWNER role
  - Valid operation type
  - Valid backup parameters
  - Data integrity checks
  - Execute backup/restore
  - Update backup metadata
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `BackupRestoreRepository`, `FamilyRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Get family data for backup
  SELECT
    f.id, f.name, f.description, f.created_at, f.updated_at,
    u.name as owner_name, u.email as owner_email
  FROM families f
  JOIN users u ON f.owner_id = u.id
  WHERE f.id = ? AND f.deleted_at IS NULL;

  -- Get family members data
  SELECT
    fm.id, fm.role, fm.status, fm.joined_at,
    u.id as user_id, u.name, u.email, u.phone
  FROM family_members fm
  JOIN users u ON fm.user_id = u.id
  WHERE fm.family_id = ? AND fm.deleted_at IS NULL;

  -- Get complete family data (all tables)
  -- This is a complex query that exports all family-related data
  -- Including medicines, prescriptions, schedules, compliance, etc.

  -- Create backup record
  INSERT INTO data_backups (family_id, user_id, operation_type, backup_type, schedule, retention_period, file_path, file_size, checksum, status, created_at, updated_at)
  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'COMPLETED', NOW(), NOW())
  RETURNING id;

  -- Update backup metadata
  UPDATE data_backups
  SET file_size = ?, checksum = ?, status = 'COMPLETED', updated_at = NOW()
  WHERE id = ?;

  -- Clean up old backups based on retention
  DELETE FROM data_backups
  WHERE family_id = ?
    AND created_at < NOW() - INTERVAL '? days'
    AND status = 'COMPLETED';

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'BACKUP_FAMILY_DATA', 'DATA_BACKUP', ?, ?, NOW());
  ```

- **Tables**: `families`, `family_members`, `users`, `medicine_inventory`, `medicine_types`, `prescriptions`, `prescription_medicines`, `medication_schedules`, `medication_schedule_instances`, `data_backups`, `audit_logs`
- **Constraints**:
  - Admin/OWNER role required
  - Valid operation parameters
  - Data integrity
  - File generation

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Backup operation completed successfully",
    "data": {
      "operation": {
        "id": 456,
        "operationType": "BACKUP",
        "backupType": "FULL",
        "schedule": "MANUAL",
        "retentionPeriod": 30,
        "filePath": "/backups/family_123_backup_456.zip",
        "fileSize": "15.2MB",
        "checksum": "sha256:abc123...",
        "status": "COMPLETED",
        "createdAt": "2024-12-01T10:00:00Z"
      },
      "summary": {
        "totalRecords": 1250,
        "familyMembers": 4,
        "medicines": 45,
        "prescriptions": 12,
        "schedules": 8,
        "complianceRecords": 1181
      },
      "metadata": {
        "compressionRatio": 0.75,
        "encryptionEnabled": true,
        "backupDuration": "45 seconds",
        "dataIntegrity": "VERIFIED"
      },
      "downloadUrl": "https://api.medication.com/backups/download/456",
      "expiresAt": "2025-01-01T10:00:00Z",
      "nextSteps": {
        "downloadBackup": true,
        "scheduleBackup": true,
        "viewHistory": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Display operation summary
  - Show download link
  - Update backup history
- **Navigation**: Redirect to Backup Details Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
POST /api/families/{familyId}/backup-restore
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "operation": "enum (required: BACKUP, RESTORE)",
  "backupType": "enum (required for backup: FULL, INCREMENTAL, SELECTIVE)",
  "schedule": "enum (required: MANUAL, DAILY, WEEKLY, MONTHLY)",
  "retentionPeriod": "integer (required, days)",
  "includeSensitiveData": "boolean (default: true)",
  "compression": "boolean (default: true)",
  "encryption": "boolean (default: true)",
  "backupFileId": "number (required for restore)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "operation": {
      "id": "number",
      "operationType": "string",
      "backupType": "string",
      "schedule": "string",
      "retentionPeriod": "number",
      "filePath": "string",
      "fileSize": "string",
      "checksum": "string",
      "status": "string (COMPLETED)",
      "createdAt": "string (ISO 8601)"
    },
    "summary": {
      "totalRecords": "number",
      "familyMembers": "number",
      "medicines": "number",
      "prescriptions": "number",
      "schedules": "number",
      "complianceRecords": "number"
    },
    "metadata": {
      "compressionRatio": "number",
      "encryptionEnabled": "boolean",
      "backupDuration": "string",
      "dataIntegrity": "string"
    },
    "downloadUrl": "string",
    "expiresAt": "string (ISO 8601)",
    "nextSteps": {
      "downloadBackup": "boolean",
      "scheduleBackup": "boolean",
      "viewHistory": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Create backup record
INSERT INTO data_backups (family_id, user_id, operation_type, backup_type, schedule, retention_period, file_path, file_size, checksum, status, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'COMPLETED', NOW(), NOW())
RETURNING id;
```

#### **Related Queries**

```sql
-- Get complete family data for backup
SELECT
  f.id, f.name, f.description, f.created_at, f.updated_at,
  u.name as owner_name, u.email as owner_email
FROM families f
JOIN users u ON f.owner_id = u.id
WHERE f.id = ? AND f.deleted_at IS NULL;

-- Clean up old backups
DELETE FROM data_backups
WHERE family_id = ?
  AND created_at < NOW() - INTERVAL '? days'
  AND status = 'COMPLETED';
```

#### **Indexes Used**

- `data_backups_family_idx`: Index on family_id
- `data_backups_user_idx`: Index on user_id
- `data_backups_created_at_idx`: Index on created_at
- `data_backups_status_idx`: Index on status

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description                     |
| ---- | --------------------- | ------------------------------- |
| 400  | Bad Request           | Invalid operation parameters    |
| 401  | Unauthorized          | User not authenticated          |
| 403  | Forbidden             | Insufficient permissions        |
| 404  | Not Found             | Family or backup file not found |
| 422  | Unprocessable Entity  | Validation failed               |
| 500  | Internal Server Error | Backup/restore failed           |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "BACKUP_FAILED",
    "message": "Backup operation failed",
    "details": "Insufficient storage space"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 60 seconds
- **Database Load**: Heavy (complex data export)
- **Caching**: Backup metadata caching
- **Optimization**: Async backup processing

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền ADMIN, có family data
2. **Steps**:
   - Operation: BACKUP
   - Type: FULL
   - Schedule: MANUAL
   - Retention: 30 days
   - Click Execute
3. **Expected Result**:
   - Backup được tạo thành công
   - File được generate
   - Download link available
   - Summary được hiển thị

### **Error Cases**

1. **Invalid Input**:
   - Invalid operation type
   - Invalid backup type
   - Invalid retention period
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không có quyền ADMIN
3. **Business Logic**:
   - Family không tồn tại
   - No data to backup
   - Backup file not found (restore)
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Large family data
   - Maximum retention period
   - Empty family data
2. **Concurrent Access**:
   - 2 users cùng backup
   - Race conditions
3. **Large Data**:
   - Many family members
   - Large medicine inventory
   - Many prescriptions
4. **Special Characters**:
   - Family name với Unicode
   - Special characters in data

### **Performance Tests**

1. **Load Testing**: 5 concurrent backups
2. **Stress Testing**: 10 concurrent backups
3. **Endurance Testing**: Continuous backups for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member with ADMIN/OWNER role
- Role-based access control
- Family context validation

### **Data Validation**

- Operation parameters validation
- Data integrity checks
- Input sanitization
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Backup encryption
- Secure file storage
- Data privacy compliance

## 📊 Business Rules

### **Validation Rules**

1. User phải có quyền ADMIN/OWNER
2. Valid operation type
3. Valid backup parameters
4. Valid retention period
5. Data integrity required

### **Business Logic**

1. Validate user permissions
2. Check data availability
3. Execute backup/restore
4. Update backup metadata
5. Create audit log

### **Constraints**

1. Admin/OWNER role required
2. Valid operation parameters
3. Data integrity
4. File generation
5. Retention policies

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-008: Manage Family Roles (có quyền ADMIN)

### **Dependencies**

- UC-020: Export Family Data (để export)
- UC-022: View Audit Logs (để view logs)

### **Related Features**

- Data Management: View, manage backups
- Security System: Data protection
- Audit Logging: Track operations
- Backup History: View history

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng async processing cho backup operations
- Implement file compression và encryption
- Handle large data sets efficiently
- Apply data integrity checks
- Create comprehensive audit log

### **Future Enhancements**

- Automated backups
- Cloud storage integration
- Incremental backups
- Backup verification

### **Known Limitations**

- Không có automated backups
- Không có cloud storage
- Không có incremental backups
- Không có backup verification

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
