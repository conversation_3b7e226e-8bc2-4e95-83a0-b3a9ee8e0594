# UC-024: System Settings

## 📋 Overview

### **UC-ID**: UC-024

### **Title**: System Settings

### **Actor**: Family Member (ADMIN/OWNER role)

### **Precondition**: User đã đăng nhập, có family context với ADMIN/OWNER role

### **Postcondition**: System settings được cập nhật và áp dụng cho family

### **Priority**: Low

### **Business Value**: Cho phép tùy chỉnh cấu hình hệ thống để phù hợp với nhu cầu và preferences của family

## 🔄 Detailed Flow

### **Step 1: User Interface Actions**

- **Screen**: Settings / System / System Settings
- **User Actions**:
  1. Navigate to Settings
  2. Click "System" section
  3. Select "System Settings"
  4. View current system settings
  5. Configure language preferences (VIETNAMESE, ENGLISH)
  6. Set timezone (Asia/Ho_Chi_Minh, UTC, etc.)
  7. Choose date format (DD/MM/YYYY, MM/DD/YYYY, YYYY-MM-DD)
  8. Set time format (12-hour, 24-hour)
  9. Configure currency (VND, USD, EUR)
  10. Set measurement units (METRIC, IMPERIAL)
  11. Configure privacy settings
  12. Set data retention policies
  13. Click "Save Settings" button
- **UI Elements**:
  - Language selector
  - Timezone picker
  - Date format dropdown
  - Time format toggle
  - Currency selector
  - Measurement units toggle
  - Privacy settings panel
  - Data retention settings
  - Save Settings button
  - Reset to Default button
  - Settings preview
- **Validation**:
  - Valid language code
  - Valid timezone
  - Valid date format
  - Valid time format
  - Valid currency code

### **Step 2: API Call Details**

- **Endpoint**: `PUT /api/families/{familyId}/system-settings`
- **Method**: PUT
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer [JWT_TOKEN]
  ```
- **Request Body**:
  ```json
  {
    "language": "VIETNAMESE",
    "timezone": "Asia/Ho_Chi_Minh",
    "dateFormat": "DD/MM/YYYY",
    "timeFormat": "24_HOUR",
    "currency": "VND",
    "measurementUnits": "METRIC",
    "privacySettings": {
      "shareDataWithDoctors": true,
      "allowAnalytics": false,
      "enableNotifications": true,
      "dataSharingLevel": "FAMILY_ONLY"
    },
    "dataRetention": {
      "auditLogsRetention": 90,
      "backupRetention": 365,
      "exportRetention": 30,
      "complianceDataRetention": 730
    }
  }
  ```

### **Step 3: Backend Processing**

- **Controller**: `SystemSettingsController.updateSettings()`
- **Service Layer**: `SystemSettingsService.updateSettings()`
- **Validation**:
  - User authentication check
  - Family membership validation với ADMIN/OWNER role
  - Settings validation
- **Business Rules**:
  - User phải là family member với ADMIN/OWNER role
  - Valid settings values
  - Settings consistency
  - Update system settings
  - Apply settings to family
  - Create audit log

### **Step 4: Database Operations**

- **Repository**: `SystemSettingsRepository`, `FamilyRepository`
- **Queries**:

  ```sql
  -- Check user permission in family
  SELECT fm.role FROM family_members fm
  WHERE fm.family_id = ? AND fm.user_id = ? AND fm.status = 'ACTIVE';

  -- Get current system settings
  SELECT
    ss.id, ss.language, ss.timezone, ss.date_format, ss.time_format,
    ss.currency, ss.measurement_units, ss.privacy_settings, ss.data_retention
  FROM system_settings ss
  WHERE ss.family_id = ? AND ss.deleted_at IS NULL;

  -- Update system settings
  UPDATE system_settings
  SET language = ?, timezone = ?, date_format = ?, time_format = ?,
      currency = ?, measurement_units = ?, privacy_settings = ?,
      data_retention = ?, updated_at = NOW()
  WHERE family_id = ? AND deleted_at IS NULL;

  -- Insert if not exists
  INSERT INTO system_settings (family_id, language, timezone, date_format, time_format, currency, measurement_units, privacy_settings, data_retention, created_at, updated_at)
  SELECT ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
  WHERE NOT EXISTS (
    SELECT 1 FROM system_settings
    WHERE family_id = ? AND deleted_at IS NULL
  );

  -- Update family preferences
  UPDATE families
  SET language = ?, timezone = ?, updated_at = NOW()
  WHERE id = ?;

  -- Apply data retention policies
  DELETE FROM audit_logs
  WHERE family_id = ?
    AND created_at < NOW() - INTERVAL '? days';

  DELETE FROM data_backups
  WHERE family_id = ?
    AND created_at < NOW() - INTERVAL '? days';

  DELETE FROM data_exports
  WHERE family_id = ?
    AND created_at < NOW() - INTERVAL '? days';

  -- Create audit log
  INSERT INTO audit_logs (user_id, family_id, action, entity_type, entity_id, details, created_at)
  VALUES (?, ?, 'UPDATE_SYSTEM_SETTINGS', 'SYSTEM_SETTINGS', ?, ?, NOW());
  ```

- **Tables**: `system_settings`, `families`, `audit_logs`, `data_backups`, `data_exports`, `family_members`
- **Constraints**:
  - Admin/OWNER role required
  - Valid settings values
  - Settings consistency
  - Data retention policies

### **Step 5: Response Handling**

- **Success Response**:
  ```json
  {
    "success": true,
    "message": "System settings updated successfully",
    "data": {
      "settings": {
        "id": 789,
        "language": "VIETNAMESE",
        "timezone": "Asia/Ho_Chi_Minh",
        "dateFormat": "DD/MM/YYYY",
        "timeFormat": "24_HOUR",
        "currency": "VND",
        "measurementUnits": "METRIC",
        "privacySettings": {
          "shareDataWithDoctors": true,
          "allowAnalytics": false,
          "enableNotifications": true,
          "dataSharingLevel": "FAMILY_ONLY"
        },
        "dataRetention": {
          "auditLogsRetention": 90,
          "backupRetention": 365,
          "exportRetention": 30,
          "complianceDataRetention": 730
        },
        "updatedAt": "2024-12-01T10:00:00Z"
      },
      "summary": {
        "cleanedAuditLogs": 45,
        "cleanedBackups": 3,
        "cleanedExports": 8,
        "totalCleanedRecords": 56
      },
      "nextSteps": {
        "restartApp": false,
        "clearCache": true,
        "viewChanges": true
      }
    }
  }
  ```
- **Error Response**: Hiển thị error message trên UI
- **UI Update**:
  - Show success message
  - Update settings display
  - Show cleanup summary
  - Update family preferences
- **Navigation**: Stay on System Settings Screen

## 🔧 Technical Details

### **API Specification**

#### **Request**

```http
PUT /api/families/{familyId}/system-settings
Content-Type: application/json
Accept: application/json
Authorization: Bearer [JWT_TOKEN]

{
  "language": "enum (required: VIETNAMESE, ENGLISH)",
  "timezone": "string (required, valid timezone)",
  "dateFormat": "enum (required: DD/MM/YYYY, MM/DD/YYYY, YYYY-MM-DD)",
  "timeFormat": "enum (required: 12_HOUR, 24_HOUR)",
  "currency": "string (required, valid currency code)",
  "measurementUnits": "enum (required: METRIC, IMPERIAL)",
  "privacySettings": "object (required)",
  "dataRetention": "object (required)"
}
```

#### **Response**

```json
{
  "success": "boolean",
  "message": "string",
  "data": {
    "settings": {
      "id": "number",
      "language": "string",
      "timezone": "string",
      "dateFormat": "string",
      "timeFormat": "string",
      "currency": "string",
      "measurementUnits": "string",
      "privacySettings": "object",
      "dataRetention": "object",
      "updatedAt": "string (ISO 8601)"
    },
    "summary": {
      "cleanedAuditLogs": "number",
      "cleanedBackups": "number",
      "cleanedExports": "number",
      "totalCleanedRecords": "number"
    },
    "nextSteps": {
      "restartApp": "boolean",
      "clearCache": "boolean",
      "viewChanges": "boolean"
    }
  },
  "error": {
    "code": "string",
    "message": "string",
    "details": "string"
  }
}
```

### **Database Operations**

#### **Primary Query**

```sql
-- Update system settings
UPDATE system_settings
SET language = ?, timezone = ?, date_format = ?, time_format = ?,
    currency = ?, measurement_units = ?, privacy_settings = ?,
    data_retention = ?, updated_at = NOW()
WHERE family_id = ? AND deleted_at IS NULL;
```

#### **Related Queries**

```sql
-- Apply data retention policies
DELETE FROM audit_logs
WHERE family_id = ?
  AND created_at < NOW() - INTERVAL '? days';

-- Update family preferences
UPDATE families
SET language = ?, timezone = ?, updated_at = NOW()
WHERE id = ?;
```

#### **Indexes Used**

- `system_settings_family_idx`: Index on family_id
- `audit_logs_family_created_idx`: Composite index on family_id, created_at
- `data_backups_family_created_idx`: Composite index on family_id, created_at
- `data_exports_family_created_idx`: Composite index on family_id, created_at

### **Error Handling**

#### **Error Codes**

| Code | Message               | Description              |
| ---- | --------------------- | ------------------------ |
| 400  | Bad Request           | Invalid settings values  |
| 401  | Unauthorized          | User not authenticated   |
| 403  | Forbidden             | Insufficient permissions |
| 404  | Not Found             | Family not found         |
| 422  | Unprocessable Entity  | Validation failed        |
| 500  | Internal Server Error | Settings update failed   |

#### **Error Response Format**

```json
{
  "success": false,
  "error": {
    "code": "INVALID_TIMEZONE",
    "message": "Invalid timezone specified",
    "details": "Please select a valid timezone from the list"
  }
}
```

### **Performance Considerations**

- **Response Time**: < 5 seconds
- **Database Load**: Medium (multiple operations)
- **Caching**: Settings caching
- **Optimization**: Batch cleanup operations

## 🧪 Testing Scenarios

### **Happy Path**

1. **Prerequisites**: User có quyền ADMIN, có system settings
2. **Steps**:
   - Language: VIETNAMESE
   - Timezone: Asia/Ho_Chi_Minh
   - Date format: DD/MM/YYYY
   - Time format: 24_HOUR
   - Click Save Settings
3. **Expected Result**:
   - Settings được cập nhật
   - Cleanup được thực hiện
   - Summary được hiển thị

### **Error Cases**

1. **Invalid Input**:
   - Invalid language code
   - Invalid timezone
   - Invalid date format
   - Invalid currency
2. **Unauthorized Access**:
   - User chưa đăng nhập
   - User không có quyền ADMIN
3. **Business Logic**:
   - Family không tồn tại
   - Invalid settings combination
4. **Network Error**:
   - API timeout
   - Database connection failed

### **Edge Cases**

1. **Boundary Values**:
   - Minimum retention periods
   - Maximum retention periods
   - All privacy settings disabled
2. **Concurrent Access**:
   - 2 users cùng update settings
   - Race conditions
3. **Large Data**:
   - Many records to cleanup
   - Large settings objects
4. **Special Characters**:
   - Unicode trong settings
   - Special characters

### **Performance Tests**

1. **Load Testing**: 10 concurrent settings updates
2. **Stress Testing**: 30 concurrent settings updates
3. **Endurance Testing**: Continuous updates for 1 hour

## 🔐 Security Considerations

### **Authentication**

- JWT token required
- User must be authenticated
- Token validation

### **Authorization**

- User must be family member with ADMIN/OWNER role
- Role-based access control
- Family context validation

### **Data Validation**

- Settings values validation
- Input sanitization
- Format validation
- SQL injection prevention

### **Data Protection**

- Family data isolation
- Settings privacy protection
- Data retention compliance
- Audit logging

## 📊 Business Rules

### **Validation Rules**

1. User phải có quyền ADMIN/OWNER
2. Valid language code
3. Valid timezone
4. Valid date/time formats
5. Valid currency code

### **Business Logic**

1. Validate user permissions
2. Check settings values
3. Update system settings
4. Apply data retention
5. Create audit log

### **Constraints**

1. Admin/OWNER role required
2. Valid settings values
3. Settings consistency
4. Data retention policies
5. Privacy compliance

## 🔗 Related User Cases

### **Prerequisites**

- UC-002: User Login (user phải đăng nhập)
- UC-005: Create Family (family phải tồn tại)
- UC-008: Manage Family Roles (có quyền ADMIN)

### **Dependencies**

- UC-021: Manage Notifications (để configure notifications)
- UC-022: View Audit Logs (để view logs)
- UC-023: Backup Restore Data (để configure backups)

### **Related Features**

- Settings Management: View, edit settings
- Privacy Controls: Configure privacy
- Data Management: Configure retention
- Localization: Language và timezone

## 📝 Notes & Considerations

### **Implementation Notes**

- Sử dụng transaction để đảm bảo data consistency
- Apply settings immediately
- Handle data cleanup efficiently
- Create comprehensive audit log
- Implement settings validation

### **Future Enhancements**

- Advanced privacy controls
- Custom themes
- Advanced localization
- Settings templates

### **Known Limitations**

- Không có advanced privacy controls
- Không có custom themes
- Không có advanced localization
- Không có settings templates

---

## 📞 Contact & Support

**Developer**: Backend Team  
**Reviewer**: Tech Lead  
**Business Owner**: Product Manager

**Created**: December 2024  
**Last Updated**: December 2024  
**Version**: 1.0  
**Status**: Draft
