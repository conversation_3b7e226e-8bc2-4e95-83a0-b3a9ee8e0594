# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/
pubspec.lock

# iOS related
**/ios/Podfile
**/ios/Podfile.lock
**/ios/Pods/
**/ios/.symlinks/
**/ios/Flutter/flutter_export_environment.sh

# macOS related
**/macos/Podfile
**/macos/Podfile.lock
**/macos/Pods/
**/macos/.symlinks/
**/macos/Flutter/flutter_export_environment.sh

# Android related
**/android/local.properties
**/android/gradle.properties
**/android/.gradle/
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/key.properties

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Web related
/web/build/

# Windows related
/windows/flutter/

# Linux related
/linux/flutter/

# Coverage
coverage/
lcov.info

# Environment files
.env
.env.local
.env.production
.env.development

# Firebase
**/firebase_options.dart
**/.firebase/

# Generated files
**/*.g.dart
**/*.freezed.dart
**/*.mocks.dart

# IDE
.vscode/launch.json
.vscode/settings.json

# FVM Version Cache
.fvm/