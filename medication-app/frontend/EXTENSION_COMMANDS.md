# Flutter Intl Extension Commands Guide

## 🎯 Available Commands

After installing the Flutter Intl extension, you can access these commands via Command Palette (`Cmd+Shift+P` / `Ctrl+Shift+P`):

### 1. **Flutter Intl: Initialize**
- **Purpose**: Initialize Flutter Intl in your project
- **When to use**: First time setup or reset
- **What it does**:
  - Creates `lib/l10n/` directory
  - Creates template ARB file (`intl_en.arb`)
  - Generates initial localization files
  - Updates VS Code settings

### 2. **Flutter Intl: Add locale**
- **Purpose**: Add support for a new language
- **When to use**: Adding Vietnamese, Spanish, etc.
- **What it does**:
  - Creates new ARB file (e.g., `intl_vi.arb`)
  - Updates generated files
  - Adds locale to supported list

### 3. **Flutter Intl: Add key**
- **Purpose**: Add a new translation key
- **When to use**: Adding new translatable text
- **What it does**:
  - Prompts for key name and description
  - Adds key to all ARB files
  - Regenerates localization files
  - Creates getter method in S class

### 4. **Flutter Intl: Generate**
- **Purpose**: Manually regenerate localization files
- **When to use**: After manual ARB edits or troubleshooting
- **What it does**:
  - Reads all ARB files
  - Generates Dart localization classes
  - Updates method signatures

### 5. **Flutter Intl: Extract to ARB**
- **Purpose**: Extract hardcoded strings to ARB files
- **When to use**: Refactoring existing code
- **What it does**:
  - Finds hardcoded strings in selected code
  - Creates ARB entries
  - Replaces strings with S.of(context) calls

## 🔧 How to Use Commands

### Adding a New Translation Key

1. **Open Command Palette**: `Cmd+Shift+P` (Mac) / `Ctrl+Shift+P` (Windows/Linux)

2. **Type**: `Flutter Intl: Add key`

3. **Enter Key Name**: e.g., `welcomeBack`

4. **Enter Description**: e.g., "Welcome back message for returning users"

5. **Choose Parameters** (if needed):
   - Simple text: No parameters
   - With variables: Add placeholders like `{userName}`
   - With plurals: Add count parameter

6. **Result**: 
   - Key added to all ARB files
   - Method `String get welcomeBack` added to S class
   - Ready to use: `S.of(context).welcomeBack`

### Adding a New Locale

1. **Command**: `Flutter Intl: Add locale`

2. **Enter Locale Code**: e.g., `es` for Spanish, `fr` for French

3. **Result**:
   - New ARB file created: `intl_es.arb`
   - All existing keys copied with empty values
   - Generated files updated
   - Locale added to `S.supportedLocales`

### Extracting Hardcoded Strings

1. **Select Text**: Highlight hardcoded string in your code

2. **Command**: `Flutter Intl: Extract to ARB`

3. **Enter Key Name**: e.g., `buttonText`

4. **Result**:
   - String moved to ARB files
   - Code updated to use `S.of(context).buttonText`
   - All locales get the key

## 📝 Example Workflow

### Scenario: Adding a new feature with translations

1. **Write Code with Hardcoded Strings**:
```dart
Text('Delete this medication?')
ElevatedButton(
  onPressed: () {},
  child: Text('Confirm Delete'),
)
```

2. **Extract First String**:
   - Select `'Delete this medication?'`
   - Command: `Flutter Intl: Extract to ARB`
   - Key: `deleteMedicationConfirm`
   - Description: "Confirmation message for deleting medication"

3. **Extract Second String**:
   - Select `'Confirm Delete'`
   - Command: `Flutter Intl: Extract to ARB`
   - Key: `confirmDelete`
   - Description: "Confirm delete button text"

4. **Result**:
```dart
Text(S.of(context).deleteMedicationConfirm)
ElevatedButton(
  onPressed: () {},
  child: Text(S.of(context).confirmDelete),
)
```

5. **Translate**:
   - Open `intl_vi.arb`
   - Add Vietnamese translations:
   ```json
   {
     "deleteMedicationConfirm": "Xóa thuốc này?",
     "confirmDelete": "Xác nhận xóa"
   }
   ```

## 🎨 Extension Features in Action

### Auto-completion
- Type `S.of(context).` → See all available keys
- IntelliSense shows parameter types
- Documentation from ARB descriptions

### Go to Definition
- `Cmd+Click` on `S.of(context).keyName`
- Jumps to ARB file definition
- Navigate between locale files

### Validation
- Red underlines for missing translations
- Warnings for parameter mismatches
- ARB syntax validation

### Refactoring
- Rename key across all files
- Update method signatures automatically
- Maintain consistency

## 🚨 Common Issues & Solutions

### Command Not Found
- **Issue**: Flutter Intl commands not appearing
- **Solution**: 
  - Ensure extension is installed and enabled
  - Restart VS Code
  - Check workspace has `pubspec.yaml`

### Generation Fails
- **Issue**: Error when running commands
- **Solution**:
  - Check ARB file syntax (valid JSON)
  - Ensure all locales have matching keys
  - Clear `lib/generated/intl/` and regenerate

### Keys Not Updating
- **Issue**: New keys not appearing in code
- **Solution**:
  - Run `Flutter Intl: Generate` manually
  - Check import statement: `import 'generated/intl/intl_localizations.dart';`
  - Restart Dart analysis server

### Missing Translations
- **Issue**: Some locales missing translations
- **Solution**:
  - Extension highlights missing keys
  - Add translations to all ARB files
  - Use fallback to template locale

## 🎯 Best Practices

### Key Naming
- Use camelCase: `welcomeMessage`, `confirmDelete`
- Be descriptive: `medicationDeleteConfirm` vs `confirm`
- Group related keys: `medication*`, `reminder*`

### Descriptions
- Always add meaningful descriptions
- Include context: "Button text for..." vs "Delete"
- Mention parameters: "Message with {count} parameter"

### Organization
- Keep ARB files organized
- Group related translations
- Use consistent formatting

### Workflow
- Add keys through extension (not manually)
- Translate immediately after adding
- Test in both languages
- Use extraction for refactoring

This extension significantly improves the internationalization workflow by providing IDE integration, validation, and automation tools.
