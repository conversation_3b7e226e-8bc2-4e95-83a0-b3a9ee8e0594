# Flutter Bloc Setup Guide

## 📋 Overview

This document outlines the complete migration from Riverpod to Flutter Bloc for state management in the Medication Manager app, with a centralized AppConfig system.

## 🔧 Dependencies

### Added Dependencies
```yaml
dependencies:
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

# Removed
# flutter_riverpod: ^2.4.9 (removed)
```

## 🏗️ Architecture Overview

### Centralized Configuration Management
All app settings are now managed through a single `AppConfig` system:
- **Theme settings** (light/dark/system)
- **Language settings** (English/Vietnamese)
- **App state** (first launch, onboarding)
- **Notification settings**
- **Security settings** (biometric)
- **Data settings** (auto backup)
- **Accessibility settings** (font size, high contrast, animations)

## 📁 File Structure

```
lib/core/config/
├── app_config.dart              # AppConfig model with all settings
├── app_config_bloc.dart         # Bloc for state management
├── app_config_event.dart        # All possible events
├── app_config_repository.dart   # Persistence layer
├── app_config_state.dart        # State definitions
└── config.dart                  # Export file

lib/main.dart                    # Updated to use Bloc
lib/app/pages/
└── responsive_demo_page.dart    # Updated demo page with Bloc
```

## 🎯 Key Components

### 1. AppConfig Model
```dart
class AppConfig extends Equatable {
  const AppConfig({
    this.themeMode = AppThemeMode.system,
    this.language = AppLanguage.english,
    this.isFirstLaunch = true,
    this.enableNotifications = true,
    this.enableBiometric = false,
    this.autoBackup = true,
    this.reminderSound = true,
    this.vibration = true,
    this.fontSize = 1.0,
    this.highContrast = false,
    this.reduceAnimations = false,
  });
  
  // ... methods for JSON serialization, copyWith, etc.
}
```

### 2. AppConfig Events
- `AppConfigLoadRequested` - Load config from storage
- `AppConfigResetRequested` - Reset to default
- `AppConfigThemeChanged` - Change theme mode
- `AppConfigLanguageChanged` - Change language
- `AppConfigNotificationsToggled` - Toggle notifications
- `AppConfigBiometricToggled` - Toggle biometric auth
- And many more...

### 3. AppConfig State
```dart
class AppConfigState extends Equatable {
  const AppConfigState({
    this.status = AppConfigStatus.initial,
    this.config = AppConfig.defaultConfig,
    this.errorMessage,
    this.isFirstTime = true,
  });
  
  // Convenience getters
  bool get isLoading => status == AppConfigStatus.loading;
  AppThemeMode get themeMode => config.themeMode;
  AppLanguage get language => config.language;
  // ... many more getters
}
```

### 4. AppConfig Repository
- **Persistence**: Uses SharedPreferences for storage
- **JSON serialization**: Automatic config serialization
- **Error handling**: Comprehensive error handling with logging
- **Backup/Restore**: Export/import functionality
- **Version management**: Config version for migrations

### 5. AppConfig Bloc
- **Event handling**: Processes all config-related events
- **State management**: Emits appropriate states
- **Persistence**: Automatically saves changes
- **Logging**: Comprehensive logging for debugging

## 🚀 Usage Examples

### Basic Setup in main.dart
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  final appConfigRepository = AppConfigRepository();
  await appConfigRepository.init();
  
  runApp(MyApp(appConfigRepository: appConfigRepository));
}

class MyApp extends StatelessWidget {
  const MyApp({required this.appConfigRepository});
  final AppConfigRepository appConfigRepository;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AppConfigBloc(repository: appConfigRepository)
        ..add(const AppConfigLoadRequested()),
      child: const _AppView(),
    );
  }
}
```

### Using in Widgets
```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppConfigBloc, AppConfigState>(
      builder: (context, state) {
        return MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: state.config.materialThemeMode,
          locale: state.config.locale,
          // ...
        );
      },
    );
  }
}
```

### Triggering Events
```dart
// Change theme
context.read<AppConfigBloc>().add(
  AppConfigThemeChanged(AppThemeMode.dark),
);

// Change language
context.read<AppConfigBloc>().add(
  AppConfigLanguageChanged(AppLanguage.vietnamese),
);

// Toggle notifications
context.read<AppConfigBloc>().add(
  AppConfigNotificationsToggled(true),
);

// Reset to default
context.read<AppConfigBloc>().add(
  const AppConfigResetRequested(),
);
```

## 🎨 Demo Page Features

The `ResponsiveDemoPage` demonstrates:
- **Real-time theme switching** with AppBar buttons
- **Language switching** with flag emoji button
- **Settings display** showing current configuration
- **Localization testing** with generated translations
- **Interactive controls** for various settings
- **Reset functionality** to restore defaults

## 🔄 Migration Benefits

### From Riverpod to Bloc
1. **Centralized state**: All app config in one place
2. **Event-driven**: Clear separation of events and state
3. **Testability**: Easy to test with clear event/state flow
4. **Scalability**: Easy to add new settings and features
5. **Persistence**: Automatic saving and loading
6. **Type safety**: Strong typing with Equatable

### Configuration Management
1. **Single source of truth**: All settings in AppConfig
2. **Automatic persistence**: Changes saved immediately
3. **Error handling**: Comprehensive error management
4. **Logging**: Full logging for debugging
5. **Backup/Restore**: Export/import functionality
6. **Version management**: Future-proof with versioning

## 🛠️ Available Settings

### Theme Settings
- Light mode
- Dark mode  
- System mode (follows device)

### Language Settings
- English
- Vietnamese
- Easy to add more languages

### App State
- First launch detection
- Onboarding completion

### Notification Settings
- Enable/disable notifications
- Reminder sounds
- Vibration

### Security Settings
- Biometric authentication
- (Future: PIN, password, etc.)

### Data Settings
- Auto backup
- (Future: sync settings, etc.)

### Accessibility Settings
- Font size scaling (0.8x - 1.5x)
- High contrast mode
- Reduce animations
- (Future: screen reader, etc.)

## 🔍 Debugging

### Logging
All config operations are logged:
```
📝 🚀 Starting Medication App...
📝 ✅ AppConfig repository initialized
📝 No saved config found, using default
📝 AppConfig loaded successfully
📝 🎨 Building app with theme: system, locale: en
```

### State Inspection
Use Flutter DevTools to inspect:
- Current AppConfigState
- Event history
- State transitions
- Performance metrics

## 🚀 Next Steps

1. **Add more settings** as needed
2. **Implement onboarding** flow
3. **Add settings screen** with full UI
4. **Implement biometric auth**
5. **Add backup/restore UI**
6. **Add more languages**
7. **Implement theme customization**

This setup provides a solid foundation for managing all app configuration with Flutter Bloc, making it easy to add new features and maintain consistency across the app.
