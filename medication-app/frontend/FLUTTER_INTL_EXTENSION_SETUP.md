# Flutter Intl Extension Setup Guide

## 📋 Overview

This guide explains how to use the Flutter Intl VS Code extension for internationalization in the Medication Manager app.

## 🔧 Extension Installation

1. Install the Flutter Intl extension from VS Code marketplace:
   - Extension ID: `localizely.flutter-intl`
   - URL: https://marketplace.visualstudio.com/items?itemName=localizely.flutter-intl

## ⚙️ Configuration

### 1. VS Code Settings (`.vscode/settings.json`)

```json
{
  "dart.flutterSdkPath": ".fvm/versions/3.29.3",
  "flutter-intl.arb-dir": "lib/l10n",
  "flutter-intl.template-arb-file": "intl_en.arb",
  "flutter-intl.output-localization-file": "intl_localizations.dart",
  "flutter-intl.output-class": "S",
  "flutter-intl.output-dir": "lib/generated/intl",
  "flutter-intl.nullable-getter": false,
  "flutter-intl.synthetic-package": false,
  "flutter-intl.use-deferred-loading": false
}
```

### 2. L10n Configuration (`l10n.yaml`)

```yaml
arb-dir: lib/l10n
template-arb-file: intl_en.arb
output-localization-file: intl_localizations.dart
output-class: S
output-dir: lib/generated/intl
nullable-getter: false
synthetic-package: false
```

### 3. Pubspec Configuration (`pubspec.yaml`)

```yaml
dependencies:
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

flutter:
  generate: true
```

## 📁 File Structure

```
lib/
├── l10n/
│   ├── intl_en.arb          # English translations (template)
│   └── intl_vi.arb          # Vietnamese translations
├── generated/
│   └── intl/
│       ├── intl_localizations.dart      # Main S class (generated)
│       ├── intl_localizations_en.dart   # English implementation (generated)
│       ├── intl_localizations_vi.dart   # Vietnamese implementation (generated)
│       ├── messages_en.dart             # English message lookup (manual)
│       └── messages_vi.dart             # Vietnamese message lookup (manual)
└── main.dart
```

**Note**: The `messages_*.dart` files are manually created to support the extension workflow. The `intl_localizations*.dart` files are auto-generated by Flutter.

## 🚀 Usage with Extension

### 1. Initialize Project

- Open Command Palette (`Cmd+Shift+P` / `Ctrl+Shift+P`)
- Run: `Flutter Intl: Initialize`
- This will create the basic structure and files

### 2. Add New Locale

- Command Palette → `Flutter Intl: Add locale`
- Enter locale code (e.g., `vi` for Vietnamese)
- This creates `intl_vi.arb` file

### 3. Add New Translation Key

- Command Palette → `Flutter Intl: Add key`
- Enter key name and description
- This adds the key to all ARB files

### 4. Generate Localization Files

- Command Palette → `Flutter Intl: Generate`
- Or save any ARB file (auto-generates)
- Updates all generated files

## 📝 ARB File Format

### Template File (`intl_en.arb`)

```json
{
  "@@locale": "en",
  "appName": "Medication Manager",
  "@appName": {
    "description": "The name of the application"
  },
  "welcomeMessage": "Welcome to {appName}!",
  "@welcomeMessage": {
    "description": "Welcome message with app name",
    "placeholders": {
      "appName": {
        "type": "String",
        "example": "Medication Manager"
      }
    }
  },
  "medicationCount": "You have {count} {count, plural, =0{medications} =1{medication} other{medications}}",
  "@medicationCount": {
    "description": "Medication count message",
    "placeholders": {
      "count": {
        "type": "int",
        "example": "5"
      }
    }
  }
}
```

### Translation File (`intl_vi.arb`)

```json
{
  "@@locale": "vi",
  "appName": "Quản Lý Thuốc",
  "welcomeMessage": "Chào mừng bạn đến với {appName}!",
  "medicationCount": "Bạn có {count} {count, plural, =0{thuốc} =1{thuốc} other{thuốc}}"
}
```

## 💻 Code Usage

### 1. Import Generated Class

```dart
import 'generated/intl/intl_localizations.dart';
```

### 2. Setup in MaterialApp

```dart
MaterialApp(
  localizationsDelegates: const [
    S.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ],
  supportedLocales: S.supportedLocales,
  // ...
)
```

### 3. Use in Widgets

```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Column(
      children: [
        Text(s.appName),
        Text(s.welcomeMessage(s.appName)),
        Text(s.medicationCount(5)),
      ],
    );
  }
}
```

## 🎯 Extension Features

### 1. Auto-completion

- Type `S.of(context).` to see all available keys
- IntelliSense shows parameter types and descriptions

### 2. Go to Definition

- Click on translation key to jump to ARB file
- Navigate between different locale files

### 3. Validation

- Highlights missing translations
- Shows parameter mismatches
- Validates ARB file syntax

### 4. Refactoring

- Rename keys across all files
- Extract hardcoded strings to ARB files
- Bulk operations on translations

## 🔄 Workflow

### Adding New Translation

1. **Add Key**: Command Palette → `Flutter Intl: Add key`
2. **Enter Details**: Key name, description, parameters
3. **Translate**: Fill in translations for all locales
4. **Generate**: Extension auto-generates code
5. **Use**: Access via `S.of(context).keyName`

### Updating Existing Translation

1. **Edit ARB**: Modify value in ARB file
2. **Save**: Extension auto-regenerates
3. **Hot Reload**: Changes appear immediately

### Managing Locales

1. **Add Locale**: `Flutter Intl: Add locale`
2. **Remove Locale**: Delete ARB file manually
3. **Update**: Extension handles code generation

## 🛠️ Commands Available

- `Flutter Intl: Initialize` - Setup project structure
- `Flutter Intl: Add locale` - Add new language
- `Flutter Intl: Add key` - Add translation key
- `Flutter Intl: Generate` - Regenerate files
- `Flutter Intl: Extract to ARB` - Extract hardcoded strings

## 📊 Benefits

### Developer Experience

- ✅ **Auto-completion** for translation keys
- ✅ **Type safety** with generated code
- ✅ **Go to definition** navigation
- ✅ **Real-time validation** of translations
- ✅ **Refactoring support** for keys

### Maintenance

- ✅ **Centralized translations** in ARB files
- ✅ **Missing translation detection**
- ✅ **Parameter validation**
- ✅ **Consistent code generation**
- ✅ **Version control friendly**

### Features

- ✅ **Pluralization** support
- ✅ **Parameter interpolation**
- ✅ **Gender support** (if needed)
- ✅ **Date/number formatting**
- ✅ **Nested parameters**

## 🔍 Troubleshooting

### Extension Not Working

1. Check VS Code settings are correct
2. Ensure `l10n.yaml` matches extension config
3. Restart VS Code
4. Run `Flutter Intl: Generate` manually

### Generation Errors

1. Validate ARB file syntax (JSON format)
2. Check parameter definitions match
3. Ensure all locales have same keys
4. Clear generated folder and regenerate

### Import Issues

1. Update import paths to `generated/intl/`
2. Use `S.of(context)` instead of old methods
3. Check `S.delegate` in MaterialApp
4. Verify supported locales list

## 📚 Resources

- **Extension Documentation**: https://marketplace.visualstudio.com/items?itemName=localizely.flutter-intl
- **Flutter Intl Package**: https://pub.dev/packages/intl
- **ARB Format Specification**: https://github.com/google/app-resource-bundle
- **Flutter Internationalization**: https://docs.flutter.dev/development/accessibility-and-localization/internationalization

This setup provides a professional internationalization workflow with excellent developer experience through the VS Code extension.
