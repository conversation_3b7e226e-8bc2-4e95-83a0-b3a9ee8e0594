# Flutter Intl & ScreenUtil Setup Guide

## 📋 Overview

This document outlines the complete setup for internationalization using `flutter_intl` and responsive design using `flutter_screenutil` in the Medication Manager app.

## 🌍 Flutter Intl Setup

### 1. Dependencies Added
```yaml
dependencies:
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0
  flutter_screenutil: ^5.9.0

flutter:
  generate: true
```

### 2. Configuration Files

#### `l10n.yaml`
```yaml
arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart
output-class: S
output-dir: lib/generated/l10n
nullable-getter: false
synthetic-package: false
header: |
  /// Generated file. Do not edit.
  /// 
  /// Localization files for Medication Manager App
  /// Generated automatically by flutter_intl
```

### 3. ARB Files

#### `lib/l10n/app_en.arb` (English)
- 60+ translation keys
- Parameterized messages with placeholders
- Pluralization support
- Comprehensive descriptions

#### `lib/l10n/app_vi.arb` (Vietnamese)
- Complete Vietnamese translations
- Matching keys with English version

### 4. Generated Files (after `fvm flutter pub get`)
- `lib/generated/l10n/app_localizations.dart` - Main localization class
- `lib/generated/l10n/app_localizations_en.dart` - English translations
- `lib/generated/l10n/app_localizations_vi.dart` - Vietnamese translations

### 5. Usage Examples

#### Basic Usage
```dart
import '../../generated/l10n/app_localizations.dart';

final s = S.of(context);
Text(s.appName)
Text(s.login)
Text(s.medications)
```

#### Parameterized Messages
```dart
Text(s.welcomeMessage(s.appName))
Text(s.medicationCount(5)) // Handles pluralization
```

#### Error Localization
```dart
final message = ErrorLocalizer.getLocalizedMessage(context, failure);
```

## 📱 ScreenUtil Setup

### 1. Configuration

#### Design Size
- Base design: iPhone 12 (375 x 812)
- Configured in `main.dart` with `ScreenUtilInit`

#### Breakpoints
- Mobile: < 600px
- Tablet: 600px - 1024px
- Desktop: > 1024px

### 2. Responsive Utilities

#### `AppScreenUtil` Class
```dart
// Responsive dimensions
AppScreenUtil.w(200)  // 200 logical pixels width
AppScreenUtil.h(100)  // 100 logical pixels height
AppScreenUtil.sp(16)  // 16 responsive font size
AppScreenUtil.r(8)    // 8 responsive radius

// Predefined spacing
AppScreenUtil.spaceXS  // 4dp
AppScreenUtil.spaceS   // 8dp
AppScreenUtil.spaceM   // 16dp
AppScreenUtil.spaceL   // 24dp
AppScreenUtil.spaceXL  // 32dp

// Responsive helpers
AppScreenUtil.responsive(
  mobile: 1,
  tablet: 2,
  desktop: 3,
)
```

#### Extension Methods
```dart
200.rw  // Responsive width
100.rh  // Responsive height
16.rsp  // Responsive font size
8.rr    // Responsive radius
```

### 3. Responsive Typography

#### `ResponsiveTypography` Class
- Screen-aware font sizes
- Different sizes for mobile/tablet/desktop
- Material Design 3 typography scale
- Medication-specific styles

```dart
Text('Title', style: ResponsiveTypography.headlineLarge)
Text('Body', style: ResponsiveTypography.bodyMedium)
Text('Medicine', style: ResponsiveTypography.medicationName)
```

### 4. Responsive Components

#### `ResponsiveContainer`
```dart
ResponsiveContainer(
  maxWidth: 1200,
  child: content,
)
```

#### `ResponsiveBuilder`
```dart
ResponsiveBuilder(
  builder: (context, constraints) {
    return adaptiveWidget;
  },
)
```

## 🎯 Key Features

### Internationalization
- ✅ English & Vietnamese support
- ✅ Parameterized messages
- ✅ Pluralization rules
- ✅ Error message localization
- ✅ Real-time language switching
- ✅ Type-safe translations

### Responsive Design
- ✅ Multi-screen support (mobile/tablet/desktop)
- ✅ Responsive typography
- ✅ Adaptive layouts
- ✅ Screen-aware spacing
- ✅ Breakpoint detection
- ✅ Responsive grids

### Error Handling
- ✅ Localized error messages
- ✅ Error type mapping
- ✅ Visual error indicators
- ✅ Retry capability detection

## 🚀 Commands

### Generate Localization Files
```bash
fvm flutter pub get
```

### Run App
```bash
fvm flutter run --debug
```

### Add New Translations
1. Edit `lib/l10n/app_en.arb`
2. Edit `lib/l10n/app_vi.arb`
3. Run `fvm flutter pub get`
4. Use `S.of(context).newKey`

## 📁 File Structure

```
lib/
├── generated/
│   └── l10n/
│       ├── app_localizations.dart
│       ├── app_localizations_en.dart
│       └── app_localizations_vi.dart
├── l10n/
│   ├── app_en.arb
│   └── app_vi.arb
├── app/
│   ├── localization/
│   │   ├── app_localizations.dart (old - can be removed)
│   │   └── language_manager.dart
│   ├── theme/
│   │   ├── responsive_typography.dart
│   │   └── ...
│   ├── pages/
│   │   └── responsive_demo_page.dart
│   └── widgets/
│       └── error_demo_widget.dart
├── core/
│   └── utils/
│       ├── screen_util.dart
│       └── error_localizer.dart
└── main.dart
```

## 🎨 Demo Features

### ResponsiveDemoPage
- Screen information display
- Typography showcase
- Responsive grid demo
- Button variations
- Medication cards
- Language switching
- Error localization demo

### Testing
- Real-time theme switching
- Language toggle
- Responsive layout adaptation
- Error message localization
- Parameterized translations
- Pluralization rules

## 📝 Notes

1. **Design System**: All dimensions are based on 375x812 design
2. **Typography**: Uses Inter font with responsive scaling
3. **Localization**: Generated files should not be edited manually
4. **Responsive**: Automatically adapts to different screen sizes
5. **Error Handling**: Centralized error localization system
6. **Performance**: Efficient state management with Riverpod

## 🔄 Migration from Old System

1. Replace `AppLocalizations.of(context)` with `S.of(context)`
2. Update import statements to use generated files
3. Use `ResponsiveTypography` instead of `AppTypography`
4. Apply responsive utilities for dimensions
5. Update error handling to use `ErrorLocalizer`

This setup provides a robust foundation for internationalization and responsive design in the Medication Manager app.
