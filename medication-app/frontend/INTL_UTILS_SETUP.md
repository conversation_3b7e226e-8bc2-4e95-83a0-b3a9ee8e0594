# Flutter Intl Utils Setup Guide

## 📋 Overview

This guide explains the correct setup for Flutter internationalization using `intl_utils` package and the Flutter Intl VS Code extension.

## 🔧 Dependencies

### pubspec.yaml
```yaml
dependencies:
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

dev_dependencies:
  intl_utils: ^2.8.7
```

## ⚙️ Configuration

### 1. VS Code Settings (`.vscode/settings.json`)
```json
{
  "dart.flutterSdkPath": ".fvm/versions/3.29.3",
  "flutter-intl.arb-dir": "lib/l10n",
  "flutter-intl.template-arb-file": "intl_en.arb",
  "flutter-intl.output-localization-file": "intl_localizations.dart",
  "flutter-intl.output-class": "S",
  "flutter-intl.output-dir": "lib/generated/intl",
  "flutter-intl.nullable-getter": false,
  "flutter-intl.synthetic-package": false,
  "flutter-intl.use-deferred-loading": false
}
```

### 2. L10n Configuration (`l10n.yaml`)
```yaml
arb-dir: lib/l10n
template-arb-file: intl_en.arb
output-localization-file: intl_localizations.dart
output-class: S
output-dir: lib/generated/intl
nullable-getter: false
synthetic-package: false
```

## 📁 File Structure (After Generation)

```
lib/
├── l10n/
│   ├── intl_en.arb          # English translations (template)
│   └── intl_vi.arb          # Vietnamese translations
├── generated/
│   ├── l10n.dart            # Main S class (generated by intl_utils)
│   └── intl/
│       ├── intl_localizations.dart      # Base localization class (generated)
│       ├── intl_localizations_en.dart   # English implementation (generated)
│       ├── intl_localizations_vi.dart   # Vietnamese implementation (generated)
│       ├── messages_all.dart            # Message loader (generated)
│       ├── messages_en.dart             # English message lookup (generated)
│       └── messages_vi.dart             # Vietnamese message lookup (generated)
└── main.dart
```

## 🚀 Generation Commands

### Method 1: Using intl_utils (Recommended)
```bash
# Generate localization files
fvm flutter pub run intl_utils:generate

# Or using dart run (newer)
fvm dart run intl_utils:generate
```

### Method 2: Using Flutter Intl Extension
- Install extension: https://marketplace.visualstudio.com/items?itemName=localizely.flutter-intl
- Command Palette → `Flutter Intl: Generate`
- Auto-generates when ARB files are saved

## 💻 Code Usage

### 1. Import Generated Class
```dart
import 'generated/l10n.dart';
```

### 2. Setup in MaterialApp
```dart
MaterialApp(
  localizationsDelegates: const [
    S.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ],
  supportedLocales: S.delegate.supportedLocales,
  // ...
)
```

### 3. Use in Widgets
```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return Column(
      children: [
        Text(s.appName),                    // Simple getter
        Text(s.welcomeMessage(s.appName)),  // Method with parameter
        Text(s.medicationCount(5)),         // Method with pluralization
      ],
    );
  }
}
```

## 🎯 Key Differences from Manual Setup

### Generated Files
- **`lib/generated/l10n.dart`**: Main S class with all translation methods
- **`lib/generated/intl/`**: Supporting files for message lookup
- All files are **auto-generated** - do not edit manually

### Usage Pattern
```dart
// Import the main generated file
import 'generated/l10n.dart';

// Use S.of(context) to access translations
final s = S.of(context);
Text(s.appName)
```

### Delegate Configuration
```dart
// Use S.delegate and its supportedLocales
supportedLocales: S.delegate.supportedLocales,
localizationsDelegates: const [S.delegate, ...]
```

## 🔄 Workflow

### 1. Add New Translation Key
**Option A: Using Extension**
- Command Palette → `Flutter Intl: Add key`
- Enter key name and description
- Extension updates ARB files and regenerates

**Option B: Manual**
- Edit `intl_en.arb` to add new key
- Edit `intl_vi.arb` to add translation
- Run `fvm flutter pub run intl_utils:generate`

### 2. Add New Locale
**Option A: Using Extension**
- Command Palette → `Flutter Intl: Add locale`
- Enter locale code (e.g., `es`)
- Extension creates ARB file and regenerates

**Option B: Manual**
- Create `intl_es.arb` file
- Copy keys from template file
- Add translations
- Run generation command

### 3. Update Existing Translation
- Edit ARB file
- Save (extension auto-regenerates) or run generation command
- Hot reload to see changes

## 📝 ARB File Examples

### Template File (`intl_en.arb`)
```json
{
  "@@locale": "en",
  "appName": "Medication Manager",
  "@appName": {
    "description": "The name of the application"
  },
  "welcomeMessage": "Welcome to {appName}!",
  "@welcomeMessage": {
    "description": "Welcome message with app name",
    "placeholders": {
      "appName": {
        "type": "String",
        "example": "Medication Manager"
      }
    }
  },
  "medicationCount": "You have {count} {count, plural, =0{medications} =1{medication} other{medications}}",
  "@medicationCount": {
    "description": "Medication count message",
    "placeholders": {
      "count": {
        "type": "int",
        "example": "5"
      }
    }
  }
}
```

### Translation File (`intl_vi.arb`)
```json
{
  "@@locale": "vi",
  "appName": "Quản Lý Thuốc",
  "welcomeMessage": "Chào mừng bạn đến với {appName}!",
  "medicationCount": "Bạn có {count} {count, plural, =0{thuốc} =1{thuốc} other{thuốc}}"
}
```

## 🛠️ Extension Commands

- `Flutter Intl: Initialize` - Setup project structure
- `Flutter Intl: Add locale` - Add new language
- `Flutter Intl: Add key` - Add translation key
- `Flutter Intl: Generate` - Regenerate files
- `Flutter Intl: Extract to ARB` - Extract hardcoded strings

## ✅ Benefits of This Setup

### Developer Experience
- ✅ **Auto-completion** for all translation keys
- ✅ **Type safety** with generated methods
- ✅ **Parameter validation** at compile time
- ✅ **IDE integration** with go-to-definition
- ✅ **Real-time validation** of ARB files

### Maintenance
- ✅ **Automatic code generation** from ARB files
- ✅ **Consistent API** across all locales
- ✅ **Missing translation detection**
- ✅ **Version control friendly** (only ARB files change)
- ✅ **No manual file editing** required

### Features
- ✅ **Pluralization** support with ICU format
- ✅ **Parameter interpolation** with type safety
- ✅ **Multiple locales** with easy addition
- ✅ **Extension integration** for productivity
- ✅ **Hot reload** support for translations

## 🔍 Troubleshooting

### Generation Issues
```bash
# Clear generated files and regenerate
rm -rf lib/generated
fvm flutter pub run intl_utils:generate
```

### Extension Not Working
1. Check VS Code settings match configuration
2. Ensure `intl_utils` is in dev_dependencies
3. Restart VS Code and Dart analysis server
4. Run generation command manually

### Import Errors
- Use `import 'generated/l10n.dart';` (not intl/ subdirectory)
- Use `S.delegate.supportedLocales` (not S.supportedLocales)
- Ensure all ARB files have matching keys

This setup provides the most robust and developer-friendly internationalization workflow for Flutter apps.
