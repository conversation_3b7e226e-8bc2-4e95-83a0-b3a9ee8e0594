# Hướng dẫn Navigation với GoRouter

## Tổng quan

Ứng dụng sử dụng **GoRouter** để quản lý navigation, cung cấp:
- ✅ Declarative routing
- ✅ Type-safe navigation
- ✅ Route guards (authentication/authorization)
- ✅ Deep linking support
- ✅ Browser back/forward support (web)
- ✅ Nested routing

## Cấu trúc Files

```
lib/app/routes/
├── app_routes.dart          # Định nghĩa route paths
├── app_router.dart          # Cấu hình GoRouter
├── route_guards.dart        # Authentication/authorization guards
├── navigation_extensions.dart # Extension methods cho navigation
└── routes.dart              # Barrel file exports
```

## Sử dụng cơ bản

### 1. Import

```dart
import 'package:frontend/app/routes/routes.dart';
```

### 2. Navigation đơn giản

```dart
// Sử dụng extension methods (khuyến nghị)
context.goToLogin();
context.goToMedications();
context.goToMedicationDetail('123');

// Hoặc sử dụng GoRouter trực tiếp
context.go('/login');
context.go('/medications/123');
```

### 3. Navigation với parameters

```dart
// Với ID
context.goToMedicationDetail('medication-123');
context.goToEditPrescription('prescription-456');

// Với query parameters
context.go('/medications?category=antibiotics&sort=name');
```

### 4. Navigation stack management

```dart
// Go back
context.goBack();

// Replace current route
context.goAndReplace('/new-route');

// Clear stack và navigate
context.goAndClearStack('/home');
```

## Route Definitions

### Auth Routes
- `/` - Splash screen
- `/onboarding` - Onboarding flow
- `/login` - Đăng nhập
- `/register` - Đăng ký
- `/forgot-password` - Quên mật khẩu

### Main App Routes
- `/home` - Trang chủ
- `/dashboard` - Dashboard

### Feature Routes
- `/medications` - Danh sách thuốc
- `/medications/:id` - Chi tiết thuốc
- `/medications/add` - Thêm thuốc mới
- `/medications/:id/edit` - Sửa thuốc

- `/prescriptions` - Danh sách đơn thuốc
- `/prescriptions/:id` - Chi tiết đơn thuốc
- `/prescriptions/add` - Thêm đơn thuốc
- `/prescriptions/:id/edit` - Sửa đơn thuốc

- `/reminders` - Danh sách nhắc nhở
- `/reminders/:id` - Chi tiết nhắc nhở
- `/reminders/add` - Thêm nhắc nhở
- `/reminders/:id/edit` - Sửa nhắc nhở

### Profile Routes
- `/profile` - Hồ sơ người dùng
- `/profile/edit` - Sửa hồ sơ
- `/settings` - Cài đặt
- `/about` - Thông tin ứng dụng

## Route Guards

### Authentication Guard

Tự động kiểm tra authentication cho tất cả protected routes:

```dart
// Trong route_guards.dart
static String? authGuard(BuildContext context, GoRouterState state) {
  if (!_isPublicRoute(currentPath) && !_checkAuthenticationStatus()) {
    return AppRoutes.login; // Redirect to login
  }
  return null; // Allow access
}
```

### Custom Guards

```dart
// Admin only routes
static String? adminGuard(BuildContext context, GoRouterState state) {
  final authResult = authGuard(context, state);
  if (authResult != null) return authResult;
  
  if (!_isAdmin()) {
    return AppRoutes.home; // Redirect to home
  }
  return null;
}
```

## Extension Methods

### Navigation Methods

```dart
// Auth navigation
context.goToLogin();
context.goToRegister();
context.goToForgotPassword();

// Feature navigation
context.goToMedications();
context.goToMedicationDetail('123');
context.goToAddMedication();
context.goToEditMedication('123');

context.goToPrescriptions();
context.goToPrescriptionDetail('456');

context.goToReminders();
context.goToReminderDetail('789');

// Profile navigation
context.goToProfile();
context.goToEditProfile();
context.goToSettings();
```

### Utility Methods

```dart
// Navigation utilities
context.goBack();
context.goAndReplace('/new-route');
context.goAndClearStack('/home');

// Route information
final currentRoute = context.currentRoute;
final params = context.routeParameters;
final queryParams = context.queryParameters;

// Check navigation capability
if (context.canNavigateTo('/some-route')) {
  context.go('/some-route');
}
```

## Best Practices

### 1. Sử dụng Extension Methods

```dart
// ✅ Tốt - Type-safe và dễ đọc
context.goToMedicationDetail(medicationId);

// ❌ Tránh - Dễ lỗi typo
context.go('/medications/$medicationId');
```

### 2. Route Parameters

```dart
// ✅ Tốt - Sử dụng helper method
final path = AppRoutes.buildPath(
  AppRoutes.medicationDetail, 
  {'id': medicationId}
);
context.go(path);

// ✅ Hoặc sử dụng extension
context.goToMedicationDetail(medicationId);
```

### 3. Error Handling

```dart
// Kiểm tra trước khi navigate
if (context.canNavigateTo(targetRoute)) {
  context.go(targetRoute);
} else {
  // Handle error
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text('Không thể truy cập trang này')),
  );
}
```

### 4. Deep Linking

```dart
// Routes tự động hỗ trợ deep linking
// URL: myapp://medications/123
// Sẽ tự động navigate đến MedicationDetailPage với id = '123'
```

## Debugging

### 1. Enable Debug Logging

```dart
// Trong app_router.dart
GoRouter(
  debugLogDiagnostics: true, // Enable logging
  // ...
)
```

### 2. Route Guard Logging

Route guards tự động log các hoạt động:

```
🛡️ RouteGuard: Checking access for /medications
✅ RouteGuard: Access granted for /medications
❌ RouteGuard: User not authenticated, redirecting to login
```

## Tích hợp với State Management

### Với BLoC

```dart
// Trong BLoC event handler
void _onLoginSuccess(LoginSuccess event, Emitter<AuthState> emit) {
  emit(AuthenticatedState(user: event.user));
  
  // Navigate sau khi login thành công
  final intendedRoute = RouteGuards.getIntendedRoute();
  if (intendedRoute != null) {
    context.go(intendedRoute);
  } else {
    context.goToHome();
  }
}
```

### Với Provider/Riverpod

```dart
// Listen to auth state changes
ref.listen<AuthState>(authProvider, (previous, next) {
  if (next is Unauthenticated) {
    context.goToLogin();
  } else if (next is Authenticated) {
    context.goToHome();
  }
});
```

## Testing

### Unit Testing Routes

```dart
testWidgets('should navigate to medication detail', (tester) async {
  await tester.pumpWidget(MyApp());
  
  // Navigate to medication detail
  final router = GoRouter.of(tester.element(find.byType(MaterialApp)));
  router.go('/medications/123');
  
  await tester.pumpAndSettle();
  
  // Verify navigation
  expect(find.text('Chi tiết thuốc #123'), findsOneWidget);
});
```

### Integration Testing

```dart
testWidgets('should redirect to login when not authenticated', (tester) async {
  // Mock unauthenticated state
  when(mockAuthService.isAuthenticated).thenReturn(false);
  
  await tester.pumpWidget(MyApp());
  
  // Try to access protected route
  final router = GoRouter.of(tester.element(find.byType(MaterialApp)));
  router.go('/medications');
  
  await tester.pumpAndSettle();
  
  // Should be redirected to login
  expect(find.text('Đăng nhập'), findsOneWidget);
});
```

## Troubleshooting

### Common Issues

1. **Route not found**: Kiểm tra route đã được định nghĩa trong `app_router.dart`
2. **Authentication loop**: Kiểm tra logic trong `route_guards.dart`
3. **Parameters not working**: Đảm bảo sử dụng đúng syntax `:id` trong route definition
4. **Back button not working**: Kiểm tra route stack và sử dụng `context.canPop()`

### Debug Commands

```dart
// Print current route
print('Current route: ${context.currentRoute}');

// Print route parameters
print('Parameters: ${context.routeParameters}');

// Print query parameters
print('Query: ${context.queryParameters}');
```
