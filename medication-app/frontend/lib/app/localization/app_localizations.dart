import 'package:flutter/material.dart';

/// Lớp quản lý localization cho ứng dụng
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  /// Lấy instance từ context
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  /// Delegate cho localization
  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// Danh sách locale được hỗ trợ
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('vi', 'VN'), // Vietnamese
  ];

  /// Map chứa tất cả translations
  static const Map<String, Map<String, String>> _localizedValues = {
    'en': {
      // Common
      'app_name': 'Medication Manager',
      'ok': 'OK',
      'cancel': 'Cancel',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'add': 'Add',
      'search': 'Search',
      'loading': 'Loading...',
      'error': 'Error',
      'success': 'Success',
      'warning': 'Warning',
      'info': 'Information',
      'yes': 'Yes',
      'no': 'No',
      'confirm': 'Confirm',
      'back': 'Back',
      'next': 'Next',
      'done': 'Done',
      'retry': 'Retry',
      'close': 'Close',

      // Authentication
      'login': 'Login',
      'logout': 'Logout',
      'register': 'Register',
      'email': 'Email',
      'password': 'Password',
      'confirm_password': 'Confirm Password',
      'forgot_password': 'Forgot Password?',
      'remember_me': 'Remember me',
      'login_success': 'Login successful',
      'logout_success': 'Logout successful',
      'invalid_credentials': 'Invalid email or password',
      'account_created': 'Account created successfully',

      // Medications
      'medications': 'Medications',
      'medication': 'Medication',
      'medication_name': 'Medication Name',
      'dosage': 'Dosage',
      'frequency': 'Frequency',
      'medication_type': 'Type',
      'description': 'Description',
      'add_medication': 'Add Medication',
      'edit_medication': 'Edit Medication',
      'delete_medication': 'Delete Medication',
      'medication_added': 'Medication added successfully',
      'medication_updated': 'Medication updated successfully',
      'medication_deleted': 'Medication deleted successfully',

      // Medication Types
      'tablet': 'Tablet',
      'capsule': 'Capsule',
      'syrup': 'Syrup',
      'injection': 'Injection',
      'eye_drops': 'Eye Drops',
      'ointment': 'Ointment',
      'other': 'Other',

      // Reminders
      'reminders': 'Reminders',
      'reminder': 'Reminder',
      'add_reminder': 'Add Reminder',
      'edit_reminder': 'Edit Reminder',
      'delete_reminder': 'Delete Reminder',
      'reminder_time': 'Reminder Time',
      'reminder_added': 'Reminder added successfully',
      'reminder_updated': 'Reminder updated successfully',
      'reminder_deleted': 'Reminder deleted successfully',
      'time_to_take_medication': 'Time to take your medication',

      // Prescriptions
      'prescriptions': 'Prescriptions',
      'prescription': 'Prescription',
      'doctor_name': 'Doctor Name',
      'prescription_date': 'Prescription Date',
      'add_prescription': 'Add Prescription',
      'edit_prescription': 'Edit Prescription',
      'delete_prescription': 'Delete Prescription',

      // Profile
      'profile': 'Profile',
      'personal_info': 'Personal Information',
      'settings': 'Settings',
      'theme': 'Theme',
      'language': 'Language',
      'notifications': 'Notifications',
      'privacy': 'Privacy',
      'about': 'About',

      // Theme
      'light_mode': 'Light Mode',
      'dark_mode': 'Dark Mode',
      'system_mode': 'System Mode',

      // Languages
      'english': 'English',
      'vietnamese': 'Tiếng Việt',

      // Error Messages
      'network_error': 'Network connection error',
      'timeout_error': 'Request timeout',
      'server_error': 'Server error',
      'unauthorized_error': 'Session expired',
      'forbidden_error': 'Access denied',
      'not_found_error': 'Data not found',
      'validation_error': 'Invalid data',
      'unknown_error': 'Unknown error occurred',

      // Validation
      'field_required': 'This field is required',
      'invalid_email': 'Invalid email format',
      'password_too_short': 'Password must be at least 6 characters',
      'passwords_not_match': 'Passwords do not match',

      // Time
      'today': 'Today',
      'tomorrow': 'Tomorrow',
      'yesterday': 'Yesterday',
      'morning': 'Morning',
      'afternoon': 'Afternoon',
      'evening': 'Evening',
      'night': 'Night',
    },
    'vi': {
      // Common
      'app_name': 'Quản Lý Thuốc',
      'ok': 'Đồng ý',
      'cancel': 'Hủy',
      'save': 'Lưu',
      'delete': 'Xóa',
      'edit': 'Sửa',
      'add': 'Thêm',
      'search': 'Tìm kiếm',
      'loading': 'Đang tải...',
      'error': 'Lỗi',
      'success': 'Thành công',
      'warning': 'Cảnh báo',
      'info': 'Thông tin',
      'yes': 'Có',
      'no': 'Không',
      'confirm': 'Xác nhận',
      'back': 'Quay lại',
      'next': 'Tiếp theo',
      'done': 'Hoàn thành',
      'retry': 'Thử lại',
      'close': 'Đóng',

      // Authentication
      'login': 'Đăng nhập',
      'logout': 'Đăng xuất',
      'register': 'Đăng ký',
      'email': 'Email',
      'password': 'Mật khẩu',
      'confirm_password': 'Xác nhận mật khẩu',
      'forgot_password': 'Quên mật khẩu?',
      'remember_me': 'Ghi nhớ đăng nhập',
      'login_success': 'Đăng nhập thành công',
      'logout_success': 'Đăng xuất thành công',
      'invalid_credentials': 'Email hoặc mật khẩu không đúng',
      'account_created': 'Tạo tài khoản thành công',

      // Medications
      'medications': 'Thuốc',
      'medication': 'Thuốc',
      'medication_name': 'Tên thuốc',
      'dosage': 'Liều lượng',
      'frequency': 'Tần suất',
      'medication_type': 'Loại thuốc',
      'description': 'Mô tả',
      'add_medication': 'Thêm thuốc',
      'edit_medication': 'Sửa thuốc',
      'delete_medication': 'Xóa thuốc',
      'medication_added': 'Thêm thuốc thành công',
      'medication_updated': 'Cập nhật thuốc thành công',
      'medication_deleted': 'Xóa thuốc thành công',

      // Medication Types
      'tablet': 'Viên nén',
      'capsule': 'Viên nang',
      'syrup': 'Siro',
      'injection': 'Thuốc tiêm',
      'eye_drops': 'Thuốc nhỏ mắt',
      'ointment': 'Thuốc bôi',
      'other': 'Khác',

      // Reminders
      'reminders': 'Nhắc nhở',
      'reminder': 'Nhắc nhở',
      'add_reminder': 'Thêm nhắc nhở',
      'edit_reminder': 'Sửa nhắc nhở',
      'delete_reminder': 'Xóa nhắc nhở',
      'reminder_time': 'Thời gian nhắc nhở',
      'reminder_added': 'Thêm nhắc nhở thành công',
      'reminder_updated': 'Cập nhật nhắc nhở thành công',
      'reminder_deleted': 'Xóa nhắc nhở thành công',
      'time_to_take_medication': 'Đến giờ uống thuốc',

      // Prescriptions
      'prescriptions': 'Đơn thuốc',
      'prescription': 'Đơn thuốc',
      'doctor_name': 'Tên bác sĩ',
      'prescription_date': 'Ngày kê đơn',
      'add_prescription': 'Thêm đơn thuốc',
      'edit_prescription': 'Sửa đơn thuốc',
      'delete_prescription': 'Xóa đơn thuốc',

      // Profile
      'profile': 'Hồ sơ',
      'personal_info': 'Thông tin cá nhân',
      'settings': 'Cài đặt',
      'theme': 'Giao diện',
      'language': 'Ngôn ngữ',
      'notifications': 'Thông báo',
      'privacy': 'Quyền riêng tư',
      'about': 'Giới thiệu',

      // Theme
      'light_mode': 'Giao diện sáng',
      'dark_mode': 'Giao diện tối',
      'system_mode': 'Theo hệ thống',

      // Languages
      'english': 'English',
      'vietnamese': 'Tiếng Việt',

      // Error Messages
      'network_error': 'Lỗi kết nối mạng',
      'timeout_error': 'Yêu cầu quá thời gian chờ',
      'server_error': 'Lỗi máy chủ',
      'unauthorized_error': 'Phiên đăng nhập đã hết hạn',
      'forbidden_error': 'Bạn không có quyền truy cập',
      'not_found_error': 'Không tìm thấy dữ liệu',
      'validation_error': 'Dữ liệu không hợp lệ',
      'unknown_error': 'Đã xảy ra lỗi không xác định',

      // Validation
      'field_required': 'Trường này là bắt buộc',
      'invalid_email': 'Định dạng email không hợp lệ',
      'password_too_short': 'Mật khẩu phải có ít nhất 6 ký tự',
      'passwords_not_match': 'Mật khẩu không khớp',

      // Time
      'today': 'Hôm nay',
      'tomorrow': 'Ngày mai',
      'yesterday': 'Hôm qua',
      'morning': 'Sáng',
      'afternoon': 'Chiều',
      'evening': 'Tối',
      'night': 'Đêm',
    },
  };

  /// Lấy text theo key
  String translate(String key) {
    final languageCode = locale.languageCode;
    return _localizedValues[languageCode]?[key] ?? key;
  }

  /// Shorthand method để lấy text
  String t(String key) => translate(key);

  // ============ GETTERS FOR COMMON TEXTS ============
  String get appName => translate('app_name');
  String get ok => translate('ok');
  String get cancel => translate('cancel');
  String get save => translate('save');
  String get delete => translate('delete');
  String get edit => translate('edit');
  String get add => translate('add');
  String get search => translate('search');
  String get loading => translate('loading');
  String get error => translate('error');
  String get success => translate('success');
  String get warning => translate('warning');
  String get info => translate('info');
}

/// Delegate cho AppLocalizations
class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return AppLocalizations.supportedLocales.contains(locale);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

/// Extension cho Locale
extension LocaleExtension on Locale {
  String get displayName {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'vi':
        return 'Tiếng Việt';
      default:
        return languageCode;
    }
  }

  String get flagEmoji {
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'vi':
        return '🇻🇳';
      default:
        return '🌐';
    }
  }
}
