import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../generated/l10n.dart';
import '../routes/routes.dart';
import '../widgets/widgets.dart';

/// Trang chủ chính của ứng dụng
///
/// Hiển thị tổng quan về:
/// - Thống kê nhanh
/// - <PERSON>h<PERSON><PERSON> nhở sắp tới
/// - <PERSON><PERSON><PERSON> cập nhanh các tính năng
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(s.appName),
        actions: [
          IconButton(
            onPressed: () => context.go('/demo'),
            icon: const Icon(Icons.science),
            tooltip: 'Demo Page',
          ),
          IconButton(
            onPressed: () => context.goToProfile(),
            icon: const Icon(Icons.account_circle),
          ),
          IconButton(
            onPressed: () => context.goToSettings(),
            icon: const Icon(Icons.settings),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            _buildWelcomeSection(context),
            SizedBox(height: 24.h),

            // Quick Stats
            _buildQuickStats(context),
            SizedBox(height: 24.h),

            // Upcoming Reminders
            _buildUpcomingReminders(context),
            SizedBox(height: 24.h),

            // Quick Actions
            _buildQuickActions(context),
            SizedBox(height: 24.h),

            // Recent Activity
            _buildRecentActivity(context),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(context),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    final s = S.of(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chào buổi sáng!',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Bạn có 2 đơn thuốc đang hiệu lực và 5 lịch uống hôm nay',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Icon(
                Icons.schedule,
                color: Colors.white,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Lần tiếp theo: 14:30',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thống kê nhanh',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.receipt_long,
                title: 'Đơn thuốc',
                value: '3',
                subtitle: 'Đang hiệu lực',
                color: Colors.blue,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.medication,
                title: 'Thuốc',
                value: '12',
                subtitle: 'Trong tủ thuốc',
                color: Colors.green,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.schedule,
                title: 'Lịch uống',
                value: '8',
                subtitle: 'Hôm nay',
                color: Colors.orange,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.check_circle,
                title: 'Tuân thủ',
                value: '92%',
                subtitle: 'Tuần này',
                color: Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 24.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: color,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
          ),
          SizedBox(height: 4.h),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingReminders(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Lịch uống thuốc hôm nay',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            TextButton(
              onPressed: () => context.goToReminders(),
              child: const Text('Xem tất cả'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3,
          separatorBuilder: (context, index) => SizedBox(height: 12.h),
          itemBuilder: (context, index) => _buildReminderCard(context, index),
        ),
      ],
    );
  }

  Widget _buildReminderCard(BuildContext context, int index) {
    final schedules = [
      {
        'time': '14:30',
        'medicine': 'Paracetamol 500mg',
        'dose': '1 viên',
        'prescription': 'Đơn thuốc #001'
      },
      {
        'time': '18:00',
        'medicine': 'Vitamin D3',
        'dose': '2 viên',
        'prescription': 'Đơn thuốc #002'
      },
      {
        'time': '21:00',
        'medicine': 'Omega-3',
        'dose': '1 viên',
        'prescription': 'Đơn thuốc #002'
      },
    ];

    final schedule = schedules[index];

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.schedule,
              color: Theme.of(context).colorScheme.primary,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  schedule['medicine']!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '${schedule['time']} - ${schedule['dose']}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                      ),
                ),
                SizedBox(height: 2.h),
                Text(
                  schedule['prescription']!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontStyle: FontStyle.italic,
                      ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // TODO: Mark as taken
            },
            icon: Icon(
              Icons.check_circle_outline,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Truy cập nhanh',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        SizedBox(height: 16.h),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          mainAxisSpacing: 12.h,
          crossAxisSpacing: 12.w,
          childAspectRatio: 1.5,
          children: [
            _buildActionCard(
              context,
              icon: Icons.receipt_long,
              title: 'Thêm đơn thuốc',
              subtitle: 'Nhập đơn thuốc mới',
              color: Colors.blue,
              onTap: () => context.goToAddPrescription(),
            ),
            _buildActionCard(
              context,
              icon: Icons.list_alt,
              title: 'Đơn thuốc',
              subtitle: 'Xem tất cả đơn thuốc',
              color: Colors.green,
              onTap: () => context.goToPrescriptions(),
            ),
            _buildActionCard(
              context,
              icon: Icons.schedule,
              title: 'Lịch uống thuốc',
              subtitle: 'Xem lịch hôm nay',
              color: Colors.orange,
              onTap: () => context.goToReminders(),
            ),
            _buildActionCard(
              context,
              icon: Icons.analytics,
              title: 'Báo cáo tuân thủ',
              subtitle: 'Xem thống kê',
              color: Colors.purple,
              onTap: () {
                // TODO: Navigate to reports
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: color.withOpacity(0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 32.sp,
            ),
            SizedBox(height: 8.h),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            SizedBox(height: 4.h),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Hoạt động gần đây',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        SizedBox(height: 16.h),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3,
          separatorBuilder: (context, index) => SizedBox(height: 12.h),
          itemBuilder: (context, index) => _buildActivityItem(context, index),
        ),
      ],
    );
  }

  Widget _buildActivityItem(BuildContext context, int index) {
    final activities = [
      {
        'action': 'Đã uống',
        'medicine': 'Paracetamol 500mg (Đơn thuốc #001)',
        'time': '2 giờ trước'
      },
      {
        'action': 'Nhập đơn thuốc',
        'medicine': 'BS. Nguyễn Văn A',
        'time': '1 ngày trước'
      },
      {
        'action': 'Hoàn thành',
        'medicine': 'Đơn thuốc #002',
        'time': '2 ngày trước'
      },
    ];

    final activity = activities[index];

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        child: Icon(
          Icons.history,
          color: Theme.of(context).colorScheme.primary,
          size: 20.sp,
        ),
      ),
      title: Text(
        '${activity['action']} ${activity['medicine']}',
        style: Theme.of(context).textTheme.bodyMedium,
      ),
      subtitle: Text(
        activity['time']!,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildBottomNavigation(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: 0,
      onTap: (index) {
        switch (index) {
          case 0:
            // Already on home
            break;
          case 1:
            context.goToPrescriptions();
            break;
          case 2:
            context.goToReminders();
            break;
          case 3:
            // TODO: Navigate to reports/analytics
            break;
          case 4:
            context.goToProfile();
            break;
        }
      },
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'Trang chủ',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.receipt_long),
          label: 'Đơn thuốc',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.schedule),
          label: 'Lịch uống',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'Báo cáo',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'Hồ sơ',
        ),
      ],
    );
  }
}
