import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/config/config.dart';
import '../../generated/l10n.dart';
import '../routes/routes.dart';

/// Demo page để test responsive design và flutter_intl với Bloc
class ResponsiveDemoPage extends StatelessWidget {
  const ResponsiveDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppConfigBloc, AppConfigState>(
      builder: (context, state) {
        final s = S.of(context);

        return Scaffold(
          appBar: AppBar(
            title: Text(s.appName),
            actions: [
              // Theme toggle button
              IconButton(
                onPressed: () {
                  final newTheme = state.themeMode == AppThemeMode.light
                      ? AppThemeMode.dark
                      : AppThemeMode.light;
                  context.read<AppConfigBloc>().add(
                        AppConfigThemeChanged(newTheme),
                      );
                },
                icon: Icon(
                  state.themeMode == AppThemeMode.dark
                      ? Icons.light_mode
                      : Icons.dark_mode,
                ),
              ),

              // Language toggle button
              IconButton(
                onPressed: () {
                  final newLanguage = state.language == AppLanguage.english
                      ? AppLanguage.vietnamese
                      : AppLanguage.english;
                  context.read<AppConfigBloc>().add(
                        AppConfigLanguageChanged(newLanguage),
                      );
                },
                icon: Text(
                  state.languageFlag,
                  style: TextStyle(fontSize: 20.sp),
                ),
              ),
            ],
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'App Configuration Demo',
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                SizedBox(height: 16.h),

                // Current Settings Card
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Current Settings:',
                            style: Theme.of(context).textTheme.titleMedium),
                        SizedBox(height: 8.h),
                        _buildSettingRow('Theme', state.themeMode.name),
                        _buildSettingRow('Language', state.languageName),
                        _buildSettingRow(
                            'Notifications', '${state.notificationsEnabled}'),
                        _buildSettingRow(
                            'Biometric', '${state.biometricEnabled}'),
                        _buildSettingRow(
                            'Auto Backup', '${state.autoBackupEnabled}'),
                        _buildSettingRow('Font Size', '${state.fontSize}x'),
                      ],
                    ),
                  ),
                ),

                SizedBox(height: 16.h),

                // Localization Test Card
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Localization Test:',
                            style: Theme.of(context).textTheme.titleMedium),
                        SizedBox(height: 8.h),
                        _buildTranslationRow('App Name', s.appName),
                        _buildTranslationRow('Login', s.login),
                        _buildTranslationRow('Settings', s.settings),
                        _buildTranslationRow('Medications', s.medications),
                        _buildTranslationRow('Save', s.save),
                        _buildTranslationRow('Cancel', s.cancel),
                        SizedBox(height: 12.h),
                        Text('Parameterized:',
                            style: Theme.of(context).textTheme.titleSmall),
                        SizedBox(height: 4.h),
                        Text('• ${s.welcomeMessage(s.appName)}'),
                        Text('• ${s.medicationCount(5)}'),
                        Text('• ${s.medicationCount(1)}'),
                        Text('• ${s.medicationCount(0)}'),
                      ],
                    ),
                  ),
                ),

                SizedBox(height: 16.h),

                // Settings Controls Card
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Settings Controls:',
                            style: Theme.of(context).textTheme.titleMedium),
                        SizedBox(height: 8.h),
                        SwitchListTile(
                          title: Text('Enable Notifications'),
                          value: state.notificationsEnabled,
                          onChanged: (value) {
                            context.read<AppConfigBloc>().add(
                                  AppConfigNotificationsToggled(value),
                                );
                          },
                        ),
                        SwitchListTile(
                          title: Text('Enable Biometric'),
                          value: state.biometricEnabled,
                          onChanged: (value) {
                            context.read<AppConfigBloc>().add(
                                  AppConfigBiometricToggled(value),
                                );
                          },
                        ),
                        SwitchListTile(
                          title: Text('Auto Backup'),
                          value: state.autoBackupEnabled,
                          onChanged: (value) {
                            context.read<AppConfigBloc>().add(
                                  AppConfigAutoBackupToggled(value),
                                );
                          },
                        ),
                        ListTile(
                          title: Text(
                              'Font Size: ${state.fontSize.toStringAsFixed(1)}x'),
                          subtitle: Slider(
                            value: state.fontSize,
                            min: 0.8,
                            max: 1.5,
                            divisions: 7,
                            onChanged: (value) {
                              context.read<AppConfigBloc>().add(
                                    AppConfigFontSizeChanged(value),
                                  );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(height: 16.h),

                // Navigation Test Card
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Navigation Test:',
                            style: Theme.of(context).textTheme.titleMedium),
                        SizedBox(height: 8.h),
                        Wrap(
                          spacing: 8.w,
                          runSpacing: 8.h,
                          children: [
                            ElevatedButton(
                              onPressed: () => context.goToLogin(),
                              child: const Text('Login'),
                            ),
                            ElevatedButton(
                              onPressed: () => context.goToRegister(),
                              child: const Text('Register'),
                            ),
                            ElevatedButton(
                              onPressed: () => context.goToMedications(),
                              child: const Text('Medications'),
                            ),
                            ElevatedButton(
                              onPressed: () =>
                                  context.goToMedicationDetail('123'),
                              child: const Text('Med Detail'),
                            ),
                            ElevatedButton(
                              onPressed: () => context.goToAddMedication(),
                              child: const Text('Add Med'),
                            ),
                            ElevatedButton(
                              onPressed: () => context.goToPrescriptions(),
                              child: const Text('Prescriptions'),
                            ),
                            ElevatedButton(
                              onPressed: () => context.goToReminders(),
                              child: const Text('Reminders'),
                            ),
                            ElevatedButton(
                              onPressed: () => context.goToProfile(),
                              child: const Text('Profile'),
                            ),
                            ElevatedButton(
                              onPressed: () => context.goToSettings(),
                              child: const Text('Settings'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(height: 16.h),

                // Reset Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      context.read<AppConfigBloc>().add(
                            const AppConfigResetRequested(),
                          );
                    },
                    child: Text('Reset to Default'),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildTranslationRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 1.h),
      child: Text('• $label: $value'),
    );
  }
}
