# Navigation System với GoRouter

## 📋 Tổng quan

Hệ thống navigation của ứng dụng được xây dựng trên **GoRouter**, cung cấp:

- ✅ **Declarative routing** - Đ<PERSON><PERSON> nghĩa routes một cách rõ ràng
- ✅ **Type-safe navigation** - Navigation an toàn với TypeScript-like experience
- ✅ **Route guards** - Bảo vệ routes với authentication/authorization
- ✅ **Deep linking** - Hỗ trợ deep links từ bên ngoài ứng dụng
- ✅ **Browser support** - Back/forward buttons trên web
- ✅ **Nested routing** - Routes lồng nhau cho cấu trúc phức tạp

## 🏗️ Kiến trúc

```
lib/app/routes/
├── app_routes.dart          # 📍 Route constants & utilities
├── app_router.dart          # ⚙️ GoRouter configuration
├── route_guards.dart        # 🛡️ Authentication guards
├── navigation_extensions.dart # 🚀 Extension methods
└── routes.dart              # 📦 Barrel exports
```

## 🚀 Quick Start

### 1. Import routing system

```dart
import 'package:frontend/app/routes/routes.dart';
```

### 2. Basic navigation

```dart
// ✅ Recommended: Extension methods
context.goToLogin();
context.goToMedicationDetail('123');

// ⚠️ Alternative: Direct GoRouter
context.go('/login');
context.go('/medications/123');
```

### 3. Navigation với parameters

```dart
// Navigate với ID
context.goToMedicationDetail('med-123');
context.goToEditPrescription('rx-456');

// Navigate với query parameters
context.go('/search?q=aspirin&category=pain-relief');
```

## 📍 Route Structure

### Public Routes (không cần authentication)
```
/                    # Splash screen
/onboarding         # Onboarding flow
/login              # Đăng nhập
/register           # Đăng ký
/forgot-password    # Quên mật khẩu
/reset-password     # Reset mật khẩu
```

### Protected Routes (cần authentication)
```
/home               # Trang chủ
/dashboard          # Dashboard

# Medication Management
/medications                    # Danh sách thuốc
/medications/add               # Thêm thuốc mới
/medications/:id               # Chi tiết thuốc
/medications/:id/edit          # Sửa thuốc

# Prescription Management  
/prescriptions                 # Danh sách đơn thuốc
/prescriptions/add            # Thêm đơn thuốc
/prescriptions/:id            # Chi tiết đơn thuốc
/prescriptions/:id/edit       # Sửa đơn thuốc

# Reminder Management
/reminders                    # Danh sách nhắc nhở
/reminders/add               # Thêm nhắc nhở
/reminders/:id               # Chi tiết nhắc nhở
/reminders/:id/edit          # Sửa nhắc nhở

# User Profile
/profile            # Hồ sơ người dùng
/profile/edit       # Sửa hồ sơ
/settings           # Cài đặt ứng dụng
/about              # Thông tin ứng dụng
```

## 🛡️ Route Guards

### Authentication Guard
Tự động kiểm tra authentication cho tất cả protected routes:

```dart
// Tự động redirect đến /login nếu chưa authenticate
// Lưu intended route để redirect sau khi login thành công
```

### Authorization Guards
```dart
// Admin routes - chỉ admin mới truy cập được
AppRouter.adminGuard()

// Doctor routes - doctor và admin truy cập được  
AppRouter.doctorGuard()
```

## 🚀 Extension Methods

### Auth Navigation
```dart
context.goToLogin();
context.goToRegister();
context.goToForgotPassword();
context.goToResetPassword();
```

### Feature Navigation
```dart
// Medications
context.goToMedications();
context.goToMedicationDetail('123');
context.goToAddMedication();
context.goToEditMedication('123');

// Prescriptions
context.goToPrescriptions();
context.goToPrescriptionDetail('456');
context.goToAddPrescription();
context.goToEditPrescription('456');

// Reminders
context.goToReminders();
context.goToReminderDetail('789');
context.goToAddReminder();
context.goToEditReminder('789');
```

### Profile Navigation
```dart
context.goToProfile();
context.goToEditProfile();
context.goToSettings();
context.goToAbout();
```

### Utility Navigation
```dart
// Navigation stack management
context.goBack();                    # Go back với fallback
context.goAndReplace('/new-route');  # Replace current route
context.goAndClearStack('/home');    # Clear stack và navigate

// Route information
final currentRoute = context.currentRoute;
final params = context.routeParameters;
final queryParams = context.queryParameters;

// Navigation checks
if (context.canNavigateTo('/some-route')) {
  context.go('/some-route');
}
```

## 🔧 Configuration

### Main App Integration

```dart
// main.dart
MaterialApp.router(
  routerConfig: AppRouter.router,
  // ... other configurations
)
```

### Route Guard Setup

```dart
// app_router.dart
GoRouter(
  redirect: RouteGuards.authGuard,  // Global auth guard
  routes: [
    // Route definitions...
  ],
)
```

## 🧪 Testing

### Unit Tests
```dart
testWidgets('should navigate to medication detail', (tester) async {
  // Test navigation logic
});
```

### Integration Tests
```dart
testWidgets('should redirect unauthenticated users', (tester) async {
  // Test route guards
});
```

## 📚 Documentation

- 📖 [Navigation Guide](../../docs/navigation_guide.md) - Chi tiết hướng dẫn sử dụng
- 🔍 [API Reference](./app_router.dart) - Chi tiết API
- 🛡️ [Security Guide](./route_guards.dart) - Hướng dẫn bảo mật

## 🐛 Debugging

### Enable Debug Logging
```dart
// app_router.dart
GoRouter(
  debugLogDiagnostics: true,
  // ...
)
```

### Route Guard Logs
```
🛡️ RouteGuard: Checking access for /medications
✅ RouteGuard: Access granted
❌ RouteGuard: User not authenticated, redirecting to login
```

## 🔄 Migration từ Navigator 1.0

### Before (Navigator 1.0)
```dart
Navigator.of(context).pushNamed('/medications/123');
```

### After (GoRouter)
```dart
context.goToMedicationDetail('123');
```

## 🎯 Best Practices

1. **✅ Sử dụng Extension Methods** thay vì raw GoRouter calls
2. **✅ Kiểm tra `canNavigateTo()`** trước khi navigate
3. **✅ Sử dụng `AppRoutes.buildPath()`** cho dynamic routes
4. **✅ Handle errors** với try-catch khi cần
5. **✅ Test navigation logic** với unit tests

## 🚨 Common Issues

| Issue | Solution |
|-------|----------|
| Route not found | Kiểm tra route definition trong `app_router.dart` |
| Auth loop | Kiểm tra logic trong `route_guards.dart` |
| Parameters null | Đảm bảo route pattern đúng (`:id`) |
| Back button issues | Sử dụng `context.canPop()` |

## 🔮 Future Enhancements

- [ ] **Route animations** - Custom transition animations
- [ ] **Route caching** - Cache route states
- [ ] **Analytics integration** - Track navigation events
- [ ] **A/B testing** - Route-based feature flags
- [ ] **Offline routing** - Handle offline navigation

---

**📞 Support**: Nếu có vấn đề với navigation system, vui lòng tạo issue hoặc liên hệ team development.
