import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../pages/responsive_demo_page.dart';
import '../pages/home_page.dart';
import '../../features/medication/presentation/pages/medication_list_page.dart';
import '../../features/medication/presentation/pages/add_medication_page.dart';
import '../../features/prescription/presentation/pages/prescription_list_page.dart';
import '../../features/prescription/presentation/pages/create_prescription_page.dart';
import '../../features/prescription/presentation/pages/add_medicine_to_prescription_page.dart';
import '../../features/prescription/presentation/pages/create_medication_schedule_page.dart';
import '../../features/prescription/presentation/pages/prescription_detail_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import 'app_routes.dart';
import 'route_guards.dart';

/// Cấu hình GoRouter cho ứng dụng
///
/// Class này chịu trách nhiệm thiết lập và quản lý tất cả các routes
/// trong ứng dụng, bao gồm navigation guards và error handling.
class AppRouter {
  // Private constructor
  AppRouter._();

  /// Instance singleton của router
  static GoRouter? _router;

  /// Lấy instance của GoRouter
  static GoRouter get router {
    _router ??= _createRouter();
    return _router!;
  }

  /// Tạo cấu hình GoRouter
  static GoRouter _createRouter() {
    return GoRouter(
      // Route ban đầu
      initialLocation: AppRoutes.splash,

      // Debug logging (chỉ trong development)
      debugLogDiagnostics: true,

      // Error handling
      errorBuilder: (context, state) => _ErrorPage(error: state.error),

      // Route guards
      redirect: RouteGuards.authGuard,

      // Định nghĩa các routes
      routes: [
        // Splash & Onboarding
        GoRoute(
          path: AppRoutes.splash,
          name: 'splash',
          builder: (context, state) => const HomePage(),
        ),

        GoRoute(
          path: AppRoutes.onboarding,
          name: 'onboarding',
          builder: (context, state) =>
              const _PlaceholderPage(title: 'Onboarding'),
        ),

        // Demo page for testing
        GoRoute(
          path: '/demo',
          name: 'demo',
          builder: (context, state) => const ResponsiveDemoPage(),
        ),

        // Auth routes
        GoRoute(
          path: AppRoutes.login,
          name: 'login',
          builder: (context, state) =>
              const _PlaceholderPage(title: 'Đăng nhập'),
        ),

        GoRoute(
          path: AppRoutes.register,
          name: 'register',
          builder: (context, state) => const _PlaceholderPage(title: 'Đăng ký'),
        ),

        GoRoute(
          path: AppRoutes.forgotPassword,
          name: 'forgot-password',
          builder: (context, state) =>
              const _PlaceholderPage(title: 'Quên mật khẩu'),
        ),

        // Main app routes
        GoRoute(
          path: AppRoutes.home,
          name: 'home',
          builder: (context, state) => const HomePage(),
        ),

        // Medication routes
        GoRoute(
          path: AppRoutes.medications,
          name: 'medications',
          builder: (context, state) => const MedicationListPage(),
          routes: [
            GoRoute(
              path: 'add',
              name: 'add-medication',
              builder: (context, state) => const AddMedicationPage(),
            ),
            GoRoute(
              path: ':id',
              name: 'medication-detail',
              builder: (context, state) {
                final id = state.pathParameters['id']!;
                return _PlaceholderPage(title: 'Chi tiết thuốc #$id');
              },
              routes: [
                GoRoute(
                  path: 'edit',
                  name: 'edit-medication',
                  builder: (context, state) {
                    final id = state.pathParameters['id']!;
                    return _PlaceholderPage(title: 'Sửa thuốc #$id');
                  },
                ),
              ],
            ),
          ],
        ),

        // Prescription routes
        GoRoute(
          path: AppRoutes.prescriptions,
          name: 'prescriptions',
          builder: (context, state) => const PrescriptionListPage(),
          routes: [
            GoRoute(
              path: 'add',
              name: 'add-prescription',
              builder: (context, state) => const CreatePrescriptionPage(),
            ),
            GoRoute(
              path: ':id',
              name: 'prescription-detail',
              builder: (context, state) {
                final id = state.pathParameters['id']!;
                return PrescriptionDetailPage(prescriptionId: id);
              },
              routes: [
                GoRoute(
                  path: 'edit',
                  name: 'edit-prescription',
                  builder: (context, state) {
                    final id = state.pathParameters['id']!;
                    return _PlaceholderPage(title: 'Sửa đơn thuốc #$id');
                  },
                ),
              ],
            ),
          ],
        ),

        // Reminder routes
        GoRoute(
          path: AppRoutes.reminders,
          name: 'reminders',
          builder: (context, state) =>
              const _PlaceholderPage(title: 'Danh sách nhắc nhở'),
          routes: [
            GoRoute(
              path: 'add',
              name: 'add-reminder',
              builder: (context, state) =>
                  const _PlaceholderPage(title: 'Thêm nhắc nhở'),
            ),
            GoRoute(
              path: ':id',
              name: 'reminder-detail',
              builder: (context, state) {
                final id = state.pathParameters['id']!;
                return _PlaceholderPage(title: 'Chi tiết nhắc nhở #$id');
              },
              routes: [
                GoRoute(
                  path: 'edit',
                  name: 'edit-reminder',
                  builder: (context, state) {
                    final id = state.pathParameters['id']!;
                    return _PlaceholderPage(title: 'Sửa nhắc nhở #$id');
                  },
                ),
              ],
            ),
          ],
        ),

        // Profile routes
        GoRoute(
          path: AppRoutes.profile,
          name: 'profile',
          builder: (context, state) => const ProfilePage(),
          routes: [
            GoRoute(
              path: 'edit',
              name: 'edit-profile',
              builder: (context, state) =>
                  const _PlaceholderPage(title: 'Sửa hồ sơ'),
            ),
          ],
        ),

        GoRoute(
          path: AppRoutes.settings,
          name: 'settings',
          builder: (context, state) => const _PlaceholderPage(title: 'Cài đặt'),
        ),

        // Error routes
        GoRoute(
          path: AppRoutes.notFound,
          name: 'not-found',
          builder: (context, state) => const _NotFoundPage(),
        ),
      ],
    );
  }

  /// Reset router (dùng cho testing hoặc hot reload)
  static void reset() {
    _router = null;
  }
}

/// Trang placeholder cho các route chưa implement
class _PlaceholderPage extends StatelessWidget {
  const _PlaceholderPage({required this.title});

  final String title;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Trang này đang được phát triển',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.splash),
              child: const Text('Về trang chủ'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Trang lỗi 404
class _NotFoundPage extends StatelessWidget {
  const _NotFoundPage();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Không tìm thấy trang'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              '404',
              style: Theme.of(context).textTheme.displayMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Không tìm thấy trang',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.splash),
              child: const Text('Về trang chủ'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Trang lỗi chung
class _ErrorPage extends StatelessWidget {
  const _ErrorPage({required this.error});

  final Exception? error;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lỗi'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Đã xảy ra lỗi',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            if (error != null) ...[
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.splash),
              child: const Text('Về trang chủ'),
            ),
          ],
        ),
      ),
    );
  }
}
