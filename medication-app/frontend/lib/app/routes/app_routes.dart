/// <PERSON><PERSON><PERSON> nghĩa các route paths cho ứng dụng
/// 
/// File này chứa tất cả các đường dẫn route được sử dụng trong ứng dụng
/// để đảm bảo tính nhất quán và dễ bảo trì.
class AppRoutes {
  // Private constructor để ngăn việc khởi tạo instance
  AppRoutes._();

  // Root routes
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  
  // Auth routes
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';
  
  // Main app routes
  static const String home = '/home';
  static const String dashboard = '/dashboard';
  
  // Medication routes
  static const String medications = '/medications';
  static const String medicationDetail = '/medications/:id';
  static const String addMedication = '/medications/add';
  static const String editMedication = '/medications/:id/edit';
  
  // Prescription routes
  static const String prescriptions = '/prescriptions';
  static const String prescriptionDetail = '/prescriptions/:id';
  static const String addPrescription = '/prescriptions/add';
  static const String editPrescription = '/prescriptions/:id/edit';
  
  // Reminder routes
  static const String reminders = '/reminders';
  static const String reminderDetail = '/reminders/:id';
  static const String addReminder = '/reminders/add';
  static const String editReminder = '/reminders/:id/edit';
  
  // Profile routes
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String settings = '/settings';
  static const String about = '/about';
  
  // Error routes
  static const String notFound = '/404';
  static const String error = '/error';

  /// Tạo route path với parameters
  /// 
  /// Ví dụ: 
  /// ```dart
  /// AppRoutes.buildPath(AppRoutes.medicationDetail, {'id': '123'})
  /// // Trả về: '/medications/123'
  /// ```
  static String buildPath(String route, Map<String, String> params) {
    String path = route;
    params.forEach((key, value) {
      path = path.replaceAll(':$key', value);
    });
    return path;
  }

  /// Kiểm tra xem route có yêu cầu authentication không
  static bool requiresAuth(String route) {
    const publicRoutes = [
      splash,
      onboarding,
      login,
      register,
      forgotPassword,
      resetPassword,
      notFound,
      error,
    ];
    
    return !publicRoutes.contains(route);
  }

  /// Lấy tên route từ path
  static String getRouteName(String path) {
    // Loại bỏ parameters và query strings
    final cleanPath = path.split('?').first;
    
    // Map các path phức tạp về tên route
    if (cleanPath.startsWith('/medications/') && cleanPath.endsWith('/edit')) {
      return editMedication;
    }
    if (cleanPath.startsWith('/medications/') && cleanPath != '/medications/add') {
      return medicationDetail;
    }
    if (cleanPath.startsWith('/prescriptions/') && cleanPath.endsWith('/edit')) {
      return editPrescription;
    }
    if (cleanPath.startsWith('/prescriptions/') && cleanPath != '/prescriptions/add') {
      return prescriptionDetail;
    }
    if (cleanPath.startsWith('/reminders/') && cleanPath.endsWith('/edit')) {
      return editReminder;
    }
    if (cleanPath.startsWith('/reminders/') && cleanPath != '/reminders/add') {
      return reminderDetail;
    }
    
    return cleanPath;
  }
}
