import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'app_routes.dart';

/// Extension methods cho navigation để sử dụng dễ dàng hơn
/// 
/// Các extension này cung cấp các method tiện lợi để navigate
/// giữa các trang mà không cần nhớ exact route paths.
extension NavigationExtensions on BuildContext {
  
  // ============================================================================
  // AUTH NAVIGATION
  // ============================================================================
  
  /// Navigate đến trang đăng nhập
  void goToLogin() => go(AppRoutes.login);
  
  /// Navigate đến trang đăng ký
  void goToRegister() => go(AppRoutes.register);
  
  /// Navigate đến trang quên mật khẩu
  void goToForgotPassword() => go(AppRoutes.forgotPassword);
  
  /// Navigate đến trang reset mật khẩu
  void goToResetPassword() => go(AppRoutes.resetPassword);
  
  // ============================================================================
  // MAIN APP NAVIGATION
  // ============================================================================
  
  /// Navigate đến trang chủ
  void goToHome() => go(AppRoutes.home);
  
  /// Navigate đến dashboard
  void goToDashboard() => go(AppRoutes.dashboard);
  
  /// Navigate đến onboarding
  void goToOnboarding() => go(AppRoutes.onboarding);
  
  // ============================================================================
  // MEDICATION NAVIGATION
  // ============================================================================
  
  /// Navigate đến danh sách thuốc
  void goToMedications() => go(AppRoutes.medications);
  
  /// Navigate đến chi tiết thuốc
  void goToMedicationDetail(String medicationId) {
    final path = AppRoutes.buildPath(AppRoutes.medicationDetail, {'id': medicationId});
    go(path);
  }
  
  /// Navigate đến trang thêm thuốc
  void goToAddMedication() => go(AppRoutes.addMedication);
  
  /// Navigate đến trang sửa thuốc
  void goToEditMedication(String medicationId) {
    final path = AppRoutes.buildPath(AppRoutes.editMedication, {'id': medicationId});
    go(path);
  }
  
  // ============================================================================
  // PRESCRIPTION NAVIGATION
  // ============================================================================
  
  /// Navigate đến danh sách đơn thuốc
  void goToPrescriptions() => go(AppRoutes.prescriptions);
  
  /// Navigate đến chi tiết đơn thuốc
  void goToPrescriptionDetail(String prescriptionId) {
    final path = AppRoutes.buildPath(AppRoutes.prescriptionDetail, {'id': prescriptionId});
    go(path);
  }
  
  /// Navigate đến trang thêm đơn thuốc
  void goToAddPrescription() => go(AppRoutes.addPrescription);
  
  /// Navigate đến trang sửa đơn thuốc
  void goToEditPrescription(String prescriptionId) {
    final path = AppRoutes.buildPath(AppRoutes.editPrescription, {'id': prescriptionId});
    go(path);
  }
  
  // ============================================================================
  // REMINDER NAVIGATION
  // ============================================================================
  
  /// Navigate đến danh sách nhắc nhở
  void goToReminders() => go(AppRoutes.reminders);
  
  /// Navigate đến chi tiết nhắc nhở
  void goToReminderDetail(String reminderId) {
    final path = AppRoutes.buildPath(AppRoutes.reminderDetail, {'id': reminderId});
    go(path);
  }
  
  /// Navigate đến trang thêm nhắc nhở
  void goToAddReminder() => go(AppRoutes.addReminder);
  
  /// Navigate đến trang sửa nhắc nhở
  void goToEditReminder(String reminderId) {
    final path = AppRoutes.buildPath(AppRoutes.editReminder, {'id': reminderId});
    go(path);
  }
  
  // ============================================================================
  // PROFILE NAVIGATION
  // ============================================================================
  
  /// Navigate đến trang hồ sơ
  void goToProfile() => go(AppRoutes.profile);
  
  /// Navigate đến trang sửa hồ sơ
  void goToEditProfile() => go(AppRoutes.editProfile);
  
  /// Navigate đến trang cài đặt
  void goToSettings() => go(AppRoutes.settings);
  
  /// Navigate đến trang about
  void goToAbout() => go(AppRoutes.about);
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Navigate back với optional result
  void goBack<T>([T? result]) {
    if (canPop()) {
      pop(result);
    } else {
      // Nếu không thể pop, navigate về home
      goToHome();
    }
  }
  
  /// Navigate và replace current route
  void goAndReplace(String route) => pushReplacement(route);
  
  /// Navigate và clear stack
  void goAndClearStack(String route) {
    while (canPop()) {
      pop();
    }
    pushReplacement(route);
  }
  
  /// Show bottom sheet với route
  Future<T?> showBottomSheetRoute<T>(Widget child) {
    return showModalBottomSheet<T>(
      context: this,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => child,
    );
  }
  
  /// Show dialog với route
  Future<T?> showDialogRoute<T>(Widget child) {
    return showDialog<T>(
      context: this,
      builder: (context) => child,
    );
  }
  
  /// Navigate với animation tùy chỉnh
  void goWithCustomTransition(
    String route, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    // TODO: Implement custom transition nếu cần
    go(route);
  }
  
  /// Kiểm tra xem có thể navigate đến route không
  bool canNavigateTo(String route) {
    try {
      // Kiểm tra route có tồn tại không
      final routeMatch = GoRouter.of(this).routerDelegate.currentConfiguration;
      return routeMatch.uri.path != route;
    } catch (e) {
      return false;
    }
  }
  
  /// Lấy current route path
  String get currentRoute {
    final routeMatch = GoRouter.of(this).routerDelegate.currentConfiguration;
    return routeMatch.uri.path;
  }
  
  /// Lấy route parameters
  Map<String, String> get routeParameters {
    final state = GoRouterState.of(this);
    return state.pathParameters;
  }
  
  /// Lấy query parameters
  Map<String, String> get queryParameters {
    final state = GoRouterState.of(this);
    return state.uri.queryParameters;
  }
}
