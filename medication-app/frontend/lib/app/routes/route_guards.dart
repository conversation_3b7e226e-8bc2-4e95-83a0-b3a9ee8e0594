import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'app_routes.dart';

/// Route guards để xử lý authentication và authorization
///
/// Class này chứa các logic kiểm tra quyền truy cập cho các routes
/// và điều hướng người dùng đến trang phù hợp.
class RouteGuards {
  // Private constructor
  RouteGuards._();

  /// Auth guard chính - kiểm tra authentication cho tất cả routes
  ///
  /// Trả về:
  /// - `null`: Cho phép truy cập route hiện tại
  /// - `String`: Redirect đến route khác
  static String? authGuard(BuildContext context, GoRouterState state) {
    final currentPath = state.uri.path;

    // Debug logging
    if (kDebugMode) {
      print('🛡️ RouteGuard: Checking access for $currentPath');
    }

    // Kiểm tra xem có phải là route công khai không
    if (_isPublicRoute(currentPath)) {
      if (kDebugMode) {
        print('✅ RouteGuard: Public route, access granted');
      }
      return null; // Cho phép truy cập
    }

    // Kiểm tra authentication status
    final isAuthenticated = _checkAuthenticationStatus();

    if (!isAuthenticated) {
      if (kDebugMode) {
        print('❌ RouteGuard: User not authenticated, redirecting to login');
      }

      // Lưu intended route để redirect sau khi login
      _saveIntendedRoute(currentPath);

      return AppRoutes.login;
    }

    // Kiểm tra authorization cho route cụ thể
    if (!_checkAuthorization(currentPath)) {
      if (kDebugMode) {
        print('❌ RouteGuard: User not authorized for $currentPath');
      }
      return AppRoutes.home; // Redirect về home nếu không có quyền
    }

    if (kDebugMode) {
      print('✅ RouteGuard: Access granted for $currentPath');
    }

    return null; // Cho phép truy cập
  }

  /// Kiểm tra xem route có phải là public không
  static bool _isPublicRoute(String path) {
    const publicRoutes = [
      AppRoutes.splash,
      AppRoutes.onboarding,
      AppRoutes.login,
      AppRoutes.register,
      AppRoutes.forgotPassword,
      AppRoutes.resetPassword,
      AppRoutes.notFound,
      AppRoutes.error,
    ];

    // Kiểm tra exact match
    if (publicRoutes.contains(path)) {
      return true;
    }

    // Kiểm tra pattern match cho các route có parameters
    for (final route in publicRoutes) {
      if (_matchesRoutePattern(path, route)) {
        return true;
      }
    }

    return false;
  }

  /// Kiểm tra authentication status
  ///
  /// TODO: Implement thực tế với AuthBloc hoặc AuthService
  static bool _checkAuthenticationStatus() {
    // Tạm thời return true để test navigation
    // Trong thực tế, sẽ kiểm tra:
    // - Token trong storage
    // - Auth state từ BLoC
    // - Session validity

    return true; // Temporary for testing

    // Ví dụ implementation thực tế:
    // final authService = GetIt.instance<AuthService>();
    // return authService.isAuthenticated;
  }

  /// Kiểm tra authorization cho route cụ thể
  ///
  /// TODO: Implement role-based access control
  static bool _checkAuthorization(String path) {
    // Tạm thời return true
    // Trong thực tế, sẽ kiểm tra:
    // - User roles
    // - Permissions
    // - Feature flags

    return true; // Temporary for testing

    // Ví dụ implementation thực tế:
    // final userService = GetIt.instance<UserService>();
    // final user = userService.currentUser;
    //
    // // Admin routes
    // if (path.startsWith('/admin')) {
    //   return user?.role == UserRole.admin;
    // }
    //
    // // Doctor routes
    // if (path.startsWith('/prescriptions')) {
    //   return user?.role == UserRole.doctor || user?.role == UserRole.admin;
    // }
    //
    // return true; // Default allow
  }

  /// Lưu intended route để redirect sau khi login
  static void _saveIntendedRoute(String path) {
    // TODO: Implement với SharedPreferences hoặc secure storage
    if (kDebugMode) {
      print('💾 RouteGuard: Saving intended route: $path');
    }

    // Ví dụ implementation:
    // final storage = GetIt.instance<StorageService>();
    // storage.setString('intended_route', path);
  }

  /// Lấy intended route sau khi login thành công
  static String? getIntendedRoute() {
    // TODO: Implement với SharedPreferences hoặc secure storage

    // Ví dụ implementation:
    // final storage = GetIt.instance<StorageService>();
    // final intendedRoute = storage.getString('intended_route');
    // storage.remove('intended_route'); // Clear after use
    // return intendedRoute;

    return null; // Temporary
  }

  /// Kiểm tra xem path có match với route pattern không
  static bool _matchesRoutePattern(String path, String pattern) {
    // Convert pattern với parameters (:id) thành regex
    final regexPattern = pattern
        .replaceAll('/', r'\/')
        .replaceAll(RegExp(r':([^/]+)'), r'([^/]+)');

    final regex = RegExp('^$regexPattern\$');
    return regex.hasMatch(path);
  }

  /// Guard cho admin routes
  static String? adminGuard(BuildContext context, GoRouterState state) {
    // Kiểm tra auth trước
    final authResult = authGuard(context, state);
    if (authResult != null) {
      return authResult;
    }

    // Kiểm tra admin role
    if (!_isAdmin()) {
      if (kDebugMode) {
        print('❌ RouteGuard: Admin access required');
      }
      return AppRoutes.home;
    }

    return null;
  }

  /// Guard cho doctor routes
  static String? doctorGuard(BuildContext context, GoRouterState state) {
    // Kiểm tra auth trước
    final authResult = authGuard(context, state);
    if (authResult != null) {
      return authResult;
    }

    // Kiểm tra doctor role
    if (!_isDoctor() && !_isAdmin()) {
      if (kDebugMode) {
        print('❌ RouteGuard: Doctor access required');
      }
      return AppRoutes.home;
    }

    return null;
  }

  /// Kiểm tra user có phải admin không
  static bool _isAdmin() {
    // TODO: Implement thực tế
    return false;
  }

  /// Kiểm tra user có phải doctor không
  static bool _isDoctor() {
    // TODO: Implement thực tế
    return false;
  }
}
