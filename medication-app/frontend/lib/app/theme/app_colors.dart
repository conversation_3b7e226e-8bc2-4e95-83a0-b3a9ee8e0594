import 'package:flutter/material.dart';

/// <PERSON><PERSON> thống màu sắc cho ứng dụng medication
class AppColors {
  // Private constructor
  AppColors._();

  // ============ PRIMARY COLORS ============
  // M<PERSON>u chính - xanh y tế (medical blue)
  static const Color primaryBlue = Color(0xFF2E86AB);
  static const Color primaryBlueDark = Color(0xFF1E5F7A);
  static const Color primaryBlueLight = Color(0xFF4A9BC7);
  
  // M<PERSON>u phụ - xanh lá (health green)
  static const Color secondaryGreen = Color(0xFF4CAF50);
  static const Color secondaryGreenDark = Color(0xFF388E3C);
  static const Color secondaryGreenLight = Color(0xFF81C784);

  // ============ FUNCTIONAL COLORS ============
  // Success - xanh lá
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFFE8F5E8);
  static const Color successDark = Color(0xFF2E7D32);
  
  // Warning - cam
  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFF3E0);
  static const Color warningDark = Color(0xFFE65100);
  
  // Error - đỏ
  static const Color error = Color(0xFFE53E3E);
  static const Color errorLight = Color(0xFFFED7D7);
  static const Color errorDark = Color(0xFFC53030);
  
  // Info - xanh dương
  static const Color info = Color(0xFF3182CE);
  static const Color infoLight = Color(0xFFE6F3FF);
  static const Color infoDark = Color(0xFF2C5282);

  // ============ MEDICATION SPECIFIC COLORS ============
  // Màu cho các loại thuốc
  static const Color pillColor = Color(0xFFE3F2FD);
  static const Color capsuleColor = Color(0xFFF3E5F5);
  static const Color syrupColor = Color(0xFFFFF8E1);
  static const Color injectionColor = Color(0xFFE8F5E8);
  static const Color dropColor = Color(0xFFE1F5FE);
  
  // Màu cho reminder
  static const Color reminderActive = Color(0xFF4CAF50);
  static const Color reminderInactive = Color(0xFF9E9E9E);
  static const Color reminderOverdue = Color(0xFFE53E3E);

  // ============ LIGHT THEME COLORS ============
  static const Color lightBackground = Color(0xFFFAFAFA);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightSurfaceVariant = Color(0xFFF5F5F5);
  
  static const Color lightOnBackground = Color(0xFF1A1A1A);
  static const Color lightOnSurface = Color(0xFF1A1A1A);
  static const Color lightOnSurfaceVariant = Color(0xFF666666);
  
  static const Color lightOutline = Color(0xFFE0E0E0);
  static const Color lightOutlineVariant = Color(0xFFF0F0F0);
  
  static const Color lightShadow = Color(0x1A000000);
  static const Color lightScrim = Color(0x66000000);

  // ============ DARK THEME COLORS ============
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkSurfaceVariant = Color(0xFF2A2A2A);
  
  static const Color darkOnBackground = Color(0xFFE0E0E0);
  static const Color darkOnSurface = Color(0xFFE0E0E0);
  static const Color darkOnSurfaceVariant = Color(0xFFB0B0B0);
  
  static const Color darkOutline = Color(0xFF404040);
  static const Color darkOutlineVariant = Color(0xFF2A2A2A);
  
  static const Color darkShadow = Color(0x33000000);
  static const Color darkScrim = Color(0x80000000);

  // ============ NEUTRAL COLORS ============
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  
  // Grey scale
  static const Color grey50 = Color(0xFFFAFAFA);
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFEEEEEE);
  static const Color grey300 = Color(0xFFE0E0E0);
  static const Color grey400 = Color(0xFFBDBDBD);
  static const Color grey500 = Color(0xFF9E9E9E);
  static const Color grey600 = Color(0xFF757575);
  static const Color grey700 = Color(0xFF616161);
  static const Color grey800 = Color(0xFF424242);
  static const Color grey900 = Color(0xFF212121);

  // ============ HELPER METHODS ============
  /// Lấy màu theo loại thuốc
  static Color getMedicationTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'viên nén':
      case 'tablet':
        return pillColor;
      case 'viên nang':
      case 'capsule':
        return capsuleColor;
      case 'siro':
      case 'syrup':
        return syrupColor;
      case 'thuốc tiêm':
      case 'injection':
        return injectionColor;
      case 'thuốc nhỏ mắt':
      case 'eye drops':
        return dropColor;
      default:
        return grey100;
    }
  }

  /// Lấy màu theo trạng thái reminder
  static Color getReminderStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'hoạt động':
        return reminderActive;
      case 'overdue':
      case 'quá hạn':
        return reminderOverdue;
      case 'inactive':
      case 'không hoạt động':
      default:
        return reminderInactive;
    }
  }

  /// Lấy màu theo mức độ ưu tiên
  static Color getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
      case 'cao':
        return error;
      case 'medium':
      case 'trung bình':
        return warning;
      case 'low':
      case 'thấp':
      default:
        return info;
    }
  }
}
