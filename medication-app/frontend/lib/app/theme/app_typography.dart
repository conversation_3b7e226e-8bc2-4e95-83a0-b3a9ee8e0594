import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/utils/screen_util.dart';

/// Hệ thống typography cho ứng dụng
class AppTypography {
  // Private constructor
  AppTypography._();

  // ============ FONT FAMILIES ============
  static const String primaryFontFamily = 'Inter';
  static const String secondaryFontFamily = 'Roboto';

  // ============ FONT WEIGHTS ============
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;

  // ============ TEXT STYLES ============

  // Display styles - cho tiêu đề lớn
  static TextStyle get displayLarge => GoogleFonts.inter(
        fontSize: AppScreenUtil.getResponsiveFontSize(32),
        fontWeight: bold,
        height: 1.2,
        letterSpacing: -0.5,
      );

  static TextStyle get displayMedium => GoogleFonts.inter(
        fontSize: 28,
        fontWeight: bold,
        height: 1.2,
        letterSpacing: -0.25,
      );

  static TextStyle get displaySmall => GoogleFonts.inter(
        fontSize: 24,
        fontWeight: semiBold,
        height: 1.3,
        letterSpacing: 0,
      );

  // Headline styles - cho tiêu đề section
  static TextStyle get headlineLarge => GoogleFonts.inter(
        fontSize: 22,
        fontWeight: semiBold,
        height: 1.3,
        letterSpacing: 0,
      );

  static TextStyle get headlineMedium => GoogleFonts.inter(
        fontSize: 20,
        fontWeight: semiBold,
        height: 1.3,
        letterSpacing: 0.15,
      );

  static TextStyle get headlineSmall => GoogleFonts.inter(
        fontSize: 18,
        fontWeight: medium,
        height: 1.4,
        letterSpacing: 0.15,
      );

  // Title styles - cho tiêu đề card, dialog
  static TextStyle get titleLarge => GoogleFonts.inter(
        fontSize: 16,
        fontWeight: semiBold,
        height: 1.4,
        letterSpacing: 0.15,
      );

  static TextStyle get titleMedium => GoogleFonts.inter(
        fontSize: 14,
        fontWeight: medium,
        height: 1.4,
        letterSpacing: 0.1,
      );

  static TextStyle get titleSmall => GoogleFonts.inter(
        fontSize: 12,
        fontWeight: medium,
        height: 1.4,
        letterSpacing: 0.1,
      );

  // Body styles - cho nội dung chính
  static TextStyle get bodyLarge => GoogleFonts.inter(
        fontSize: 16,
        fontWeight: regular,
        height: 1.5,
        letterSpacing: 0.15,
      );

  static TextStyle get bodyMedium => GoogleFonts.inter(
        fontSize: 14,
        fontWeight: regular,
        height: 1.5,
        letterSpacing: 0.25,
      );

  static TextStyle get bodySmall => GoogleFonts.inter(
        fontSize: 12,
        fontWeight: regular,
        height: 1.4,
        letterSpacing: 0.4,
      );

  // Label styles - cho button, chip, tab
  static TextStyle get labelLarge => GoogleFonts.inter(
        fontSize: 14,
        fontWeight: medium,
        height: 1.4,
        letterSpacing: 0.1,
      );

  static TextStyle get labelMedium => GoogleFonts.inter(
        fontSize: 12,
        fontWeight: medium,
        height: 1.3,
        letterSpacing: 0.5,
      );

  static TextStyle get labelSmall => GoogleFonts.inter(
        fontSize: 10,
        fontWeight: medium,
        height: 1.3,
        letterSpacing: 0.5,
      );

  // ============ CUSTOM STYLES ============

  // Medication specific styles
  static TextStyle get medicationName => GoogleFonts.inter(
        fontSize: 16,
        fontWeight: semiBold,
        height: 1.3,
        letterSpacing: 0.1,
      );

  static TextStyle get medicationDosage => GoogleFonts.inter(
        fontSize: 14,
        fontWeight: medium,
        height: 1.4,
        letterSpacing: 0.1,
      );

  static TextStyle get medicationDescription => GoogleFonts.inter(
        fontSize: 12,
        fontWeight: regular,
        height: 1.4,
        letterSpacing: 0.25,
      );

  // Time and date styles
  static TextStyle get timeDisplay => GoogleFonts.robotoMono(
        fontSize: 18,
        fontWeight: medium,
        height: 1.2,
        letterSpacing: 0.5,
      );

  static TextStyle get dateDisplay => GoogleFonts.inter(
        fontSize: 14,
        fontWeight: medium,
        height: 1.3,
        letterSpacing: 0.1,
      );

  // Status and badge styles
  static TextStyle get statusBadge => GoogleFonts.inter(
        fontSize: 10,
        fontWeight: semiBold,
        height: 1.2,
        letterSpacing: 0.5,
      );

  static TextStyle get reminderTime => GoogleFonts.robotoMono(
        fontSize: 12,
        fontWeight: medium,
        height: 1.3,
        letterSpacing: 0.25,
      );

  // Button styles
  static TextStyle get buttonLarge => GoogleFonts.inter(
        fontSize: 16,
        fontWeight: semiBold,
        height: 1.2,
        letterSpacing: 0.1,
      );

  static TextStyle get buttonMedium => GoogleFonts.inter(
        fontSize: 14,
        fontWeight: medium,
        height: 1.2,
        letterSpacing: 0.1,
      );

  static TextStyle get buttonSmall => GoogleFonts.inter(
        fontSize: 12,
        fontWeight: medium,
        height: 1.2,
        letterSpacing: 0.1,
      );

  // ============ HELPER METHODS ============

  /// Tạo TextTheme cho Material Design
  static TextTheme createTextTheme(Color textColor) {
    return TextTheme(
      displayLarge: displayLarge.copyWith(color: textColor),
      displayMedium: displayMedium.copyWith(color: textColor),
      displaySmall: displaySmall.copyWith(color: textColor),
      headlineLarge: headlineLarge.copyWith(color: textColor),
      headlineMedium: headlineMedium.copyWith(color: textColor),
      headlineSmall: headlineSmall.copyWith(color: textColor),
      titleLarge: titleLarge.copyWith(color: textColor),
      titleMedium: titleMedium.copyWith(color: textColor),
      titleSmall: titleSmall.copyWith(color: textColor),
      bodyLarge: bodyLarge.copyWith(color: textColor),
      bodyMedium: bodyMedium.copyWith(color: textColor),
      bodySmall: bodySmall.copyWith(color: textColor),
      labelLarge: labelLarge.copyWith(color: textColor),
      labelMedium: labelMedium.copyWith(color: textColor),
      labelSmall: labelSmall.copyWith(color: textColor),
    );
  }

  /// Tạo style với màu tùy chỉnh
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  /// Tạo style với font weight tùy chỉnh
  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }

  /// Tạo style với font size tùy chỉnh
  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }
}
