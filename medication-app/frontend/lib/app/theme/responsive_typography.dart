import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/utils/screen_util.dart';

/// Responsive typography system
class ResponsiveTypography {
  // Private constructor
  ResponsiveTypography._();

  // ============ RESPONSIVE TEXT STYLES ============
  
  /// Display Large - responsive
  static TextStyle get displayLarge => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 28.sp,
      tablet: 32.sp,
      desktop: 36.sp,
    ),
    fontWeight: FontWeight.w700,
    height: 1.2,
    letterSpacing: -0.5,
  );

  /// Display Medium - responsive
  static TextStyle get displayMedium => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 24.sp,
      tablet: 28.sp,
      desktop: 32.sp,
    ),
    fontWeight: FontWeight.w700,
    height: 1.2,
    letterSpacing: -0.25,
  );

  /// Display Small - responsive
  static TextStyle get displaySmall => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 20.sp,
      tablet: 24.sp,
      desktop: 28.sp,
    ),
    fontWeight: FontWeight.w600,
    height: 1.3,
    letterSpacing: 0,
  );

  /// Headline Large - responsive
  static TextStyle get headlineLarge => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 18.sp,
      tablet: 22.sp,
      desktop: 24.sp,
    ),
    fontWeight: FontWeight.w600,
    height: 1.3,
    letterSpacing: 0,
  );

  /// Headline Medium - responsive
  static TextStyle get headlineMedium => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 16.sp,
      tablet: 20.sp,
      desktop: 22.sp,
    ),
    fontWeight: FontWeight.w600,
    height: 1.3,
    letterSpacing: 0.15,
  );

  /// Headline Small - responsive
  static TextStyle get headlineSmall => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 14.sp,
      tablet: 18.sp,
      desktop: 20.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.4,
    letterSpacing: 0.15,
  );

  /// Title Large - responsive
  static TextStyle get titleLarge => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 14.sp,
      tablet: 16.sp,
      desktop: 18.sp,
    ),
    fontWeight: FontWeight.w600,
    height: 1.4,
    letterSpacing: 0.15,
  );

  /// Title Medium - responsive
  static TextStyle get titleMedium => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 12.sp,
      tablet: 14.sp,
      desktop: 16.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.4,
    letterSpacing: 0.1,
  );

  /// Title Small - responsive
  static TextStyle get titleSmall => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 10.sp,
      tablet: 12.sp,
      desktop: 14.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.4,
    letterSpacing: 0.1,
  );

  /// Body Large - responsive
  static TextStyle get bodyLarge => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 14.sp,
      tablet: 16.sp,
      desktop: 18.sp,
    ),
    fontWeight: FontWeight.w400,
    height: 1.5,
    letterSpacing: 0.15,
  );

  /// Body Medium - responsive
  static TextStyle get bodyMedium => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 12.sp,
      tablet: 14.sp,
      desktop: 16.sp,
    ),
    fontWeight: FontWeight.w400,
    height: 1.5,
    letterSpacing: 0.25,
  );

  /// Body Small - responsive
  static TextStyle get bodySmall => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 10.sp,
      tablet: 12.sp,
      desktop: 14.sp,
    ),
    fontWeight: FontWeight.w400,
    height: 1.4,
    letterSpacing: 0.4,
  );

  /// Label Large - responsive
  static TextStyle get labelLarge => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 12.sp,
      tablet: 14.sp,
      desktop: 16.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.4,
    letterSpacing: 0.1,
  );

  /// Label Medium - responsive
  static TextStyle get labelMedium => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 10.sp,
      tablet: 12.sp,
      desktop: 14.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.3,
    letterSpacing: 0.5,
  );

  /// Label Small - responsive
  static TextStyle get labelSmall => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 8.sp,
      tablet: 10.sp,
      desktop: 12.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.3,
    letterSpacing: 0.5,
  );

  // ============ MEDICATION SPECIFIC RESPONSIVE STYLES ============
  
  /// Medication name - responsive
  static TextStyle get medicationName => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 14.sp,
      tablet: 16.sp,
      desktop: 18.sp,
    ),
    fontWeight: FontWeight.w600,
    height: 1.3,
    letterSpacing: 0.1,
  );

  /// Medication dosage - responsive
  static TextStyle get medicationDosage => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 12.sp,
      tablet: 14.sp,
      desktop: 16.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.4,
    letterSpacing: 0.1,
  );

  /// Medication description - responsive
  static TextStyle get medicationDescription => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 10.sp,
      tablet: 12.sp,
      desktop: 14.sp,
    ),
    fontWeight: FontWeight.w400,
    height: 1.4,
    letterSpacing: 0.25,
  );

  /// Time display - responsive
  static TextStyle get timeDisplay => GoogleFonts.robotoMono(
    fontSize: AppScreenUtil.responsive(
      mobile: 16.sp,
      tablet: 18.sp,
      desktop: 20.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.2,
    letterSpacing: 0.5,
  );

  /// Date display - responsive
  static TextStyle get dateDisplay => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 12.sp,
      tablet: 14.sp,
      desktop: 16.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.3,
    letterSpacing: 0.1,
  );

  /// Status badge - responsive
  static TextStyle get statusBadge => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 8.sp,
      tablet: 10.sp,
      desktop: 12.sp,
    ),
    fontWeight: FontWeight.w600,
    height: 1.2,
    letterSpacing: 0.5,
  );

  /// Button large - responsive
  static TextStyle get buttonLarge => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 14.sp,
      tablet: 16.sp,
      desktop: 18.sp,
    ),
    fontWeight: FontWeight.w600,
    height: 1.2,
    letterSpacing: 0.1,
  );

  /// Button medium - responsive
  static TextStyle get buttonMedium => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 12.sp,
      tablet: 14.sp,
      desktop: 16.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.2,
    letterSpacing: 0.1,
  );

  /// Button small - responsive
  static TextStyle get buttonSmall => GoogleFonts.inter(
    fontSize: AppScreenUtil.responsive(
      mobile: 10.sp,
      tablet: 12.sp,
      desktop: 14.sp,
    ),
    fontWeight: FontWeight.w500,
    height: 1.2,
    letterSpacing: 0.1,
  );

  // ============ HELPER METHODS ============
  
  /// Create responsive TextTheme
  static TextTheme createResponsiveTextTheme(Color textColor) {
    return TextTheme(
      displayLarge: displayLarge.copyWith(color: textColor),
      displayMedium: displayMedium.copyWith(color: textColor),
      displaySmall: displaySmall.copyWith(color: textColor),
      headlineLarge: headlineLarge.copyWith(color: textColor),
      headlineMedium: headlineMedium.copyWith(color: textColor),
      headlineSmall: headlineSmall.copyWith(color: textColor),
      titleLarge: titleLarge.copyWith(color: textColor),
      titleMedium: titleMedium.copyWith(color: textColor),
      titleSmall: titleSmall.copyWith(color: textColor),
      bodyLarge: bodyLarge.copyWith(color: textColor),
      bodyMedium: bodyMedium.copyWith(color: textColor),
      bodySmall: bodySmall.copyWith(color: textColor),
      labelLarge: labelLarge.copyWith(color: textColor),
      labelMedium: labelMedium.copyWith(color: textColor),
      labelSmall: labelSmall.copyWith(color: textColor),
    );
  }
}
