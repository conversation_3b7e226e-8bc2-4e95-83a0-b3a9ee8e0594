import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Enum cho theme mode
enum AppThemeMode {
  light,
  dark,
  system,
}

/// Enum cho supported languages
enum AppLanguage {
  english('en', 'English', '🇺🇸'),
  vietnamese('vi', 'Tiếng Việt', '🇻🇳');

  const AppLanguage(this.code, this.name, this.flag);

  final String code;
  final String name;
  final String flag;

  /// Convert từ language code sang AppLanguage
  static AppLanguage fromCode(String code) {
    return AppLanguage.values.firstWhere(
      (lang) => lang.code == code,
      orElse: () => AppLanguage.english,
    );
  }

  /// Convert sang Locale
  Locale get locale => Locale(code);
}

/// Model chứa tất cả app configuration
class AppConfig extends Equatable {
  const AppConfig({
    this.themeMode = AppThemeMode.system,
    this.language = AppLanguage.english,
    this.isFirstLaunch = true,
    this.enableNotifications = true,
    this.enableBiometric = false,
    this.autoBackup = true,
    this.reminderSound = true,
    this.vibration = true,
    this.fontSize = 1.0,
    this.highContrast = false,
    this.reduceAnimations = false,
  });

  // Theme settings
  final AppThemeMode themeMode;
  
  // Language settings
  final AppLanguage language;
  
  // App state
  final bool isFirstLaunch;
  
  // Notification settings
  final bool enableNotifications;
  final bool reminderSound;
  final bool vibration;
  
  // Security settings
  final bool enableBiometric;
  
  // Data settings
  final bool autoBackup;
  
  // Accessibility settings
  final double fontSize;
  final bool highContrast;
  final bool reduceAnimations;

  /// Convert sang ThemeMode cho MaterialApp
  ThemeMode get materialThemeMode {
    switch (themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  /// Convert sang Locale cho MaterialApp
  Locale get locale => language.locale;

  /// Copy with method để tạo instance mới
  AppConfig copyWith({
    AppThemeMode? themeMode,
    AppLanguage? language,
    bool? isFirstLaunch,
    bool? enableNotifications,
    bool? enableBiometric,
    bool? autoBackup,
    bool? reminderSound,
    bool? vibration,
    double? fontSize,
    bool? highContrast,
    bool? reduceAnimations,
  }) {
    return AppConfig(
      themeMode: themeMode ?? this.themeMode,
      language: language ?? this.language,
      isFirstLaunch: isFirstLaunch ?? this.isFirstLaunch,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableBiometric: enableBiometric ?? this.enableBiometric,
      autoBackup: autoBackup ?? this.autoBackup,
      reminderSound: reminderSound ?? this.reminderSound,
      vibration: vibration ?? this.vibration,
      fontSize: fontSize ?? this.fontSize,
      highContrast: highContrast ?? this.highContrast,
      reduceAnimations: reduceAnimations ?? this.reduceAnimations,
    );
  }

  /// Convert to JSON để lưu vào SharedPreferences
  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.name,
      'language': language.code,
      'isFirstLaunch': isFirstLaunch,
      'enableNotifications': enableNotifications,
      'enableBiometric': enableBiometric,
      'autoBackup': autoBackup,
      'reminderSound': reminderSound,
      'vibration': vibration,
      'fontSize': fontSize,
      'highContrast': highContrast,
      'reduceAnimations': reduceAnimations,
    };
  }

  /// Create từ JSON
  factory AppConfig.fromJson(Map<String, dynamic> json) {
    return AppConfig(
      themeMode: AppThemeMode.values.firstWhere(
        (mode) => mode.name == json['themeMode'],
        orElse: () => AppThemeMode.system,
      ),
      language: AppLanguage.fromCode(json['language'] ?? 'en'),
      isFirstLaunch: json['isFirstLaunch'] ?? true,
      enableNotifications: json['enableNotifications'] ?? true,
      enableBiometric: json['enableBiometric'] ?? false,
      autoBackup: json['autoBackup'] ?? true,
      reminderSound: json['reminderSound'] ?? true,
      vibration: json['vibration'] ?? true,
      fontSize: (json['fontSize'] ?? 1.0).toDouble(),
      highContrast: json['highContrast'] ?? false,
      reduceAnimations: json['reduceAnimations'] ?? false,
    );
  }

  /// Default config cho lần đầu sử dụng app
  static const AppConfig defaultConfig = AppConfig();

  @override
  List<Object?> get props => [
        themeMode,
        language,
        isFirstLaunch,
        enableNotifications,
        enableBiometric,
        autoBackup,
        reminderSound,
        vibration,
        fontSize,
        highContrast,
        reduceAnimations,
      ];

  @override
  String toString() {
    return 'AppConfig('
        'themeMode: $themeMode, '
        'language: ${language.name}, '
        'isFirstLaunch: $isFirstLaunch, '
        'enableNotifications: $enableNotifications, '
        'enableBiometric: $enableBiometric, '
        'autoBackup: $autoBackup, '
        'reminderSound: $reminderSound, '
        'vibration: $vibration, '
        'fontSize: $fontSize, '
        'highContrast: $highContrast, '
        'reduceAnimations: $reduceAnimations'
        ')';
  }
}
