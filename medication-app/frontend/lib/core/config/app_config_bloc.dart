import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';

import '../utils/app_logger.dart';
import 'app_config.dart';
import 'app_config_event.dart';
import 'app_config_repository.dart';
import 'app_config_state.dart';

/// Bloc để quản lý AppConfig state
class AppConfigBloc extends Bloc<AppConfigEvent, AppConfigState> {
  AppConfigBloc({
    required AppConfigRepository repository,
  })  : _repository = repository,
        super(const AppConfigState()) {
    // Register event handlers
    on<AppConfigLoadRequested>(_onLoadRequested);
    on<AppConfigResetRequested>(_onResetRequested);
    on<AppConfigThemeChanged>(_onThemeChanged);
    on<AppConfigLanguageChanged>(_onLanguageChanged);
    on<AppConfigFirstLaunchCompleted>(_onFirstLaunchCompleted);
    on<AppConfigNotificationsToggled>(_onNotificationsToggled);
    on<AppConfigBiometricToggled>(_onBiometricToggled);
    on<AppConfigAutoBackupToggled>(_onAutoBackupToggled);
    on<AppConfigReminderSoundToggled>(_onReminderSoundToggled);
    on<AppConfigVibrationToggled>(_onVibrationToggled);
    on<AppConfigFontSizeChanged>(_onFontSizeChanged);
    on<AppConfigHighContrastToggled>(_onHighContrastToggled);
    on<AppConfigReduceAnimationsToggled>(_onReduceAnimationsToggled);
    on<AppConfigUpdated>(_onConfigUpdated);
    on<AppConfigExportRequested>(_onExportRequested);
    on<AppConfigImportRequested>(_onImportRequested);
  }

  final AppConfigRepository _repository;

  /// Load config từ repository
  Future<void> _onLoadRequested(
    AppConfigLoadRequested event,
    Emitter<AppConfigState> emit,
  ) async {
    try {
      emit(state.copyWithLoading());

      final config = await _repository.loadConfig();
      emit(state.copyWithLoaded(config));

      AppLogger.i('AppConfig loaded successfully');
    } catch (e, stackTrace) {
      AppLogger.e('Failed to load AppConfig', error: e, stackTrace: stackTrace);
      emit(state.copyWithError('Failed to load configuration: $e'));
    }
  }

  /// Reset config về default
  Future<void> _onResetRequested(
    AppConfigResetRequested event,
    Emitter<AppConfigState> emit,
  ) async {
    try {
      emit(state.copyWithLoading());

      await _repository.clearConfig();
      const defaultConfig = AppConfig.defaultConfig;
      await _repository.saveConfig(defaultConfig);

      emit(state.copyWithLoaded(defaultConfig));

      AppLogger.i('AppConfig reset to default');
    } catch (e, stackTrace) {
      AppLogger.e('Failed to reset AppConfig', error: e, stackTrace: stackTrace);
      emit(state.copyWithError('Failed to reset configuration: $e'));
    }
  }

  /// Thay đổi theme mode
  Future<void> _onThemeChanged(
    AppConfigThemeChanged event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(themeMode: event.themeMode),
      'Theme changed to ${event.themeMode.name}',
    );
  }

  /// Thay đổi language
  Future<void> _onLanguageChanged(
    AppConfigLanguageChanged event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(language: event.language),
      'Language changed to ${event.language.name}',
    );
  }

  /// Complete first launch
  Future<void> _onFirstLaunchCompleted(
    AppConfigFirstLaunchCompleted event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(isFirstLaunch: false),
      'First launch completed',
    );
  }

  /// Toggle notifications
  Future<void> _onNotificationsToggled(
    AppConfigNotificationsToggled event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(enableNotifications: event.enabled),
      'Notifications ${event.enabled ? 'enabled' : 'disabled'}',
    );
  }

  /// Toggle biometric
  Future<void> _onBiometricToggled(
    AppConfigBiometricToggled event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(enableBiometric: event.enabled),
      'Biometric ${event.enabled ? 'enabled' : 'disabled'}',
    );
  }

  /// Toggle auto backup
  Future<void> _onAutoBackupToggled(
    AppConfigAutoBackupToggled event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(autoBackup: event.enabled),
      'Auto backup ${event.enabled ? 'enabled' : 'disabled'}',
    );
  }

  /// Toggle reminder sound
  Future<void> _onReminderSoundToggled(
    AppConfigReminderSoundToggled event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(reminderSound: event.enabled),
      'Reminder sound ${event.enabled ? 'enabled' : 'disabled'}',
    );
  }

  /// Toggle vibration
  Future<void> _onVibrationToggled(
    AppConfigVibrationToggled event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(vibration: event.enabled),
      'Vibration ${event.enabled ? 'enabled' : 'disabled'}',
    );
  }

  /// Change font size
  Future<void> _onFontSizeChanged(
    AppConfigFontSizeChanged event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(fontSize: event.fontSize),
      'Font size changed to ${event.fontSize}',
    );
  }

  /// Toggle high contrast
  Future<void> _onHighContrastToggled(
    AppConfigHighContrastToggled event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(highContrast: event.enabled),
      'High contrast ${event.enabled ? 'enabled' : 'disabled'}',
    );
  }

  /// Toggle reduce animations
  Future<void> _onReduceAnimationsToggled(
    AppConfigReduceAnimationsToggled event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      state.config.copyWith(reduceAnimations: event.enabled),
      'Reduce animations ${event.enabled ? 'enabled' : 'disabled'}',
    );
  }

  /// Update toàn bộ config
  Future<void> _onConfigUpdated(
    AppConfigUpdated event,
    Emitter<AppConfigState> emit,
  ) async {
    await _updateConfig(
      emit,
      event.config,
      'Config updated',
    );
  }

  /// Export config
  Future<void> _onExportRequested(
    AppConfigExportRequested event,
    Emitter<AppConfigState> emit,
  ) async {
    try {
      final configJson = await _repository.exportConfig();
      if (configJson != null) {
        AppLogger.i('Config exported successfully');
        // Có thể emit event để UI handle export
      } else {
        AppLogger.w('Failed to export config');
      }
    } catch (e, stackTrace) {
      AppLogger.e('Failed to export config', error: e, stackTrace: stackTrace);
    }
  }

  /// Import config
  Future<void> _onImportRequested(
    AppConfigImportRequested event,
    Emitter<AppConfigState> emit,
  ) async {
    try {
      emit(state.copyWithLoading());

      final success = await _repository.importConfig(event.configJson);
      if (success) {
        final config = await _repository.loadConfig();
        emit(state.copyWithLoaded(config));
        AppLogger.i('Config imported successfully');
      } else {
        emit(state.copyWithError('Failed to import configuration'));
      }
    } catch (e, stackTrace) {
      AppLogger.e('Failed to import config', error: e, stackTrace: stackTrace);
      emit(state.copyWithError('Failed to import configuration: $e'));
    }
  }

  /// Helper method để update config
  Future<void> _updateConfig(
    Emitter<AppConfigState> emit,
    AppConfig newConfig,
    String logMessage,
  ) async {
    try {
      final success = await _repository.saveConfig(newConfig);
      if (success) {
        emit(state.copyWithConfig(newConfig));
        AppLogger.i(logMessage);
      } else {
        emit(state.copyWithError('Failed to save configuration'));
      }
    } catch (e, stackTrace) {
      AppLogger.e('Failed to update config', error: e, stackTrace: stackTrace);
      emit(state.copyWithError('Failed to update configuration: $e'));
    }
  }
}
