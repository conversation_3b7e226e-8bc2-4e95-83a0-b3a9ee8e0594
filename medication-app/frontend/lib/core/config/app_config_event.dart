import 'package:equatable/equatable.dart';

import 'app_config.dart';

/// Base class cho tất cả AppConfig events
abstract class AppConfigEvent extends Equatable {
  const AppConfigEvent();

  @override
  List<Object?> get props => [];
}

/// Event để load config từ storage
class AppConfigLoadRequested extends AppConfigEvent {
  const AppConfigLoadRequested();
}

/// Event để reset config về default
class AppConfigResetRequested extends AppConfigEvent {
  const AppConfigResetRequested();
}

/// Event để thay đổi theme mode
class AppConfigThemeChanged extends AppConfigEvent {
  const AppConfigThemeChanged(this.themeMode);

  final AppThemeMode themeMode;

  @override
  List<Object?> get props => [themeMode];
}

/// Event để thay đổi language
class AppConfigLanguageChanged extends AppConfigEvent {
  const AppConfigLanguageChanged(this.language);

  final AppLanguage language;

  @override
  List<Object?> get props => [language];
}

/// Event để toggle first launch status
class AppConfigFirstLaunchCompleted extends AppConfigEvent {
  const AppConfigFirstLaunchCompleted();
}

/// Event để toggle notifications
class AppConfigNotificationsToggled extends AppConfigEvent {
  const AppConfigNotificationsToggled(this.enabled);

  final bool enabled;

  @override
  List<Object?> get props => [enabled];
}

/// Event để toggle biometric authentication
class AppConfigBiometricToggled extends AppConfigEvent {
  const AppConfigBiometricToggled(this.enabled);

  final bool enabled;

  @override
  List<Object?> get props => [enabled];
}

/// Event để toggle auto backup
class AppConfigAutoBackupToggled extends AppConfigEvent {
  const AppConfigAutoBackupToggled(this.enabled);

  final bool enabled;

  @override
  List<Object?> get props => [enabled];
}

/// Event để toggle reminder sound
class AppConfigReminderSoundToggled extends AppConfigEvent {
  const AppConfigReminderSoundToggled(this.enabled);

  final bool enabled;

  @override
  List<Object?> get props => [enabled];
}

/// Event để toggle vibration
class AppConfigVibrationToggled extends AppConfigEvent {
  const AppConfigVibrationToggled(this.enabled);

  final bool enabled;

  @override
  List<Object?> get props => [enabled];
}

/// Event để thay đổi font size
class AppConfigFontSizeChanged extends AppConfigEvent {
  const AppConfigFontSizeChanged(this.fontSize);

  final double fontSize;

  @override
  List<Object?> get props => [fontSize];
}

/// Event để toggle high contrast
class AppConfigHighContrastToggled extends AppConfigEvent {
  const AppConfigHighContrastToggled(this.enabled);

  final bool enabled;

  @override
  List<Object?> get props => [enabled];
}

/// Event để toggle reduce animations
class AppConfigReduceAnimationsToggled extends AppConfigEvent {
  const AppConfigReduceAnimationsToggled(this.enabled);

  final bool enabled;

  @override
  List<Object?> get props => [enabled];
}

/// Event để update toàn bộ config
class AppConfigUpdated extends AppConfigEvent {
  const AppConfigUpdated(this.config);

  final AppConfig config;

  @override
  List<Object?> get props => [config];
}

/// Event để export config
class AppConfigExportRequested extends AppConfigEvent {
  const AppConfigExportRequested();
}

/// Event để import config
class AppConfigImportRequested extends AppConfigEvent {
  const AppConfigImportRequested(this.configJson);

  final String configJson;

  @override
  List<Object?> get props => [configJson];
}
