import 'dart:convert';

import '../storage/storage_service.dart';
import '../utils/app_logger.dart';
import 'app_config.dart';

/// Repository để quản lý persistence của AppConfig
class AppConfigRepository {
  AppConfigRepository({
    StorageService? storageService,
  }) : _storageService = storageService ?? StorageService.instance;

  final StorageService _storageService;
  static const String _configKey = 'app_config';

  /// Initialize storage
  Future<void> init() async {
    if (!_storageService.isInitialized) {
      await _storageService.init();
    }
  }

  /// Load config từ SharedPreferences
  Future<AppConfig> loadConfig() async {
    try {
      await init();

      final configJson = _storageService.getString(_configKey);
      if (configJson == null) {
        AppLogger.i('No saved config found, using default');
        return AppConfig.defaultConfig;
      }

      final configMap = jsonDecode(configJson) as Map<String, dynamic>;
      final config = AppConfig.fromJson(configMap);

      AppLogger.i('Loaded config: $config');
      return config;
    } catch (e, stackTrace) {
      AppLogger.e('Failed to load config', error: e, stackTrace: stackTrace);
      return AppConfig.defaultConfig;
    }
  }

  /// Save config vào SharedPreferences
  Future<bool> saveConfig(AppConfig config) async {
    try {
      await init();

      final configJson = jsonEncode(config.toJson());
      final success = await _storageService.setString(_configKey, configJson);

      if (success) {
        AppLogger.i('Config saved successfully: $config');
      } else {
        AppLogger.w('Failed to save config');
      }

      return success;
    } catch (e, stackTrace) {
      AppLogger.e('Failed to save config', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Clear all config (reset về default)
  Future<bool> clearConfig() async {
    try {
      await init();

      final success = await _storageService.remove(_configKey);

      if (success) {
        AppLogger.i('Config cleared successfully');
      } else {
        AppLogger.w('Failed to clear config');
      }

      return success;
    } catch (e, stackTrace) {
      AppLogger.e('Failed to clear config', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Check xem có config đã lưu không
  Future<bool> hasConfig() async {
    try {
      await init();
      return _storageService.containsKey(_configKey);
    } catch (e, stackTrace) {
      AppLogger.e('Failed to check config existence',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Get specific setting value
  Future<T?> getSetting<T>(String key) async {
    try {
      final config = await loadConfig();
      final configMap = config.toJson();
      return configMap[key] as T?;
    } catch (e, stackTrace) {
      AppLogger.e('Failed to get setting: $key',
          error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// Update specific setting
  Future<bool> updateSetting<T>(String key, T value) async {
    try {
      final config = await loadConfig();
      final configMap = config.toJson();
      configMap[key] = value;

      final updatedConfig = AppConfig.fromJson(configMap);
      return await saveConfig(updatedConfig);
    } catch (e, stackTrace) {
      AppLogger.e('Failed to update setting: $key',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Backup config to JSON string (for export)
  Future<String?> exportConfig() async {
    try {
      final config = await loadConfig();
      return jsonEncode(config.toJson());
    } catch (e, stackTrace) {
      AppLogger.e('Failed to export config', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// Restore config from JSON string (for import)
  Future<bool> importConfig(String configJson) async {
    try {
      final configMap = jsonDecode(configJson) as Map<String, dynamic>;
      final config = AppConfig.fromJson(configMap);
      return await saveConfig(config);
    } catch (e, stackTrace) {
      AppLogger.e('Failed to import config', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Get config version (for migration purposes)
  Future<int> getConfigVersion() async {
    try {
      await init();
      return _storageService.getInt('config_version') ?? 1;
    } catch (e, stackTrace) {
      AppLogger.e('Failed to get config version',
          error: e, stackTrace: stackTrace);
      return 1;
    }
  }

  /// Set config version
  Future<bool> setConfigVersion(int version) async {
    try {
      await init();
      return await _storageService.setInt('config_version', version);
    } catch (e, stackTrace) {
      AppLogger.e('Failed to set config version',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }
}
