import 'package:equatable/equatable.dart';

import 'app_config.dart';

/// Status của AppConfig loading
enum AppConfigStatus {
  initial,
  loading,
  loaded,
  error,
}

/// State cho AppConfig Bloc
class AppConfigState extends Equatable {
  const AppConfigState({
    this.status = AppConfigStatus.initial,
    this.config = AppConfig.defaultConfig,
    this.errorMessage,
    this.isFirstTime = true,
  });

  /// Status của config loading
  final AppConfigStatus status;
  
  /// Current app configuration
  final AppConfig config;
  
  /// Error message nếu có lỗi
  final String? errorMessage;
  
  /// Flag để check xem có phải lần đầu load app không
  final bool isFirstTime;

  /// Convenience getters
  bool get isLoading => status == AppConfigStatus.loading;
  bool get isLoaded => status == AppConfigStatus.loaded;
  bool get hasError => status == AppConfigStatus.error;
  bool get isInitial => status == AppConfigStatus.initial;

  /// Theme-related getters
  AppThemeMode get themeMode => config.themeMode;
  AppLanguage get language => config.language;
  bool get isDarkMode => config.themeMode == AppThemeMode.dark;
  bool get isLightMode => config.themeMode == AppThemeMode.light;
  bool get isSystemMode => config.themeMode == AppThemeMode.system;

  /// Language-related getters
  bool get isEnglish => config.language == AppLanguage.english;
  bool get isVietnamese => config.language == AppLanguage.vietnamese;
  String get languageCode => config.language.code;
  String get languageName => config.language.name;
  String get languageFlag => config.language.flag;

  /// App state getters
  bool get isFirstLaunch => config.isFirstLaunch;
  bool get hasCompletedOnboarding => !config.isFirstLaunch;

  /// Settings getters
  bool get notificationsEnabled => config.enableNotifications;
  bool get biometricEnabled => config.enableBiometric;
  bool get autoBackupEnabled => config.autoBackup;
  bool get reminderSoundEnabled => config.reminderSound;
  bool get vibrationEnabled => config.vibration;
  double get fontSize => config.fontSize;
  bool get highContrastEnabled => config.highContrast;
  bool get reduceAnimationsEnabled => config.reduceAnimations;

  /// Copy with method
  AppConfigState copyWith({
    AppConfigStatus? status,
    AppConfig? config,
    String? errorMessage,
    bool? isFirstTime,
  }) {
    return AppConfigState(
      status: status ?? this.status,
      config: config ?? this.config,
      errorMessage: errorMessage ?? this.errorMessage,
      isFirstTime: isFirstTime ?? this.isFirstTime,
    );
  }

  /// Copy with loading state
  AppConfigState copyWithLoading() {
    return copyWith(
      status: AppConfigStatus.loading,
      errorMessage: null,
    );
  }

  /// Copy with loaded state
  AppConfigState copyWithLoaded(AppConfig config) {
    return copyWith(
      status: AppConfigStatus.loaded,
      config: config,
      errorMessage: null,
      isFirstTime: false,
    );
  }

  /// Copy with error state
  AppConfigState copyWithError(String errorMessage) {
    return copyWith(
      status: AppConfigStatus.error,
      errorMessage: errorMessage,
    );
  }

  /// Copy with updated config
  AppConfigState copyWithConfig(AppConfig config) {
    return copyWith(
      config: config,
      status: AppConfigStatus.loaded,
      errorMessage: null,
    );
  }

  @override
  List<Object?> get props => [
        status,
        config,
        errorMessage,
        isFirstTime,
      ];

  @override
  String toString() {
    return 'AppConfigState('
        'status: $status, '
        'config: $config, '
        'errorMessage: $errorMessage, '
        'isFirstTime: $isFirstTime'
        ')';
  }
}
