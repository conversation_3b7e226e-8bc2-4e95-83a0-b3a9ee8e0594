/// Các hằng số liên quan đến API
class ApiConstants {
  // Base URLs
  static const String baseUrl = 'http://localhost:8080/api';
  static const String baseUrlProd = 'https://your-production-url.com/api';

  // Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds

  // Headers
  static const String contentType = 'application/json';
  static const String accept = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearerPrefix = 'Bearer ';

  // Auth endpoints
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String refreshToken = '/auth/refresh';
  static const String logout = '/auth/logout';
  static const String profile = '/auth/profile';

  // Medication endpoints
  static const String medications = '/medications';
  static const String medicationById = '/medications/{id}';
  static const String medicationSearch = '/medications/search';

  // Prescription endpoints
  static const String prescriptions = '/prescriptions';
  static const String prescriptionById = '/prescriptions/{id}';
  static const String prescriptionsByUser = '/prescriptions/user/{userId}';

  // Reminder endpoints
  static const String reminders = '/reminders';
  static const String reminderById = '/reminders/{id}';
  static const String remindersByUser = '/reminders/user/{userId}';

  // User endpoints
  static const String users = '/users';
  static const String userById = '/users/{id}';
  static const String updateProfile = '/users/profile';

  // Status codes
  static const int statusOk = 200;
  static const int statusCreated = 201;
  static const int statusNoContent = 204;
  static const int statusBadRequest = 400;
  static const int statusUnauthorized = 401;
  static const int statusForbidden = 403;
  static const int statusNotFound = 404;
  static const int statusUnprocessableEntity = 422;
  static const int statusInternalServerError = 500;

  // Error codes (sẽ được map với translation keys)
  static const String networkErrorCode = 'network_error';
  static const String timeoutErrorCode = 'timeout_error';
  static const String serverErrorCode = 'server_error';
  static const String unauthorizedErrorCode = 'unauthorized_error';
  static const String forbiddenErrorCode = 'forbidden_error';
  static const String notFoundErrorCode = 'not_found_error';
  static const String validationErrorCode = 'validation_error';
  static const String unknownErrorCode = 'unknown_error';
}
