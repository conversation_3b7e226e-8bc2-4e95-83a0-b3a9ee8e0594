/// Các hằng số chung của ứng dụng
class AppConstants {
  // App info
  static const String appName = 'Medication Manager';
  static const String appVersion = '1.0.0';
  
  // Storage keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String isFirstLaunchKey = 'is_first_launch';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;
  
  // Date formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String apiDateFormat = 'yyyy-MM-dd';
  static const String apiDateTimeFormat = 'yyyy-MM-ddTHH:mm:ss';
  
  // Medication
  static const List<String> medicationTypes = [
    'Viên nén',
    'Viên nang',
    'Siro',
    'Thuốc tiêm',
    'Thuốc nhỏ mắt',
    'Thuốc bôi',
    'Khác',
  ];
  
  static const List<String> dosageUnits = [
    'viên',
    'ml',
    'mg',
    'g',
    'giọt',
    'ống',
    'gói',
  ];
  
  static const List<String> frequencies = [
    'Một lần/ngày',
    'Hai lần/ngày',
    'Ba lần/ngày',
    'Bốn lần/ngày',
    'Khi cần thiết',
    'Khác',
  ];
  
  // Reminder
  static const List<String> reminderTypes = [
    'Uống thuốc',
    'Tái khám',
    'Mua thuốc',
    'Khác',
  ];
  
  // Images
  static const String defaultAvatarPath = 'assets/images/default_avatar.png';
  static const String logoPath = 'assets/images/logo.png';
  static const String splashImagePath = 'assets/images/splash.png';
  
  // Animation durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Debounce
  static const Duration searchDebounce = Duration(milliseconds: 500);
  
  // Cache
  static const Duration cacheExpiration = Duration(hours: 24);
}
