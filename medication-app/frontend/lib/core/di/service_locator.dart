import 'package:get_it/get_it.dart';

import '../network/dio_client.dart';
import '../storage/token_storage.dart';
import '../utils/app_logger.dart';

/// Service Locator để quản lý dependency injection
class ServiceLocator {
  static final GetIt _getIt = GetIt.instance;

  /// Getter cho GetIt instance
  static GetIt get instance => _getIt;

  /// Khởi tạo tất cả dependencies
  static Future<void> init() async {
    // Core services
    await _initCoreServices();

    // Network
    _initNetwork();

    // Repositories
    _initRepositories();

    // Use cases
    _initUseCases();
  }

  /// Khởi tạo core services
  static Future<void> _initCoreServices() async {
    // Logger - Singleton
    _getIt.registerLazySingleton<AppLogger>(
      () => AppLogger.instance,
    );

    // Token Storage - Singleton
    _getIt.registerLazySingleton<TokenStorage>(
      () => TokenStorage.instance,
    );

    // Khởi tạo TokenStorage
    await TokenStorage.instance.init();

    // Log khởi tạo thành công
    AppLogger.i('🚀 Core services initialized successfully');
  }

  /// Khởi tạo network services
  static void _initNetwork() {
    // Dio Client - Singleton
    _getIt.registerLazySingleton<DioClient>(
      () => DioClient.instance,
    );
  }

  /// Khởi tạo repositories
  static void _initRepositories() {
    // TODO: Đăng ký repositories khi tạo
    // _getIt.registerLazySingleton<AuthRepository>(
    //   () => AuthRepositoryImpl(
    //     remoteDataSource: _getIt(),
    //     localDataSource: _getIt(),
    //   ),
    // );
  }

  /// Khởi tạo use cases
  static void _initUseCases() {
    // TODO: Đăng ký use cases khi tạo
    // _getIt.registerLazySingleton<LoginUseCase>(
    //   () => LoginUseCase(_getIt()),
    // );
  }

  /// Reset tất cả dependencies (dùng cho testing)
  static Future<void> reset() async {
    await _getIt.reset();
  }
}
