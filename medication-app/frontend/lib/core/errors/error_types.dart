/// Enum định nghĩa các loại lỗi để hỗ trợ đa ngôn ngữ
enum ErrorType {
  // Network errors
  networkError,
  timeoutError,
  connectionError,
  
  // Server errors
  serverError,
  internalServerError,
  serviceUnavailable,
  
  // Client errors
  badRequest,
  unauthorized,
  forbidden,
  notFound,
  methodNotAllowed,
  conflict,
  unprocessableEntity,
  tooManyRequests,
  
  // Authentication errors
  invalidCredentials,
  tokenExpired,
  tokenInvalid,
  accountLocked,
  accountNotVerified,
  
  // Validation errors
  validationError,
  requiredFieldMissing,
  invalidFormat,
  invalidLength,
  invalidValue,
  
  // Business logic errors
  medicationNotFound,
  prescriptionNotFound,
  reminderNotFound,
  userNotFound,
  duplicateEntry,
  insufficientPermissions,
  operationNotAllowed,
  
  // Cache/Storage errors
  cacheError,
  storageError,
  diskSpaceError,
  
  // Unknown errors
  unknownError,
  cancelledError,
}

/// Extension để lấy error code từ ErrorType
extension ErrorTypeExtension on ErrorType {
  /// Lấy error code dạng string để map với translation
  String get code {
    switch (this) {
      // Network errors
      case ErrorType.networkError:
        return 'network_error';
      case ErrorType.timeoutError:
        return 'timeout_error';
      case ErrorType.connectionError:
        return 'connection_error';
        
      // Server errors
      case ErrorType.serverError:
        return 'server_error';
      case ErrorType.internalServerError:
        return 'internal_server_error';
      case ErrorType.serviceUnavailable:
        return 'service_unavailable';
        
      // Client errors
      case ErrorType.badRequest:
        return 'bad_request';
      case ErrorType.unauthorized:
        return 'unauthorized';
      case ErrorType.forbidden:
        return 'forbidden';
      case ErrorType.notFound:
        return 'not_found';
      case ErrorType.methodNotAllowed:
        return 'method_not_allowed';
      case ErrorType.conflict:
        return 'conflict';
      case ErrorType.unprocessableEntity:
        return 'unprocessable_entity';
      case ErrorType.tooManyRequests:
        return 'too_many_requests';
        
      // Authentication errors
      case ErrorType.invalidCredentials:
        return 'invalid_credentials';
      case ErrorType.tokenExpired:
        return 'token_expired';
      case ErrorType.tokenInvalid:
        return 'token_invalid';
      case ErrorType.accountLocked:
        return 'account_locked';
      case ErrorType.accountNotVerified:
        return 'account_not_verified';
        
      // Validation errors
      case ErrorType.validationError:
        return 'validation_error';
      case ErrorType.requiredFieldMissing:
        return 'required_field_missing';
      case ErrorType.invalidFormat:
        return 'invalid_format';
      case ErrorType.invalidLength:
        return 'invalid_length';
      case ErrorType.invalidValue:
        return 'invalid_value';
        
      // Business logic errors
      case ErrorType.medicationNotFound:
        return 'medication_not_found';
      case ErrorType.prescriptionNotFound:
        return 'prescription_not_found';
      case ErrorType.reminderNotFound:
        return 'reminder_not_found';
      case ErrorType.userNotFound:
        return 'user_not_found';
      case ErrorType.duplicateEntry:
        return 'duplicate_entry';
      case ErrorType.insufficientPermissions:
        return 'insufficient_permissions';
      case ErrorType.operationNotAllowed:
        return 'operation_not_allowed';
        
      // Cache/Storage errors
      case ErrorType.cacheError:
        return 'cache_error';
      case ErrorType.storageError:
        return 'storage_error';
      case ErrorType.diskSpaceError:
        return 'disk_space_error';
        
      // Unknown errors
      case ErrorType.unknownError:
        return 'unknown_error';
      case ErrorType.cancelledError:
        return 'cancelled_error';
    }
  }
}
