import 'error_types.dart';

/// <PERSON><PERSON><PERSON> cơ sở cho tất cả các exception trong ứng dụng
abstract class AppException implements Exception {
  const AppException({
    required this.errorType,
    this.statusCode,
    this.details,
  });

  final ErrorType errorType;
  final int? statusCode;
  final Map<String, dynamic>? details;

  @override
  String toString() => 'AppException: ${errorType.code} (Status: $statusCode)';
}

/// Exception server (5xx)
class ServerException extends AppException {
  const ServerException({
    super.errorType = ErrorType.serverError,
    super.statusCode,
    super.details,
  });
}

/// Exception client (4xx)
class ClientException extends AppException {
  const ClientException({
    super.errorType = ErrorType.badRequest,
    super.statusCode,
    super.details,
  });
}

/// Exception mạng
class NetworkException extends AppException {
  const NetworkException({
    super.errorType = ErrorType.networkError,
    super.statusCode,
    super.details,
  });
}

/// Exception timeout
class TimeoutException extends AppException {
  const TimeoutException({
    super.errorType = ErrorType.timeoutError,
    super.statusCode,
    super.details,
  });
}

/// Exception xác thực (401)
class AuthException extends AppException {
  const AuthException({
    super.errorType = ErrorType.unauthorized,
    super.statusCode = 401,
    super.details,
  });
}

/// Exception không có quyền truy cập (403)
class ForbiddenException extends AppException {
  const ForbiddenException({
    super.errorType = ErrorType.forbidden,
    super.statusCode = 403,
    super.details,
  });
}

/// Exception không tìm thấy (404)
class NotFoundException extends AppException {
  const NotFoundException({
    super.errorType = ErrorType.notFound,
    super.statusCode = 404,
    super.details,
  });
}

/// Exception validation
class ValidationException extends AppException {
  const ValidationException({
    super.errorType = ErrorType.validationError,
    super.statusCode = 422,
    super.details,
  });
}

/// Exception cache/storage
class CacheException extends AppException {
  const CacheException({
    super.errorType = ErrorType.cacheError,
    super.statusCode,
    super.details,
  });
}

/// Exception không xác định
class UnknownException extends AppException {
  const UnknownException({
    super.errorType = ErrorType.unknownError,
    super.statusCode,
    super.details,
  });
}
