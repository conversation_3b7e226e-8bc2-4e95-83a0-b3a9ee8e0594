import 'package:equatable/equatable.dart';

import 'error_types.dart';

/// <PERSON>ớ<PERSON> cơ sở cho tất cả các lỗi trong ứng dụng
abstract class Failure extends Equatable {
  const Failure({
    required this.errorType,
    this.statusCode,
    this.details,
  });

  final ErrorType errorType;
  final int? statusCode;
  final Map<String, dynamic>? details;

  @override
  List<Object?> get props => [errorType, statusCode, details];
}

/// Lỗi server (5xx)
class ServerFailure extends Failure {
  const ServerFailure({
    super.errorType = ErrorType.serverError,
    super.statusCode,
    super.details,
  });
}

/// Lỗi client (4xx)
class ClientFailure extends Failure {
  const ClientFailure({
    super.errorType = ErrorType.badRequest,
    super.statusCode,
    super.details,
  });
}

/// Lỗi mạng (không có kết nối internet)
class NetworkFailure extends Failure {
  const NetworkFailure({
    super.errorType = ErrorType.networkError,
    super.statusCode,
    super.details,
  });
}

/// Lỗi timeout
class TimeoutFailure extends Failure {
  const TimeoutFailure({
    super.errorType = ErrorType.timeoutError,
    super.statusCode,
    super.details,
  });
}

/// Lỗi xác thực (401)
class AuthFailure extends Failure {
  const AuthFailure({
    super.errorType = ErrorType.unauthorized,
    super.statusCode = 401,
    super.details,
  });
}

/// Lỗi không có quyền truy cập (403)
class ForbiddenFailure extends Failure {
  const ForbiddenFailure({
    super.errorType = ErrorType.forbidden,
    super.statusCode = 403,
    super.details,
  });
}

/// Lỗi không tìm thấy (404)
class NotFoundFailure extends Failure {
  const NotFoundFailure({
    super.errorType = ErrorType.notFound,
    super.statusCode = 404,
    super.details,
  });
}

/// Lỗi validation
class ValidationFailure extends Failure {
  const ValidationFailure({
    super.errorType = ErrorType.validationError,
    super.statusCode = 422,
    super.details,
  });
}

/// Lỗi cache/storage
class CacheFailure extends Failure {
  const CacheFailure({
    super.errorType = ErrorType.cacheError,
    super.statusCode,
    super.details,
  });
}

/// Lỗi không xác định
class UnknownFailure extends Failure {
  const UnknownFailure({
    super.errorType = ErrorType.unknownError,
    super.statusCode,
    super.details,
  });
}
