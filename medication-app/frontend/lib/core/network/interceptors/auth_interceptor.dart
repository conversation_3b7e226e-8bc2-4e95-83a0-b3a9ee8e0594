import 'package:dio/dio.dart';

import '../../constants/api_constants.dart';
import '../../storage/token_storage.dart';
import '../../utils/app_logger.dart';

/// Interceptor xử lý authentication token
class AuthInterceptor extends Interceptor {
  final TokenStorage _tokenStorage = TokenStorage.instance;

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // Bỏ qua việc thêm token cho các endpoint không cần auth
    if (_isPublicEndpoint(options.path)) {
      return handler.next(options);
    }

    try {
      final accessToken = await _tokenStorage.getAccessToken();
      if (accessToken != null) {
        options.headers[ApiConstants.authorization] =
            '${ApiConstants.bearerPrefix}$accessToken';
      }
    } catch (e) {
      AppLogger.e('AuthInterceptor - Lỗi khi lấy access token', error: e);
    }

    handler.next(options);
  }

  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    // Xử lý lỗi 401 - token hết hạn
    if (err.response?.statusCode == ApiConstants.statusUnauthorized) {
      try {
        final refreshed = await _refreshToken();
        if (refreshed) {
          // Retry request với token mới
          final response = await _retryRequest(err.requestOptions);
          return handler.resolve(response);
        }
      } catch (e) {
        AppLogger.e('AuthInterceptor - Lỗi khi refresh token', error: e);
        // Clear token và chuyển về màn hình login
        await _tokenStorage.clearTokens();
      }
    }

    handler.next(err);
  }

  /// Kiểm tra endpoint có cần authentication không
  bool _isPublicEndpoint(String path) {
    final publicEndpoints = [
      ApiConstants.login,
      ApiConstants.register,
      ApiConstants.refreshToken,
    ];

    return publicEndpoints.any((endpoint) => path.contains(endpoint));
  }

  /// Refresh access token
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _tokenStorage.getRefreshToken();
      if (refreshToken == null) {
        return false;
      }

      final dio = Dio();
      final response = await dio.post(
        '${ApiConstants.baseUrl}${ApiConstants.refreshToken}',
        data: {'refreshToken': refreshToken},
      );

      if (response.statusCode == ApiConstants.statusOk) {
        final data = response.data;
        await _tokenStorage.saveTokens(
          accessToken: data['accessToken'],
          refreshToken: data['refreshToken'],
        );
        return true;
      }
    } catch (e) {
      AppLogger.e('AuthInterceptor - Lỗi refresh token', error: e);
    }

    return false;
  }

  /// Retry request với token mới
  Future<Response> _retryRequest(RequestOptions requestOptions) async {
    final accessToken = await _tokenStorage.getAccessToken();
    if (accessToken != null) {
      requestOptions.headers[ApiConstants.authorization] =
          '${ApiConstants.bearerPrefix}$accessToken';
    }

    final dio = Dio();
    return await dio.fetch(requestOptions);
  }
}
