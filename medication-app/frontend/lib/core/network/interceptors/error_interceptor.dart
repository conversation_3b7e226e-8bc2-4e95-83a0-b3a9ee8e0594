import 'dart:io';

import 'package:dio/dio.dart';

import '../../constants/api_constants.dart';
import '../../errors/exceptions.dart';
import '../../errors/error_types.dart';
import '../../utils/app_logger.dart';

/// Interceptor xử lý lỗi HTTP
class ErrorInterceptor extends Interceptor {
  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) {
    AppException exception;

    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        exception = const TimeoutException();
        break;

      case DioExceptionType.connectionError:
        if (err.error is SocketException) {
          exception = const NetworkException();
        } else {
          exception = ServerException(
            statusCode: err.response?.statusCode,
            details: {'originalMessage': err.message},
          );
        }
        break;

      case DioExceptionType.badResponse:
        exception = _handleResponseError(err.response!);
        break;

      case DioExceptionType.cancel:
        exception = UnknownException(
          errorType: ErrorType.cancelledError,
          details: {'originalMessage': 'Yêu cầu đã bị hủy'},
        );
        break;

      case DioExceptionType.unknown:
        if (err.error is SocketException) {
          exception = const NetworkException();
        } else {
          exception = UnknownException(
            statusCode: err.response?.statusCode,
            details: {'originalMessage': err.message},
          );
        }
        break;

      default:
        exception = UnknownException(
          statusCode: err.response?.statusCode,
          details: {'originalMessage': err.message},
        );
    }

    AppLogger.e(
      'ErrorInterceptor - ${exception.runtimeType}: ${exception.errorType.code}',
      error:
          'Status: ${exception.statusCode}, Details: ${exception.details}, Request: ${err.requestOptions.method} ${err.requestOptions.uri}',
    );

    // Tạo DioException mới với exception đã xử lý
    final processedError = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: exception,
      message: exception.errorType.code,
    );

    handler.next(processedError);
  }

  /// Xử lý lỗi response dựa trên status code
  AppException _handleResponseError(Response response) {
    final statusCode = response.statusCode ?? 0;
    final data = response.data;

    // Lấy thông tin chi tiết từ response
    Map<String, dynamic> details = {};
    if (data is Map<String, dynamic>) {
      details = {
        'serverMessage': data['message'] ?? data['error'] ?? data['detail'],
        'responseData': data,
      };
    }

    switch (statusCode) {
      case ApiConstants.statusBadRequest:
        return ClientException(
          statusCode: statusCode,
          details: details,
        );

      case ApiConstants.statusUnauthorized:
        return AuthException(
          statusCode: statusCode,
          details: details,
        );

      case ApiConstants.statusForbidden:
        return ForbiddenException(
          statusCode: statusCode,
          details: details,
        );

      case ApiConstants.statusNotFound:
        return NotFoundException(
          statusCode: statusCode,
          details: details,
        );

      case ApiConstants.statusUnprocessableEntity:
        return ValidationException(
          statusCode: statusCode,
          details: details,
        );

      case >= 400 && < 500:
        return ClientException(
          statusCode: statusCode,
          details: details,
        );

      case >= 500:
        return ServerException(
          statusCode: statusCode,
          details: details,
        );

      default:
        return UnknownException(
          statusCode: statusCode,
          details: details,
        );
    }
  }
}
