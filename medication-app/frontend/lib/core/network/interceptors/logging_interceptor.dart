import 'package:dio/dio.dart';

import '../../utils/app_logger.dart';

/// Interceptor để log các request/response (chỉ trong debug mode)
class LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    AppLogger.network(
      'REQUEST[${options.method}] => PATH: ${options.path}\n'
      'Headers: ${options.headers}\n'
      'Query Parameters: ${options.queryParameters}\n'
      'Body: ${options.data}',
    );
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    AppLogger.network(
      'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}\n'
      'Headers: ${response.headers}\n'
      'Data: ${response.data}',
    );
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    AppLogger.network(
      'ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}\n'
      'Message: ${err.message}\n'
      'Type: ${err.type}\n'
      'Response Data: ${err.response?.data}',
    );
    super.onError(err, handler);
  }
}
