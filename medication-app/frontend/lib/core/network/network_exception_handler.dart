import 'package:dio/dio.dart';
import 'package:dartz/dartz.dart';

import '../errors/exceptions.dart';
import '../errors/failures.dart';
import '../errors/error_types.dart';

/// Handler chuyển đổi Exception thành Failure
class NetworkExceptionHandler {
  /// Xử lý exception và trả về Failure
  static Failure handleException(Object exception) {
    if (exception is DioException) {
      return _handleDioException(exception);
    }

    if (exception is AppException) {
      return _handleAppException(exception);
    }

    // Exception không xác định
    return UnknownFailure(
      details: {'originalException': exception.toString()},
    );
  }

  /// Xử lý DioException
  static Failure _handleDioException(DioException exception) {
    // Nếu error là AppException thì xử lý theo AppException
    if (exception.error is AppException) {
      return _handleAppException(exception.error as AppException);
    }

    // Xử lý theo type của DioException
    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const TimeoutFailure();

      case DioExceptionType.connectionError:
        return const NetworkFailure();

      case DioExceptionType.badResponse:
        return _handleResponseFailure(exception.response);

      case DioExceptionType.cancel:
        return UnknownFailure(
          errorType: ErrorType.cancelledError,
          details: {'originalMessage': 'Yêu cầu đã bị hủy'},
        );

      case DioExceptionType.unknown:
      default:
        return UnknownFailure(
          details: {
            'originalMessage':
                exception.message ?? 'Đã xảy ra lỗi không xác định'
          },
        );
    }
  }

  /// Xử lý AppException
  static Failure _handleAppException(AppException exception) {
    switch (exception.runtimeType) {
      case ServerException:
        return ServerFailure(
          errorType: exception.errorType,
          statusCode: exception.statusCode,
          details: exception.details,
        );

      case ClientException:
        return ClientFailure(
          errorType: exception.errorType,
          statusCode: exception.statusCode,
          details: exception.details,
        );

      case NetworkException:
        return NetworkFailure(
          errorType: exception.errorType,
          statusCode: exception.statusCode,
          details: exception.details,
        );

      case TimeoutException:
        return TimeoutFailure(
          errorType: exception.errorType,
          statusCode: exception.statusCode,
          details: exception.details,
        );

      case AuthException:
        return AuthFailure(
          errorType: exception.errorType,
          statusCode: exception.statusCode,
          details: exception.details,
        );

      case ForbiddenException:
        return ForbiddenFailure(
          errorType: exception.errorType,
          statusCode: exception.statusCode,
          details: exception.details,
        );

      case NotFoundException:
        return NotFoundFailure(
          errorType: exception.errorType,
          statusCode: exception.statusCode,
          details: exception.details,
        );

      case ValidationException:
        return ValidationFailure(
          errorType: exception.errorType,
          statusCode: exception.statusCode,
          details: exception.details,
        );

      case CacheException:
        return CacheFailure(
          errorType: exception.errorType,
          statusCode: exception.statusCode,
          details: exception.details,
        );

      default:
        return UnknownFailure(
          errorType: exception.errorType,
          statusCode: exception.statusCode,
          details: exception.details,
        );
    }
  }

  /// Xử lý response failure
  static Failure _handleResponseFailure(Response? response) {
    if (response == null) {
      return UnknownFailure(
        details: {'serverMessage': 'Không nhận được phản hồi từ server'},
      );
    }

    final statusCode = response.statusCode ?? 0;
    final data = response.data;

    // Lấy thông tin chi tiết từ response
    Map<String, dynamic> details = {};
    if (data is Map<String, dynamic>) {
      details = {
        'serverMessage': data['message'] ?? data['error'] ?? data['detail'],
        'responseData': data,
      };
    }

    switch (statusCode) {
      case 400:
        return ClientFailure(statusCode: statusCode, details: details);
      case 401:
        return AuthFailure(statusCode: statusCode, details: details);
      case 403:
        return ForbiddenFailure(statusCode: statusCode, details: details);
      case 404:
        return NotFoundFailure(statusCode: statusCode, details: details);
      case 422:
        return ValidationFailure(statusCode: statusCode, details: details);
      case >= 400 && < 500:
        return ClientFailure(statusCode: statusCode, details: details);
      case >= 500:
        return ServerFailure(statusCode: statusCode, details: details);
      default:
        return UnknownFailure(statusCode: statusCode, details: details);
    }
  }

  /// Wrapper function để xử lý async operation
  static Future<Either<Failure, T>> handleAsyncOperation<T>(
    Future<T> Function() operation,
  ) async {
    try {
      final result = await operation();
      return Right(result);
    } catch (exception) {
      final failure = handleException(exception);
      return Left(failure);
    }
  }
}
