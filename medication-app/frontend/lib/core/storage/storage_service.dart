import 'package:shared_preferences/shared_preferences.dart';

import '../utils/app_logger.dart';

/// Base storage service để share SharedPreferences instance
class StorageService {
  static StorageService? _instance;
  SharedPreferences? _sharedPreferences;

  StorageService._internal();

  /// Singleton instance
  static StorageService get instance {
    _instance ??= StorageService._internal();
    return _instance!;
  }

  /// Initialize SharedPreferences
  Future<void> init() async {
    try {
      _sharedPreferences ??= await SharedPreferences.getInstance();
      AppLogger.i('StorageService initialized successfully');
    } catch (e, stackTrace) {
      AppLogger.e('Failed to initialize StorageService', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get SharedPreferences instance
  SharedPreferences get prefs {
    if (_sharedPreferences == null) {
      throw StateError('StorageService not initialized. Call init() first.');
    }
    return _sharedPreferences!;
  }

  /// Check if initialized
  bool get isInitialized => _sharedPreferences != null;

  /// Clear all data (for logout, reset, etc.)
  Future<bool> clearAll() async {
    try {
      final success = await prefs.clear();
      if (success) {
        AppLogger.i('All storage data cleared');
      } else {
        AppLogger.w('Failed to clear storage data');
      }
      return success;
    } catch (e, stackTrace) {
      AppLogger.e('Failed to clear storage', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Get all keys
  Set<String> getAllKeys() {
    return prefs.getKeys();
  }

  /// Check if key exists
  bool containsKey(String key) {
    return prefs.containsKey(key);
  }

  /// Remove specific key
  Future<bool> remove(String key) async {
    try {
      final success = await prefs.remove(key);
      if (success) {
        AppLogger.d('Removed key: $key');
      } else {
        AppLogger.w('Failed to remove key: $key');
      }
      return success;
    } catch (e, stackTrace) {
      AppLogger.e('Failed to remove key: $key', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  // Convenience methods for different data types
  
  /// Get string value
  String? getString(String key) => prefs.getString(key);
  
  /// Set string value
  Future<bool> setString(String key, String value) => prefs.setString(key, value);
  
  /// Get int value
  int? getInt(String key) => prefs.getInt(key);
  
  /// Set int value
  Future<bool> setInt(String key, int value) => prefs.setInt(key, value);
  
  /// Get bool value
  bool? getBool(String key) => prefs.getBool(key);
  
  /// Set bool value
  Future<bool> setBool(String key, bool value) => prefs.setBool(key, value);
  
  /// Get double value
  double? getDouble(String key) => prefs.getDouble(key);
  
  /// Set double value
  Future<bool> setDouble(String key, double value) => prefs.setDouble(key, value);
  
  /// Get string list value
  List<String>? getStringList(String key) => prefs.getStringList(key);
  
  /// Set string list value
  Future<bool> setStringList(String key, List<String> value) => prefs.setStringList(key, value);
}
