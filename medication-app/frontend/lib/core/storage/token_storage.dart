import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../constants/app_constants.dart';
import 'storage_service.dart';

/// Service quản lý lưu trữ token
class TokenStorage {
  static TokenStorage? _instance;
  late FlutterSecureStorage _secureStorage;
  final StorageService _storageService;

  TokenStorage._internal() : _storageService = StorageService.instance {
    _secureStorage = const FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    );
  }

  /// Singleton instance
  static TokenStorage get instance {
    _instance ??= TokenStorage._internal();
    return _instance!;
  }

  /// Khởi tạo storage services
  Future<void> init() async {
    if (!_storageService.isInitialized) {
      await _storageService.init();
    }
  }

  /// Lưu access token và refresh token
  Future<void> saveTokens({
    required String accessToken,
    required String refreshToken,
  }) async {
    try {
      await Future.wait([
        _secureStorage.write(
          key: AppConstants.accessTokenKey,
          value: accessToken,
        ),
        _secureStorage.write(
          key: AppConstants.refreshTokenKey,
          value: refreshToken,
        ),
      ]);
    } catch (e) {
      throw Exception('Lỗi khi lưu token: $e');
    }
  }

  /// Lấy access token
  Future<String?> getAccessToken() async {
    try {
      return await _secureStorage.read(key: AppConstants.accessTokenKey);
    } catch (e) {
      throw Exception('Lỗi khi lấy access token: $e');
    }
  }

  /// Lấy refresh token
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(key: AppConstants.refreshTokenKey);
    } catch (e) {
      throw Exception('Lỗi khi lấy refresh token: $e');
    }
  }

  /// Kiểm tra có token không
  Future<bool> hasTokens() async {
    try {
      final accessToken = await getAccessToken();
      final refreshToken = await getRefreshToken();
      return accessToken != null && refreshToken != null;
    } catch (e) {
      return false;
    }
  }

  /// Xóa tất cả token
  Future<void> clearTokens() async {
    try {
      await Future.wait([
        _secureStorage.delete(key: AppConstants.accessTokenKey),
        _secureStorage.delete(key: AppConstants.refreshTokenKey),
      ]);
    } catch (e) {
      throw Exception('Lỗi khi xóa token: $e');
    }
  }

  /// Lưu dữ liệu user
  Future<void> saveUserData(String userData) async {
    try {
      await init();
      await _storageService.setString(AppConstants.userDataKey, userData);
    } catch (e) {
      throw Exception('Lỗi khi lưu dữ liệu user: $e');
    }
  }

  /// Lấy dữ liệu user
  Future<String?> getUserData() async {
    try {
      await init();
      return _storageService.getString(AppConstants.userDataKey);
    } catch (e) {
      throw Exception('Lỗi khi lấy dữ liệu user: $e');
    }
  }

  /// Xóa dữ liệu user
  Future<void> clearUserData() async {
    try {
      await init();
      await _storageService.remove(AppConstants.userDataKey);
    } catch (e) {
      throw Exception('Lỗi khi xóa dữ liệu user: $e');
    }
  }

  /// Lưu trạng thái first launch
  Future<void> setFirstLaunch(bool isFirstLaunch) async {
    try {
      await init();
      await _storageService.setBool(
          AppConstants.isFirstLaunchKey, isFirstLaunch);
    } catch (e) {
      throw Exception('Lỗi khi lưu trạng thái first launch: $e');
    }
  }

  /// Kiểm tra có phải first launch không
  Future<bool> isFirstLaunch() async {
    try {
      await init();
      return _storageService.getBool(AppConstants.isFirstLaunchKey) ?? true;
    } catch (e) {
      return true;
    }
  }

  /// Lưu theme mode
  Future<void> saveThemeMode(String themeMode) async {
    try {
      await init();
      await _storageService.setString(AppConstants.themeKey, themeMode);
    } catch (e) {
      throw Exception('Lỗi khi lưu theme mode: $e');
    }
  }

  /// Lấy theme mode
  Future<String?> getThemeMode() async {
    try {
      await init();
      return _storageService.getString(AppConstants.themeKey);
    } catch (e) {
      return null;
    }
  }

  /// Lưu ngôn ngữ
  Future<void> saveLanguage(String language) async {
    try {
      await init();
      await _storageService.setString(AppConstants.languageKey, language);
    } catch (e) {
      throw Exception('Lỗi khi lưu ngôn ngữ: $e');
    }
  }

  /// Lấy ngôn ngữ
  Future<String?> getLanguage() async {
    try {
      await init();
      return _storageService.getString(AppConstants.languageKey);
    } catch (e) {
      return null;
    }
  }

  /// Xóa tất cả dữ liệu
  Future<void> clearAll() async {
    try {
      await init();
      await Future.wait([
        clearTokens(),
        clearUserData(),
        _storageService.clearAll(),
      ]);
    } catch (e) {
      throw Exception('Lỗi khi xóa tất cả dữ liệu: $e');
    }
  }
}
