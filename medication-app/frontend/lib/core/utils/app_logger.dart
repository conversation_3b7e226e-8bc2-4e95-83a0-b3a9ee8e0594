import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// Logger cho toàn bộ ứng dụng
class AppLogger {
  static AppLogger? _instance;
  late Logger _logger;

  AppLogger._internal() {
    _setupLogger();
  }

  /// Singleton instance
  static AppLogger get instance {
    _instance ??= AppLogger._internal();
    return _instance!;
  }

  /// Getter cho Logger instance
  Logger get logger => _logger;

  /// C<PERSON><PERSON> h<PERSON>nh <PERSON>
  void _setupLogger() {
    _logger = Logger(
      filter: _AppLogFilter(),
      printer: _AppLogPrinter(),
      output: _AppLogOutput(),
      level: kDebugMode ? Level.debug : Level.off,
    );
  }

  /// Log debug message
  static void d(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (kDebugMode) {
      instance._logger.d(message, time: time, error: error, stackTrace: stackTrace);
    }
  }

  /// Log info message
  static void i(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (kDebugMode) {
      instance._logger.i(message, time: time, error: error, stackTrace: stackTrace);
    }
  }

  /// Log warning message
  static void w(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (kDebugMode) {
      instance._logger.w(message, time: time, error: error, stackTrace: stackTrace);
    }
  }

  /// Log error message
  static void e(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (kDebugMode) {
      instance._logger.e(message, time: time, error: error, stackTrace: stackTrace);
    }
  }

  /// Log fatal message
  static void f(
    dynamic message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (kDebugMode) {
      instance._logger.f(message, time: time, error: error, stackTrace: stackTrace);
    }
  }

  /// Log network request
  static void network(String message) {
    if (kDebugMode) {
      instance._logger.d('🌐 NETWORK: $message');
    }
  }

  /// Log authentication
  static void auth(String message) {
    if (kDebugMode) {
      instance._logger.d('🔐 AUTH: $message');
    }
  }

  /// Log database operations
  static void database(String message) {
    if (kDebugMode) {
      instance._logger.d('💾 DATABASE: $message');
    }
  }

  /// Log navigation
  static void navigation(String message) {
    if (kDebugMode) {
      instance._logger.d('🧭 NAVIGATION: $message');
    }
  }

  /// Log business logic
  static void business(String message) {
    if (kDebugMode) {
      instance._logger.d('💼 BUSINESS: $message');
    }
  }
}

/// Custom Log Filter - chỉ log trong debug mode
class _AppLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return kDebugMode;
  }
}

/// Custom Log Printer với format đẹp
class _AppLogPrinter extends LogPrinter {
  static final _levelEmojis = {
    Level.debug: '🐛',
    Level.info: 'ℹ️',
    Level.warning: '⚠️',
    Level.error: '❌',
    Level.fatal: '💀',
  };

  static final _levelColors = {
    Level.debug: AnsiColor.fg(8),
    Level.info: AnsiColor.fg(12),
    Level.warning: AnsiColor.fg(208),
    Level.error: AnsiColor.fg(196),
    Level.fatal: AnsiColor.fg(199),
  };

  @override
  List<String> log(LogEvent event) {
    final color = _levelColors[event.level] ?? AnsiColor.none();
    final emoji = _levelEmojis[event.level] ?? '';
    final time = DateTime.now().toString().substring(11, 23);
    
    final lines = <String>[];
    
    // Header line
    lines.add(color('$emoji [$time] [${event.level.name.toUpperCase()}]'));
    
    // Message
    if (event.message != null) {
      lines.add(color('📝 ${event.message}'));
    }
    
    // Error
    if (event.error != null) {
      lines.add(color('💥 Error: ${event.error}'));
    }
    
    // Stack trace (chỉ hiển thị 5 dòng đầu)
    if (event.stackTrace != null) {
      final stackLines = event.stackTrace.toString().split('\n');
      lines.add(color('📍 Stack Trace:'));
      for (int i = 0; i < stackLines.length && i < 5; i++) {
        lines.add(color('   ${stackLines[i]}'));
      }
      if (stackLines.length > 5) {
        lines.add(color('   ... và ${stackLines.length - 5} dòng nữa'));
      }
    }
    
    // Separator
    lines.add(color('${'─' * 50}'));
    
    return lines;
  }
}

/// Custom Log Output
class _AppLogOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    for (final line in event.lines) {
      // ignore: avoid_print
      print(line);
    }
  }
}
