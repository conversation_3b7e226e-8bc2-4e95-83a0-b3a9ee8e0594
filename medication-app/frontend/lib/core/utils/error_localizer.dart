import 'package:flutter/material.dart';

import '../errors/error_types.dart';
import '../errors/failures.dart';
import '../../generated/l10n.dart';

/// Helper để localize error messages
class ErrorLocalizer {
  // Private constructor
  ErrorLocalizer._();

  /// Map ErrorType với localization key
  static String getErrorKey(ErrorType errorType) {
    switch (errorType) {
      // Network errors
      case ErrorType.networkError:
        return 'networkError';
      case ErrorType.timeoutError:
        return 'timeoutError';
      case ErrorType.connectionError:
        return 'networkError';

      // Server errors
      case ErrorType.serverError:
      case ErrorType.internalServerError:
        return 'serverError';
      case ErrorType.serviceUnavailable:
        return 'serverError';

      // Client errors
      case ErrorType.badRequest:
        return 'validationError';
      case ErrorType.unauthorized:
      case ErrorType.tokenExpired:
      case ErrorType.tokenInvalid:
        return 'unauthorizedError';
      case ErrorType.forbidden:
      case ErrorType.insufficientPermissions:
        return 'forbiddenError';
      case ErrorType.notFound:
      case ErrorType.medicationNotFound:
      case ErrorType.prescriptionNotFound:
      case ErrorType.reminderNotFound:
      case ErrorType.userNotFound:
        return 'notFoundError';
      case ErrorType.methodNotAllowed:
      case ErrorType.conflict:
      case ErrorType.duplicateEntry:
      case ErrorType.operationNotAllowed:
        return 'validationError';
      case ErrorType.unprocessableEntity:
      case ErrorType.validationError:
      case ErrorType.requiredFieldMissing:
      case ErrorType.invalidFormat:
      case ErrorType.invalidLength:
      case ErrorType.invalidValue:
        return 'validationError';
      case ErrorType.tooManyRequests:
        return 'serverError';

      // Authentication errors
      case ErrorType.invalidCredentials:
        return 'invalidCredentials';
      case ErrorType.accountLocked:
        return 'forbiddenError';
      case ErrorType.accountNotVerified:
        return 'unauthorizedError';

      // Cache/Storage errors
      case ErrorType.cacheError:
      case ErrorType.storageError:
      case ErrorType.diskSpaceError:
        return 'unknownError';

      // Unknown errors
      case ErrorType.unknownError:
      case ErrorType.cancelledError:
      return 'unknownError';
    }
  }

  /// Lấy localized error message từ Failure
  static String getLocalizedMessage(BuildContext context, Failure failure) {
    final s = S.of(context);
    final errorKey = getErrorKey(failure.errorType);

    switch (errorKey) {
      case 'networkError':
        return s.networkError;
      case 'timeoutError':
        return s.timeoutError;
      case 'serverError':
        return s.serverError;
      case 'unauthorizedError':
        return s.unauthorizedError;
      case 'forbiddenError':
        return s.forbiddenError;
      case 'notFoundError':
        return s.notFoundError;
      case 'validationError':
        return s.validationError;
      case 'invalidCredentials':
        return s.invalidCredentials;
      case 'unknownError':
      default:
        return s.unknownError;
    }
  }

  /// Lấy localized error message với server message
  static String getDetailedLocalizedMessage(
      BuildContext context, Failure failure) {
    String message = getLocalizedMessage(context, failure);

    // Thêm server message nếu có
    final serverMessage = failure.details?['serverMessage'];
    if (serverMessage != null && serverMessage.toString().isNotEmpty) {
      message += '\n$serverMessage';
    }

    return message;
  }

  /// Lấy error icon theo ErrorType
  static IconData getErrorIcon(ErrorType errorType) {
    switch (errorType) {
      case ErrorType.networkError:
      case ErrorType.timeoutError:
      case ErrorType.connectionError:
        return Icons.wifi_off;

      case ErrorType.serverError:
      case ErrorType.internalServerError:
      case ErrorType.serviceUnavailable:
        return Icons.error_outline;

      case ErrorType.unauthorized:
      case ErrorType.tokenExpired:
      case ErrorType.tokenInvalid:
      case ErrorType.invalidCredentials:
        return Icons.lock_outline;

      case ErrorType.forbidden:
      case ErrorType.insufficientPermissions:
      case ErrorType.accountLocked:
        return Icons.block;

      case ErrorType.notFound:
      case ErrorType.medicationNotFound:
      case ErrorType.prescriptionNotFound:
      case ErrorType.reminderNotFound:
      case ErrorType.userNotFound:
        return Icons.search_off;

      case ErrorType.validationError:
      case ErrorType.badRequest:
      case ErrorType.unprocessableEntity:
      case ErrorType.requiredFieldMissing:
      case ErrorType.invalidFormat:
      case ErrorType.invalidLength:
      case ErrorType.invalidValue:
        return Icons.warning_amber;

      case ErrorType.cacheError:
      case ErrorType.storageError:
      case ErrorType.diskSpaceError:
        return Icons.storage;

      case ErrorType.cancelledError:
        return Icons.cancel_outlined;

      case ErrorType.unknownError:
      default:
        return Icons.help_outline;
    }
  }

  /// Lấy error color theo ErrorType
  static Color getErrorColor(ErrorType errorType) {
    switch (errorType) {
      case ErrorType.networkError:
      case ErrorType.timeoutError:
      case ErrorType.connectionError:
        return Colors.orange;

      case ErrorType.serverError:
      case ErrorType.internalServerError:
      case ErrorType.serviceUnavailable:
        return Colors.red;

      case ErrorType.unauthorized:
      case ErrorType.forbidden:
      case ErrorType.tokenExpired:
      case ErrorType.tokenInvalid:
      case ErrorType.invalidCredentials:
      case ErrorType.insufficientPermissions:
      case ErrorType.accountLocked:
        return Colors.red;

      case ErrorType.notFound:
      case ErrorType.medicationNotFound:
      case ErrorType.prescriptionNotFound:
      case ErrorType.reminderNotFound:
      case ErrorType.userNotFound:
        return Colors.blue;

      case ErrorType.validationError:
      case ErrorType.badRequest:
      case ErrorType.unprocessableEntity:
      case ErrorType.requiredFieldMissing:
      case ErrorType.invalidFormat:
      case ErrorType.invalidLength:
      case ErrorType.invalidValue:
        return Colors.amber;

      case ErrorType.cacheError:
      case ErrorType.storageError:
      case ErrorType.diskSpaceError:
        return Colors.grey;

      case ErrorType.cancelledError:
        return Colors.grey;

      case ErrorType.unknownError:
      default:
        return Colors.red;
    }
  }

  /// Kiểm tra có phải lỗi có thể retry không
  static bool canRetry(ErrorType errorType) {
    switch (errorType) {
      case ErrorType.networkError:
      case ErrorType.timeoutError:
      case ErrorType.connectionError:
      case ErrorType.serverError:
      case ErrorType.internalServerError:
      case ErrorType.serviceUnavailable:
      case ErrorType.tooManyRequests:
        return true;

      case ErrorType.unauthorized:
      case ErrorType.forbidden:
      case ErrorType.notFound:
      case ErrorType.validationError:
      case ErrorType.badRequest:
      case ErrorType.unprocessableEntity:
      case ErrorType.invalidCredentials:
      case ErrorType.accountLocked:
      case ErrorType.accountNotVerified:
      case ErrorType.duplicateEntry:
      case ErrorType.operationNotAllowed:
      case ErrorType.insufficientPermissions:
        return false;

      case ErrorType.tokenExpired:
      case ErrorType.tokenInvalid:
        return true; // Có thể refresh token

      case ErrorType.cacheError:
      case ErrorType.storageError:
        return true;

      case ErrorType.cancelledError:
      case ErrorType.unknownError:
      default:
        return false;
    }
  }
}
