import 'app_logger.dart';

/// Helper functions để sử dụng logger dễ dàng hơn
class LoggerHelper {
  /// Log thông tin debug
  static void debug(String message, [Object? error, StackTrace? stackTrace]) {
    AppLogger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Log thông tin
  static void info(String message, [Object? error, StackTrace? stackTrace]) {
    AppLogger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log cảnh báo
  static void warning(String message, [Object? error, StackTrace? stackTrace]) {
    AppLogger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log lỗi
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    AppLogger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log lỗi nghiêm trọng
  static void fatal(String message, [Object? error, StackTrace? stackTrace]) {
    AppLogger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Log network operations
  static void network(String message) {
    AppLogger.network(message);
  }

  /// Log authentication operations
  static void auth(String message) {
    AppLogger.auth(message);
  }

  /// Log database operations
  static void database(String message) {
    AppLogger.database(message);
  }

  /// Log navigation
  static void navigation(String message) {
    AppLogger.navigation(message);
  }

  /// Log business logic
  static void business(String message) {
    AppLogger.business(message);
  }

  /// Log API call với thông tin chi tiết
  static void apiCall({
    required String method,
    required String endpoint,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParams,
    dynamic body,
  }) {
    final buffer = StringBuffer();
    buffer.writeln('API Call: $method $endpoint');
    
    if (headers != null && headers.isNotEmpty) {
      buffer.writeln('Headers: $headers');
    }
    
    if (queryParams != null && queryParams.isNotEmpty) {
      buffer.writeln('Query Params: $queryParams');
    }
    
    if (body != null) {
      buffer.writeln('Body: $body');
    }
    
    AppLogger.network(buffer.toString());
  }

  /// Log API response với thông tin chi tiết
  static void apiResponse({
    required int statusCode,
    required String endpoint,
    Map<String, dynamic>? headers,
    dynamic data,
  }) {
    final buffer = StringBuffer();
    buffer.writeln('API Response: $statusCode $endpoint');
    
    if (headers != null && headers.isNotEmpty) {
      buffer.writeln('Headers: $headers');
    }
    
    if (data != null) {
      buffer.writeln('Data: $data');
    }
    
    AppLogger.network(buffer.toString());
  }

  /// Log user action
  static void userAction(String action, [Map<String, dynamic>? details]) {
    final buffer = StringBuffer();
    buffer.writeln('User Action: $action');
    
    if (details != null && details.isNotEmpty) {
      buffer.writeln('Details: $details');
    }
    
    AppLogger.i(buffer.toString());
  }

  /// Log performance timing
  static void performance(String operation, Duration duration, [Map<String, dynamic>? details]) {
    final buffer = StringBuffer();
    buffer.writeln('Performance: $operation took ${duration.inMilliseconds}ms');
    
    if (details != null && details.isNotEmpty) {
      buffer.writeln('Details: $details');
    }
    
    AppLogger.i(buffer.toString());
  }

  /// Log với custom tag
  static void custom(String tag, String message, [Object? error, StackTrace? stackTrace]) {
    AppLogger.d('[$tag] $message', error: error, stackTrace: stackTrace);
  }
}
