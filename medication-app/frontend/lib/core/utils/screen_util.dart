import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Utilities cho responsive design
class AppScreenUtil {
  // Private constructor
  AppScreenUtil._();

  /// Khởi tạo ScreenUtil
  static Future<void> init(BuildContext context) async {
    await ScreenUtil.ensureScreenSize();
  }

  // ============ RESPONSIVE DIMENSIONS ============
  
  /// Responsive width
  static double w(double width) => width.w;
  
  /// Responsive height
  static double h(double height) => height.h;
  
  /// Responsive font size
  static double sp(double fontSize) => fontSize.sp;
  
  /// Responsive radius
  static double r(double radius) => radius.r;
  
  /// Screen width
  static double get screenWidth => 1.sw;
  
  /// Screen height
  static double get screenHeight => 1.sh;
  
  /// Status bar height
  static double get statusBarHeight => ScreenUtil().statusBarHeight;
  
  /// Bottom bar height
  static double get bottomBarHeight => ScreenUtil().bottomBarHeight;
  
  /// App bar height
  static double get appBarHeight => kToolbarHeight.h;

  // ============ RESPONSIVE SPACING ============
  
  /// Extra small spacing (4dp)
  static double get spaceXS => 4.h;
  
  /// Small spacing (8dp)
  static double get spaceS => 8.h;
  
  /// Medium spacing (16dp)
  static double get spaceM => 16.h;
  
  /// Large spacing (24dp)
  static double get spaceL => 24.h;
  
  /// Extra large spacing (32dp)
  static double get spaceXL => 32.h;
  
  /// Extra extra large spacing (48dp)
  static double get spaceXXL => 48.h;

  // ============ RESPONSIVE PADDING ============
  
  /// Horizontal padding small
  static EdgeInsets get paddingHorizontalS => EdgeInsets.symmetric(horizontal: spaceS);
  
  /// Horizontal padding medium
  static EdgeInsets get paddingHorizontalM => EdgeInsets.symmetric(horizontal: spaceM);
  
  /// Horizontal padding large
  static EdgeInsets get paddingHorizontalL => EdgeInsets.symmetric(horizontal: spaceL);
  
  /// Vertical padding small
  static EdgeInsets get paddingVerticalS => EdgeInsets.symmetric(vertical: spaceS);
  
  /// Vertical padding medium
  static EdgeInsets get paddingVerticalM => EdgeInsets.symmetric(vertical: spaceM);
  
  /// Vertical padding large
  static EdgeInsets get paddingVerticalL => EdgeInsets.symmetric(vertical: spaceL);
  
  /// All padding small
  static EdgeInsets get paddingAllS => EdgeInsets.all(spaceS);
  
  /// All padding medium
  static EdgeInsets get paddingAllM => EdgeInsets.all(spaceM);
  
  /// All padding large
  static EdgeInsets get paddingAllL => EdgeInsets.all(spaceL);
  
  /// Screen padding (safe area)
  static EdgeInsets get paddingScreen => EdgeInsets.fromLTRB(
    spaceM,
    statusBarHeight + spaceS,
    spaceM,
    bottomBarHeight + spaceS,
  );

  // ============ RESPONSIVE BORDER RADIUS ============
  
  /// Small border radius (4dp)
  static BorderRadius get radiusS => BorderRadius.circular(4.r);
  
  /// Medium border radius (8dp)
  static BorderRadius get radiusM => BorderRadius.circular(8.r);
  
  /// Large border radius (12dp)
  static BorderRadius get radiusL => BorderRadius.circular(12.r);
  
  /// Extra large border radius (16dp)
  static BorderRadius get radiusXL => BorderRadius.circular(16.r);
  
  /// Circular border radius
  static BorderRadius get radiusCircular => BorderRadius.circular(100.r);

  // ============ RESPONSIVE SIZES ============
  
  /// Icon size small
  static double get iconSizeS => 16.r;
  
  /// Icon size medium
  static double get iconSizeM => 24.r;
  
  /// Icon size large
  static double get iconSizeL => 32.r;
  
  /// Icon size extra large
  static double get iconSizeXL => 48.r;
  
  /// Button height small
  static double get buttonHeightS => 32.h;
  
  /// Button height medium
  static double get buttonHeightM => 48.h;
  
  /// Button height large
  static double get buttonHeightL => 56.h;
  
  /// Card elevation
  static double get cardElevation => 2.r;
  
  /// Dialog elevation
  static double get dialogElevation => 8.r;

  // ============ BREAKPOINTS ============
  
  /// Mobile breakpoint
  static const double mobileBreakpoint = 600;
  
  /// Tablet breakpoint
  static const double tabletBreakpoint = 1024;
  
  /// Desktop breakpoint
  static const double desktopBreakpoint = 1440;
  
  /// Check if current screen is mobile
  static bool get isMobile => screenWidth < mobileBreakpoint;
  
  /// Check if current screen is tablet
  static bool get isTablet => screenWidth >= mobileBreakpoint && screenWidth < tabletBreakpoint;
  
  /// Check if current screen is desktop
  static bool get isDesktop => screenWidth >= tabletBreakpoint;
  
  /// Check if current screen is small mobile
  static bool get isSmallMobile => screenWidth < 360;
  
  /// Check if current screen is large mobile
  static bool get isLargeMobile => screenWidth >= 360 && screenWidth < mobileBreakpoint;

  // ============ RESPONSIVE HELPERS ============
  
  /// Get responsive value based on screen size
  static T responsive<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop && desktop != null) return desktop;
    if (isTablet && tablet != null) return tablet;
    return mobile;
  }
  
  /// Get responsive columns count
  static int getColumnsCount({
    int mobile = 1,
    int tablet = 2,
    int desktop = 3,
  }) {
    return responsive(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }
  
  /// Get responsive aspect ratio
  static double getAspectRatio({
    double mobile = 1.0,
    double tablet = 1.2,
    double desktop = 1.5,
  }) {
    return responsive(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }
  
  /// Get responsive font size
  static double getResponsiveFontSize(double baseFontSize) {
    if (isSmallMobile) return (baseFontSize * 0.9).sp;
    if (isLargeMobile) return baseFontSize.sp;
    if (isTablet) return (baseFontSize * 1.1).sp;
    if (isDesktop) return (baseFontSize * 1.2).sp;
    return baseFontSize.sp;
  }
  
  /// Get responsive spacing
  static double getResponsiveSpacing(double baseSpacing) {
    return responsive(
      mobile: baseSpacing.h,
      tablet: (baseSpacing * 1.2).h,
      desktop: (baseSpacing * 1.5).h,
    );
  }
  
  /// Get responsive width percentage
  static double getWidthPercent(double percent) {
    return screenWidth * (percent / 100);
  }
  
  /// Get responsive height percentage
  static double getHeightPercent(double percent) {
    return screenHeight * (percent / 100);
  }
}

/// Extension cho responsive design
extension ResponsiveExtension on num {
  /// Responsive width
  double get rw => AppScreenUtil.w(toDouble());
  
  /// Responsive height
  double get rh => AppScreenUtil.h(toDouble());
  
  /// Responsive font size
  double get rsp => AppScreenUtil.sp(toDouble());
  
  /// Responsive radius
  double get rr => AppScreenUtil.r(toDouble());
}

/// Widget helper cho responsive design
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, BoxConstraints constraints) builder;
  
  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Update screen size if needed
        ScreenUtil.init(context);
        return builder(context, constraints);
      },
    );
  }
}

/// Responsive container widget
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? maxWidth;
  
  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.maxWidth,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? AppScreenUtil.responsive(
          mobile: double.infinity,
          tablet: 800,
          desktop: 1200,
        ),
      ),
      padding: padding ?? AppScreenUtil.paddingAllM,
      child: child,
    );
  }
}
