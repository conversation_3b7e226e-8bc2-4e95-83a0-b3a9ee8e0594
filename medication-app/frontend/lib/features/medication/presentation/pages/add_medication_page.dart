import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../app/routes/routes.dart';
import '../../../../generated/l10n.dart';

/// Trang thêm thuốc mới
/// 
/// Form để nhập thông tin thuốc:
/// - Tên thuốc
/// - Li<PERSON><PERSON> l<PERSON>
/// - Tần suất sử dụng
/// - Th<PERSON><PERSON> gian uống
/// - Ghi chú
class AddMedicationPage extends StatefulWidget {
  const AddMedicationPage({super.key});

  @override
  State<AddMedicationPage> createState() => _AddMedicationPageState();
}

class _AddMedicationPageState extends State<AddMedicationPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _dosageController = TextEditingController();
  final _notesController = TextEditingController();
  
  String _selectedCategory = 'Giả<PERSON> đau';
  String _selectedFrequency = '1 lần/ngày';
  List<TimeOfDay> _selectedTimes = [const TimeOfDay(hour: 8, minute: 0)];
  int _totalPills = 30;
  bool _enableReminder = true;

  final List<String> _categories = [
    'Giảm đau',
    'Kháng sinh',
    'Vitamin',
    'Tim mạch',
    'Tiêu hóa',
    'Khác',
  ];

  final List<String> _frequencies = [
    '1 lần/ngày',
    '2 lần/ngày',
    '3 lần/ngày',
    '4 lần/ngày',
    'Khi cần thiết',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thêm thuốc mới'),
        actions: [
          TextButton(
            onPressed: _saveMedication,
            child: const Text('Lưu'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information Section
              _buildSectionTitle('Thông tin cơ bản'),
              SizedBox(height: 16.h),
              _buildBasicInfoSection(),
              
              SizedBox(height: 32.h),
              
              // Dosage Section
              _buildSectionTitle('Liều lượng và tần suất'),
              SizedBox(height: 16.h),
              _buildDosageSection(),
              
              SizedBox(height: 32.h),
              
              // Schedule Section
              _buildSectionTitle('Lịch trình uống thuốc'),
              SizedBox(height: 16.h),
              _buildScheduleSection(),
              
              SizedBox(height: 32.h),
              
              // Reminder Section
              _buildSectionTitle('Nhắc nhở'),
              SizedBox(height: 16.h),
              _buildReminderSection(),
              
              SizedBox(height: 32.h),
              
              // Notes Section
              _buildSectionTitle('Ghi chú'),
              SizedBox(height: 16.h),
              _buildNotesSection(),
              
              SizedBox(height: 32.h),
              
              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveMedication,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: const Text('Lưu thuốc'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      children: [
        // Medication Name
        TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            labelText: 'Tên thuốc *',
            hintText: 'Ví dụ: Paracetamol 500mg',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            prefixIcon: const Icon(Icons.medication),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Vui lòng nhập tên thuốc';
            }
            return null;
          },
        ),
        
        SizedBox(height: 16.h),
        
        // Category Dropdown
        DropdownButtonFormField<String>(
          value: _selectedCategory,
          decoration: InputDecoration(
            labelText: 'Loại thuốc',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            prefixIcon: const Icon(Icons.category),
          ),
          items: _categories.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Text(category),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategory = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDosageSection() {
    return Column(
      children: [
        // Dosage
        TextFormField(
          controller: _dosageController,
          decoration: InputDecoration(
            labelText: 'Liều lượng *',
            hintText: 'Ví dụ: 500mg, 1 viên',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            prefixIcon: const Icon(Icons.medication_liquid),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Vui lòng nhập liều lượng';
            }
            return null;
          },
        ),
        
        SizedBox(height: 16.h),
        
        // Frequency
        DropdownButtonFormField<String>(
          value: _selectedFrequency,
          decoration: InputDecoration(
            labelText: 'Tần suất sử dụng',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            prefixIcon: const Icon(Icons.schedule),
          ),
          items: _frequencies.map((frequency) {
            return DropdownMenuItem(
              value: frequency,
              child: Text(frequency),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedFrequency = value!;
              _updateTimesBasedOnFrequency();
            });
          },
        ),
        
        SizedBox(height: 16.h),
        
        // Total Pills
        Row(
          children: [
            Icon(
              Icons.inventory,
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(width: 12.w),
            Text(
              'Tổng số viên: ',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Expanded(
              child: Slider(
                value: _totalPills.toDouble(),
                min: 10,
                max: 100,
                divisions: 18,
                label: '$_totalPills viên',
                onChanged: (value) {
                  setState(() {
                    _totalPills = value.round();
                  });
                },
              ),
            ),
            Text(
              '$_totalPills',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildScheduleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thời gian uống thuốc',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 12.h),
        
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _selectedTimes.length,
          separatorBuilder: (context, index) => SizedBox(height: 8.h),
          itemBuilder: (context, index) {
            return _buildTimeSelector(index);
          },
        ),
        
        if (_selectedTimes.length < 4) ...[
          SizedBox(height: 12.h),
          TextButton.icon(
            onPressed: _addTimeSlot,
            icon: const Icon(Icons.add),
            label: const Text('Thêm thời gian'),
          ),
        ],
      ],
    );
  }

  Widget _buildTimeSelector(int index) {
    final time = _selectedTimes[index];
    
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(width: 12.w),
          Text(
            'Lần ${index + 1}:',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: InkWell(
              onTap: () => _selectTime(index),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  time.format(context),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          if (_selectedTimes.length > 1)
            IconButton(
              onPressed: () => _removeTimeSlot(index),
              icon: const Icon(Icons.remove_circle_outline, color: Colors.red),
            ),
        ],
      ),
    );
  }

  Widget _buildReminderSection() {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Bật nhắc nhở'),
          subtitle: const Text('Nhận thông báo khi đến giờ uống thuốc'),
          value: _enableReminder,
          onChanged: (value) {
            setState(() {
              _enableReminder = value;
            });
          },
          secondary: const Icon(Icons.notifications),
        ),
        
        if (_enableReminder) ...[
          SizedBox(height: 16.h),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    'Bạn sẽ nhận được thông báo 5 phút trước mỗi lần uống thuốc',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNotesSection() {
    return TextFormField(
      controller: _notesController,
      maxLines: 4,
      decoration: InputDecoration(
        labelText: 'Ghi chú (tùy chọn)',
        hintText: 'Thêm ghi chú về thuốc, cách sử dụng, tác dụng phụ...',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        alignLabelWithHint: true,
      ),
    );
  }

  void _updateTimesBasedOnFrequency() {
    switch (_selectedFrequency) {
      case '1 lần/ngày':
        _selectedTimes = [const TimeOfDay(hour: 8, minute: 0)];
        break;
      case '2 lần/ngày':
        _selectedTimes = [
          const TimeOfDay(hour: 8, minute: 0),
          const TimeOfDay(hour: 20, minute: 0),
        ];
        break;
      case '3 lần/ngày':
        _selectedTimes = [
          const TimeOfDay(hour: 8, minute: 0),
          const TimeOfDay(hour: 14, minute: 0),
          const TimeOfDay(hour: 20, minute: 0),
        ];
        break;
      case '4 lần/ngày':
        _selectedTimes = [
          const TimeOfDay(hour: 8, minute: 0),
          const TimeOfDay(hour: 12, minute: 0),
          const TimeOfDay(hour: 16, minute: 0),
          const TimeOfDay(hour: 20, minute: 0),
        ];
        break;
      default:
        _selectedTimes = [const TimeOfDay(hour: 8, minute: 0)];
    }
  }

  void _addTimeSlot() {
    setState(() {
      _selectedTimes.add(const TimeOfDay(hour: 12, minute: 0));
    });
  }

  void _removeTimeSlot(int index) {
    setState(() {
      _selectedTimes.removeAt(index);
    });
  }

  Future<void> _selectTime(int index) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTimes[index],
    );
    
    if (picked != null) {
      setState(() {
        _selectedTimes[index] = picked;
      });
    }
  }

  void _saveMedication() {
    if (_formKey.currentState!.validate()) {
      // TODO: Save medication using BLoC
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Đã lưu thuốc thành công!'),
          backgroundColor: Colors.green,
        ),
      );
      
      // Navigate back to medications list
      context.goToMedications();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _dosageController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
