import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../app/routes/routes.dart';
import '../../../../generated/l10n.dart';

/// Trang danh sách thuốc
///
/// Hiển thị:
/// - <PERSON><PERSON> sách tất cả thuốc đang sử dụng
/// - T<PERSON><PERSON> kiếm và lọc thuốc
/// - Thêm thuốc mới
/// - T<PERSON><PERSON> cập nhanh các thao tác
class MedicationListPage extends StatefulWidget {
  const MedicationListPage({super.key});

  @override
  State<MedicationListPage> createState() => _MedicationListPageState();
}

class _MedicationListPageState extends State<MedicationListPage> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'Tất cả';

  final List<String> _categories = [
    'T<PERSON>t cả',
    '<PERSON><PERSON><PERSON><PERSON> sin<PERSON>',
    '<PERSON><PERSON><PERSON><PERSON> đ<PERSON>',
    'Vitamin',
    '<PERSON> mạ<PERSON>',
    '<PERSON>i<PERSON><PERSON> hóa',
  ];

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Danh sách thuốc'),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Show filter options
            },
            icon: const Icon(Icons.filter_list),
          ),
          IconButton(
            onPressed: () => context.goToAddMedication(),
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          _buildSearchAndFilter(),

          // Medications List
          Expanded(
            child: _buildMedicationsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.goToAddMedication(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Tìm kiếm thuốc...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() {});
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.background,
            ),
            onChanged: (value) => setState(() {}),
          ),

          SizedBox(height: 16.h),

          // Category Filter
          SizedBox(
            height: 40.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              separatorBuilder: (context, index) => SizedBox(width: 8.w),
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = category == _selectedCategory;

                return FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  selectedColor:
                      Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).colorScheme.primary,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMedicationsList() {
    // Mock data - trong thực tế sẽ lấy từ BLoC/Provider
    final medications = _getMockMedications();

    if (medications.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.separated(
      padding: EdgeInsets.all(16.w),
      itemCount: medications.length,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        final medication = medications[index];
        return _buildMedicationCard(medication);
      },
    );
  }

  Widget _buildMedicationCard(Map<String, dynamic> medication) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () => context.goToMedicationDetail(medication['id']),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Medication Icon
                  Container(
                    width: 48.w,
                    height: 48.w,
                    decoration: BoxDecoration(
                      color: _getCategoryColor(medication['category'])
                          .withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      _getCategoryIcon(medication['category']),
                      color: _getCategoryColor(medication['category']),
                      size: 24.sp,
                    ),
                  ),

                  SizedBox(width: 16.w),

                  // Medication Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          medication['name'],
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          '${medication['dosage']} • ${medication['frequency']}',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withOpacity(0.6),
                                  ),
                        ),
                        SizedBox(height: 4.h),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 8.w, vertical: 4.h),
                          decoration: BoxDecoration(
                            color: _getCategoryColor(medication['category'])
                                .withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Text(
                            medication['category'],
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall
                                ?.copyWith(
                                  color:
                                      _getCategoryColor(medication['category']),
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Actions
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          context.goToEditMedication(medication['id']);
                          break;
                        case 'reminder':
                          // TODO: Add reminder
                          break;
                        case 'delete':
                          _showDeleteDialog(medication);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('Chỉnh sửa'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'reminder',
                        child: ListTile(
                          leading: Icon(Icons.notification_add),
                          title: Text('Thêm nhắc nhở'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title:
                              Text('Xóa', style: TextStyle(color: Colors.red)),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 16.h),

              // Progress and Next Dose
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Còn lại: ${medication['remaining']} viên',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        SizedBox(height: 4.h),
                        LinearProgressIndicator(
                          value: medication['remaining'] / medication['total'],
                          backgroundColor: Theme.of(context)
                              .colorScheme
                              .outline
                              .withOpacity(0.2),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _getProgressColor(
                                medication['remaining'] / medication['total']),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Lần tiếp theo',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withOpacity(0.6),
                            ),
                      ),
                      Text(
                        medication['nextDose'],
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medication_outlined,
            size: 64.sp,
            color: Theme.of(context).colorScheme.outline,
          ),
          SizedBox(height: 16.h),
          Text(
            'Chưa có thuốc nào',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          SizedBox(height: 8.h),
          Text(
            'Thêm thuốc đầu tiên của bạn',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
          ),
          SizedBox(height: 24.h),
          ElevatedButton.icon(
            onPressed: () => context.goToAddMedication(),
            icon: const Icon(Icons.add),
            label: const Text('Thêm thuốc'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(Map<String, dynamic> medication) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content:
            Text('Bạn có chắc chắn muốn xóa thuốc "${medication['name']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Delete medication
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Đã xóa ${medication['name']}')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getMockMedications() {
    return [
      {
        'id': '1',
        'name': 'Paracetamol 500mg',
        'dosage': '500mg',
        'frequency': '3 lần/ngày',
        'category': 'Giảm đau',
        'remaining': 15,
        'total': 30,
        'nextDose': '14:30',
      },
      {
        'id': '2',
        'name': 'Vitamin D3',
        'dosage': '1000 IU',
        'frequency': '1 lần/ngày',
        'category': 'Vitamin',
        'remaining': 25,
        'total': 30,
        'nextDose': '08:00',
      },
      {
        'id': '3',
        'name': 'Amoxicillin',
        'dosage': '250mg',
        'frequency': '2 lần/ngày',
        'category': 'Kháng sinh',
        'remaining': 8,
        'total': 20,
        'nextDose': '20:00',
      },
      {
        'id': '4',
        'name': 'Omega-3',
        'dosage': '1000mg',
        'frequency': '1 lần/ngày',
        'category': 'Vitamin',
        'remaining': 20,
        'total': 60,
        'nextDose': '21:00',
      },
    ];
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Kháng sinh':
        return Colors.red;
      case 'Giảm đau':
        return Colors.blue;
      case 'Vitamin':
        return Colors.orange;
      case 'Tim mạch':
        return Colors.purple;
      case 'Tiêu hóa':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Kháng sinh':
        return Icons.healing;
      case 'Giảm đau':
        return Icons.medication_liquid;
      case 'Vitamin':
        return Icons.local_pharmacy;
      case 'Tim mạch':
        return Icons.favorite;
      case 'Tiêu hóa':
        return Icons.restaurant;
      default:
        return Icons.medication;
    }
  }

  Color _getProgressColor(double progress) {
    if (progress > 0.5) return Colors.green;
    if (progress > 0.2) return Colors.orange;
    return Colors.red;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
