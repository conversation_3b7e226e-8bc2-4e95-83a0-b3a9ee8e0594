import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../app/routes/routes.dart';
import '../../../../generated/l10n.dart';

/// Trang quản lý tủ thuốc
/// 
/// Hi<PERSON>n thị:
/// - <PERSON><PERSON> sách thuốc trong tủ thuốc
/// - T<PERSON><PERSON> kiếm và lọc thuốc
/// - Thêm thuốc mới vào tủ thuốc
/// - Qu<PERSON>n lý thông tin thuốc (hạn sử dụng, số lượng)
class MedicineCabinetPage extends StatefulWidget {
  const MedicineCabinetPage({super.key});

  @override
  State<MedicineCabinetPage> createState() => _MedicineCabinetPageState();
}

class _MedicineCabinetPageState extends State<MedicineCabinetPage> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'Tất cả';
  String _sortBy = 'Tên thuốc';
  
  final List<String> _categories = [
    'Tất cả',
    '<PERSON><PERSON><PERSON><PERSON> sinh',
    '<PERSON><PERSON><PERSON><PERSON> đau',
    '<PERSON>min',
    '<PERSON> mạch',
    'Tiêu hóa',
    'Khác',
  ];
  
  final List<String> _sortOptions = [
    'Tên thuốc',
    'Ngày thêm',
    'Hạn sử dụng',
    'Số lượng',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tủ thuốc'),
        actions: [
          IconButton(
            onPressed: () {
              _showSortDialog();
            },
            icon: const Icon(Icons.sort),
          ),
          IconButton(
            onPressed: () {
              // TODO: Navigate to add medicine page
            },
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          _buildSearchAndFilter(),
          
          // Statistics Section
          _buildStatisticsSection(),
          
          // Medicines List
          Expanded(
            child: _buildMedicinesList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Navigate to add medicine page
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Tìm kiếm thuốc trong tủ...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() {});
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.background,
            ),
            onChanged: (value) => setState(() {}),
          ),
          
          SizedBox(height: 16.h),
          
          // Category Filter
          SizedBox(
            height: 40.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              separatorBuilder: (context, index) => SizedBox(width: 8.w),
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = category == _selectedCategory;
                
                return FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).colorScheme.primary,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsSection() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem('Tổng thuốc', '24', Icons.medication),
          ),
          Container(
            width: 1,
            height: 40.h,
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
          Expanded(
            child: _buildStatItem('Sắp hết hạn', '3', Icons.warning, color: Colors.orange),
          ),
          Container(
            width: 1,
            height: 40.h,
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
          Expanded(
            child: _buildStatItem('Hết thuốc', '2', Icons.inventory, color: Colors.red),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, {Color? color}) {
    final itemColor = color ?? Theme.of(context).colorScheme.primary;
    
    return Column(
      children: [
        Icon(
          icon,
          color: itemColor,
          size: 24.sp,
        ),
        SizedBox(height: 4.h),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: itemColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMedicinesList() {
    // Mock data - trong thực tế sẽ lấy từ BLoC/Provider
    final medicines = _getMockMedicines();
    
    if (medicines.isEmpty) {
      return _buildEmptyState();
    }
    
    return ListView.separated(
      padding: EdgeInsets.all(16.w),
      itemCount: medicines.length,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        final medicine = medicines[index];
        return _buildMedicineCard(medicine);
      },
    );
  }

  Widget _buildMedicineCard(Map<String, dynamic> medicine) {
    final isExpiringSoon = medicine['days_to_expiry'] <= 30;
    final isOutOfStock = medicine['quantity'] <= 0;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to medicine detail
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Medicine Icon
                  Container(
                    width: 48.w,
                    height: 48.w,
                    decoration: BoxDecoration(
                      color: _getCategoryColor(medicine['type']).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      _getCategoryIcon(medicine['type']),
                      color: _getCategoryColor(medicine['type']),
                      size: 24.sp,
                    ),
                  ),
                  
                  SizedBox(width: 16.w),
                  
                  // Medicine Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          medicine['name'],
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          '${medicine['manufacturer']} • ${medicine['strength']}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                          decoration: BoxDecoration(
                            color: _getCategoryColor(medicine['type']).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Text(
                            medicine['type'],
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: _getCategoryColor(medicine['type']),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Actions
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          // TODO: Edit medicine
                          break;
                        case 'add_to_prescription':
                          // TODO: Add to prescription
                          break;
                        case 'update_quantity':
                          _showUpdateQuantityDialog(medicine);
                          break;
                        case 'delete':
                          _showDeleteDialog(medicine);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('Chỉnh sửa'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'add_to_prescription',
                        child: ListTile(
                          leading: Icon(Icons.add_circle),
                          title: Text('Thêm vào đơn thuốc'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'update_quantity',
                        child: ListTile(
                          leading: Icon(Icons.inventory),
                          title: Text('Cập nhật số lượng'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title: Text('Xóa', style: TextStyle(color: Colors.red)),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              SizedBox(height: 16.h),
              
              // Status and Expiry Info
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.inventory,
                              size: 16.sp,
                              color: isOutOfStock ? Colors.red : Colors.green,
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              'Còn lại: ${medicine['quantity']} ${medicine['unit']}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: isOutOfStock ? Colors.red : Colors.green,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 4.h),
                        Row(
                          children: [
                            Icon(
                              Icons.schedule,
                              size: 16.sp,
                              color: isExpiringSoon ? Colors.orange : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              'HSD: ${medicine['expiry_date']}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: isExpiringSoon ? Colors.orange : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                            ),
                            if (isExpiringSoon) ...[
                              SizedBox(width: 4.w),
                              Text(
                                '(${medicine['days_to_expiry']} ngày)',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.orange,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  if (isOutOfStock || isExpiringSoon)
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color: isOutOfStock ? Colors.red.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Text(
                        isOutOfStock ? 'Hết thuốc' : 'Sắp hết hạn',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isOutOfStock ? Colors.red : Colors.orange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medication_outlined,
            size: 64.sp,
            color: Theme.of(context).colorScheme.outline,
          ),
          SizedBox(height: 16.h),
          Text(
            'Tủ thuốc trống',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          SizedBox(height: 8.h),
          Text(
            'Thêm thuốc đầu tiên vào tủ thuốc của bạn',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          SizedBox(height: 24.h),
          ElevatedButton.icon(
            onPressed: () {
              // TODO: Navigate to add medicine
            },
            icon: const Icon(Icons.add),
            label: const Text('Thêm thuốc'),
          ),
        ],
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sắp xếp theo'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _sortOptions.map((option) {
            return RadioListTile<String>(
              title: Text(option),
              value: option,
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showUpdateQuantityDialog(Map<String, dynamic> medicine) {
    final controller = TextEditingController(text: medicine['quantity'].toString());
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Cập nhật số lượng - ${medicine['name']}'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: 'Số lượng mới',
            suffixText: medicine['unit'],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Update quantity
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Đã cập nhật số lượng ${medicine['name']}')),
              );
            },
            child: const Text('Cập nhật'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(Map<String, dynamic> medicine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text('Bạn có chắc chắn muốn xóa "${medicine['name']}" khỏi tủ thuốc?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Delete medicine
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Đã xóa ${medicine['name']}')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getMockMedicines() {
    return [
      {
        'id': '1',
        'name': 'Paracetamol 500mg',
        'manufacturer': 'Traphaco',
        'strength': '500mg',
        'type': 'Giảm đau',
        'quantity': 45,
        'unit': 'viên',
        'expiry_date': '12/2024',
        'days_to_expiry': 120,
        'added_date': '01/03/2024',
      },
      {
        'id': '2',
        'name': 'Amoxicillin 250mg',
        'manufacturer': 'Imexpharm',
        'strength': '250mg',
        'type': 'Kháng sinh',
        'quantity': 0,
        'unit': 'viên',
        'expiry_date': '08/2024',
        'days_to_expiry': 60,
        'added_date': '15/02/2024',
      },
      {
        'id': '3',
        'name': 'Vitamin C 1000mg',
        'manufacturer': 'DHG Pharma',
        'strength': '1000mg',
        'type': 'Vitamin',
        'quantity': 85,
        'unit': 'viên',
        'expiry_date': '06/2025',
        'days_to_expiry': 365,
        'added_date': '20/02/2024',
      },
      {
        'id': '4',
        'name': 'Omeprazole 20mg',
        'manufacturer': 'Stada',
        'strength': '20mg',
        'type': 'Tiêu hóa',
        'quantity': 15,
        'unit': 'viên',
        'expiry_date': '04/2024',
        'days_to_expiry': 25,
        'added_date': '10/03/2024',
      },
    ];
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Kháng sinh':
        return Colors.red;
      case 'Giảm đau':
        return Colors.blue;
      case 'Vitamin':
        return Colors.orange;
      case 'Tim mạch':
        return Colors.purple;
      case 'Tiêu hóa':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Kháng sinh':
        return Icons.healing;
      case 'Giảm đau':
        return Icons.medication_liquid;
      case 'Vitamin':
        return Icons.local_pharmacy;
      case 'Tim mạch':
        return Icons.favorite;
      case 'Tiêu hóa':
        return Icons.restaurant;
      default:
        return Icons.medication;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
