import 'package:flutter_bloc/flutter_bloc.dart';

import 'create_prescription_step_event.dart';
import 'create_prescription_step_state.dart';

/// BLoC quản lý các bước tạo đơn thuốc
class CreatePrescriptionStepBloc
    extends Bloc<CreatePrescriptionStepEvent, CreatePrescriptionStepState> {
  CreatePrescriptionStepBloc() : super(const CreatePrescriptionStepState()) {
    on<NextStepRequested>(_onNextStepRequested);
    on<PreviousStepRequested>(_onPreviousStepRequested);
    on<GoToStepRequested>(_onGoToStepRequested);
    on<ResetStepsRequested>(_onResetStepsRequested);
    on<UpdateStepDataRequested>(_onUpdateStepDataRequested);
    on<MarkStepCompletedRequested>(_onMarkStepCompletedRequested);
  }

  /// Xử lý event chuyển đến step tiếp theo
  void _onNextStepRequested(
    NextStepRequested event,
    Emitter<CreatePrescriptionStepState> emit,
  ) {
    if (state.currentStepIndex < CreatePrescriptionStepState.totalSteps - 1) {
      final newStepIndex = state.currentStepIndex + 1;
      final newStep = CreatePrescriptionStepState.allSteps[newStepIndex];
      
      // Đánh dấu step hiện tại đã hoàn thành
      final updatedCompletedSteps = List<bool>.from(state.completedSteps);
      updatedCompletedSteps[state.currentStepIndex] = true;

      emit(state.copyWith(
        currentStep: newStep,
        currentStepIndex: newStepIndex,
        completedSteps: updatedCompletedSteps,
        canGoPrevious: true,
        canGoNext: _canGoToNextStep(newStepIndex, state.stepData),
        errorMessage: null,
      ));
    }
  }

  /// Xử lý event quay lại step trước đó
  void _onPreviousStepRequested(
    PreviousStepRequested event,
    Emitter<CreatePrescriptionStepState> emit,
  ) {
    if (state.currentStepIndex > 0) {
      final newStepIndex = state.currentStepIndex - 1;
      final newStep = CreatePrescriptionStepState.allSteps[newStepIndex];

      emit(state.copyWith(
        currentStep: newStep,
        currentStepIndex: newStepIndex,
        canGoPrevious: newStepIndex > 0,
        canGoNext: _canGoToNextStep(newStepIndex, state.stepData),
        errorMessage: null,
      ));
    }
  }

  /// Xử lý event chuyển đến step cụ thể
  void _onGoToStepRequested(
    GoToStepRequested event,
    Emitter<CreatePrescriptionStepState> emit,
  ) {
    if (event.stepIndex >= 0 &&
        event.stepIndex < CreatePrescriptionStepState.totalSteps) {
      final newStep = CreatePrescriptionStepState.allSteps[event.stepIndex];

      emit(state.copyWith(
        currentStep: newStep,
        currentStepIndex: event.stepIndex,
        canGoPrevious: event.stepIndex > 0,
        canGoNext: _canGoToNextStep(event.stepIndex, state.stepData),
        errorMessage: null,
      ));
    }
  }

  /// Xử lý event reset về step đầu tiên
  void _onResetStepsRequested(
    ResetStepsRequested event,
    Emitter<CreatePrescriptionStepState> emit,
  ) {
    emit(const CreatePrescriptionStepState());
  }

  /// Xử lý event cập nhật dữ liệu step
  void _onUpdateStepDataRequested(
    UpdateStepDataRequested event,
    Emitter<CreatePrescriptionStepState> emit,
  ) {
    final updatedStepData = Map<String, dynamic>.from(state.stepData);
    updatedStepData.addAll(event.data);

    emit(state.copyWith(
      stepData: updatedStepData,
      canGoNext: _canGoToNextStep(state.currentStepIndex, updatedStepData),
    ));
  }

  /// Xử lý event đánh dấu step đã hoàn thành
  void _onMarkStepCompletedRequested(
    MarkStepCompletedRequested event,
    Emitter<CreatePrescriptionStepState> emit,
  ) {
    if (event.stepIndex >= 0 &&
        event.stepIndex < CreatePrescriptionStepState.totalSteps) {
      final updatedCompletedSteps = List<bool>.from(state.completedSteps);
      updatedCompletedSteps[event.stepIndex] = true;

      emit(state.copyWith(
        completedSteps: updatedCompletedSteps,
      ));
    }
  }

  /// Kiểm tra xem có thể chuyển đến step tiếp theo không
  bool _canGoToNextStep(int stepIndex, Map<String, dynamic> stepData) {
    switch (stepIndex) {
      case 0: // Basic Info Step
        return _validateBasicInfoStep(stepData);
      case 1: // Add Medicine Step
        return _validateAddMedicineStep(stepData);
      case 2: // Create Schedule Step
        return _validateCreateScheduleStep(stepData);
      default:
        return false;
    }
  }

  /// Validate dữ liệu bước thông tin cơ bản
  bool _validateBasicInfoStep(Map<String, dynamic> stepData) {
    return stepData['doctorName'] != null &&
        stepData['doctorName'].toString().isNotEmpty &&
        stepData['diagnosis'] != null &&
        stepData['diagnosis'].toString().isNotEmpty &&
        stepData['selectedPatient'] != null &&
        stepData['prescriptionDate'] != null &&
        stepData['startDate'] != null;
  }

  /// Validate dữ liệu bước thêm thuốc
  bool _validateAddMedicineStep(Map<String, dynamic> stepData) {
    final medicines = stepData['medicines'] as List<dynamic>?;
    return medicines != null && medicines.isNotEmpty;
  }

  /// Validate dữ liệu bước tạo lịch
  bool _validateCreateScheduleStep(Map<String, dynamic> stepData) {
    final schedules = stepData['schedules'] as List<dynamic>?;
    return schedules != null && schedules.isNotEmpty;
  }
}
