import 'package:equatable/equatable.dart';

/// Events cho việc quản lý các bước tạo đơn thuốc
abstract class CreatePrescriptionStepEvent extends Equatable {
  const CreatePrescriptionStepEvent();

  @override
  List<Object?> get props => [];
}

/// Event chuyển đến step tiếp theo
class NextStepRequested extends CreatePrescriptionStepEvent {
  const NextStepRequested();
}

/// Event quay lại step trước đó
class PreviousStepRequested extends CreatePrescriptionStepEvent {
  const PreviousStepRequested();
}

/// Event chuyển đến step cụ thể
class GoToStepRequested extends CreatePrescriptionStepEvent {
  final int stepIndex;

  const GoToStepRequested(this.stepIndex);

  @override
  List<Object?> get props => [stepIndex];
}

/// Event reset về step đầu tiên
class ResetStepsRequested extends CreatePrescriptionStepEvent {
  const ResetStepsRequested();
}

/// Event cập nhật dữ liệu của step hiện tại
class UpdateStepDataRequested extends CreatePrescriptionStepEvent {
  final Map<String, dynamic> data;

  const UpdateStepDataRequested(this.data);

  @override
  List<Object?> get props => [data];
}

/// Event đánh dấu step hiện tại đã hoàn thành
class MarkStepCompletedRequested extends CreatePrescriptionStepEvent {
  final int stepIndex;

  const MarkStepCompletedRequested(this.stepIndex);

  @override
  List<Object?> get props => [stepIndex];
}
