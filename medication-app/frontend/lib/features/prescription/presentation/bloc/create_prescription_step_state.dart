import 'package:equatable/equatable.dart';

/// Enum định nghĩa các bước tạo đơn thuốc
enum PrescriptionStep {
  basicInfo,    // Bước 1: Thông tin cơ bản
  addMedicine,  // Bước 2: <PERSON><PERSON><PERSON><PERSON> thuốc
  createSchedule, // Bước 3: <PERSON><PERSON><PERSON> l<PERSON>ch uống thuốc
}

/// State cho việc quản lý các bước tạo đơn thuốc
class CreatePrescriptionStepState extends Equatable {
  final PrescriptionStep currentStep;
  final int currentStepIndex;
  final List<bool> completedSteps;
  final Map<String, dynamic> stepData;
  final bool canGoNext;
  final bool canGoPrevious;
  final bool isLoading;
  final String? errorMessage;

  const CreatePrescriptionStepState({
    this.currentStep = PrescriptionStep.basicInfo,
    this.currentStepIndex = 0,
    this.completedSteps = const [false, false, false],
    this.stepData = const {},
    this.canGoNext = false,
    this.canGoPrevious = false,
    this.isLoading = false,
    this.errorMessage,
  });

  /// Tổng số bước
  static const int totalSteps = 3;

  /// Danh sách tất cả các bước
  static const List<PrescriptionStep> allSteps = [
    PrescriptionStep.basicInfo,
    PrescriptionStep.addMedicine,
    PrescriptionStep.createSchedule,
  ];

  /// Tiêu đề của từng bước
  String get stepTitle {
    switch (currentStep) {
      case PrescriptionStep.basicInfo:
        return 'Thông tin cơ bản';
      case PrescriptionStep.addMedicine:
        return 'Thêm thuốc';
      case PrescriptionStep.createSchedule:
        return 'Tạo lịch uống thuốc';
    }
  }

  /// Mô tả của từng bước
  String get stepDescription {
    switch (currentStep) {
      case PrescriptionStep.basicInfo:
        return 'Điền thông tin cơ bản của đơn thuốc';
      case PrescriptionStep.addMedicine:
        return 'Thêm thuốc vào đơn thuốc';
      case PrescriptionStep.createSchedule:
        return 'Tạo lịch uống thuốc cho từng loại thuốc';
    }
  }

  /// Tỷ lệ hoàn thành (0.0 - 1.0)
  double get progress {
    return (currentStepIndex + 1) / totalSteps;
  }

  /// Kiểm tra xem có phải là bước cuối cùng không
  bool get isLastStep {
    return currentStepIndex == totalSteps - 1;
  }

  /// Kiểm tra xem có phải là bước đầu tiên không
  bool get isFirstStep {
    return currentStepIndex == 0;
  }

  /// Copy state với các thay đổi
  CreatePrescriptionStepState copyWith({
    PrescriptionStep? currentStep,
    int? currentStepIndex,
    List<bool>? completedSteps,
    Map<String, dynamic>? stepData,
    bool? canGoNext,
    bool? canGoPrevious,
    bool? isLoading,
    String? errorMessage,
  }) {
    return CreatePrescriptionStepState(
      currentStep: currentStep ?? this.currentStep,
      currentStepIndex: currentStepIndex ?? this.currentStepIndex,
      completedSteps: completedSteps ?? this.completedSteps,
      stepData: stepData ?? this.stepData,
      canGoNext: canGoNext ?? this.canGoNext,
      canGoPrevious: canGoPrevious ?? this.canGoPrevious,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        currentStep,
        currentStepIndex,
        completedSteps,
        stepData,
        canGoNext,
        canGoPrevious,
        isLoading,
        errorMessage,
      ];
}
