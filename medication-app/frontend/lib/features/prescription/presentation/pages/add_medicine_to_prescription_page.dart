import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../generated/l10n.dart';
import 'create_medication_schedule_page_new.dart';

/// Trang thêm thuốc vào đơn thuốc
///
/// Bước 2/3 trong flow tạo đơn thuốc:
/// 1. <PERSON><PERSON><PERSON> thuốc từ tủ thuốc hoặc thêm thuốc mới
/// 2. <PERSON>h<PERSON>p thông tin liều lượng và cách dùng
/// 3. Lưu prescription item
/// 4. <PERSON><PERSON> thể thêm nhiều thuốc
/// 5. <PERSON><PERSON> khi hoàn thành navigate đến CreateMedicationSchedulePage
class AddMedicineToPrescriptionPage extends StatefulWidget {
  final String prescriptionId;

  const AddMedicineToPrescriptionPage({
    super.key,
    required this.prescriptionId,
  });

  @override
  State<AddMedicineToPrescriptionPage> createState() =>
      _AddMedicineToPrescriptionPageState();
}

class _AddMedicineToPrescriptionPageState
    extends State<AddMedicineToPrescriptionPage> {
  final _formKey = GlobalKey<FormState>();
  final _searchController = TextEditingController();
  final _instructionsController = TextEditingController();

  Map<String, dynamic>? _selectedMedicine;
  double _doseAmount = 1.0;
  String _doseUnit = 'viên';
  int _quantity = 30;
  String _route = 'Uống';
  int _durationDays = 7;

  final List<String> _doseUnits = ['viên', 'ml', 'mg', 'g', 'gói', 'ống'];
  final List<String> _routes = [
    'Uống',
    'Tiêm',
    'Bôi',
    'Nhỏ mắt',
    'Nhỏ tai',
    'Xịt mũi'
  ];

  List<Map<String, dynamic>> _searchResults = [];
  List<Map<String, dynamic>> _addedMedicines = []; // Danh sách thuốc đã thêm
  bool _isSearching = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadAvailableMedicines();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thêm thuốc vào đơn'),
        actions: [
          TextButton(
            onPressed: _continueToSchedule,
            child: const Text('Tiếp theo'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress Indicator
              _buildProgressIndicator(),

              SizedBox(height: 24.h),

              // Added Medicines Section
              if (_addedMedicines.isNotEmpty) ...[
                _buildSectionTitle('Thuốc đã thêm (${_addedMedicines.length})'),
                SizedBox(height: 16.h),
                _buildAddedMedicinesList(),
                SizedBox(height: 32.h),
              ],

              // Medicine Selection Section
              _buildSectionTitle('Thêm thuốc mới'),
              SizedBox(height: 16.h),
              _buildMedicineSelectionSection(),

              if (_selectedMedicine != null) ...[
                SizedBox(height: 32.h),

                // Dosage Information Section
                _buildSectionTitle('Thông tin liều lượng'),
                SizedBox(height: 16.h),
                _buildDosageSection(),

                SizedBox(height: 32.h),

                // Usage Information Section
                _buildSectionTitle('Thông tin sử dụng'),
                SizedBox(height: 16.h),
                _buildUsageSection(),

                SizedBox(height: 32.h),

                // Instructions Section
                _buildSectionTitle('Hướng dẫn sử dụng'),
                SizedBox(height: 16.h),
                _buildInstructionsSection(),

                SizedBox(height: 32.h),

                // Action Buttons
                _buildActionButtons(),
              ],

              // Continue Button (always visible if medicines added)
              if (_addedMedicines.isNotEmpty) ...[
                SizedBox(height: 32.h),
                _buildContinueButton(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.medication,
                color: Theme.of(context).colorScheme.primary,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Bước 2/3: Thêm thuốc vào đơn',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          LinearProgressIndicator(
            value: 2 / 3,
            backgroundColor:
                Theme.of(context).colorScheme.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Đơn thuốc #${widget.prescriptionId}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
    );
  }

  Widget _buildMedicineSelectionSection() {
    return Column(
      children: [
        // Search Bar
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Tìm kiếm thuốc trong tủ thuốc...',
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _searchResults.clear();
                        _selectedMedicine = null;
                      });
                    },
                    icon: const Icon(Icons.clear),
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          onChanged: _searchMedicines,
        ),

        SizedBox(height: 16.h),

        // Selected Medicine Display
        if (_selectedMedicine != null)
          _buildSelectedMedicineCard()
        else if (_searchResults.isNotEmpty)
          _buildSearchResults()
        else if (_searchController.text.isEmpty)
          _buildRecentMedicines(),

        SizedBox(height: 16.h),

        // Add New Medicine Button
        OutlinedButton.icon(
          onPressed: () {
            // TODO: Navigate to add new medicine page
          },
          icon: const Icon(Icons.add),
          label: const Text('Thêm thuốc mới vào tủ thuốc'),
          style: OutlinedButton.styleFrom(
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedMedicineCard() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary,
          width: 2,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.medication,
              color: Theme.of(context).colorScheme.primary,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedMedicine!['name'],
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '${_selectedMedicine!['manufacturer']} • ${_selectedMedicine!['type']}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                      ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Còn lại: ${_selectedMedicine!['quantity']} ${_selectedMedicine!['unit']}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _selectedMedicine = null;
              });
            },
            icon: const Icon(Icons.close),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return Container(
      constraints: BoxConstraints(maxHeight: 300.h),
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: _searchResults.length,
        separatorBuilder: (context, index) => SizedBox(height: 8.h),
        itemBuilder: (context, index) {
          final medicine = _searchResults[index];
          return _buildMedicineItem(medicine);
        },
      ),
    );
  }

  Widget _buildRecentMedicines() {
    // Mock recent medicines
    final recentMedicines = _getMockMedicines().take(3).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thuốc gần đây',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        SizedBox(height: 12.h),
        ...recentMedicines
            .map((medicine) => Padding(
                  padding: EdgeInsets.only(bottom: 8.h),
                  child: _buildMedicineItem(medicine),
                ))
            .toList(),
      ],
    );
  }

  Widget _buildMedicineItem(Map<String, dynamic> medicine) {
    return InkWell(
      onTap: () {
        setState(() {
          _selectedMedicine = medicine;
          _searchController.clear();
          _searchResults.clear();
        });
      },
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: _getCategoryColor(medicine['type']).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.medication,
                color: _getCategoryColor(medicine['type']),
                size: 20.sp,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    medicine['name'],
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                  Text(
                    '${medicine['manufacturer']} • Còn ${medicine['quantity']} ${medicine['unit']}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.6),
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDosageSection() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              flex: 2,
              child: TextFormField(
                initialValue: _doseAmount.toString(),
                decoration: InputDecoration(
                  labelText: 'Liều lượng *',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  _doseAmount = double.tryParse(value) ?? 1.0;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Bắt buộc';
                  }
                  if (double.tryParse(value) == null ||
                      double.parse(value) <= 0) {
                    return 'Phải > 0';
                  }
                  return null;
                },
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _doseUnit,
                decoration: InputDecoration(
                  labelText: 'Đơn vị',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                items: _doseUnits.map((unit) {
                  return DropdownMenuItem(
                    value: unit,
                    child: Text(unit),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _doseUnit = value!;
                  });
                },
              ),
            ),
          ],
        ),

        SizedBox(height: 16.h),

        // Route
        DropdownButtonFormField<String>(
          value: _route,
          decoration: InputDecoration(
            labelText: 'Đường dùng *',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          items: _routes.map((route) {
            return DropdownMenuItem(
              value: route,
              child: Text(route),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _route = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildUsageSection() {
    return Column(
      children: [
        // Quantity
        TextFormField(
          initialValue: _quantity.toString(),
          decoration: InputDecoration(
            labelText: 'Số lượng cần dùng *',
            hintText: 'Tổng số lượng thuốc cần dùng',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _quantity = int.tryParse(value) ?? 30;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Vui lòng nhập số lượng';
            }
            if (int.tryParse(value) == null || int.parse(value) <= 0) {
              return 'Số lượng phải lớn hơn 0';
            }
            return null;
          },
        ),

        SizedBox(height: 16.h),

        // Duration
        TextFormField(
          initialValue: _durationDays.toString(),
          decoration: InputDecoration(
            labelText: 'Thời gian điều trị (ngày) *',
            hintText: 'Số ngày sử dụng thuốc',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _durationDays = int.tryParse(value) ?? 7;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Vui lòng nhập thời gian điều trị';
            }
            if (int.tryParse(value) == null || int.parse(value) <= 0) {
              return 'Thời gian phải lớn hơn 0';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildInstructionsSection() {
    return TextFormField(
      controller: _instructionsController,
      maxLines: 4,
      decoration: InputDecoration(
        labelText: 'Hướng dẫn sử dụng',
        hintText: 'Ví dụ: Uống sau ăn, tránh ánh nắng...',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        alignLabelWithHint: true,
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Continue Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _continueToSchedule,
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Tiếp theo'),
                SizedBox(width: 8.w),
                const Icon(Icons.arrow_forward, size: 20),
              ],
            ),
          ),
        ),

        SizedBox(height: 12.h),

        // Save and Stay Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: _saveMedicineToPrescrition,
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.save, size: 20),
                SizedBox(width: 8.w),
                const Text('Lưu và tiếp tục thêm thuốc'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _loadAvailableMedicines() {
    // TODO: Load medicines from API
    setState(() {
      _searchResults = _getMockMedicines();
    });
  }

  void _searchMedicines(String query) {
    if (query.isEmpty) {
      setState(() {
        _searchResults.clear();
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    // TODO: Implement actual search API call
    // Mock search
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          _searchResults = _getMockMedicines()
              .where((medicine) =>
                  medicine['name']
                      .toLowerCase()
                      .contains(query.toLowerCase()) ||
                  medicine['manufacturer']
                      .toLowerCase()
                      .contains(query.toLowerCase()))
              .toList();
          _isSearching = false;
        });
      }
    });
  }

  void _saveMedicineToPrescrition() {
    if (_selectedMedicine == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng chọn thuốc'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_formKey.currentState!.validate()) {
      // Check if medicine already added
      final isAlreadyAdded =
          _addedMedicines.any((m) => m['id'] == _selectedMedicine!['id']);
      if (isAlreadyAdded) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Thuốc này đã được thêm vào đơn'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Add medicine to list
      final medicineData = {
        'id': _selectedMedicine!['id'],
        'name': _selectedMedicine!['name'],
        'dose_amount': _doseAmount,
        'dose_unit': _doseUnit,
        'quantity': _quantity,
        'route': _route,
        'duration_days': _durationDays,
        'instructions': _instructionsController.text,
        'prescription_id': widget.prescriptionId,
      };

      setState(() {
        _addedMedicines.add(medicineData);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Đã thêm ${_selectedMedicine!['name']} vào đơn thuốc'),
          backgroundColor: Colors.green,
        ),
      );

      // Reset form to add more medicines
      setState(() {
        _selectedMedicine = null;
        _doseAmount = 1.0;
        _doseUnit = 'viên';
        _quantity = 30;
        _route = 'Uống';
        _durationDays = 7;
        _instructionsController.clear();
        _searchController.clear();
        _searchResults.clear();
      });
    }
  }

  Widget _buildAddedMedicinesList() {
    return Column(
      children: _addedMedicines.map((medicine) {
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              // Medicine Icon
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.medication,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24.sp,
                ),
              ),

              SizedBox(width: 12.w),

              // Medicine Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      medicine['name'] ?? 'Tên thuốc',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '${medicine['dose_amount']} ${medicine['dose_unit']} - ${medicine['route']}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.7),
                          ),
                    ),
                    if (medicine['instructions'] != null &&
                        medicine['instructions'].isNotEmpty) ...[
                      SizedBox(height: 4.h),
                      Text(
                        medicine['instructions'],
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withOpacity(0.6),
                            ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // Remove Button
              IconButton(
                onPressed: () => _removeMedicine(medicine),
                icon: Icon(
                  Icons.remove_circle,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildContinueButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _continueToSchedule,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: _isLoading
            ? SizedBox(
                width: 20.w,
                height: 20.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Tiếp theo: Tạo lịch uống thuốc'),
                  SizedBox(width: 8.w),
                  const Icon(Icons.arrow_forward, size: 20),
                ],
              ),
      ),
    );
  }

  void _removeMedicine(Map<String, dynamic> medicine) {
    setState(() {
      _addedMedicines.removeWhere((m) => m['id'] == medicine['id']);
    });
  }

  void _continueToSchedule() {
    if (_addedMedicines.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng thêm ít nhất một thuốc trước khi tiếp tục'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // TODO: Save all added medicines to API
    // For now, simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Navigate to create medication schedule
        // Navigate to create medication schedule
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => CreateMedicationSchedulePageNew(
              prescriptionId: widget.prescriptionId,
              addedMedicines: _addedMedicines,
            ),
          ),
        );
      }
    });
  }

  List<Map<String, dynamic>> _getMockMedicines() {
    return [
      {
        'id': '1',
        'name': 'Paracetamol 500mg',
        'manufacturer': 'Traphaco',
        'type': 'Giảm đau',
        'quantity': 50,
        'unit': 'viên',
      },
      {
        'id': '2',
        'name': 'Amoxicillin 250mg',
        'manufacturer': 'Imexpharm',
        'type': 'Kháng sinh',
        'quantity': 30,
        'unit': 'viên',
      },
      {
        'id': '3',
        'name': 'Vitamin C 1000mg',
        'manufacturer': 'DHG Pharma',
        'type': 'Vitamin',
        'quantity': 100,
        'unit': 'viên',
      },
      {
        'id': '4',
        'name': 'Omeprazole 20mg',
        'manufacturer': 'Stada',
        'type': 'Tiêu hóa',
        'quantity': 28,
        'unit': 'viên',
      },
    ];
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Kháng sinh':
        return Colors.red;
      case 'Giảm đau':
        return Colors.blue;
      case 'Vitamin':
        return Colors.orange;
      case 'Tiêu hóa':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _instructionsController.dispose();
    super.dispose();
  }
}
