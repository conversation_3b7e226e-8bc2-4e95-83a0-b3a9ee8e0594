import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../generated/l10n.dart';
import 'prescription_detail_page.dart';

/// Trang tạo lịch uống thuốc
///
/// Bước 3/3 trong flow tạo đơn thuốc:
/// - Thi<PERSON>t lập thời gian uống thuốc trong ngày
/// - Chọn ngày trong tuần (nếu cần)
/// - Cài đặt nhắc nhở
/// - <PERSON><PERSON>n thành tạo đơn thuốc
class CreateMedicationSchedulePage extends StatefulWidget {
  final String prescriptionId;
  final String prescriptionItemId;
  final Map<String, dynamic> medicineInfo;

  const CreateMedicationSchedulePage({
    super.key,
    required this.prescriptionId,
    required this.prescriptionItemId,
    required this.medicineInfo,
  });

  @override
  State<CreateMedicationSchedulePage> createState() =>
      _CreateMedicationSchedulePageState();
}

class _CreateMedicationSchedulePageState
    extends State<CreateMedicationSchedulePage> {
  final _formKey = GlobalKey<FormState>();

  List<TimeOfDay> _scheduleTimes = [const TimeOfDay(hour: 8, minute: 0)];
  List<bool> _selectedDays = List.filled(7, true); // Mon-Sun
  bool _enableReminder = true;
  int _reminderMinutesBefore = 5;
  String _frequency = 'Hàng ngày';

  final List<String> _frequencies = [
    'Hàng ngày',
    'Theo ngày trong tuần',
    'Cách ngày',
    'Khi cần thiết',
  ];

  final List<String> _dayNames = [
    'Thứ 2',
    'Thứ 3',
    'Thứ 4',
    'Thứ 5',
    'Thứ 6',
    'Thứ 7',
    'Chủ nhật'
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tạo lịch uống thuốc'),
        actions: [
          TextButton(
            onPressed: _saveSchedule,
            child: const Text('Hoàn thành'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress Indicator
              _buildProgressIndicator(),

              SizedBox(height: 24.h),

              // Medicine Info Summary
              _buildMedicineInfoCard(),

              SizedBox(height: 24.h),

              // Frequency Section
              _buildSectionTitle('Tần suất sử dụng'),
              SizedBox(height: 16.h),
              _buildFrequencySection(),

              SizedBox(height: 32.h),

              // Schedule Times Section
              _buildSectionTitle('Thời gian uống thuốc'),
              SizedBox(height: 16.h),
              _buildScheduleTimesSection(),

              if (_frequency == 'Theo ngày trong tuần') ...[
                SizedBox(height: 32.h),
                _buildSectionTitle('Ngày trong tuần'),
                SizedBox(height: 16.h),
                _buildDaysOfWeekSection(),
              ],

              SizedBox(height: 32.h),

              // Reminder Section
              _buildSectionTitle('Cài đặt nhắc nhở'),
              SizedBox(height: 16.h),
              _buildReminderSection(),

              SizedBox(height: 32.h),

              // Action Buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.schedule,
                color: Theme.of(context).colorScheme.primary,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Bước 3/3: Tạo lịch uống thuốc',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          LinearProgressIndicator(
            value: 1.0,
            backgroundColor:
                Theme.of(context).colorScheme.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Hoàn thành để tạo đơn thuốc và bắt đầu nhận nhắc nhở',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildMedicineInfoCard() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.medication,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24.sp,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.medicineInfo['name'] ?? 'Thuốc',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Liều: ${widget.medicineInfo['dose_amount']} ${widget.medicineInfo['dose_unit']}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.6),
                          ),
                    ),
                    Text(
                      'Đơn thuốc #${widget.prescriptionId}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
    );
  }

  Widget _buildFrequencySection() {
    return DropdownButtonFormField<String>(
      value: _frequency,
      decoration: InputDecoration(
        labelText: 'Tần suất sử dụng',
        prefixIcon: const Icon(Icons.repeat),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
      items: _frequencies.map((frequency) {
        return DropdownMenuItem(
          value: frequency,
          child: Text(frequency),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _frequency = value!;
          if (_frequency == 'Hàng ngày') {
            _selectedDays = List.filled(7, true);
          } else if (_frequency == 'Theo ngày trong tuần') {
            _selectedDays = List.filled(7, false);
          }
        });
      },
    );
  }

  Widget _buildScheduleTimesSection() {
    return Column(
      children: [
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _scheduleTimes.length,
          separatorBuilder: (context, index) => SizedBox(height: 12.h),
          itemBuilder: (context, index) => _buildTimeSelector(index),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _addTimeSlot,
                icon: const Icon(Icons.add),
                label: const Text('Thêm thời gian'),
              ),
            ),
            if (_scheduleTimes.length > 1) ...[
              SizedBox(width: 12.w),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _removeLastTimeSlot,
                  icon: const Icon(Icons.remove),
                  label: const Text('Bớt thời gian'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildTimeSelector(int index) {
    final time = _scheduleTimes[index];

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(width: 12.w),
          Text(
            'Lần ${index + 1}:',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: InkWell(
              onTap: () => _selectTime(index),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  time.format(context),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDaysOfWeekSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn ngày trong tuần:',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
          SizedBox(height: 12.h),
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: List.generate(7, (index) {
              return FilterChip(
                label: Text(_dayNames[index]),
                selected: _selectedDays[index],
                onSelected: (selected) {
                  setState(() {
                    _selectedDays[index] = selected;
                  });
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildReminderSection() {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('Bật nhắc nhở'),
          subtitle: const Text('Nhận thông báo khi đến giờ uống thuốc'),
          value: _enableReminder,
          onChanged: (value) {
            setState(() {
              _enableReminder = value;
            });
          },
          secondary: const Icon(Icons.notifications),
        ),
        if (_enableReminder) ...[
          SizedBox(height: 16.h),
          DropdownButtonFormField<int>(
            value: _reminderMinutesBefore,
            decoration: InputDecoration(
              labelText: 'Nhắc nhở trước',
              prefixIcon: const Icon(Icons.timer),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            items: [1, 5, 10, 15, 30].map((minutes) {
              return DropdownMenuItem(
                value: minutes,
                child: Text('$minutes phút trước'),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _reminderMinutesBefore = value!;
              });
            },
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Complete Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _saveSchedule,
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.check_circle, size: 20),
                SizedBox(width: 8.w),
                const Text('Hoàn thành tạo đơn thuốc'),
              ],
            ),
          ),
        ),

        SizedBox(height: 12.h),

        // Save Draft Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: _saveDraft,
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: const Text('Lưu nháp'),
          ),
        ),
      ],
    );
  }

  void _addTimeSlot() {
    if (_scheduleTimes.length < 6) {
      setState(() {
        _scheduleTimes.add(const TimeOfDay(hour: 12, minute: 0));
      });
    }
  }

  void _removeLastTimeSlot() {
    if (_scheduleTimes.length > 1) {
      setState(() {
        _scheduleTimes.removeLast();
      });
    }
  }

  Future<void> _selectTime(int index) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _scheduleTimes[index],
    );

    if (picked != null) {
      setState(() {
        _scheduleTimes[index] = picked;
      });
    }
  }

  void _saveSchedule() {
    if (_frequency == 'Theo ngày trong tuần' && !_selectedDays.contains(true)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng chọn ít nhất một ngày trong tuần'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // TODO: Save medication schedule using BLoC

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Đã tạo đơn thuốc và lịch uống thuốc thành công!'),
        backgroundColor: Colors.green,
      ),
    );

    // Navigate to prescription detail page
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => PrescriptionDetailPage(
          prescriptionId: widget.prescriptionId,
        ),
      ),
      (route) => route.isFirst, // Keep only the first route (HomePage)
    );
  }

  void _saveDraft() {
    // TODO: Save as draft

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Đã lưu nháp thành công!'),
        backgroundColor: Colors.blue,
      ),
    );

    Navigator.of(context).pop();
  }
}
