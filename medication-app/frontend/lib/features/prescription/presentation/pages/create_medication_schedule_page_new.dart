import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Trang tạo lịch uống thuốc cho các thuốc đã thêm vào đơn
///
/// Bước 3/3 trong flow tạo đơn thuốc:
/// 1. <PERSON><PERSON><PERSON> thị danh sách thuốc đã thêm
/// 2. <PERSON> phép tạo lịch uống thuốc cho từng thuốc
/// 3. Lưu tất cả schedules và hoàn thành flow
class CreateMedicationSchedulePageNew extends StatefulWidget {
  final String prescriptionId;
  final List<Map<String, dynamic>> addedMedicines;

  const CreateMedicationSchedulePageNew({
    super.key,
    required this.prescriptionId,
    required this.addedMedicines,
  });

  @override
  State<CreateMedicationSchedulePageNew> createState() =>
      _CreateMedicationSchedulePageNewState();
}

class _CreateMedicationSchedulePageNewState
    extends State<CreateMedicationSchedulePageNew> {
  Map<String, Map<String, dynamic>> _schedules = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeSchedules();
  }

  void _initializeSchedules() {
    // Tạo schedule mặc định cho mỗi thuốc
    for (final medicine in widget.addedMedicines) {
      _schedules[medicine['id']] = {
        'medicine_id': medicine['id'],
        'medicine_name': medicine['name'],
        'frequency': 'Hàng ngày',
        'times_per_day': 2,
        'times': ['08:00', '20:00'],
        'selected_days': [true, true, true, true, true, true, true], // All days
        'dose_amount': medicine['dose_amount'] ?? 1.0,
        'dose_unit': medicine['dose_unit'] ?? 'viên',
      };
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tạo lịch uống thuốc'),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Header
          _buildHeader(),

          // Scrollable Content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Medicines List with Schedules
                  _buildMedicinesWithSchedules(),
                ],
              ),
            ),
          ),

          // Fixed Bottom Button
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(16.r),
          bottomRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.schedule,
                color: Theme.of(context).colorScheme.primary,
                size: 24.sp,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  'Bước 3/3: Tạo lịch uống thuốc',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          LinearProgressIndicator(
            value: 1.0, // 100% completed
            backgroundColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Thiết lập thời gian uống thuốc cho ${widget.addedMedicines.length} loại thuốc',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildMedicinesWithSchedules() {
    return Column(
      children: widget.addedMedicines.map((medicine) {
        final schedule = _schedules[medicine['id']]!;
        
        return Container(
          margin: EdgeInsets.only(bottom: 16.h),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Medicine Header
              _buildMedicineHeader(medicine),
              
              SizedBox(height: 16.h),
              
              // Schedule Settings
              _buildScheduleSettings(medicine['id'], schedule),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMedicineHeader(Map<String, dynamic> medicine) {
    return Row(
      children: [
        Container(
          width: 48.w,
          height: 48.w,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            Icons.medication,
            color: Theme.of(context).colorScheme.primary,
            size: 24.sp,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                medicine['name'] ?? 'Tên thuốc',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                '${medicine['dose_amount']} ${medicine['dose_unit']} - ${medicine['route']}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => _editSchedule(medicine['id']),
          icon: const Icon(Icons.edit),
        ),
      ],
    );
  }

  Widget _buildScheduleSettings(String medicineId, Map<String, dynamic> schedule) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Frequency
        Row(
          children: [
            Icon(
              Icons.repeat,
              size: 16.sp,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            SizedBox(width: 8.w),
            Text(
              'Tần suất: ${schedule['frequency']}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
        
        SizedBox(height: 8.h),
        
        // Times
        Row(
          children: [
            Icon(
              Icons.access_time,
              size: 16.sp,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                'Giờ uống: ${(schedule['times'] as List<String>).join(', ')}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
        
        SizedBox(height: 8.h),
        
        // Days (if not daily)
        if (schedule['frequency'] == 'Theo ngày trong tuần') ...[
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16.sp,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  'Ngày: ${_getSelectedDaysText(schedule['selected_days'])}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _completeScheduleCreation,
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    width: 20.w,
                    height: 20.h,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('Hoàn thành tạo đơn thuốc'),
                      SizedBox(width: 8.w),
                      const Icon(Icons.check, size: 20),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  void _editSchedule(String medicineId) {
    // TODO: Show dialog to edit schedule
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Chức năng chỉnh sửa lịch sẽ được phát triển sau'),
      ),
    );
  }

  void _completeScheduleCreation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Save all schedules to API
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã tạo đơn thuốc và lịch uống thuốc thành công!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to prescription list
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tạo lịch: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getSelectedDaysText(List<bool> selectedDays) {
    final dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
    final selectedDayNames = <String>[];
    
    for (int i = 0; i < selectedDays.length; i++) {
      if (selectedDays[i]) {
        selectedDayNames.add(dayNames[i]);
      }
    }
    
    return selectedDayNames.join(', ');
  }
}
