import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/create_prescription_step_bloc.dart';
import '../bloc/create_prescription_step_event.dart';
import '../bloc/create_prescription_step_state.dart';
import '../widgets/step_indicator.dart';
import '../widgets/navigation_buttons.dart';
import '../widgets/steps/basic_info_step.dart';
import '../widgets/steps/add_medicine_step.dart';
import '../widgets/steps/create_schedule_step.dart';

/// Trang tạo đơn thuốc mới với PageView và BLoC
///
/// Flow tạo đơn thuốc:
/// 1. Thông tin cơ bản đơn thuốc
/// 2. Thêm thuốc vào đơn thuốc
/// 3. Tạo lịch uống thuốc
class CreatePrescriptionPage extends StatefulWidget {
  const CreatePrescriptionPage({super.key});

  @override
  State<CreatePrescriptionPage> createState() => _CreatePrescriptionPageState();
}

class _CreatePrescriptionPageState extends State<CreatePrescriptionPage> {
  late PageController _pageController;
  late CreatePrescriptionStepBloc _stepBloc;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _stepBloc = CreatePrescriptionStepBloc();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _stepBloc,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Tạo đơn thuốc mới'),
          elevation: 0,
        ),
        body: BlocConsumer<CreatePrescriptionStepBloc,
            CreatePrescriptionStepState>(
          listener: (context, state) {
            // Sync PageView with BLoC state
            if (_pageController.hasClients) {
              _pageController.animateToPage(
                state.currentStepIndex,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            }
          },
          builder: (context, state) {
            return Column(
              children: [
                // Fixed Step Indicator
                Container(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  child: StepIndicator(
                    currentStep: state.currentStepIndex,
                    totalSteps: CreatePrescriptionStepState.totalSteps,
                    completedSteps: state.completedSteps,
                    stepTitle: state.stepTitle,
                    stepDescription: state.stepDescription,
                  ),
                ),

                // Scrollable Content
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    physics:
                        const NeverScrollableScrollPhysics(), // Disable swipe
                    children: [
                      // Step 1: Basic Info
                      BasicInfoStep(
                        initialData: state.stepData,
                        onDataChanged: (data) {
                          _stepBloc.add(UpdateStepDataRequested(data));
                        },
                      ),

                      // Step 2: Add Medicine
                      AddMedicineStep(
                        initialData: state.stepData,
                        onDataChanged: (data) {
                          _stepBloc.add(UpdateStepDataRequested(data));
                        },
                      ),

                      // Step 3: Create Schedule
                      CreateScheduleStep(
                        initialData: state.stepData,
                        onDataChanged: (data) {
                          _stepBloc.add(UpdateStepDataRequested(data));
                        },
                      ),
                    ],
                  ),
                ),

                // Fixed Navigation Buttons
                NavigationButtons(
                  canGoNext: state.canGoNext,
                  canGoPrevious: state.canGoPrevious,
                  isLastStep: state.isLastStep,
                  isLoading: state.isLoading,
                  onNextPressed: () {
                    if (state.isLastStep) {
                      _completePrescription(state);
                    } else {
                      _stepBloc.add(const NextStepRequested());
                    }
                  },
                  onPreviousPressed: () {
                    _stepBloc.add(const PreviousStepRequested());
                  },
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// Hoàn thành tạo đơn thuốc
  void _completePrescription(CreatePrescriptionStepState state) async {
    try {
      // TODO: Implement prescription creation logic using BLoC
      // For now, show success message and navigate back

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Đã tạo đơn thuốc thành công!'),
          backgroundColor: Colors.green,
        ),
      );

      // Navigate back to prescription list
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi khi tạo đơn thuốc: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _stepBloc.close();
    super.dispose();
  }
}
