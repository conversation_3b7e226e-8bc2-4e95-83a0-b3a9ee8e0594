import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'add_medicine_to_prescription_page.dart';

/// Trang tạo đơn thuốc mới - chỉ tạo prescription cơ bản
///
/// <PERSON>u khi tạo thành công sẽ navigate đến AddMedicineToPrescriptionPage
class CreatePrescriptionPage extends StatefulWidget {
  const CreatePrescriptionPage({super.key});

  @override
  State<CreatePrescriptionPage> createState() => _CreatePrescriptionPageState();
}

class _CreatePrescriptionPageState extends State<CreatePrescriptionPage> {
  final _formKey = GlobalKey<FormState>();
  final _doctorNameController = TextEditingController();
  final _diagnosisController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _prescriptionDate = DateTime.now();
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  String _selectedPatient = 'Tôi';

  final List<String> _patients = [
    'Tôi',
    'Vợ/Chồng',
    'Con trai',
    'Con gái',
    'B<PERSON>',
    'Mẹ',
  ];

  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tạo đơn thuốc mới'),
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),

              SizedBox(height: 24.h),

              // Basic Information Section
              _buildSectionTitle('Thông tin cơ bản'),
              SizedBox(height: 16.h),
              _buildBasicInfoSection(),

              SizedBox(height: 32.h),

              // Doctor Information Section
              _buildSectionTitle('Thông tin bác sĩ'),
              SizedBox(height: 16.h),
              _buildDoctorInfoSection(),

              SizedBox(height: 32.h),

              // Medical Information Section
              _buildSectionTitle('Thông tin y tế'),
              SizedBox(height: 16.h),
              _buildMedicalInfoSection(),

              SizedBox(height: 32.h),

              // Timeline Section
              _buildSectionTitle('Thời gian điều trị'),
              SizedBox(height: 16.h),
              _buildTimelineSection(),

              SizedBox(height: 32.h),

              // Notes Section
              _buildSectionTitle('Ghi chú'),
              SizedBox(height: 16.h),
              _buildNotesSection(),

              SizedBox(height: 32.h),

              // Action Buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.receipt_long,
                color: Theme.of(context).colorScheme.primary,
                size: 24.sp,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  'Tạo đơn thuốc mới',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            'Nhập thông tin cơ bản của đơn thuốc. Sau khi tạo xong, bạn sẽ có thể thêm thuốc và tạo lịch uống thuốc.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      children: [
        // Patient Selection
        DropdownButtonFormField<String>(
          value: _selectedPatient,
          decoration: InputDecoration(
            labelText: 'Bệnh nhân *',
            prefixIcon: const Icon(Icons.person),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          items: _patients.map((patient) {
            return DropdownMenuItem(
              value: patient,
              child: Text(patient),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedPatient = value!;
            });
          },
        ),

        SizedBox(height: 16.h),

        // Prescription Date
        InkWell(
          onTap: () => _selectPrescriptionDate(),
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'Ngày kê đơn *',
              prefixIcon: const Icon(Icons.calendar_today),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              '${_prescriptionDate.day}/${_prescriptionDate.month}/${_prescriptionDate.year}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDoctorInfoSection() {
    return TextFormField(
      controller: _doctorNameController,
      decoration: InputDecoration(
        labelText: 'Tên bác sĩ *',
        hintText: 'Ví dụ: BS. Nguyễn Văn A',
        prefixIcon: const Icon(Icons.local_hospital),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Vui lòng nhập tên bác sĩ';
        }
        return null;
      },
    );
  }

  Widget _buildMedicalInfoSection() {
    return TextFormField(
      controller: _diagnosisController,
      maxLines: 3,
      decoration: InputDecoration(
        labelText: 'Chẩn đoán *',
        hintText: 'Ví dụ: Viêm họng cấp, sốt',
        prefixIcon: const Icon(Icons.medical_services),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        alignLabelWithHint: true,
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Vui lòng nhập chẩn đoán';
        }
        return null;
      },
    );
  }

  Widget _buildTimelineSection() {
    return Column(
      children: [
        // Start Date
        InkWell(
          onTap: () => _selectStartDate(),
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'Ngày bắt đầu điều trị *',
              prefixIcon: const Icon(Icons.play_arrow),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              '${_startDate.day}/${_startDate.month}/${_startDate.year}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ),
        ),

        SizedBox(height: 16.h),

        // End Date (Optional)
        InkWell(
          onTap: () => _selectEndDate(),
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'Ngày kết thúc điều trị (tùy chọn)',
              prefixIcon: const Icon(Icons.stop),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              suffixIcon: _endDate != null
                  ? IconButton(
                      onPressed: () {
                        setState(() {
                          _endDate = null;
                        });
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
            ),
            child: Text(
              _endDate != null
                  ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                  : 'Chọn ngày kết thúc',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: _endDate != null
                        ? null
                        : Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                  ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return TextFormField(
      controller: _notesController,
      maxLines: 4,
      decoration: InputDecoration(
        labelText: 'Ghi chú (tùy chọn)',
        hintText: 'Thêm ghi chú về đơn thuốc, lưu ý đặc biệt...',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        alignLabelWithHint: true,
      ),
    );
  }

  Widget _buildActionButtons() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _createPrescription,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: _isLoading
            ? SizedBox(
                width: 20.w,
                height: 20.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Tạo đơn thuốc'),
                  SizedBox(width: 8.w),
                  const Icon(Icons.arrow_forward, size: 20),
                ],
              ),
      ),
    );
  }

  Future<void> _selectPrescriptionDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _prescriptionDate,
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        _prescriptionDate = picked;
      });
    }
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now().subtract(const Duration(days: 7)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked;
        // Reset end date if it's before start date
        if (_endDate != null && _endDate!.isBefore(picked)) {
          _endDate = null;
        }
      });
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate.add(const Duration(days: 7)),
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  Future<void> _createPrescription() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Call API to create prescription
      // For now, simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock prescription ID
      final prescriptionId = DateTime.now().millisecondsSinceEpoch.toString();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã tạo đơn thuốc thành công!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to add medicine page
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => AddMedicineToPrescriptionPage(
              prescriptionId: prescriptionId,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tạo đơn thuốc: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _doctorNameController.dispose();
    _diagnosisController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
