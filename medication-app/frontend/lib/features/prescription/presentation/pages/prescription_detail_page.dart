import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../app/routes/routes.dart';
import '../../../../generated/l10n.dart';

/// Trang chi tiết đơn thuốc
/// 
/// Hi<PERSON>n thị:
/// - Thông tin đơn thuốc (b<PERSON>c s<PERSON>, ng<PERSON><PERSON> kê, ch<PERSON>n đo<PERSON>)
/// - <PERSON><PERSON> sách thuốc trong đơn
/// - Lịch uống thuốc
/// - Tiến độ điều trị
/// - Actions (thêm thuốc, chỉnh sửa, ho<PERSON>n thành)
class PrescriptionDetailPage extends StatefulWidget {
  final String prescriptionId;
  
  const PrescriptionDetailPage({
    super.key,
    required this.prescriptionId,
  });

  @override
  State<PrescriptionDetailPage> createState() => _PrescriptionDetailPageState();
}

class _PrescriptionDetailPageState extends State<PrescriptionDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Map<String, dynamic>? _prescription;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadPrescriptionDetail();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: Text('Đơn thuốc #${widget.prescriptionId}'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Đơn thuốc #${widget.prescriptionId}'),
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              if (_prescription!['status'] == 'active') ...[
                const PopupMenuItem(
                  value: 'add_medicine',
                  child: ListTile(
                    leading: Icon(Icons.add_circle),
                    title: Text('Thêm thuốc'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('Chỉnh sửa'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'complete',
                  child: ListTile(
                    leading: Icon(Icons.check_circle, color: Colors.green),
                    title: Text('Đánh dấu hoàn thành'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('Chia sẻ'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Thông tin', icon: Icon(Icons.info_outline)),
            Tab(text: 'Thuốc', icon: Icon(Icons.medication)),
            Tab(text: 'Lịch trình', icon: Icon(Icons.schedule)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildInfoTab(),
          _buildMedicinesTab(),
          _buildScheduleTab(),
        ],
      ),
      floatingActionButton: _prescription!['status'] == 'active'
          ? FloatingActionButton.extended(
              onPressed: () {
                // TODO: Navigate to add medicine page
              },
              icon: const Icon(Icons.add),
              label: const Text('Thêm thuốc'),
            )
          : null,
    );
  }

  Widget _buildInfoTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status Card
          _buildStatusCard(),
          
          SizedBox(height: 24.h),
          
          // Basic Info
          _buildInfoSection('Thông tin cơ bản', [
            _buildInfoRow('Bệnh nhân', _prescription!['patient_name']),
            _buildInfoRow('Ngày kê đơn', _prescription!['prescribed_date']),
            _buildInfoRow('Ngày bắt đầu', _prescription!['start_date']),
            if (_prescription!['end_date'] != null)
              _buildInfoRow('Ngày kết thúc', _prescription!['end_date']),
          ]),
          
          SizedBox(height: 24.h),
          
          // Doctor Info
          _buildInfoSection('Thông tin bác sĩ', [
            _buildInfoRow('Bác sĩ', _prescription!['doctor_name']),
            if (_prescription!['hospital'] != null)
              _buildInfoRow('Bệnh viện', _prescription!['hospital']),
          ]),
          
          SizedBox(height: 24.h),
          
          // Medical Info
          _buildInfoSection('Thông tin y tế', [
            _buildInfoRow('Chẩn đoán', _prescription!['diagnosis'], isMultiline: true),
            if (_prescription!['notes'] != null && _prescription!['notes'].isNotEmpty)
              _buildInfoRow('Ghi chú', _prescription!['notes'], isMultiline: true),
          ]),
          
          SizedBox(height: 24.h),
          
          // Progress
          _buildProgressSection(),
        ],
      ),
    );
  }

  Widget _buildMedicinesTab() {
    final medicines = _prescription!['medicines'] as List<Map<String, dynamic>>;
    
    if (medicines.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.medication_outlined,
              size: 64.sp,
              color: Theme.of(context).colorScheme.outline,
            ),
            SizedBox(height: 16.h),
            Text(
              'Chưa có thuốc nào trong đơn',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 8.h),
            Text(
              'Thêm thuốc đầu tiên vào đơn thuốc',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to add medicine
              },
              icon: const Icon(Icons.add),
              label: const Text('Thêm thuốc'),
            ),
          ],
        ),
      );
    }
    
    return ListView.separated(
      padding: EdgeInsets.all(16.w),
      itemCount: medicines.length,
      separatorBuilder: (context, index) => SizedBox(height: 16.h),
      itemBuilder: (context, index) {
        final medicine = medicines[index];
        return _buildMedicineCard(medicine);
      },
    );
  }

  Widget _buildScheduleTab() {
    final schedules = _prescription!['schedules'] as List<Map<String, dynamic>>;
    
    if (schedules.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.schedule,
              size: 64.sp,
              color: Theme.of(context).colorScheme.outline,
            ),
            SizedBox(height: 16.h),
            Text(
              'Chưa có lịch uống thuốc',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 8.h),
            Text(
              'Tạo lịch uống thuốc để nhận nhắc nhở',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      );
    }
    
    return ListView.separated(
      padding: EdgeInsets.all(16.w),
      itemCount: schedules.length,
      separatorBuilder: (context, index) => SizedBox(height: 16.h),
      itemBuilder: (context, index) {
        final schedule = schedules[index];
        return _buildScheduleCard(schedule);
      },
    );
  }

  Widget _buildStatusCard() {
    final status = _prescription!['status'];
    Color statusColor;
    String statusText;
    IconData statusIcon;
    
    switch (status) {
      case 'active':
        statusColor = Colors.green;
        statusText = 'Đang hiệu lực';
        statusIcon = Icons.check_circle;
        break;
      case 'completed':
        statusColor = Colors.blue;
        statusText = 'Đã hoàn thành';
        statusIcon = Icons.task_alt;
        break;
      case 'expired':
        statusColor = Colors.red;
        statusText = 'Đã hết hạn';
        statusIcon = Icons.schedule;
        break;
      default:
        statusColor = Colors.grey;
        statusText = 'Không xác định';
        statusIcon = Icons.help_outline;
    }
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              statusIcon,
              color: statusColor,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  statusText,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Đơn thuốc #${widget.prescriptionId}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isMultiline = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    final totalMedicines = (_prescription!['medicines'] as List).length;
    final completedMedicines = (_prescription!['medicines'] as List)
        .where((m) => m['status'] == 'completed')
        .length;
    final progress = totalMedicines > 0 ? completedMedicines / totalMedicines : 0.0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tiến độ điều trị',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Hoàn thành',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '$completedMedicines/$totalMedicines thuốc',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.h),
              LinearProgressIndicator(
                value: progress,
                backgroundColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                '${(progress * 100).toInt()}% hoàn thành',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMedicineCard(Map<String, dynamic> medicine) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Icons.medication,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        medicine['name'],
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '${medicine['dose_amount']} ${medicine['dose_unit']} • ${medicine['route']}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMedicineAction(value, medicine),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'schedule',
                      child: ListTile(
                        leading: Icon(Icons.schedule),
                        title: Text('Xem lịch uống'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit),
                        title: Text('Chỉnh sửa'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'remove',
                      child: ListTile(
                        leading: Icon(Icons.remove_circle, color: Colors.red),
                        title: Text('Xóa khỏi đơn', style: TextStyle(color: Colors.red)),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            if (medicine['instructions'] != null && medicine['instructions'].isNotEmpty) ...[
              SizedBox(height: 12.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  'Hướng dẫn: ${medicine['instructions']}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleCard(Map<String, dynamic> schedule) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Icons.schedule,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        schedule['medicine_name'],
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Lúc ${schedule['time']} • ${schedule['dose_amount']} ${schedule['dose_unit']}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: schedule['is_active'],
                  onChanged: (value) {
                    // TODO: Toggle schedule active status
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _loadPrescriptionDetail() {
    // TODO: Load from API
    // Mock data for now
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _prescription = _getMockPrescription();
        _isLoading = false;
      });
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'add_medicine':
        // TODO: Navigate to add medicine page
        break;
      case 'edit':
        // TODO: Navigate to edit prescription page
        break;
      case 'complete':
        _markAsCompleted();
        break;
      case 'share':
        // TODO: Share prescription
        break;
    }
  }

  void _handleMedicineAction(String action, Map<String, dynamic> medicine) {
    switch (action) {
      case 'schedule':
        // TODO: Navigate to medicine schedule
        break;
      case 'edit':
        // TODO: Navigate to edit medicine
        break;
      case 'remove':
        _removeMedicine(medicine);
        break;
    }
  }

  void _markAsCompleted() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Đánh dấu hoàn thành'),
        content: Text('Đánh dấu đơn thuốc #${widget.prescriptionId} là đã hoàn thành?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Mark as completed
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Đã đánh dấu hoàn thành!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Hoàn thành'),
          ),
        ],
      ),
    );
  }

  void _removeMedicine(Map<String, dynamic> medicine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xóa thuốc'),
        content: Text('Xóa ${medicine['name']} khỏi đơn thuốc?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Remove medicine
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Đã xóa ${medicine['name']}')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getMockPrescription() {
    return {
      'id': widget.prescriptionId,
      'status': 'active',
      'patient_name': 'Nguyễn Văn A',
      'prescribed_date': '15/03/2024',
      'start_date': '16/03/2024',
      'end_date': '30/03/2024',
      'doctor_name': 'BS. Trần Thị B',
      'hospital': 'Bệnh viện Đa khoa Thành phố',
      'diagnosis': 'Viêm họng cấp, sốt nhẹ. Cần nghỉ ngơi và uống thuốc theo đúng chỉ định.',
      'notes': 'Tái khám sau 1 tuần nếu không có cải thiện',
      'medicines': [
        {
          'id': '1',
          'name': 'Paracetamol 500mg',
          'dose_amount': 1.0,
          'dose_unit': 'viên',
          'route': 'Uống',
          'instructions': 'Uống sau ăn, cách nhau 6 tiếng',
          'status': 'active',
        },
        {
          'id': '2',
          'name': 'Amoxicillin 250mg',
          'dose_amount': 1.0,
          'dose_unit': 'viên',
          'route': 'Uống',
          'instructions': 'Uống trước ăn 30 phút, ngày 2 lần',
          'status': 'active',
        },
      ],
      'schedules': [
        {
          'id': '1',
          'medicine_name': 'Paracetamol 500mg',
          'time': '08:00',
          'dose_amount': 1.0,
          'dose_unit': 'viên',
          'is_active': true,
        },
        {
          'id': '2',
          'medicine_name': 'Paracetamol 500mg',
          'time': '14:00',
          'dose_amount': 1.0,
          'dose_unit': 'viên',
          'is_active': true,
        },
        {
          'id': '3',
          'medicine_name': 'Amoxicillin 250mg',
          'time': '07:30',
          'dose_amount': 1.0,
          'dose_unit': 'viên',
          'is_active': true,
        },
        {
          'id': '4',
          'medicine_name': 'Amoxicillin 250mg',
          'time': '19:30',
          'dose_amount': 1.0,
          'dose_unit': 'viên',
          'is_active': true,
        },
      ],
    };
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
