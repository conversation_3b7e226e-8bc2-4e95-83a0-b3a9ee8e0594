import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../app/routes/routes.dart';
import '../../../../generated/l10n.dart';

/// Trang danh sách đơn thuốc
/// 
/// Hi<PERSON>n thị:
/// - <PERSON><PERSON> s<PERSON>ch đơn thuốc (đang hiệu lực, đ<PERSON> hết hạn)
/// - Thông tin bác sĩ kê đơn
/// - Trạng thái đơn thuốc
/// - Thêm đơn thuốc mới
class PrescriptionListPage extends StatefulWidget {
  const PrescriptionListPage({super.key});

  @override
  State<PrescriptionListPage> createState() => _PrescriptionListPageState();
}

class _PrescriptionListPageState extends State<PrescriptionListPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Đơn thuốc'),
        actions: [
          IconButton(
            onPressed: () => context.goToAddPrescription(),
            icon: const Icon(Icons.add),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Đang hiệu lực'),
            Tab(text: 'Đã hoàn thành'),
            Tab(text: 'Đã hết hạn'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPrescriptionList('active'),
          _buildPrescriptionList('completed'),
          _buildPrescriptionList('expired'),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.goToAddPrescription(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildPrescriptionList(String status) {
    final prescriptions = _getMockPrescriptions(status);
    
    if (prescriptions.isEmpty) {
      return _buildEmptyState(status);
    }
    
    return ListView.separated(
      padding: EdgeInsets.all(16.w),
      itemCount: prescriptions.length,
      separatorBuilder: (context, index) => SizedBox(height: 16.h),
      itemBuilder: (context, index) {
        final prescription = prescriptions[index];
        return _buildPrescriptionCard(prescription);
      },
    );
  }

  Widget _buildPrescriptionCard(Map<String, dynamic> prescription) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: InkWell(
        onTap: () => context.goToPrescriptionDetail(prescription['id']),
        borderRadius: BorderRadius.circular(16.r),
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Đơn thuốc #${prescription['id']}',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          'Ngày kê: ${prescription['prescribedDate']}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: _getStatusColor(prescription['status']).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text(
                      _getStatusText(prescription['status']),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getStatusColor(prescription['status']),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 16.h),
              
              // Doctor Information
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20.r,
                      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      child: Icon(
                        Icons.local_hospital,
                        color: Theme.of(context).colorScheme.primary,
                        size: 20.sp,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            prescription['doctorName'],
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            prescription['hospital'],
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 16.h),
              
              // Medications Summary
              Text(
                'Thuốc trong đơn (${prescription['medications'].length}):',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              
              ...prescription['medications'].take(3).map<Widget>((med) {
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: 2.h),
                  child: Row(
                    children: [
                      Container(
                        width: 6.w,
                        height: 6.w,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          '${med['name']} - ${med['dosage']}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              
              if (prescription['medications'].length > 3) ...[
                SizedBox(height: 4.h),
                Text(
                  '... và ${prescription['medications'].length - 3} thuốc khác',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
              
              SizedBox(height: 16.h),
              
              // Footer with dates and actions
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (prescription['status'] == 'active') ...[
                          Text(
                            'Hết hạn: ${prescription['expiryDate']}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ),
                        ] else if (prescription['status'] == 'completed') ...[
                          Text(
                            'Hoàn thành: ${prescription['completedDate']}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.green,
                            ),
                          ),
                        ] else ...[
                          Text(
                            'Đã hết hạn: ${prescription['expiryDate']}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'view':
                          context.goToPrescriptionDetail(prescription['id']);
                          break;
                        case 'edit':
                          context.goToEditPrescription(prescription['id']);
                          break;
                        case 'add_medications':
                          _showAddMedicationsDialog(prescription);
                          break;
                        case 'mark_completed':
                          _markAsCompleted(prescription);
                          break;
                        case 'delete':
                          _showDeleteDialog(prescription);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'view',
                        child: ListTile(
                          leading: Icon(Icons.visibility),
                          title: Text('Xem chi tiết'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      if (prescription['status'] == 'active') ...[
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('Chỉnh sửa'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'add_medications',
                          child: ListTile(
                            leading: Icon(Icons.add_circle),
                            title: Text('Thêm vào danh sách thuốc'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'mark_completed',
                          child: ListTile(
                            leading: Icon(Icons.check_circle, color: Colors.green),
                            title: Text('Đánh dấu hoàn thành'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title: Text('Xóa', style: TextStyle(color: Colors.red)),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(String status) {
    String title, subtitle;
    IconData icon;
    
    switch (status) {
      case 'active':
        title = 'Chưa có đơn thuốc nào đang hiệu lực';
        subtitle = 'Thêm đơn thuốc mới từ bác sĩ';
        icon = Icons.receipt_long;
        break;
      case 'completed':
        title = 'Chưa có đơn thuốc nào hoàn thành';
        subtitle = 'Các đơn thuốc đã hoàn thành sẽ hiển thị ở đây';
        icon = Icons.check_circle_outline;
        break;
      case 'expired':
        title = 'Chưa có đơn thuốc nào hết hạn';
        subtitle = 'Các đơn thuốc hết hạn sẽ hiển thị ở đây';
        icon = Icons.schedule;
        break;
      default:
        title = 'Không có dữ liệu';
        subtitle = '';
        icon = Icons.inbox;
    }
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64.sp,
            color: Theme.of(context).colorScheme.outline,
          ),
          SizedBox(height: 16.h),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
          if (status == 'active') ...[
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: () => context.goToAddPrescription(),
              icon: const Icon(Icons.add),
              label: const Text('Thêm đơn thuốc'),
            ),
          ],
        ],
      ),
    );
  }

  void _showAddMedicationsDialog(Map<String, dynamic> prescription) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thêm thuốc vào danh sách'),
        content: Text(
          'Thêm tất cả thuốc trong đơn thuốc #${prescription['id']} vào danh sách thuốc của bạn?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Add medications to list
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Đã thêm thuốc vào danh sách!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Thêm'),
          ),
        ],
      ),
    );
  }

  void _markAsCompleted(Map<String, dynamic> prescription) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Đánh dấu hoàn thành'),
        content: Text(
          'Đánh dấu đơn thuốc #${prescription['id']} là đã hoàn thành?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Mark as completed
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Đã đánh dấu hoàn thành!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Hoàn thành'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(Map<String, dynamic> prescription) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text(
          'Bạn có chắc chắn muốn xóa đơn thuốc #${prescription['id']}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Delete prescription
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Đã xóa đơn thuốc #${prescription['id']}'),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getMockPrescriptions(String status) {
    final allPrescriptions = [
      {
        'id': '001',
        'status': 'active',
        'prescribedDate': '15/03/2024',
        'expiryDate': '15/04/2024',
        'doctorName': 'BS. Nguyễn Văn A',
        'hospital': 'Bệnh viện Đa khoa Thành phố',
        'medications': [
          {'name': 'Paracetamol', 'dosage': '500mg x 3 lần/ngày'},
          {'name': 'Amoxicillin', 'dosage': '250mg x 2 lần/ngày'},
          {'name': 'Vitamin C', 'dosage': '1000mg x 1 lần/ngày'},
        ],
      },
      {
        'id': '002',
        'status': 'completed',
        'prescribedDate': '01/03/2024',
        'expiryDate': '01/04/2024',
        'completedDate': '28/03/2024',
        'doctorName': 'BS. Trần Thị B',
        'hospital': 'Phòng khám Đa khoa ABC',
        'medications': [
          {'name': 'Ibuprofen', 'dosage': '400mg x 2 lần/ngày'},
          {'name': 'Omeprazole', 'dosage': '20mg x 1 lần/ngày'},
        ],
      },
      {
        'id': '003',
        'status': 'expired',
        'prescribedDate': '10/02/2024',
        'expiryDate': '10/03/2024',
        'doctorName': 'BS. Lê Văn C',
        'hospital': 'Bệnh viện Quận 1',
        'medications': [
          {'name': 'Aspirin', 'dosage': '100mg x 1 lần/ngày'},
        ],
      },
    ];
    
    return allPrescriptions.where((p) => p['status'] == status).toList();
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return Colors.green;
      case 'completed':
        return Colors.blue;
      case 'expired':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'active':
        return 'Đang hiệu lực';
      case 'completed':
        return 'Đã hoàn thành';
      case 'expired':
        return 'Đã hết hạn';
      default:
        return 'Không xác định';
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
