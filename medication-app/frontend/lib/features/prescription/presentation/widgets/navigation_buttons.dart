import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Widget chứa các nút điều h<PERSON> (Tiếp theo, Quay lại) cho các bước tạo đơn thuốc
class NavigationButtons extends StatelessWidget {
  final bool canGoNext;
  final bool canGoPrevious;
  final bool isLastStep;
  final bool isLoading;
  final VoidCallback? onNextPressed;
  final VoidCallback? onPreviousPressed;
  final String? nextButtonText;
  final String? previousButtonText;

  const NavigationButtons({
    super.key,
    required this.canGoNext,
    required this.canGoPrevious,
    required this.isLastStep,
    this.isLoading = false,
    this.onNextPressed,
    this.onPreviousPressed,
    this.nextButtonText,
    this.previousButtonText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Previous Button
            if (canGoPrevious)
              Expanded(
                child: OutlinedButton(
                  onPressed: isLoading ? null : onPreviousPressed,
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.arrow_back,
                        size: 20.sp,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        previousButtonText ?? 'Quay lại',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ],
                  ),
                ),
              ),

            // Spacing between buttons
            if (canGoPrevious && canGoNext) SizedBox(width: 16.w),

            // Next Button
            if (canGoNext)
              Expanded(
                child: ElevatedButton(
                  onPressed: isLoading ? null : onNextPressed,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: isLoading
                      ? SizedBox(
                          width: 20.w,
                          height: 20.h,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).colorScheme.onPrimary,
                            ),
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              _getNextButtonText(),
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Theme.of(context).colorScheme.onPrimary,
                                  ),
                            ),
                            SizedBox(width: 8.w),
                            Icon(
                              isLastStep ? Icons.check : Icons.arrow_forward,
                              size: 20.sp,
                              color: Theme.of(context).colorScheme.onPrimary,
                            ),
                          ],
                        ),
                ),
              ),

            // If only previous button is available, center it
            if (!canGoNext && canGoPrevious)
              const Expanded(child: SizedBox()),
          ],
        ),
      ),
    );
  }

  String _getNextButtonText() {
    if (nextButtonText != null) {
      return nextButtonText!;
    }
    
    if (isLastStep) {
      return 'Hoàn thành';
    }
    
    return 'Tiếp theo';
  }
}

/// Widget đơn giản chỉ chứa nút Tiếp theo
class SingleNavigationButton extends StatelessWidget {
  final bool enabled;
  final bool isLoading;
  final VoidCallback? onPressed;
  final String buttonText;
  final IconData? icon;

  const SingleNavigationButton({
    super.key,
    required this.enabled,
    this.isLoading = false,
    this.onPressed,
    this.buttonText = 'Tiếp theo',
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: enabled && !isLoading ? onPressed : null,
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    width: 20.w,
                    height: 20.h,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        buttonText,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onPrimary,
                            ),
                      ),
                      if (icon != null) ...[
                        SizedBox(width: 8.w),
                        Icon(
                          icon,
                          size: 20.sp,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                      ],
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
