import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Widget hiển thị progress indicator cho các bước tạo đơn thuốc
class StepIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final List<bool> completedSteps;
  final String stepTitle;
  final String stepDescription;
  final VoidCallback? onStepTapped;

  const StepIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.completedSteps,
    required this.stepTitle,
    required this.stepDescription,
    this.onStepTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          // Step Progress Row
          _buildStepProgressRow(context),
          
          SizedBox(height: 16.h),
          
          // Current Step Info
          _buildCurrentStepInfo(context),
          
          SizedBox(height: 12.h),
          
          // Progress Bar
          _buildProgressBar(context),
        ],
      ),
    );
  }

  Widget _buildStepProgressRow(BuildContext context) {
    return Row(
      children: List.generate(totalSteps, (index) {
        final isActive = index == currentStep;
        final isCompleted = index < completedSteps.length && completedSteps[index];
        final isPast = index < currentStep;
        
        return Expanded(
          child: Row(
            children: [
              // Step Circle
              GestureDetector(
                onTap: onStepTapped,
                child: Container(
                  width: 32.w,
                  height: 32.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getStepColor(context, isActive, isCompleted, isPast),
                    border: Border.all(
                      color: _getStepBorderColor(context, isActive, isCompleted, isPast),
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: _getStepIcon(context, index, isActive, isCompleted, isPast),
                  ),
                ),
              ),
              
              // Connector Line (except for last step)
              if (index < totalSteps - 1)
                Expanded(
                  child: Container(
                    height: 2.h,
                    margin: EdgeInsets.symmetric(horizontal: 8.w),
                    decoration: BoxDecoration(
                      color: isPast || isCompleted
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(1.r),
                    ),
                  ),
                ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildCurrentStepInfo(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.info_outline,
          color: Theme.of(context).colorScheme.primary,
          size: 20.sp,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            'Bước ${currentStep + 1}/$totalSteps: $stepTitle',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar(BuildContext context) {
    return Column(
      children: [
        LinearProgressIndicator(
          value: (currentStep + 1) / totalSteps,
          backgroundColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).colorScheme.primary,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          stepDescription,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Color _getStepColor(BuildContext context, bool isActive, bool isCompleted, bool isPast) {
    if (isCompleted) {
      return Theme.of(context).colorScheme.primary;
    } else if (isActive) {
      return Theme.of(context).colorScheme.primary.withOpacity(0.2);
    } else if (isPast) {
      return Theme.of(context).colorScheme.primary.withOpacity(0.1);
    } else {
      return Colors.transparent;
    }
  }

  Color _getStepBorderColor(BuildContext context, bool isActive, bool isCompleted, bool isPast) {
    if (isCompleted || isActive) {
      return Theme.of(context).colorScheme.primary;
    } else {
      return Theme.of(context).colorScheme.outline.withOpacity(0.3);
    }
  }

  Widget _getStepIcon(BuildContext context, int index, bool isActive, bool isCompleted, bool isPast) {
    if (isCompleted) {
      return Icon(
        Icons.check,
        size: 16.sp,
        color: Colors.white,
      );
    } else {
      return Text(
        '${index + 1}',
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: isActive
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.outline,
              fontWeight: FontWeight.bold,
            ),
      );
    }
  }
}
