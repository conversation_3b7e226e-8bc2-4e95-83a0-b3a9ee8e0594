import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Widget cho bước 2: Th<PERSON><PERSON> thuốc v<PERSON>o đơn thuốc
class AddMedicineStep extends StatefulWidget {
  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onDataChanged;

  const AddMedicineStep({
    super.key,
    required this.initialData,
    required this.onDataChanged,
  });

  @override
  State<AddMedicineStep> createState() => _AddMedicineStepState();
}

class _AddMedicineStepState extends State<AddMedicineStep> {
  final _searchController = TextEditingController();
  
  List<Map<String, dynamic>> _medicines = [];
  List<Map<String, dynamic>> _searchResults = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
    _loadAvailableMedicines();
  }

  void _initializeData() {
    final data = widget.initialData;
    _medicines = List<Map<String, dynamic>>.from(data['medicines'] ?? []);
  }

  void _updateData() {
    final data = {'medicines': _medicines};
    widget.onDataChanged(data);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search Section
          _buildSearchSection(),
          
          SizedBox(height: 24.h),
          
          // Added Medicines Section
          if (_medicines.isNotEmpty) ...[
            _buildSectionTitle('Thuốc đã thêm (${_medicines.length})'),
            SizedBox(height: 16.h),
            _buildAddedMedicinesList(),
            SizedBox(height: 24.h),
          ],
          
          // Search Results or Available Medicines
          if (_searchResults.isNotEmpty) ...[
            _buildSectionTitle('Kết quả tìm kiếm'),
            SizedBox(height: 16.h),
            _buildSearchResults(),
          ] else if (_searchController.text.isEmpty) ...[
            _buildSectionTitle('Thuốc có sẵn'),
            SizedBox(height: 16.h),
            _buildAvailableMedicines(),
          ],
          
          SizedBox(height: 24.h),
          
          // Add New Medicine Button
          _buildAddNewMedicineButton(),
          
          // Add bottom padding for navigation buttons
          SizedBox(height: 100.h),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
    );
  }

  Widget _buildSearchSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tìm kiếm thuốc',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Nhập tên thuốc để tìm kiếm...',
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _searchResults.clear();
                      });
                    },
                    icon: const Icon(Icons.clear),
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          onChanged: _onSearchChanged,
        ),
      ],
    );
  }

  Widget _buildAddedMedicinesList() {
    return Column(
      children: _medicines.map((medicine) {
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              // Medicine Icon
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.medication,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24.sp,
                ),
              ),
              
              SizedBox(width: 12.w),
              
              // Medicine Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      medicine['name'] ?? 'Tên thuốc',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '${medicine['dose_amount']} ${medicine['dose_unit']} - ${medicine['route']}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          ),
                    ),
                    if (medicine['instructions'] != null && medicine['instructions'].isNotEmpty) ...[
                      SizedBox(height: 4.h),
                      Text(
                        medicine['instructions'],
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                            ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              
              // Remove Button
              IconButton(
                onPressed: () => _removeMedicine(medicine),
                icon: Icon(
                  Icons.remove_circle,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSearchResults() {
    return Column(
      children: _searchResults.map((medicine) {
        final isAdded = _medicines.any((m) => m['id'] == medicine['id']);
        
        return Container(
          margin: EdgeInsets.only(bottom: 8.h),
          child: ListTile(
            leading: Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.medication,
                color: Theme.of(context).colorScheme.primary,
                size: 20.sp,
              ),
            ),
            title: Text(medicine['name'] ?? 'Tên thuốc'),
            subtitle: Text(medicine['description'] ?? 'Mô tả thuốc'),
            trailing: isAdded
                ? Icon(
                    Icons.check_circle,
                    color: Theme.of(context).colorScheme.primary,
                  )
                : IconButton(
                    onPressed: () => _showAddMedicineDialog(medicine),
                    icon: const Icon(Icons.add_circle_outline),
                  ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            tileColor: Theme.of(context).colorScheme.surface,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAvailableMedicines() {
    // Mock data for available medicines
    final availableMedicines = [
      {'id': '1', 'name': 'Paracetamol 500mg', 'description': 'Thuốc giảm đau, hạ sốt'},
      {'id': '2', 'name': 'Amoxicillin 250mg', 'description': 'Kháng sinh'},
      {'id': '3', 'name': 'Vitamin C 1000mg', 'description': 'Bổ sung vitamin C'},
    ];

    return Column(
      children: availableMedicines.map((medicine) {
        final isAdded = _medicines.any((m) => m['id'] == medicine['id']);
        
        return Container(
          margin: EdgeInsets.only(bottom: 8.h),
          child: ListTile(
            leading: Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.medication,
                color: Theme.of(context).colorScheme.primary,
                size: 20.sp,
              ),
            ),
            title: Text(medicine['name'] ?? 'Tên thuốc'),
            subtitle: Text(medicine['description'] ?? 'Mô tả thuốc'),
            trailing: isAdded
                ? Icon(
                    Icons.check_circle,
                    color: Theme.of(context).colorScheme.primary,
                  )
                : IconButton(
                    onPressed: () => _showAddMedicineDialog(medicine),
                    icon: const Icon(Icons.add_circle_outline),
                  ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            tileColor: Theme.of(context).colorScheme.surface,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAddNewMedicineButton() {
    return OutlinedButton.icon(
      onPressed: () {
        // TODO: Navigate to add new medicine page
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chức năng thêm thuốc mới sẽ được phát triển sau'),
          ),
        );
      },
      icon: const Icon(Icons.add),
      label: const Text('Thêm thuốc mới vào tủ thuốc'),
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
    );
  }

  void _onSearchChanged(String query) {
    setState(() {
      _isSearching = query.isNotEmpty;
    });

    if (query.isEmpty) {
      setState(() {
        _searchResults.clear();
      });
      return;
    }

    // Mock search implementation
    final mockResults = [
      {'id': '1', 'name': 'Paracetamol 500mg', 'description': 'Thuốc giảm đau, hạ sốt'},
      {'id': '2', 'name': 'Amoxicillin 250mg', 'description': 'Kháng sinh'},
    ].where((medicine) => 
        medicine['name']!.toLowerCase().contains(query.toLowerCase())
    ).toList();

    setState(() {
      _searchResults = mockResults;
    });
  }

  void _showAddMedicineDialog(Map<String, dynamic> medicine) {
    double doseAmount = 1.0;
    String doseUnit = 'viên';
    String route = 'Uống';
    int quantity = 30;
    String instructions = '';

    final doseUnits = ['viên', 'ml', 'mg', 'g', 'gói', 'ống'];
    final routes = ['Uống', 'Tiêm', 'Bôi', 'Nhỏ mắt', 'Nhỏ tai', 'Xịt mũi'];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('Thêm ${medicine['name']}'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Dose Amount
                TextFormField(
                  initialValue: doseAmount.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Liều lượng',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    doseAmount = double.tryParse(value) ?? 1.0;
                  },
                ),
                SizedBox(height: 16.h),
                
                // Dose Unit
                DropdownButtonFormField<String>(
                  value: doseUnit,
                  decoration: const InputDecoration(
                    labelText: 'Đơn vị',
                    border: OutlineInputBorder(),
                  ),
                  items: doseUnits.map((unit) => DropdownMenuItem(
                    value: unit,
                    child: Text(unit),
                  )).toList(),
                  onChanged: (value) {
                    setDialogState(() {
                      doseUnit = value!;
                    });
                  },
                ),
                SizedBox(height: 16.h),
                
                // Route
                DropdownButtonFormField<String>(
                  value: route,
                  decoration: const InputDecoration(
                    labelText: 'Đường dùng',
                    border: OutlineInputBorder(),
                  ),
                  items: routes.map((r) => DropdownMenuItem(
                    value: r,
                    child: Text(r),
                  )).toList(),
                  onChanged: (value) {
                    setDialogState(() {
                      route = value!;
                    });
                  },
                ),
                SizedBox(height: 16.h),
                
                // Instructions
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Hướng dẫn sử dụng',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  onChanged: (value) {
                    instructions = value;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () {
                _addMedicine({
                  ...medicine,
                  'dose_amount': doseAmount,
                  'dose_unit': doseUnit,
                  'route': route,
                  'quantity': quantity,
                  'instructions': instructions,
                });
                Navigator.of(context).pop();
              },
              child: const Text('Thêm'),
            ),
          ],
        ),
      ),
    );
  }

  void _addMedicine(Map<String, dynamic> medicine) {
    setState(() {
      _medicines.add(medicine);
    });
    _updateData();
  }

  void _removeMedicine(Map<String, dynamic> medicine) {
    setState(() {
      _medicines.removeWhere((m) => m['id'] == medicine['id']);
    });
    _updateData();
  }

  void _loadAvailableMedicines() {
    // TODO: Load available medicines from API
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
