import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Widget cho bước 1: Thông tin cơ bản của đơn thuốc
class BasicInfoStep extends StatefulWidget {
  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onDataChanged;

  const BasicInfoStep({
    super.key,
    required this.initialData,
    required this.onDataChanged,
  });

  @override
  State<BasicInfoStep> createState() => _BasicInfoStepState();
}

class _BasicInfoStepState extends State<BasicInfoStep> {
  final _formKey = GlobalKey<FormState>();
  final _doctorNameController = TextEditingController();
  final _diagnosisController = TextEditingController();
  final _notesController = TextEditingController();
  final _hospitalController = TextEditingController();

  DateTime _prescriptionDate = DateTime.now();
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  String _selectedPatient = 'Tôi';

  final List<String> _patients = [
    'Tôi',
    'Vợ/Chồng',
    'Con trai',
    'Con gái',
    'B<PERSON>',
    'Mẹ',
  ];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final data = widget.initialData;
    
    _doctorNameController.text = data['doctorName'] ?? '';
    _diagnosisController.text = data['diagnosis'] ?? '';
    _notesController.text = data['notes'] ?? '';
    _hospitalController.text = data['hospital'] ?? '';
    
    _selectedPatient = data['selectedPatient'] ?? 'Tôi';
    _prescriptionDate = data['prescriptionDate'] ?? DateTime.now();
    _startDate = data['startDate'] ?? DateTime.now();
    _endDate = data['endDate'];
  }

  void _updateData() {
    final data = {
      'doctorName': _doctorNameController.text,
      'diagnosis': _diagnosisController.text,
      'notes': _notesController.text,
      'hospital': _hospitalController.text,
      'selectedPatient': _selectedPatient,
      'prescriptionDate': _prescriptionDate,
      'startDate': _startDate,
      'endDate': _endDate,
    };
    
    widget.onDataChanged(data);
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Basic Information Section
            _buildSectionTitle('Thông tin cơ bản'),
            SizedBox(height: 16.h),
            _buildBasicInfoSection(),

            SizedBox(height: 32.h),

            // Doctor Information Section
            _buildSectionTitle('Thông tin bác sĩ'),
            SizedBox(height: 16.h),
            _buildDoctorInfoSection(),

            SizedBox(height: 32.h),

            // Medical Information Section
            _buildSectionTitle('Thông tin y tế'),
            SizedBox(height: 16.h),
            _buildMedicalInfoSection(),

            SizedBox(height: 32.h),

            // Timeline Section
            _buildSectionTitle('Thời gian điều trị'),
            SizedBox(height: 16.h),
            _buildTimelineSection(),

            SizedBox(height: 32.h),

            // Notes Section
            _buildSectionTitle('Ghi chú'),
            SizedBox(height: 16.h),
            _buildNotesSection(),

            // Add bottom padding for navigation buttons
            SizedBox(height: 100.h),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      children: [
        // Patient Selection
        DropdownButtonFormField<String>(
          value: _selectedPatient,
          decoration: InputDecoration(
            labelText: 'Bệnh nhân *',
            prefixIcon: const Icon(Icons.person),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          items: _patients.map((patient) {
            return DropdownMenuItem(
              value: patient,
              child: Text(patient),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedPatient = value!;
            });
            _updateData();
          },
        ),

        SizedBox(height: 16.h),

        // Prescription Date
        InkWell(
          onTap: () => _selectPrescriptionDate(),
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'Ngày kê đơn *',
              prefixIcon: const Icon(Icons.calendar_today),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              '${_prescriptionDate.day}/${_prescriptionDate.month}/${_prescriptionDate.year}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDoctorInfoSection() {
    return Column(
      children: [
        // Doctor Name
        TextFormField(
          controller: _doctorNameController,
          decoration: InputDecoration(
            labelText: 'Tên bác sĩ *',
            hintText: 'Ví dụ: BS. Nguyễn Văn A',
            prefixIcon: const Icon(Icons.local_hospital),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Vui lòng nhập tên bác sĩ';
            }
            return null;
          },
          onChanged: (_) => _updateData(),
        ),

        SizedBox(height: 16.h),

        // Hospital/Clinic (Optional)
        TextFormField(
          controller: _hospitalController,
          decoration: InputDecoration(
            labelText: 'Bệnh viện/Phòng khám',
            hintText: 'Ví dụ: Bệnh viện Đa khoa Thành phố',
            prefixIcon: const Icon(Icons.business),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          onChanged: (_) => _updateData(),
        ),
      ],
    );
  }

  Widget _buildMedicalInfoSection() {
    return TextFormField(
      controller: _diagnosisController,
      maxLines: 3,
      decoration: InputDecoration(
        labelText: 'Chẩn đoán *',
        hintText: 'Ví dụ: Viêm họng cấp, sốt',
        prefixIcon: const Icon(Icons.medical_services),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        alignLabelWithHint: true,
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Vui lòng nhập chẩn đoán';
        }
        return null;
      },
      onChanged: (_) => _updateData(),
    );
  }

  Widget _buildTimelineSection() {
    return Column(
      children: [
        // Start Date
        InkWell(
          onTap: () => _selectStartDate(),
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'Ngày bắt đầu điều trị *',
              prefixIcon: const Icon(Icons.play_arrow),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              '${_startDate.day}/${_startDate.month}/${_startDate.year}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ),
        ),

        SizedBox(height: 16.h),

        // End Date (Optional)
        InkWell(
          onTap: () => _selectEndDate(),
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'Ngày kết thúc điều trị (tùy chọn)',
              prefixIcon: const Icon(Icons.stop),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              suffixIcon: _endDate != null
                  ? IconButton(
                      onPressed: () {
                        setState(() {
                          _endDate = null;
                        });
                        _updateData();
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
            ),
            child: Text(
              _endDate != null
                  ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                  : 'Chọn ngày kết thúc',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: _endDate != null
                        ? null
                        : Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                  ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return TextFormField(
      controller: _notesController,
      maxLines: 4,
      decoration: InputDecoration(
        labelText: 'Ghi chú (tùy chọn)',
        hintText: 'Thêm ghi chú về đơn thuốc, lưu ý đặc biệt...',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        alignLabelWithHint: true,
      ),
      onChanged: (_) => _updateData(),
    );
  }

  Future<void> _selectPrescriptionDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _prescriptionDate,
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        _prescriptionDate = picked;
      });
      _updateData();
    }
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now().subtract(const Duration(days: 7)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked;
        // Reset end date if it's before start date
        if (_endDate != null && _endDate!.isBefore(picked)) {
          _endDate = null;
        }
      });
      _updateData();
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate.add(const Duration(days: 7)),
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _endDate = picked;
      });
      _updateData();
    }
  }

  @override
  void dispose() {
    _doctorNameController.dispose();
    _diagnosisController.dispose();
    _notesController.dispose();
    _hospitalController.dispose();
    super.dispose();
  }
}
