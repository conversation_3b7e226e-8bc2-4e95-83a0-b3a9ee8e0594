import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Widget cho bước 3: <PERSON><PERSON><PERSON> lịch uống thuốc
class CreateScheduleStep extends StatefulWidget {
  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onDataChanged;

  const CreateScheduleStep({
    super.key,
    required this.initialData,
    required this.onDataChanged,
  });

  @override
  State<CreateScheduleStep> createState() => _CreateScheduleStepState();
}

class _CreateScheduleStepState extends State<CreateScheduleStep> {
  List<Map<String, dynamic>> _medicines = [];
  List<Map<String, dynamic>> _schedules = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    final data = widget.initialData;
    _medicines = List<Map<String, dynamic>>.from(data['medicines'] ?? []);
    _schedules = List<Map<String, dynamic>>.from(data['schedules'] ?? []);
    
    // Tạo schedule mặc định cho các thuốc chưa có schedule
    for (final medicine in _medicines) {
      final hasSchedule = _schedules.any((s) => s['medicine_id'] == medicine['id']);
      if (!hasSchedule) {
        _schedules.add({
          'medicine_id': medicine['id'],
          'medicine_name': medicine['name'],
          'frequency': 'Hàng ngày',
          'times_per_day': 2,
          'times': ['08:00', '20:00'],
          'selected_days': [true, true, true, true, true, true, true], // All days
          'dose_amount': medicine['dose_amount'] ?? 1.0,
          'dose_unit': medicine['dose_unit'] ?? 'viên',
        });
      }
    }
  }

  void _updateData() {
    final data = {'schedules': _schedules};
    widget.onDataChanged(data);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(),
          
          SizedBox(height: 24.h),
          
          // Schedules List
          if (_schedules.isNotEmpty) ...[
            _buildSchedulesList(),
          ] else ...[
            _buildEmptyState(),
          ],
          
          // Add bottom padding for navigation buttons
          SizedBox(height: 100.h),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tạo lịch uống thuốc',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        SizedBox(height: 8.h),
        Text(
          'Thiết lập thời gian uống thuốc cho từng loại thuốc trong đơn',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
        ),
      ],
    );
  }

  Widget _buildSchedulesList() {
    return Column(
      children: _schedules.map((schedule) {
        return Container(
          margin: EdgeInsets.only(bottom: 16.h),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Medicine Header
              Row(
                children: [
                  Container(
                    width: 40.w,
                    height: 40.w,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      Icons.medication,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20.sp,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          schedule['medicine_name'] ?? 'Tên thuốc',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        Text(
                          '${schedule['dose_amount']} ${schedule['dose_unit']} mỗi lần',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => _editSchedule(schedule),
                    icon: const Icon(Icons.edit),
                  ),
                ],
              ),
              
              SizedBox(height: 16.h),
              
              // Schedule Details
              _buildScheduleDetails(schedule),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildScheduleDetails(Map<String, dynamic> schedule) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Frequency
        Row(
          children: [
            Icon(
              Icons.repeat,
              size: 16.sp,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            SizedBox(width: 8.w),
            Text(
              'Tần suất: ${schedule['frequency']}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
        
        SizedBox(height: 8.h),
        
        // Times
        Row(
          children: [
            Icon(
              Icons.access_time,
              size: 16.sp,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                'Giờ uống: ${(schedule['times'] as List<String>).join(', ')}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
        
        SizedBox(height: 8.h),
        
        // Days (if not daily)
        if (schedule['frequency'] == 'Theo ngày trong tuần') ...[
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16.sp,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  'Ngày: ${_getSelectedDaysText(schedule['selected_days'])}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Icon(
            Icons.schedule,
            size: 64.sp,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
          ),
          SizedBox(height: 16.h),
          Text(
            'Chưa có thuốc nào để tạo lịch',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Vui lòng thêm thuốc ở bước trước',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
          ),
        ],
      ),
    );
  }

  void _editSchedule(Map<String, dynamic> schedule) {
    showDialog(
      context: context,
      builder: (context) => _ScheduleEditDialog(
        schedule: schedule,
        onSave: (updatedSchedule) {
          setState(() {
            final index = _schedules.indexWhere((s) => s['medicine_id'] == schedule['medicine_id']);
            if (index != -1) {
              _schedules[index] = updatedSchedule;
            }
          });
          _updateData();
        },
      ),
    );
  }

  String _getSelectedDaysText(List<bool> selectedDays) {
    final dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
    final selectedDayNames = <String>[];
    
    for (int i = 0; i < selectedDays.length; i++) {
      if (selectedDays[i]) {
        selectedDayNames.add(dayNames[i]);
      }
    }
    
    return selectedDayNames.join(', ');
  }
}

/// Dialog để chỉnh sửa lịch uống thuốc
class _ScheduleEditDialog extends StatefulWidget {
  final Map<String, dynamic> schedule;
  final Function(Map<String, dynamic>) onSave;

  const _ScheduleEditDialog({
    required this.schedule,
    required this.onSave,
  });

  @override
  State<_ScheduleEditDialog> createState() => _ScheduleEditDialogState();
}

class _ScheduleEditDialogState extends State<_ScheduleEditDialog> {
  late String _frequency;
  late int _timesPerDay;
  late List<String> _times;
  late List<bool> _selectedDays;
  late double _doseAmount;
  late String _doseUnit;

  final List<String> _frequencies = ['Hàng ngày', 'Theo ngày trong tuần'];
  final List<String> _doseUnits = ['viên', 'ml', 'mg', 'g', 'gói', 'ống'];

  @override
  void initState() {
    super.initState();
    _frequency = widget.schedule['frequency'] ?? 'Hàng ngày';
    _timesPerDay = widget.schedule['times_per_day'] ?? 2;
    _times = List<String>.from(widget.schedule['times'] ?? ['08:00', '20:00']);
    _selectedDays = List<bool>.from(widget.schedule['selected_days'] ?? [true, true, true, true, true, true, true]);
    _doseAmount = widget.schedule['dose_amount']?.toDouble() ?? 1.0;
    _doseUnit = widget.schedule['dose_unit'] ?? 'viên';
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Chỉnh sửa lịch uống thuốc'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Dose Amount
            TextFormField(
              initialValue: _doseAmount.toString(),
              decoration: const InputDecoration(
                labelText: 'Liều lượng mỗi lần',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                _doseAmount = double.tryParse(value) ?? 1.0;
              },
            ),
            SizedBox(height: 16.h),
            
            // Dose Unit
            DropdownButtonFormField<String>(
              value: _doseUnit,
              decoration: const InputDecoration(
                labelText: 'Đơn vị',
                border: OutlineInputBorder(),
              ),
              items: _doseUnits.map((unit) => DropdownMenuItem(
                value: unit,
                child: Text(unit),
              )).toList(),
              onChanged: (value) {
                setState(() {
                  _doseUnit = value!;
                });
              },
            ),
            SizedBox(height: 16.h),
            
            // Frequency
            DropdownButtonFormField<String>(
              value: _frequency,
              decoration: const InputDecoration(
                labelText: 'Tần suất',
                border: OutlineInputBorder(),
              ),
              items: _frequencies.map((freq) => DropdownMenuItem(
                value: freq,
                child: Text(freq),
              )).toList(),
              onChanged: (value) {
                setState(() {
                  _frequency = value!;
                });
              },
            ),
            SizedBox(height: 16.h),
            
            // Times per day
            TextFormField(
              initialValue: _timesPerDay.toString(),
              decoration: const InputDecoration(
                labelText: 'Số lần uống mỗi ngày',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                final newTimesPerDay = int.tryParse(value) ?? 2;
                setState(() {
                  _timesPerDay = newTimesPerDay;
                  _updateTimes();
                });
              },
            ),
            SizedBox(height: 16.h),
            
            // Times
            Text('Giờ uống thuốc:', style: Theme.of(context).textTheme.titleSmall),
            SizedBox(height: 8.h),
            ..._times.asMap().entries.map((entry) {
              final index = entry.key;
              final time = entry.value;
              
              return Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: InkWell(
                  onTap: () => _selectTime(index),
                  child: InputDecorator(
                    decoration: InputDecoration(
                      labelText: 'Lần ${index + 1}',
                      border: OutlineInputBorder(),
                    ),
                    child: Text(time),
                  ),
                ),
              );
            }).toList(),
            
            // Days selection (if weekly frequency)
            if (_frequency == 'Theo ngày trong tuần') ...[
              SizedBox(height: 16.h),
              Text('Chọn ngày trong tuần:', style: Theme.of(context).textTheme.titleSmall),
              SizedBox(height: 8.h),
              _buildDaySelector(),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Hủy'),
        ),
        ElevatedButton(
          onPressed: _saveSchedule,
          child: const Text('Lưu'),
        ),
      ],
    );
  }

  Widget _buildDaySelector() {
    final dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
    
    return Wrap(
      spacing: 8.w,
      children: List.generate(7, (index) {
        return FilterChip(
          label: Text(dayNames[index]),
          selected: _selectedDays[index],
          onSelected: (selected) {
            setState(() {
              _selectedDays[index] = selected;
            });
          },
        );
      }),
    );
  }

  void _updateTimes() {
    final defaultTimes = ['08:00', '12:00', '18:00', '22:00'];
    _times = List.generate(_timesPerDay, (index) {
      if (index < _times.length) {
        return _times[index];
      } else if (index < defaultTimes.length) {
        return defaultTimes[index];
      } else {
        return '08:00';
      }
    });
  }

  Future<void> _selectTime(int index) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: int.parse(_times[index].split(':')[0]),
        minute: int.parse(_times[index].split(':')[1]),
      ),
    );

    if (picked != null) {
      setState(() {
        _times[index] = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      });
    }
  }

  void _saveSchedule() {
    final updatedSchedule = Map<String, dynamic>.from(widget.schedule);
    updatedSchedule.addAll({
      'frequency': _frequency,
      'times_per_day': _timesPerDay,
      'times': _times,
      'selected_days': _selectedDays,
      'dose_amount': _doseAmount,
      'dose_unit': _doseUnit,
    });

    widget.onSave(updatedSchedule);
    Navigator.of(context).pop();
  }
}
