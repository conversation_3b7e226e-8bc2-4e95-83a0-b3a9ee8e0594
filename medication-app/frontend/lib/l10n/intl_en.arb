{"@@locale": "en", "@@last_modified": "2024-01-01T00:00:00.000Z", "appName": "Medication Manager", "@appName": {"description": "The name of the application"}, "ok": "OK", "@ok": {"description": "OK button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "save": "Save", "@save": {"description": "Save button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "add": "Add", "@add": {"description": "Add button text"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Error label"}, "success": "Success", "@success": {"description": "Success label"}, "warning": "Warning", "@warning": {"description": "Warning label"}, "info": "Information", "@info": {"description": "Information label"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "no": "No", "@no": {"description": "No button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "back": "Back", "@back": {"description": "Back button text"}, "next": "Next", "@next": {"description": "Next button text"}, "done": "Done", "@done": {"description": "Done button text"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "close": "Close", "@close": {"description": "Close button text"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "register": "Register", "@register": {"description": "Register button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "forgotPassword": "Forgot Password?", "@forgotPassword": {"description": "Forgot password link text"}, "rememberMe": "Remember me", "@rememberMe": {"description": "Remember me checkbox text"}, "loginSuccess": "Login successful", "@loginSuccess": {"description": "Login success message"}, "logoutSuccess": "Logout successful", "@logoutSuccess": {"description": "Logout success message"}, "invalidCredentials": "Invalid email or password", "@invalidCredentials": {"description": "Invalid credentials error message"}, "accountCreated": "Account created successfully", "@accountCreated": {"description": "Account creation success message"}, "medications": "Medications", "@medications": {"description": "Medications tab/page title"}, "medication": "Medication", "@medication": {"description": "Single medication label"}, "medicationName": "Medication Name", "@medicationName": {"description": "Medication name field label"}, "dosage": "Dosage", "@dosage": {"description": "Dosage field label"}, "frequency": "Frequency", "@frequency": {"description": "Frequency field label"}, "medicationType": "Type", "@medicationType": {"description": "Medication type field label"}, "description": "Description", "@description": {"description": "Description field label"}, "addMedication": "Add Medication", "@addMedication": {"description": "Add medication button text"}, "editMedication": "Edit Medication", "@editMedication": {"description": "Edit medication button text"}, "deleteMedication": "Delete Medication", "@deleteMedication": {"description": "Delete medication button text"}, "medicationAdded": "Medication added successfully", "@medicationAdded": {"description": "Medication added success message"}, "medicationUpdated": "Medication updated successfully", "@medicationUpdated": {"description": "Medication updated success message"}, "medicationDeleted": "Medication deleted successfully", "@medicationDeleted": {"description": "Medication deleted success message"}, "tablet": "Tablet", "@tablet": {"description": "Tablet medication type"}, "capsule": "Capsule", "@capsule": {"description": "Capsule medication type"}, "syrup": "<PERSON><PERSON><PERSON>", "@syrup": {"description": "Syrup medication type"}, "injection": "Injection", "@injection": {"description": "Injection medication type"}, "eyeDrops": "Eye Drops", "@eyeDrops": {"description": "Eye drops medication type"}, "ointment": "Ointment", "@ointment": {"description": "Ointment medication type"}, "other": "Other", "@other": {"description": "Other medication type"}, "reminders": "Reminders", "@reminders": {"description": "Reminders tab/page title"}, "reminder": "Reminder", "@reminder": {"description": "Single reminder label"}, "addReminder": "<PERSON><PERSON>", "@addReminder": {"description": "Add reminder button text"}, "editReminder": "<PERSON>minder", "@editReminder": {"description": "Edit reminder button text"}, "deleteReminder": "Delete Reminder", "@deleteReminder": {"description": "Delete reminder button text"}, "reminderTime": "Reminder Time", "@reminderTime": {"description": "Reminder time field label"}, "reminderAdded": "<PERSON><PERSON><PERSON> added successfully", "@reminderAdded": {"description": "<PERSON>mind<PERSON> added success message"}, "reminderUpdated": "Reminder updated successfully", "@reminderUpdated": {"description": "Reminder updated success message"}, "reminderDeleted": "<PERSON><PERSON><PERSON> deleted successfully", "@reminderDeleted": {"description": "<PERSON>mind<PERSON> deleted success message"}, "timeToTakeMedication": "Time to take your medication", "@timeToTakeMedication": {"description": "Medication reminder notification text"}, "prescriptions": "Prescriptions", "@prescriptions": {"description": "Prescriptions tab/page title"}, "prescription": "Prescription", "@prescription": {"description": "Single prescription label"}, "doctorName": "Doctor Name", "@doctorName": {"description": "Doctor name field label"}, "prescriptionDate": "Prescription Date", "@prescriptionDate": {"description": "Prescription date field label"}, "addPrescription": "Add Prescription", "@addPrescription": {"description": "Add prescription button text"}, "editPrescription": "Edit Prescription", "@editPrescription": {"description": "Edit prescription button text"}, "deletePrescription": "Delete Prescription", "@deletePrescription": {"description": "Delete prescription button text"}, "profile": "Profile", "@profile": {"description": "Profile tab/page title"}, "personalInfo": "Personal Information", "@personalInfo": {"description": "Personal information section title"}, "settings": "Settings", "@settings": {"description": "Settings page title"}, "theme": "Theme", "@theme": {"description": "Theme setting label"}, "language": "Language", "@language": {"description": "Language setting label"}, "notifications": "Notifications", "@notifications": {"description": "Notifications setting label"}, "privacy": "Privacy", "@privacy": {"description": "Privacy setting label"}, "about": "About", "@about": {"description": "About page title"}, "lightMode": "Light Mode", "@lightMode": {"description": "Light theme mode label"}, "darkMode": "Dark Mode", "@darkMode": {"description": "Dark theme mode label"}, "systemMode": "System Mode", "@systemMode": {"description": "System theme mode label"}, "english": "English", "@english": {"description": "English language label"}, "vietnamese": "Tiếng <PERSON>", "@vietnamese": {"description": "Vietnamese language label"}, "networkError": "Network connection error", "@networkError": {"description": "Network error message"}, "timeoutError": "Request timeout", "@timeoutError": {"description": "Timeout error message"}, "serverError": "Server error", "@serverError": {"description": "Server error message"}, "unauthorizedError": "Session expired", "@unauthorizedError": {"description": "Unauthorized error message"}, "forbiddenError": "Access denied", "@forbiddenError": {"description": "Forbidden error message"}, "notFoundError": "Data not found", "@notFoundError": {"description": "Not found error message"}, "validationError": "Invalid data", "@validationError": {"description": "Validation error message"}, "unknownError": "Unknown error occurred", "@unknownError": {"description": "Unknown error message"}, "fieldRequired": "This field is required", "@fieldRequired": {"description": "Required field validation message"}, "invalidEmail": "Invalid email format", "@invalidEmail": {"description": "Invalid email validation message"}, "passwordTooShort": "Password must be at least 6 characters", "@passwordTooShort": {"description": "Password too short validation message"}, "passwordsNotMatch": "Passwords do not match", "@passwordsNotMatch": {"description": "Passwords not match validation message"}, "today": "Today", "@today": {"description": "Today label"}, "tomorrow": "Tomorrow", "@tomorrow": {"description": "Tomorrow label"}, "yesterday": "Yesterday", "@yesterday": {"description": "Yesterday label"}, "morning": "Morning", "@morning": {"description": "Morning time label"}, "afternoon": "Afternoon", "@afternoon": {"description": "Afternoon time label"}, "evening": "Evening", "@evening": {"description": "Evening time label"}, "night": "Night", "@night": {"description": "Night time label"}, "welcomeMessage": "Welcome to {appName}!", "@welcomeMessage": {"description": "Welcome message with app name", "placeholders": {"appName": {"type": "String", "example": "Medication Manager"}}}, "medicationCount": "You have {count} {count, plural, =0{medications} =1{medication} other{medications}}", "@medicationCount": {"description": "Medication count message", "placeholders": {"count": {"type": "int", "example": "5"}}}}