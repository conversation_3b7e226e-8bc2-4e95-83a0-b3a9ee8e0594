import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'generated/l10n.dart';
import 'app/routes/routes.dart';
import 'app/theme/app_theme.dart';
import 'core/config/config.dart';
import 'core/utils/app_logger.dart';

void main() async {
  // Đảm bảo Flutter binding được khởi tạo
  WidgetsFlutterBinding.ensureInitialized();

  AppLogger.i('🚀 Starting Medication App...');

  // Khóa orientation ở portrait mode
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  AppLogger.i('📱 Screen orientation locked to portrait');

  // Khởi tạo StorageService và AppConfig repository
  final appConfigRepository = AppConfigRepository();
  await appConfigRepository.init();
  AppLogger.i('✅ Storage services initialized');

  runApp(
    MyApp(appConfigRepository: appConfigRepository),
  );
  AppLogger.i('🎯 App started successfully');
}

class MyApp extends StatelessWidget {
  const MyApp({
    super.key,
    required this.appConfigRepository,
  });

  final AppConfigRepository appConfigRepository;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AppConfigBloc(repository: appConfigRepository)
        ..add(const AppConfigLoadRequested()),
      child: const _AppView(),
    );
  }
}

class _AppView extends StatelessWidget {
  const _AppView();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppConfigBloc, AppConfigState>(
      builder: (context, state) {
        AppLogger.i(
          '🎨 Building app with theme: ${state.themeMode.name}, '
          'locale: ${state.languageCode}',
        );

        return ScreenUtilInit(
          designSize: const Size(375, 812), // iPhone 12 design size
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return MaterialApp.router(
              title: 'Medication Manager',

              // Router configuration
              routerConfig: AppRouter.router,

              // Theme configuration
              theme: AppTheme.lightTheme,
              darkTheme: AppTheme.darkTheme,
              themeMode: state.config.materialThemeMode,

              // Localization configuration
              locale: state.config.locale,
              supportedLocales: S.delegate.supportedLocales,
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],

              // Remove debug banner
              debugShowCheckedModeBanner: false,
            );
          },
        );
      },
    );
  }
}
